% Basic test script to verify LAWN simulation code
% This script tests core functionality

function test_basic()
    fprintf('=== LAWN Simulation Basic Test ===\n');

    % Set random seed for reproducibility
    rng(20250820, 'twister');

    % Test 1: Configuration
    fprintf('\n[Test 1] Loading configuration...\n');
    try
        cfg = config();
        fprintf('  ✓ Configuration loaded\n');
        fprintf('    - Nt = %d antennas\n', cfg.Nt);
        fprintf('    - K = %d users\n', cfg.K);
        fprintf('    - theta0 = %d degrees\n', cfg.theta0_deg);
    catch ME
        fprintf('  ✗ Failed: %s\n', ME.message);
        return;
    end
    
    % Test 2: Steering vector
    fprintf('\n[Test 2] Testing steering vector...\n');
    try
        theta_rad = cfg.theta0_deg * pi/180;
        a = steering_vector(theta_rad, cfg.Nt, cfg.d, cfg.lambda);
        fprintf('  ✓ Steering vector computed, size: %dx%d\n', size(a,1), size(a,2));
        if size(a,1) ~= cfg.Nt || size(a,2) ~= 1
            fprintf('  ⚠ Warning: Expected size %dx1, got %dx%d\n', cfg.Nt, size(a,1), size(a,2));
        end
    catch ME
        fprintf('  ✗ Failed: %s\n', ME.message);
        return;
    end
    
    % Test 3: Channel generation
    fprintf('\n[Test 3] Testing channel generation...\n');
    try
        [H_list, He] = generate_channels(cfg, 'rayleigh');
        fprintf('  ✓ Channels generated\n');
        fprintf('    - H_list has %d users\n', length(H_list));
        fprintf('    - H1 size: %dx%d\n', size(H_list{1},1), size(H_list{1},2));
        fprintf('    - He size: %dx%d\n', size(He,1), size(He,2));
    catch ME
        fprintf('  ✗ Failed: %s\n', ME.message);
        return;
    end
    
    % Test 4: Basic precoder design
    fprintf('\n[Test 4] Testing MRT precoder...\n');
    try
        problem.H_list = H_list;
        problem.He = He;
        problem.theta0_rad = cfg.theta0_deg * pi/180;
        problem.Ptot = cfg.Ptot;
        problem.Nt = cfg.Nt;
        problem.d = cfg.d;
        problem.lambda = cfg.lambda;
        problem.sigma2 = cfg.sigma2;
        
        [F, Z, info] = design_precoder(problem, 'MRT', []);
        fprintf('  ✓ MRT precoder designed\n');
        fprintf('    - F size: %dx%d\n', size(F,1), size(F,2));
        fprintf('    - Z size: %dx%d\n', size(Z,1), size(Z,2));
        
        % Check power constraint
        Rx = F*F' + Z;
        power = trace(Rx);
        fprintf('    - Total power: %.4f (should be ≤ %.4f)\n', power, problem.Ptot);
        if power > problem.Ptot * 1.01
            fprintf('  ⚠ Warning: Power constraint violated\n');
        end
    catch ME
        fprintf('  ✗ Failed: %s\n', ME.message);
        return;
    end
    
    % Test 5: Rate computation
    fprintf('\n[Test 5] Testing rate computation...\n');
    try
        [R_sum, R_user] = sum_rate(H_list, Rx, cfg.sigma2);
        fprintf('  ✓ Sum rate computed: %.4f bits/s/Hz\n', R_sum);
        fprintf('    - User rates: [%.4f, %.4f]\n', R_user(1), R_user(2));
    catch ME
        fprintf('  ✗ Failed: %s\n', ME.message);
        return;
    end
    
    % Test 6: Beam pattern
    fprintf('\n[Test 6] Testing beam pattern...\n');
    try
        [theta_grid_deg, Pscan] = scan_beam(Rx, cfg.Nt, cfg.d, cfg.lambda, -90, 90, 5);
        fprintf('  ✓ Beam pattern computed\n');
        fprintf('    - Scanned %d angles\n', length(theta_grid_deg));
        fprintf('    - Max gain: %.2f dB\n', 10*log10(max(Pscan)));
    catch ME
        fprintf('  ✗ Failed: %s\n', ME.message);
        return;
    end
    
    fprintf('\n=== All tests passed! ===\n');
end
