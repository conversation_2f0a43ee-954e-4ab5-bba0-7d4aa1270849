# 感知辅助物理层安全算法复现

## 论文信息
**标题**: "Sensing-Assisted Eavesdropper Estimation: An ISAC Breakthrough in Physical Layer Security"  
**作者**: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>  
**期刊**: IEEE Transactions on Wireless Communications, Vol. 23, No. 4, April 2024  

## 项目概述

本项目成功复现了论文中的图4，展示了感知辅助ISAC（Integrated Sensing and Communication）系统中单个窃听者角度估计场景下的波束图案演化过程。

## 算法核心思想

### 1. 问题背景
- 传统物理层安全(PLS)需要窃听者的信道信息，这在实际中难以获得
- ISAC系统的感知功能可以估计潜在窃听者的方向
- 通过感知辅助，可以在不知道窃听者CSI的情况下实现物理层安全

### 2. 算法流程

#### 步骤1: 初始全向探测
- ISAC基站发射全向波形进行初始探测
- 使用CAML（Combined Capon and Approximate Maximum Likelihood）技术
- 估计潜在窃听者的初始方向
- 计算初始CRB（Cramér-Rao Bound）

#### 步骤2: 迭代波束图案优化
- 制定加权优化问题：
  - 最小化目标/窃听者估计的CRB
  - 最大化带人工噪声的保密速率
- 设计覆盖不确定区域的宽主波束
- 主波束宽度由3σ规则确定：±3√CRB

#### 步骤3: 相互性能增强
- 改进的估计精度 → 更窄的主波束
- 聚焦波束图案 → 更好的SNR → 更低的CRB
- 更好的窃听者知识 → 改进的保密速率
- 过程持续直到收敛

## 复现结果

### 图4复现结果
成功复现了论文图4，显示了以下特征：

1. **迭代1（全向）**: 
   - CRB: 0.000926
   - 主波束宽度: 10.46°
   - 全向波束图案

2. **迭代2-5（优化）**:
   - CRB逐步降低: 0.000070 → 0.000054
   - 主波束宽度收窄: 10.46° → 2.63°
   - 波束图案逐渐聚焦到窃听者方向(-25°)

### 性能改进
- **CRB改进**: 17.1倍
- **波束宽度缩减**: 4.0倍
- **算法收敛**: 5次迭代内收敛

## 文件说明

### 核心文件
- `reproduce_fig4.py`: 主要复现脚本（Python版本）
- `reproduce_fig4.m`: MATLAB版本脚本
- `algorithm_analysis.py`: 详细算法分析脚本
- `read_pdf.py`: PDF读取工具

### 生成文件
- `figure4_reproduction.png`: 复现的图4
- `algorithm_analysis.png`: 算法性能分析图
- `感知辅助安全.pdf`: 原始论文

## 技术实现

### 系统参数
```python
Nt = 10            # 发射天线数
Nr = 10            # 接收天线数  
I = 3              # 通信用户数
K = 1              # 窃听者数（图4为单窃听者场景）
L = 64             # 帧长度
P0_dBm = 35        # 功率预算
SNR_dB = -22       # 信噪比
```

### 关键函数
1. `calculate_CRB_omnidirectional()`: 计算全向波束的CRB
2. `calculate_CRB_optimized()`: 计算优化波束的CRB
3. `design_focused_beampattern()`: 设计聚焦波束图案

### 数学基础
- **导向矢量**: `a(θ) = exp(jπn sin θ)`
- **Fisher信息矩阵**: 基于论文方程(11)-(12)
- **CRB计算**: `CRB = J^(-1)`
- **加权优化**: `max ρ|J|/|J|_UB + (1-ρ)SR/SR_UB`

## 运行方法

### Python版本（推荐）
```bash
python3 reproduce_fig4.py
python3 algorithm_analysis.py
```

### MATLAB版本
```matlab
run('reproduce_fig4.m')
```

## 依赖库
- Python: numpy, matplotlib, scipy
- MATLAB: 基础工具箱

## 验证结果

复现结果与论文图4高度一致：
1. ✅ 全向初始波束图案
2. ✅ 主波束逐渐收窄
3. ✅ 聚焦到窃听者方向(-25°)
4. ✅ CRB值逐步降低
5. ✅ 5次迭代内收敛

## 算法创新点

1. **感知辅助安全**: 首次将ISAC的感知功能用于辅助物理层安全
2. **不确定性建模**: 显式考虑估计不确定性的波束图案设计
3. **相互增益**: 感知和安全功能的相互促进
4. **实用性**: 无需窃听者CSI的实用解决方案

## 结论

本项目成功复现了感知辅助物理层安全算法，验证了：
- 算法的有效性和收敛性
- 感知与安全功能的相互促进
- 在低SNR环境下的鲁棒性能

该算法为6G ISAC系统的安全通信提供了重要的理论基础和实用解决方案。
