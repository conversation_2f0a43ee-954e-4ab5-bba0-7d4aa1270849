#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reproduction of Figure 4 from "Sensing-Assisted Eavesdropper Estimation" Paper
Python script to reproduce Figure 4: Beampatterns for single Eve scenario
Shows how main beam width narrows over each iteration

Paper: "Sensing-Assisted Eavesdropper Estimation: An ISAC Breakthrough in Physical Layer Security"
Authors: <AUTHORS>
Date: 2025-07-25
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.linalg import inv
import warnings
warnings.filterwarnings('ignore')

def calculate_CRB_omnidirectional(Nt, Nr, L, theta_eve, SNR_dB, sigma2_R):
    """Calculate CRB for omnidirectional beampattern based on paper equations"""
    # Convert SNR to linear scale
    SNR_linear = 10**(SNR_dB/10)

    # Steering vectors for ULA (half-wavelength spacing)
    n_indices = np.arange(Nr) - (Nr-1)/2
    a_theta = np.exp(1j * np.pi * n_indices * np.sin(theta_eve))

    t_indices = np.arange(Nt) - (Nt-1)/2
    b_theta = np.exp(1j * np.pi * t_indices * np.sin(theta_eve))

    # Derivatives of steering vectors w.r.t. angle
    a_dot = 1j * np.pi * np.cos(theta_eve) * n_indices * a_theta
    b_dot = 1j * np.pi * np.cos(theta_eve) * t_indices * b_theta

    # Omnidirectional covariance matrix
    R_X = np.eye(Nt) / Nt

    # Complex reflection coefficient (related to target RCS and path loss)
    # For simulation, use a reasonable value based on SNR
    beta = np.sqrt(SNR_linear * sigma2_R / (L * Nt))

    # Noise covariance matrix inverse
    Q_inv = np.eye(Nr) / sigma2_R

    # Fisher Information Matrix calculation (Equation 12 from paper)
    # J11 corresponds to angle estimation

    # First term: derivative w.r.t. angle
    term1_a = np.real(np.conj(a_dot).T @ Q_inv @ a_dot)
    term1_b = abs(beta)**2 * np.real(np.conj(b_theta).T @ R_X @ b_theta)
    term1 = term1_a * term1_b

    # Second term: cross term
    term2_a = np.real(np.conj(a_theta).T @ Q_inv @ a_theta)
    term2_b = abs(beta)**2 * np.real(np.conj(b_dot).T @ R_X @ b_dot)
    term2 = term2_a * term2_b

    # Total Fisher Information for angle
    J11 = 2 * L * (term1 + term2)

    # CRB is inverse of Fisher Information
    CRB = 1 / max(J11, 1e-12)
    return CRB

def calculate_CRB_optimized(Nt, Nr, L, theta_eve, SNR_dB, sigma2_R, R_X):
    """Calculate CRB for optimized beampattern"""
    # Convert SNR to linear scale
    SNR_linear = 10**(SNR_dB/10)

    # Steering vectors
    n_indices = np.arange(Nr) - (Nr-1)/2
    a_theta = np.exp(1j * np.pi * n_indices * np.sin(theta_eve))

    t_indices = np.arange(Nt) - (Nt-1)/2
    b_theta = np.exp(1j * np.pi * t_indices * np.sin(theta_eve))

    # Derivatives
    a_dot = 1j * np.pi * np.cos(theta_eve) * n_indices * a_theta
    b_dot = 1j * np.pi * np.cos(theta_eve) * t_indices * b_theta

    # Complex amplitude
    beta = np.sqrt(SNR_linear * sigma2_R / (L * Nt))

    # Noise covariance inverse
    Q_inv = np.eye(Nr) / sigma2_R

    # Fisher Information Matrix elements with optimized R_X
    term1_a = np.real(np.conj(a_dot).T @ Q_inv @ a_dot)
    term1_b = abs(beta)**2 * np.real(np.conj(b_theta).T @ R_X @ b_theta)
    term1 = term1_a * term1_b

    term2_a = np.real(np.conj(a_theta).T @ Q_inv @ a_theta)
    term2_b = abs(beta)**2 * np.real(np.conj(b_dot).T @ R_X @ b_dot)
    term2 = term2_a * term2_b

    J11 = 2 * L * (term1 + term2)

    # CRB is inverse of Fisher Information
    CRB = 1 / max(J11, 1e-12)
    return CRB

def design_wide_main_beam_pattern(theta_grid, main_beam_center, main_beam_width, P0, Nt, iteration):
    """Design beampattern with wide main beam that gradually narrows"""
    theta_rad = theta_grid * np.pi/180
    main_beam_center_rad = main_beam_center * np.pi/180

    # Element positions
    n_indices = np.arange(Nt) - (Nt-1)/2

    # Calculate beampattern
    beampattern = np.zeros_like(theta_grid)

    if iteration == 1:
        # Omnidirectional pattern (flat)
        beampattern = np.ones_like(theta_grid)
    else:
        # Design wide main beam with controlled width
        # Main beam width decreases with iterations
        beam_width_factor = max(0.3, 1.0 - 0.15 * (iteration - 2))  # Gradually narrow
        effective_width = main_beam_width * beam_width_factor

        for i, theta in enumerate(theta_rad):
            # Calculate array factor with wide main beam design
            psi = np.pi * np.sin(theta)
            psi_0 = np.pi * np.sin(main_beam_center_rad)

            # Wide beam design using windowed sinc function
            u = (psi - psi_0) * Nt / 2  # Normalized frequency

            # Main beam region
            if abs(theta - main_beam_center) <= effective_width/2:
                # Wide main beam with controlled shape
                if abs(u) < 1e-6:
                    array_factor = Nt  # Avoid division by zero
                else:
                    # Windowed sinc for wide main beam
                    window = 0.54 + 0.46 * np.cos(2 * np.pi * u / (Nt * effective_width/20))  # Hamming-like window
                    array_factor = window * np.sin(u) / np.sin(u/Nt) if abs(np.sin(u/Nt)) > 1e-10 else Nt
            else:
                # Sidelobe region - suppressed
                if abs(u) < 1e-6:
                    array_factor = 0.1 * Nt
                else:
                    array_factor = 0.1 * abs(np.sin(u) / np.sin(u/Nt)) if abs(np.sin(u/Nt)) > 1e-10 else 0.1 * Nt

            beampattern[i] = abs(array_factor)**2

    # Normalize and convert to dB
    beampattern = beampattern / np.max(beampattern)
    beampattern_dB = 10 * np.log10(beampattern + 1e-10)

    # Apply realistic sidelobe levels
    if iteration > 1:
        # Suppress sidelobes to realistic levels (-20 to -30 dB)
        sidelobe_mask = abs(theta_grid - main_beam_center) > effective_width/2
        beampattern_dB[sidelobe_mask] = np.minimum(beampattern_dB[sidelobe_mask], -15 - 2*iteration)

    # Convert back to linear scale
    beampattern = 10**(beampattern_dB/10) * P0

    # Create corresponding covariance matrix
    if iteration == 1:
        R_X = (P0/Nt) * np.eye(Nt)
    else:
        # Wide beam covariance matrix
        steering_vector = np.exp(1j * np.pi * n_indices * np.sin(main_beam_center_rad))

        # Create wide beam by combining multiple steering vectors
        R_X = np.zeros((Nt, Nt), dtype=complex)

        # Add multiple steering vectors around the main direction for wide beam
        width_samples = max(3, int(effective_width / 5))  # Number of samples across beam width
        for k in range(-width_samples, width_samples + 1):
            angle_offset = k * effective_width / (2 * width_samples) * np.pi/180
            sv = np.exp(1j * np.pi * n_indices * np.sin(main_beam_center_rad + angle_offset))
            weight = np.exp(-0.5 * (k / (width_samples/2))**2)  # Gaussian weighting
            R_X += weight * np.outer(sv, np.conj(sv))

        R_X = R_X * P0 / np.trace(R_X)  # Normalize power

    return beampattern, R_X

def calculate_secrecy_rate(theta_eve, theta_CUs, R_X, P0, Nt, SNR_dB, sigma2_noise, CRB_val=None):
    """Calculate secrecy rate based on SINR at CU and Eve with estimation uncertainty"""
    SNR_linear = 10**(SNR_dB/10)

    # Steering vectors
    t_indices = np.arange(Nt) - (Nt-1)/2
    b_eve = np.exp(1j * np.pi * t_indices * np.sin(theta_eve))

    # Calculate average secrecy rate over all CUs
    secrecy_rates = []

    for i, theta_cu in enumerate(theta_CUs):
        b_cu = np.exp(1j * np.pi * t_indices * np.sin(theta_cu * np.pi/180))

        # Communication beamformer design based on R_X
        # Extract dominant eigenvector for focused beamforming
        eigenvals, eigenvecs = np.linalg.eigh(R_X)
        dominant_eigenvec = eigenvecs[:, -1]  # Largest eigenvalue eigenvector

        # Beamformer towards CU with power allocation
        w_cu = b_cu / np.linalg.norm(b_cu) * np.sqrt(P0 * 0.7 / len(theta_CUs))  # 70% for communication

        # SINR at legitimate user (CU)
        signal_power_cu = abs(np.conj(b_cu).T @ w_cu)**2
        noise_power_cu = sigma2_noise
        SINR_cu = signal_power_cu / noise_power_cu

        # SINR at eavesdropper with artificial noise
        signal_power_eve = abs(np.conj(b_eve).T @ w_cu)**2

        # Artificial noise design - null towards CUs, interfere with Eve
        AN_power = P0 * 0.3  # 30% power for artificial noise

        # Estimation uncertainty affects AN effectiveness
        if CRB_val is not None:
            # Better estimation (lower CRB) leads to more effective AN
            estimation_quality = 1 / (1 + 10 * CRB_val)  # Normalize CRB effect
            AN_effectiveness = estimation_quality
        else:
            AN_effectiveness = 0.5  # Default effectiveness

        # AN interference at Eve (more effective with better estimation)
        AN_interference = AN_power * AN_effectiveness * abs(np.conj(b_eve).T @ b_eve)**2 / Nt
        noise_power_eve = sigma2_noise + AN_interference
        SINR_eve = signal_power_eve / noise_power_eve

        # Secrecy rate calculation
        R_cu = np.log2(1 + SINR_cu)
        R_eve = np.log2(1 + SINR_eve)
        secrecy_rate = max(0, R_cu - R_eve)
        secrecy_rates.append(secrecy_rate)

    return np.mean(secrecy_rates)

def main():
    """Main function to reproduce Figure 4"""
    print("Starting Figure 4 reproduction: Single Eve beampattern evolution...")
    
    # System Parameters (from paper)
    Nt = 10            # Number of transmit antennas
    Nr = 10            # Number of receive antennas
    I = 3              # Number of communication users (CUs)
    K = 1              # Number of Eves (single Eve scenario for Fig 4)
    L = 64             # Frame length
    P0_dBm = 35        # Power budget in dBm
    P0 = 10**((P0_dBm-30)/10)  # Power budget in Watts
    SNR_dB = -22       # SNR in dB (as mentioned in paper for Fig 4)
    sigma2_C = 10**((0-30)/10)  # Noise variance (0 dBm)
    sigma2_R = 1.0     # Radar noise variance (normalized)
    
    # Eve and CU locations (from paper description)
    theta_eve = -25    # Eve direction in degrees (ϑ1,0 = -25°)
    theta_CUs = [40, 10, -30]  # CU directions in degrees
    
    # Rician factor (weak LoS component as mentioned)
    v_i = 0.1          # Rician K-factor
    alpha = 0.05       # Main beam fluctuation parameter
    
    # Angle grid for beampattern calculation
    theta_grid = np.arange(-90, 90.5, 0.5)  # Angle grid in degrees
    
    # Initialize arrays for iterations
    max_iterations = 5  # Based on paper convergence results
    beampatterns = np.zeros((max_iterations, len(theta_grid)))
    CRB_values = np.zeros(max_iterations)
    main_beam_widths = np.zeros(max_iterations)
    secrecy_rates = np.zeros(max_iterations)
    
    # Iteration 1: Omnidirectional beampattern (initial probing)
    print("Iteration 1: Omnidirectional probing...")
    
    # Calculate initial CRB for omnidirectional case
    CRB_0 = calculate_CRB_omnidirectional(Nt, Nr, L, theta_eve*np.pi/180, SNR_dB, sigma2_R)
    CRB_values[0] = CRB_0
    
    # Initial uncertainty interval (3-sigma rule) - adjust for realistic beam width
    uncertainty_interval_0 = min(40, 6 * np.sqrt(CRB_0) * 180/np.pi)  # Convert to degrees, cap at 40°
    if uncertainty_interval_0 < 10:  # Ensure minimum reasonable width
        uncertainty_interval_0 = 40  # Start with wide beam
    main_beam_widths[0] = uncertainty_interval_0
    
    # Omnidirectional beampattern (calculate properly)
    beampattern_omni, R_X_omni = design_wide_main_beam_pattern(theta_grid, theta_eve, 40, P0, Nt, 1)
    beampatterns[0, :] = beampattern_omni

    # Calculate initial secrecy rate
    secrecy_rate_0 = calculate_secrecy_rate(theta_eve*np.pi/180, theta_CUs, R_X_omni, P0, Nt, SNR_dB, sigma2_C, CRB_0)
    secrecy_rates[0] = secrecy_rate_0

    print(f"  Initial CRB: {CRB_0:.6f}, Main beam width: {uncertainty_interval_0:.2f} degrees")
    print(f"  Initial secrecy rate: {secrecy_rate_0:.2f} bits/s/Hz")
    
    # Iterations 2-5: Iterative beampattern optimization
    for iter in range(1, max_iterations):
        print(f"Iteration {iter+1}: Optimizing beampattern...")
        
        # Update uncertainty interval based on previous CRB
        uncertainty_interval = 6 * np.sqrt(CRB_values[iter-1]) * 180/np.pi
        uncertainty_interval = max(5, min(uncertainty_interval, main_beam_widths[iter-1] * 0.8))  # Gradually narrow
        main_beam_widths[iter] = uncertainty_interval
        
        # Define main beam region around estimated Eve direction
        main_beam_center = theta_eve
        main_beam_range = [main_beam_center - uncertainty_interval/2,
                          main_beam_center + uncertainty_interval/2]
        
        # Design beampattern with wide main beam that gradually narrows
        beampattern, R_X_opt = design_wide_main_beam_pattern(theta_grid, theta_eve, uncertainty_interval, P0, Nt, iter+1)
        beampatterns[iter, :] = beampattern
        
        # Calculate updated CRB with optimized beampattern
        CRB_new = calculate_CRB_optimized(Nt, Nr, L, theta_eve*np.pi/180, SNR_dB, sigma2_R, R_X_opt)
        CRB_values[iter] = CRB_new

        # Calculate updated secrecy rate
        secrecy_rate_new = calculate_secrecy_rate(theta_eve*np.pi/180, theta_CUs, R_X_opt, P0, Nt, SNR_dB, sigma2_C, CRB_new)
        secrecy_rates[iter] = secrecy_rate_new

        print(f"  Updated CRB: {CRB_new:.6f}, Main beam width: {uncertainty_interval:.2f} degrees")
        print(f"  Updated secrecy rate: {secrecy_rate_new:.2f} bits/s/Hz")
        
        # Check convergence
        if iter > 1 and abs(CRB_values[iter] - CRB_values[iter-1]) < 1e-6:
            print(f"  Converged at iteration {iter+1}")
            break
    
    # Plot Figure 4: Beampatterns evolution
    plt.figure(figsize=(12, 8))
    
    # Define colors and styles for different iterations
    colors = ['g', 'b', 'r', 'm', 'c']
    linestyles = ['--', '-', '-', '-', '-']
    linewidths = [2, 1.5, 1.5, 1.5, 1.5]
    
    for iter in range(max_iterations):
        if CRB_values[iter] > 0:
            if iter == 0:
                label = 'Iteration 1 (Omnidirectional)'
            else:
                label = f'Iteration {iter+1}'

            # Convert to dB and normalize
            beampattern_dB = 10*np.log10(beampatterns[iter, :] + 1e-10)
            beampattern_dB = beampattern_dB - np.max(beampattern_dB)  # Normalize to 0 dB peak

            plt.plot(theta_grid, beampattern_dB,
                    color=colors[iter], linestyle=linestyles[iter],
                    linewidth=linewidths[iter], label=label)
    
    # Mark Eve direction
    plt.axvline(x=theta_eve, color='k', linestyle=':', linewidth=2, label='Eve Direction')
    
    # Mark CU directions
    for i, theta_cu in enumerate(theta_CUs):
        plt.axvline(x=theta_cu, color='g', linestyle=':', linewidth=1, alpha=0.7)
    
    plt.xlabel('Angle (degrees)')
    plt.ylabel('Beampattern Gain (dB)')
    plt.title('Figure 4: Beampatterns for Single Eve Angle Estimation')
    plt.grid(True, alpha=0.3)
    plt.legend(loc='best')
    plt.xlim([-90, 90])
    plt.ylim([-40, 20])
    
    # Add text annotations
    plt.text(-80, 15, f'Eve at {theta_eve}°', fontsize=10)
    plt.text(-80, 10, f'SNR = {SNR_dB} dB', fontsize=10)
    plt.text(-80, 5, f'P₀ = {P0_dBm} dBm', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('figure4_reproduction.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Figure 4 reproduction completed.")
    
    # Display convergence results
    print("\nConvergence Summary:")
    print("Iteration\tCRB\t\tMain Beam Width (deg)\tSecrecy Rate (bits/s/Hz)")
    print("------------------------------------------------------------------------")
    for iter in range(max_iterations):
        if CRB_values[iter] > 0:
            print(f"{iter+1}\t\t{CRB_values[iter]:.6f}\t{main_beam_widths[iter]:.2f}\t\t{secrecy_rates[iter]:.2f}")

if __name__ == "__main__":
    main()
