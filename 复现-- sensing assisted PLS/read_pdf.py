#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import fitz  # PyMuPDF
import sys
import os

def read_pdf(filename):
    """Read PDF file and extract text"""
    try:
        # Check if file exists
        if not os.path.exists(filename):
            print(f"File {filename} not found")
            return None
            
        # Open the PDF file
        doc = fitz.open(filename)
        print(f"PDF opened successfully. Number of pages: {len(doc)}")
        
        # Extract text from all pages
        full_text = ""
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            full_text += f"\n=== PAGE {page_num + 1} ===\n"
            full_text += text
            full_text += "\n"
            
        doc.close()
        return full_text
        
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None

if __name__ == "__main__":
    filename = "感知辅助安全.pdf"
    text = read_pdf(filename)
    if text:
        print(text)
    else:
        print("Failed to read PDF")
