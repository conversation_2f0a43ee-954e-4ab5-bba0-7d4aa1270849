#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correct Beampattern Implementation using Paper Formula
P_bp = a^H(θ) R_X a(θ)
"""

import numpy as np
import matplotlib.pyplot as plt

def steering_vector(theta, N):
    """导向矢量 - 论文方程(7)"""
    n = np.arange(N) - (N-1)/2
    return np.exp(1j * np.pi * n * np.sin(theta))

def calculate_beampattern(R_X, theta_grid_deg):
    """
    使用论文公式计算波束图案: P_bp = a^H(θ) R_X a(θ)
    """
    theta_grid_rad = theta_grid_deg * np.pi / 180
    Nt = R_X.shape[0]
    beampattern = np.zeros(len(theta_grid_deg))
    
    for i, theta in enumerate(theta_grid_rad):
        # 计算导向矢量
        a_theta = steering_vector(theta, Nt)
        
        # 波束图案公式: P_bp = a^H(θ) R_X a(θ)
        P_bp = np.real(np.conj(a_theta).T @ R_X @ a_theta)
        beampattern[i] = P_bp
    
    return beampattern

def design_covariance_matrix(theta_center, beam_width_deg, P0, Nt, iteration):
    """
    设计发射协方差矩阵 R_X
    """
    if iteration == 1:
        # 全向：R_X = (P0/Nt) * I
        R_X = (P0/Nt) * np.eye(Nt)
    else:
        # 宽波束设计：在主波束范围内分布多个导向矢量
        R_X = np.zeros((Nt, Nt), dtype=complex)
        
        # 主波束范围内的角度采样
        beam_width_rad = beam_width_deg * np.pi / 180
        num_samples = max(5, int(beam_width_deg / 3))  # 根据波束宽度确定采样数
        
        for k in range(-num_samples, num_samples + 1):
            # 在主波束范围内的角度
            angle_offset = k * beam_width_rad / (2 * num_samples)
            theta_k = theta_center + angle_offset
            
            # 确保角度在有效范围内
            if abs(theta_k) <= np.pi/2:
                a_k = steering_vector(theta_k, Nt)
                
                # 高斯权重，中心权重最大
                weight = np.exp(-0.5 * (k / (num_samples/3))**2)
                
                # 累加协方差矩阵
                R_X += weight * np.outer(a_k, np.conj(a_k))
        
        # 功率归一化
        R_X = R_X * P0 / np.real(np.trace(R_X))
    
    return R_X

def simulate_figure_4():
    """
    使用正确公式仿真图4
    """
    print("使用正确公式仿真图4: P_bp = a^H(θ) R_X a(θ)")
    print("="*50)
    
    # 系统参数
    Nt = 10
    P0_dBm = 35
    P0 = 10**((P0_dBm - 30)/10)
    theta_eve = -25 * np.pi/180  # 窃听者角度
    
    # 角度网格：[-90°, 90°]
    theta_grid_deg = np.arange(-90, 90.5, 0.5)
    
    # 不同迭代的波束宽度（逐渐收窄）
    beam_widths = [60, 40, 25, 20, 15]  # 度
    iterations = len(beam_widths)
    
    # 存储结果
    beampatterns = []
    R_X_matrices = []
    
    # 计算每次迭代的波束图案
    for i in range(iterations):
        iteration = i + 1
        beam_width = beam_widths[i]
        
        print(f"迭代 {iteration}: 波束宽度 = {beam_width}°")
        
        # 设计协方差矩阵
        R_X = design_covariance_matrix(theta_eve, beam_width, P0, Nt, iteration)
        R_X_matrices.append(R_X)
        
        # 计算波束图案: P_bp = a^H(θ) R_X a(θ)
        beampattern = calculate_beampattern(R_X, theta_grid_deg)
        beampatterns.append(beampattern)
        
        # 验证功率约束
        total_power = np.real(np.trace(R_X))
        print(f"  协方差矩阵功率: {total_power:.2f} W (目标: {P0:.2f} W)")
    
    # 绘制图4
    plt.figure(figsize=(12, 8))
    
    colors = ['g', 'b', 'r', 'm', 'c']
    linestyles = ['--', '-', '-', '-', '-']
    linewidths = [2, 1.5, 1.5, 1.5, 1.5]
    
    for i in range(iterations):
        # 转换为dB并归一化
        beampattern_dB = 10 * np.log10(beampatterns[i] + 1e-10)
        beampattern_dB = beampattern_dB - np.max(beampattern_dB)  # 归一化到0dB峰值
        
        if i == 0:
            label = f'{i+1}st Iter (Omnidirectional)'
        elif i == 1:
            label = f'{i+1}nd Iter'
        elif i == 2:
            label = f'{i+1}rd Iter'
        else:
            label = f'{i+1}th Iter'
        
        plt.plot(theta_grid_deg, beampattern_dB,
                color=colors[i], linestyle=linestyles[i], 
                linewidth=linewidths[i], label=label)
    
    # 标记窃听者方向
    plt.axvline(x=theta_eve*180/np.pi, color='k', linestyle=':', 
               linewidth=2, alpha=0.7, label='Eve Direction')
    
    # 标记通信用户方向
    theta_CUs = [40, 10, -30]
    for theta_cu in theta_CUs:
        plt.axvline(x=theta_cu, color='g', linestyle=':', 
                   linewidth=1, alpha=0.5)
    
    plt.xlabel('θ (deg)')
    plt.ylabel('Beam gain (dB)')
    plt.title('Figure 4: Beampatterns using Correct Formula P_bp = a^H(θ) R_X a(θ)\n' + 
             f'(Main beam width narrows over iterations, Eve at {theta_eve*180/np.pi:.0f}°)')
    plt.grid(True, alpha=0.3)
    plt.legend(loc='upper right')
    plt.xlim([-90, 90])
    plt.ylim([-30, 5])
    plt.xticks(np.arange(-90, 91, 30))
    
    # 添加注释
    plt.text(-80, 0, f'Formula: P_bp = a^H(θ) R_X a(θ)', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.8))
    plt.text(-80, -5, f'SNR = -22 dB', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    plt.text(-80, -10, f'P₀ = {P0_dBm} dBm', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('figure4_correct_formula.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n" + "="*50)
    print("仿真完成！")
    print("使用公式: P_bp = a^H(θ) R_X a(θ)")
    print("生成文件: figure4_correct_formula.png")
    print("="*50)
    
    # 验证波束图案特性
    print("\n波束图案验证:")
    for i in range(iterations):
        peak_idx = np.argmax(beampatterns[i])
        peak_angle = theta_grid_deg[peak_idx]
        peak_power = beampatterns[i][peak_idx]
        
        # 计算3dB波束宽度
        half_power = peak_power / 2
        half_power_indices = np.where(beampatterns[i] >= half_power)[0]
        if len(half_power_indices) > 0:
            beam_width_3dB = theta_grid_deg[half_power_indices[-1]] - theta_grid_deg[half_power_indices[0]]
        else:
            beam_width_3dB = 0
        
        print(f"迭代 {i+1}: 峰值角度 = {peak_angle:.1f}°, 3dB波束宽度 = {beam_width_3dB:.1f}°")

def verify_formula():
    """验证公式的正确性"""
    print("\n验证公式 P_bp = a^H(θ) R_X a(θ):")
    print("-" * 40)
    
    Nt = 4  # 简单例子
    theta = 0  # 0度方向
    
    # 导向矢量
    a = steering_vector(theta, Nt)
    print(f"导向矢量 a(0°) = {a}")
    
    # 简单协方差矩阵
    R_X = np.eye(Nt)
    print(f"协方差矩阵 R_X = \n{R_X}")
    
    # 计算波束图案
    P_bp = np.real(np.conj(a).T @ R_X @ a)
    print(f"波束图案 P_bp = a^H R_X a = {P_bp}")
    
    # 理论值（对于单位矩阵）
    theoretical = np.real(np.conj(a).T @ a)
    print(f"理论值 (R_X=I): |a|^2 = {theoretical}")
    print(f"验证: P_bp = |a|^2 = {Nt} ✓")

if __name__ == "__main__":
    verify_formula()
    simulate_figure_4()
