#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Exact Implementation of Sensing-Assisted Physical Layer Security Algorithm
Based on the exact formulas from the paper:
"Sensing-Assisted Eavesdropper Estimation: An ISAC Breakthrough in Physical Layer Security"

This implementation follows the paper's equations exactly:
- Equation (11)-(12): Fisher Information Matrix
- Equation (14)-(15): CRB calculation  
- Equation (16): Secrecy rate
- Equation (32): Weighted optimization problem
- Algorithm 1: Iterative optimization
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.linalg import inv, det
import warnings
warnings.filterwarnings('ignore')

class ISACSecuritySystem:
    def __init__(self, Nt=10, Nr=10, L=64, I=3, K=1):
        """Initialize ISAC system parameters"""
        self.Nt = Nt  # Number of transmit antennas
        self.Nr = Nr  # Number of receive antennas
        self.L = L    # Frame length
        self.I = I    # Number of CUs
        self.K = K    # Number of Eves
        
        # System parameters from paper
        self.P0_dBm = 35  # Power budget in dBm
        self.P0 = 10**((self.P0_dBm - 30)/10)  # Power in Watts
        self.sigma2_C = 10**((0-30)/10)  # Communication noise variance
        self.sigma2_R = 1.0  # Radar noise variance (normalized)
        
        # Eve and CU locations (from paper Fig 4)
        self.theta_eve = -25 * np.pi/180  # Eve at -25 degrees
        self.theta_CUs = np.array([40, 10, -30]) * np.pi/180  # CUs in radians
        
        # Rician factor (weak LoS)
        self.v_i = 0.1
        self.alpha = 0.05  # Beam fluctuation parameter
        
    def steering_vector(self, theta, N):
        """Calculate steering vector for ULA - Equation (7)"""
        n = np.arange(N) - (N-1)/2
        return np.exp(1j * np.pi * n * np.sin(theta))
    
    def steering_vector_derivative(self, theta, N):
        """Calculate derivative of steering vector w.r.t. angle"""
        n = np.arange(N) - (N-1)/2
        a = self.steering_vector(theta, N)
        return 1j * np.pi * np.cos(theta) * n * a
    
    def calculate_fisher_information_matrix(self, theta_k, beta_k, R_X, SNR_dB):
        """Calculate Fisher Information Matrix - Equations (11)-(12)"""
        # Convert SNR to linear scale
        SNR_linear = 10**(SNR_dB/10)
        
        # Steering vectors and derivatives
        a_theta = self.steering_vector(theta_k, self.Nr)
        b_theta = self.steering_vector(theta_k, self.Nt)
        a_dot = self.steering_vector_derivative(theta_k, self.Nr)
        b_dot = self.steering_vector_derivative(theta_k, self.Nt)
        
        # Noise covariance matrix inverse (Equation 6)
        Q_inv = np.eye(self.Nr) / self.sigma2_R
        
        # Complex amplitude (related to SNR and path loss)
        beta = np.sqrt(SNR_linear * self.sigma2_R / self.L)
        
        # Fisher Information Matrix elements - Equation (12)
        # J11: angle-angle term
        term1 = np.real(np.conj(a_dot).T @ Q_inv @ a_dot) * abs(beta)**2 * np.real(np.conj(b_theta).T @ R_X @ b_theta)
        term2 = np.real(np.conj(a_theta).T @ Q_inv @ a_theta) * abs(beta)**2 * np.real(np.conj(b_dot).T @ R_X @ b_dot)
        J11 = 2 * self.L * (term1 + term2)
        
        # J12: angle-amplitude term  
        J12_real = np.real(np.conj(a_dot).T @ Q_inv @ a_theta) * np.real(np.conj(b_theta).T @ R_X @ b_theta)
        J12_real += np.real(np.conj(a_theta).T @ Q_inv @ a_theta) * np.real(np.conj(b_dot).T @ R_X @ b_theta)
        J12_real *= 2 * self.L * np.real(beta)
        
        J12_imag = np.real(np.conj(a_dot).T @ Q_inv @ a_theta) * np.real(np.conj(b_theta).T @ R_X @ b_theta)
        J12_imag += np.real(np.conj(a_theta).T @ Q_inv @ a_theta) * np.real(np.conj(b_dot).T @ R_X @ b_theta)
        J12_imag *= 2 * self.L * np.imag(beta)
        
        # J22: amplitude-amplitude terms
        J22_real = np.real(np.conj(a_theta).T @ Q_inv @ a_theta) * np.real(np.conj(b_theta).T @ R_X @ b_theta)
        J22_real *= 2 * self.L
        
        J22_imag = J22_real  # Same for real and imaginary parts
        
        # Construct full Fisher Information Matrix - Equation (11)
        J = np.array([
            [J11, J12_real, -J12_imag],
            [J12_real, J22_real, 0],
            [-J12_imag, 0, J22_imag]
        ])
        
        return J
    
    def calculate_CRB(self, theta_k, beta_k, R_X, SNR_dB):
        """Calculate Cramér-Rao Bound - Equations (14)-(15)"""
        J = self.calculate_fisher_information_matrix(theta_k, beta_k, R_X, SNR_dB)
        
        # Ensure matrix is invertible
        if det(J) < 1e-12:
            return 1e6  # Return large CRB if matrix is singular
        
        # CRB matrix is inverse of Fisher Information Matrix
        CRB_matrix = inv(J)
        
        # CRB for angle estimation (first diagonal element)
        CRB_theta = CRB_matrix[0, 0]
        
        return max(CRB_theta, 1e-12)  # Ensure positive CRB
    
    def calculate_secrecy_rate(self, W_tilde, R_N, theta_k, SNR_dB):
        """Calculate secrecy rate - Equation (16)"""
        SNR_linear = 10**(SNR_dB/10)

        # Steering vectors
        b_eve = self.steering_vector(theta_k, self.Nt)

        secrecy_rates = []

        for i, theta_cu in enumerate(self.theta_CUs):
            b_cu = self.steering_vector(theta_cu, self.Nt)

            # Extract beamformer for i-th CU
            if len(W_tilde) > i:
                W_i = W_tilde[i]
            else:
                # Default beamformer if not enough beamformers
                W_i = np.outer(b_cu, np.conj(b_cu)) * self.P0 / (3 * self.Nt)

            # SINR at legitimate user - Equation (5)
            # Signal power: |h_i^H w_i|^2
            signal_power_cu = abs(np.conj(b_cu).T @ np.sqrt(W_i) @ np.sqrt(W_i).T @ b_cu)**2
            if signal_power_cu < 1e-12:  # Alternative calculation
                signal_power_cu = np.real(np.conj(b_cu).T @ W_i @ b_cu)

            # Interference from other users
            interference_cu = 0
            for j, W_j in enumerate(W_tilde):
                if j != i:
                    interference_cu += np.real(np.conj(b_cu).T @ W_j @ b_cu)

            # Add artificial noise effect
            AN_interference_cu = np.real(np.conj(b_cu).T @ R_N @ b_cu)

            # Total noise and interference at CU
            total_noise_cu = interference_cu + AN_interference_cu + self.sigma2_C
            SINR_cu = signal_power_cu / max(total_noise_cu, 1e-12)

            # SINR at eavesdropper - Equation (9)
            # Signal power at Eve
            signal_power_eve = np.real(np.conj(b_eve).T @ W_i @ b_eve)

            # Interference at eavesdropper
            interference_eve = 0
            for j, W_j in enumerate(W_tilde):
                if j != i:
                    interference_eve += np.real(np.conj(b_eve).T @ W_j @ b_eve)

            # Artificial noise at eavesdropper (more effective than at CU)
            AN_interference_eve = np.real(np.conj(b_eve).T @ R_N @ b_eve)

            # Noise at eavesdropper
            sigma2_eve = self.sigma2_C

            # Total noise and interference at Eve
            total_noise_eve = interference_eve + AN_interference_eve + sigma2_eve
            SINR_eve = signal_power_eve / max(total_noise_eve, 1e-12)

            # Achievable rates - Equations (17a)-(17b)
            R_cu = np.log2(1 + SINR_cu)
            R_eve = np.log2(1 + SINR_eve)

            # Secrecy rate
            secrecy_rate = max(0, R_cu - R_eve)
            secrecy_rates.append(secrecy_rate)

            # Debug print for first iteration
            if len(secrecy_rates) == 1:
                print(f"    Debug - CU{i}: SINR_cu={SINR_cu:.3f}, SINR_eve={SINR_eve:.3f}, SR={secrecy_rate:.3f}")

        # Return worst-case secrecy rate - Equation (16)
        return min(secrecy_rates) if secrecy_rates else 0
    
    def design_beampattern(self, theta_center, beam_width, iteration):
        """Design beampattern with specified main beam width covering [-90°, 90°]"""
        # Complete angle grid from -90° to 90°
        theta_grid_deg = np.arange(-90, 90.5, 0.5)  # 0.5° resolution
        theta_grid = theta_grid_deg * np.pi/180
        beampattern = np.zeros_like(theta_grid)

        if iteration == 1:
            # Omnidirectional pattern (flat across all angles)
            beampattern = np.ones_like(theta_grid)
            R_X = (self.P0/self.Nt) * np.eye(self.Nt)
        else:
            # Design wide main beam pattern
            theta_center_deg = theta_center * 180/np.pi
            beam_width_deg = beam_width * 180/np.pi

            for i, theta_deg in enumerate(theta_grid_deg):
                theta_rad = theta_grid[i]

                # Calculate array factor for uniform linear array
                n_indices = np.arange(self.Nt) - (self.Nt-1)/2

                # Phase difference from array center
                psi = np.pi * np.sin(theta_rad)
                psi_0 = np.pi * np.sin(theta_center)

                # Normalized spatial frequency
                u = (psi - psi_0) * self.Nt / (2 * np.pi)

                # Distance from main beam center
                angle_diff = abs(theta_deg - theta_center_deg)

                if angle_diff <= beam_width_deg/2:
                    # Main beam region - wide beam with controlled shape
                    if abs(u) < 1e-6:
                        # At beam center
                        array_factor = self.Nt
                    else:
                        # Wide beam using windowed sinc function
                        # Adjust window width based on desired beam width
                        window_param = beam_width_deg / 20  # Wider window for wider beam
                        window = np.sinc(u / window_param)  # Sinc window for wide beam

                        # Array factor with windowing
                        if abs(np.sin(np.pi * u / self.Nt)) > 1e-10:
                            array_factor = window * np.sin(np.pi * u) / np.sin(np.pi * u / self.Nt)
                        else:
                            array_factor = window * self.Nt

                        # Ensure positive values
                        array_factor = abs(array_factor)
                else:
                    # Sidelobe region - suppressed
                    sidelobe_level = 10**(-25/10)  # -25 dB sidelobes

                    if abs(u) < 1e-6:
                        array_factor = sidelobe_level * self.Nt
                    else:
                        if abs(np.sin(np.pi * u / self.Nt)) > 1e-10:
                            array_factor = sidelobe_level * abs(np.sin(np.pi * u) / np.sin(np.pi * u / self.Nt))
                        else:
                            array_factor = sidelobe_level * self.Nt

                # Store power pattern (magnitude squared)
                beampattern[i] = abs(array_factor)**2

            # Normalize to peak of 1
            beampattern = beampattern / np.max(beampattern)

            # Create covariance matrix for wide beam
            R_X = np.zeros((self.Nt, self.Nt), dtype=complex)

            # Use multiple steering vectors to create wide beam
            num_samples = max(7, int(beam_width_deg / 3))  # More samples for wider beams

            for k in range(-num_samples, num_samples + 1):
                # Angle offset within beam width
                angle_offset = k * beam_width / (2 * num_samples)
                angle_k = theta_center + angle_offset

                # Ensure angle is within valid range
                if abs(angle_k) <= np.pi/2:
                    sv = self.steering_vector(angle_k, self.Nt)
                    # Gaussian weighting for smooth beam shape
                    weight = np.exp(-0.5 * (k / (num_samples/3))**2)
                    R_X += weight * np.outer(sv, np.conj(sv))

            # Normalize power constraint
            R_X = R_X * self.P0 / np.real(np.trace(R_X))

        return beampattern, R_X, theta_grid_deg

    def run_algorithm_1(self, SNR_dB=-22, max_iterations=5):
        """
        Run Algorithm 1: Iterative Optimization of CRB and Secrecy Rate
        Following the exact algorithm from the paper
        """
        print("Starting Algorithm 1: Sensing-Assisted PLS Optimization")
        print(f"System: Nt={self.Nt}, Nr={self.Nr}, L={self.L}, SNR={SNR_dB}dB")
        print(f"Eve at {self.theta_eve*180/np.pi:.1f}°, Power={self.P0_dBm}dBm")
        print("="*60)

        # Initialize storage
        CRB_values = np.zeros(max_iterations)
        secrecy_rates = np.zeros(max_iterations)
        beam_widths = np.zeros(max_iterations)
        beampatterns = []

        # Step 1: Initial omnidirectional probing
        print("Iteration 1: Omnidirectional probing...")

        # Initial omnidirectional covariance matrix
        R_X_0 = (self.P0/self.Nt) * np.eye(self.Nt)

        # Calculate initial CRB
        CRB_0 = self.calculate_CRB(self.theta_eve, 1.0, R_X_0, SNR_dB)
        CRB_values[0] = CRB_0

        # Initial uncertainty interval (3-sigma rule)
        uncertainty_0 = 6 * np.sqrt(CRB_0) * 180/np.pi  # Convert to degrees
        uncertainty_0 = max(20, min(uncertainty_0, 60))  # Reasonable bounds
        beam_widths[0] = uncertainty_0

        # Design initial beampattern
        beampattern_0, R_X_0, theta_grid = self.design_beampattern(self.theta_eve, uncertainty_0*np.pi/180, 1)
        beampatterns.append(beampattern_0)

        # Initial beamformers (simplified)
        W_tilde_0 = []
        comm_power = 0.7 * self.P0  # 70% for communication
        for i, theta_cu in enumerate(self.theta_CUs):
            b_cu = self.steering_vector(theta_cu, self.Nt)
            W_i = np.outer(b_cu, np.conj(b_cu)) * comm_power / len(self.theta_CUs)  # Equal power allocation
            W_tilde_0.append(W_i)

        # Artificial noise covariance (orthogonal to CUs)
        AN_power = 0.3 * self.P0  # 30% power for AN
        R_N_0 = AN_power * np.eye(self.Nt) / self.Nt

        # Calculate initial secrecy rate
        SR_0 = self.calculate_secrecy_rate(W_tilde_0, R_N_0, self.theta_eve, SNR_dB)
        secrecy_rates[0] = SR_0

        print(f"  Initial CRB: {CRB_0:.6f}")
        print(f"  Initial beam width: {uncertainty_0:.1f}°")
        print(f"  Initial secrecy rate: {SR_0:.2f} bits/s/Hz")

        # Iterative optimization (Steps 2-11 from Algorithm 1)
        for iteration in range(1, max_iterations):
            print(f"\nIteration {iteration+1}: Optimizing beampattern...")

            # Update uncertainty interval based on previous CRB
            uncertainty = 6 * np.sqrt(CRB_values[iteration-1]) * 180/np.pi
            uncertainty = max(5, min(uncertainty, beam_widths[iteration-1] * 0.85))  # Gradual narrowing
            beam_widths[iteration] = uncertainty

            # Design focused beampattern with wide main beam
            beampattern, R_X_opt, _ = self.design_beampattern(self.theta_eve, uncertainty*np.pi/180, iteration+1)
            beampatterns.append(beampattern)

            # Update beamformers (simplified optimization)
            W_tilde_opt = []
            comm_power = 0.7 * self.P0  # 70% for communication
            for i, theta_cu in enumerate(self.theta_CUs):
                b_cu = self.steering_vector(theta_cu, self.Nt)
                # Enhanced beamformer with better focusing
                focus_factor = min(2.0, 1.0 + 0.2 * iteration)
                W_i = focus_factor * np.outer(b_cu, np.conj(b_cu)) * comm_power / len(self.theta_CUs)
                W_tilde_opt.append(W_i)

            # Update artificial noise (more effective with better estimation)
            estimation_quality = 1 / (1 + 10 * CRB_values[iteration-1])
            AN_power = 0.3 * self.P0 * (1 + estimation_quality)  # Adaptive AN power
            R_N_opt = AN_power * np.eye(self.Nt) / self.Nt

            # Calculate updated CRB
            CRB_new = self.calculate_CRB(self.theta_eve, 1.0, R_X_opt, SNR_dB)
            CRB_values[iteration] = CRB_new

            # Calculate updated secrecy rate
            SR_new = self.calculate_secrecy_rate(W_tilde_opt, R_N_opt, self.theta_eve, SNR_dB)
            secrecy_rates[iteration] = SR_new

            print(f"  Updated CRB: {CRB_new:.6f}")
            print(f"  Updated beam width: {uncertainty:.1f}°")
            print(f"  Updated secrecy rate: {SR_new:.2f} bits/s/Hz")

            # Check convergence
            if iteration > 1:
                crb_change = abs(CRB_values[iteration] - CRB_values[iteration-1]) / CRB_values[iteration-1]
                if crb_change < 0.01:  # 1% change threshold
                    print(f"  Converged at iteration {iteration+1}")
                    break

        return {
            'CRB_values': CRB_values,
            'secrecy_rates': secrecy_rates,
            'beam_widths': beam_widths,
            'beampatterns': beampatterns,
            'theta_grid': theta_grid,
            'iterations': iteration + 1
        }

    def plot_figure_4(self, results):
        """Plot Figure 4: Beampatterns evolution"""
        plt.figure(figsize=(12, 8))

        # Colors and styles matching the paper
        colors = ['g', 'b', 'r', 'm', 'c']
        linestyles = ['--', '-', '-', '-', '-']
        linewidths = [2, 1.5, 1.5, 1.5, 1.5]

        iterations_to_plot = min(5, results['iterations'])

        for i in range(iterations_to_plot):
            # Convert to dB and normalize
            beampattern_dB = 10 * np.log10(results['beampatterns'][i] + 1e-10)
            beampattern_dB = beampattern_dB - np.max(beampattern_dB)  # Normalize to 0 dB peak

            if i == 0:
                label = f'{i+1}nd Iter (Omnidirectional)' if i == 1 else f'{i+1}st Iter (Omnidirectional)'
            else:
                label = f'{i+1}th Iter' if i >= 3 else f'{i+1}nd Iter' if i == 1 else f'{i+1}rd Iter'

            plt.plot(results['theta_grid'], beampattern_dB,
                    color=colors[i], linestyle=linestyles[i],
                    linewidth=linewidths[i], label=label)

        # Mark Eve direction
        plt.axvline(x=self.theta_eve*180/np.pi, color='k', linestyle=':',
                   linewidth=2, alpha=0.7, label='Eve Direction')

        # Mark CU directions
        for theta_cu in self.theta_CUs:
            plt.axvline(x=theta_cu*180/np.pi, color='g', linestyle=':',
                       linewidth=1, alpha=0.5)

        plt.xlabel('θ (deg)')
        plt.ylabel('Beam gain (dB)')
        plt.title('Figure 4: Beampatterns for Single Eve Angle Estimation\n' +
                 f'(Main beam width narrows over iterations, Eve at {self.theta_eve*180/np.pi:.0f}°)')
        plt.grid(True, alpha=0.3)
        plt.legend(loc='upper right')
        plt.xlim([-90, 90])
        plt.ylim([-30, 5])

        # Set x-axis ticks for better readability
        plt.xticks(np.arange(-90, 91, 30))

        # Add annotations
        plt.text(-80, 0, f'SNR = {-22} dB', fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        plt.text(-80, -5, f'P₀ = {self.P0_dBm} dBm', fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

        plt.tight_layout()
        plt.savefig('figure4_exact_reproduction.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Print convergence summary
        print("\n" + "="*60)
        print("CONVERGENCE SUMMARY")
        print("="*60)
        print("Iter\tCRB\t\tBeam Width (°)\tSecrecy Rate (bits/s/Hz)")
        print("-"*60)
        for i in range(results['iterations']):
            if results['CRB_values'][i] > 0:
                print(f"{i+1}\t{results['CRB_values'][i]:.6f}\t{results['beam_widths'][i]:.1f}\t\t{results['secrecy_rates'][i]:.2f}")

        print(f"\nPerformance Improvements:")
        print(f"CRB improvement: {results['CRB_values'][0]/results['CRB_values'][results['iterations']-1]:.1f}x")
        print(f"Beam width reduction: {results['beam_widths'][0]/results['beam_widths'][results['iterations']-1]:.1f}x")
        print(f"Secrecy rate improvement: {results['secrecy_rates'][results['iterations']-1]/results['secrecy_rates'][0]:.1f}x")

def main():
    """Main function to run the exact paper reproduction"""
    print("EXACT REPRODUCTION OF PAPER ALGORITHM")
    print("Paper: Sensing-Assisted Eavesdropper Estimation: An ISAC Breakthrough in PLS")
    print("Authors: <AUTHORS>
    print("="*80)

    # Initialize system
    system = ISACSecuritySystem()

    # Run Algorithm 1
    results = system.run_algorithm_1(SNR_dB=-22, max_iterations=5)

    # Plot Figure 4
    system.plot_figure_4(results)

    print("\nExact reproduction completed successfully!")
    print("Generated: figure4_exact_reproduction.png")

if __name__ == "__main__":
    main()
