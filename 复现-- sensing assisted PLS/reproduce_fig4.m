%% Reproduction of Figure 4 from "Sensing-Assisted Eavesdropper Estimation" Paper
% MATLAB script to reproduce Figure 4: Beampatterns for single Eve scenario
% Shows how main beam width narrows over each iteration
%
% Paper: "Sensing-Assisted Eavesdropper Estimation: An ISAC Breakthrough in Physical Layer Security"
% Authors: <AUTHORS>
% Date: 2025-07-25

clear all;
close all;
clc;

%% System Parameters (from paper)
Nt = 10;            % Number of transmit antennas
Nr = 10;            % Number of receive antennas
I = 3;              % Number of communication users (CUs)
K = 1;              % Number of Eves (single Eve scenario for Fig 4)
L = 64;             % Frame length
P0_dBm = 35;        % Power budget in dBm
P0 = 10^((P0_dBm-30)/10); % Power budget in Watts
SNR_dB = -22;       % SNR in dB (as mentioned in paper for Fig 4)
sigma2_C = 10^((0-30)/10); % Noise variance (0 dBm)
sigma2_R = 10^((0-30)/10); % Radar noise variance

% Eve and CU locations (from paper description)
theta_eve = -25;    % Eve direction in degrees (ϑ1,0 = -25°)
theta_CUs = [40, 10, -30]; % CU directions in degrees

% Rician factor (weak LoS component as mentioned)
v_i = 0.1;          % Rician K-factor
alpha = 0.05;       % Main beam fluctuation parameter

%% Angle grid for beampattern calculation
theta_grid = -90:0.5:90; % Angle grid in degrees
theta_rad = theta_grid * pi/180; % Convert to radians

%% Initialize arrays for iterations
max_iterations = 5; % Based on paper convergence results
beampatterns = zeros(max_iterations, length(theta_grid));
CRB_values = zeros(max_iterations, 1);
main_beam_widths = zeros(max_iterations, 1);

fprintf('Starting Figure 4 reproduction: Single Eve beampattern evolution...\n');

%% Iteration 1: Omnidirectional beampattern (initial probing)
fprintf('Iteration 1: Omnidirectional probing...\n');

% Omnidirectional covariance matrix
R_X_omni = (P0/Nt) * eye(Nt);

% Calculate initial CRB for omnidirectional case
CRB_0 = calculate_CRB_omnidirectional(Nt, Nr, L, theta_eve*pi/180, SNR_dB, sigma2_R);
CRB_values(1) = CRB_0;

% Initial uncertainty interval (3-sigma rule)
uncertainty_interval_0 = 6 * sqrt(CRB_0) * 180/pi; % Convert to degrees
main_beam_widths(1) = uncertainty_interval_0;

% Omnidirectional beampattern (flat)
beampatterns(1, :) = ones(1, length(theta_grid)) * P0/Nt;

fprintf('  Initial CRB: %.6f, Main beam width: %.2f degrees\n', CRB_0, uncertainty_interval_0);

%% Iterations 2-5: Iterative beampattern optimization
for iter = 2:max_iterations
    fprintf('Iteration %d: Optimizing beampattern...\n', iter);

    % Update uncertainty interval based on previous CRB
    uncertainty_interval = 6 * sqrt(CRB_values(iter-1)) * 180/pi;
    main_beam_widths(iter) = uncertainty_interval;

    % Define main beam region around estimated Eve direction
    main_beam_center = theta_eve;
    main_beam_range = [main_beam_center - uncertainty_interval/2, ...
                       main_beam_center + uncertainty_interval/2];

    % Design beampattern with focused main beam
    [beampattern, R_X_opt] = design_focused_beampattern(theta_grid, main_beam_range, P0, Nt);
    beampatterns(iter, :) = beampattern;

    % Calculate updated CRB with optimized beampattern
    CRB_new = calculate_CRB_optimized(Nt, Nr, L, theta_eve*pi/180, SNR_dB, sigma2_R, R_X_opt);
    CRB_values(iter) = CRB_new;

    fprintf('  Updated CRB: %.6f, Main beam width: %.2f degrees\n', CRB_new, uncertainty_interval);

    % Check convergence
    if iter > 2 && abs(CRB_values(iter) - CRB_values(iter-1)) < 1e-6
        fprintf('  Converged at iteration %d\n', iter);
        break;
    end
end

%% Plot Figure 4: Beampatterns evolution
figure('Name', 'Figure 4: Beampatterns for Single Eve Scenario', 'Position', [100, 100, 1000, 700]);

% Define colors for different iterations
colors = {'g--', 'b-', 'r-', 'm-', 'c-'}; % Green dashed for omnidirectional
line_widths = [2, 1.5, 1.5, 1.5, 1.5];

hold on;
for iter = 1:max_iterations
    if iter == 1
        plot(theta_grid, 10*log10(beampatterns(iter, :)), colors{iter}, ...
             'LineWidth', line_widths(iter), 'DisplayName', 'Iteration 1 (Omnidirectional)');
    else
        plot(theta_grid, 10*log10(beampatterns(iter, :)), colors{iter}, ...
             'LineWidth', line_widths(iter), 'DisplayName', sprintf('Iteration %d', iter));
    end
end

% Mark Eve direction
xline(theta_eve, 'k:', 'LineWidth', 2, 'DisplayName', 'Eve Direction');

% Mark CU directions
for i = 1:length(theta_CUs)
    xline(theta_CUs(i), 'g:', 'LineWidth', 1, 'HandleVisibility', 'off');
end

xlabel('Angle (degrees)');
ylabel('Beampattern Gain (dB)');
title('Figure 4: Beampatterns for Single Eve Angle Estimation');
grid on;
legend('Location', 'best');
xlim([-90, 90]);
ylim([-40, 20]);

% Add text annotations
text(-80, 15, sprintf('Eve at %d°', theta_eve), 'FontSize', 10);
text(-80, 10, sprintf('SNR = %d dB', SNR_dB), 'FontSize', 10);
text(-80, 5, sprintf('P_0 = %d dBm', P0_dBm), 'FontSize', 10);

hold off;

fprintf('Figure 4 reproduction completed.\n');

%% Display convergence results
fprintf('\nConvergence Summary:\n');
fprintf('Iteration\tCRB\t\tMain Beam Width (deg)\n');
fprintf('----------------------------------------\n');
for iter = 1:max_iterations
    if CRB_values(iter) > 0
        fprintf('%d\t\t%.6f\t%.2f\n', iter, CRB_values(iter), main_beam_widths(iter));
    end
end

%% Helper Functions

function CRB = calculate_CRB_omnidirectional(Nt, Nr, L, theta_eve, SNR_dB, sigma2_R)
    % Calculate CRB for omnidirectional beampattern
    % Based on equations (11)-(15) in the paper

    % Convert SNR to linear scale
    SNR = 10^(SNR_dB/10);

    % Steering vectors for ULA (half-wavelength spacing)
    a_theta = exp(1j * pi * (-(Nr-1)/2:(Nr-1)/2)' * sin(theta_eve));
    b_theta = exp(1j * pi * (-(Nt-1)/2:(Nt-1)/2)' * sin(theta_eve));

    % Derivatives of steering vectors
    a_dot = 1j * pi * cos(theta_eve) * (-(Nr-1)/2:(Nr-1)/2)' .* a_theta;
    b_dot = 1j * pi * cos(theta_eve) * (-(Nt-1)/2:(Nt-1)/2)' .* b_theta;

    % Omnidirectional covariance matrix
    R_X = eye(Nt);

    % Complex amplitude (assumed unit for simplicity)
    beta = 1;

    % Noise covariance
    Q_inv = eye(Nr) / sigma2_R;

    % Fisher Information Matrix elements (simplified for single target)
    J11 = 2 * L * real(a_dot' * Q_inv * a_dot * abs(beta)^2 * real(b_theta' * R_X * b_theta) + ...
                       a_theta' * Q_inv * a_theta * abs(beta)^2 * real(b_dot' * R_X * b_dot));

    % CRB is inverse of Fisher Information
    CRB = 1 / J11;
end

function CRB = calculate_CRB_optimized(Nt, Nr, L, theta_eve, SNR_dB, sigma2_R, R_X)
    % Calculate CRB for optimized beampattern

    % Convert SNR to linear scale
    SNR = 10^(SNR_dB/10);

    % Steering vectors
    a_theta = exp(1j * pi * (-(Nr-1)/2:(Nr-1)/2)' * sin(theta_eve));
    b_theta = exp(1j * pi * (-(Nt-1)/2:(Nt-1)/2)' * sin(theta_eve));

    % Derivatives
    a_dot = 1j * pi * cos(theta_eve) * (-(Nr-1)/2:(Nr-1)/2)' .* a_theta;
    b_dot = 1j * pi * cos(theta_eve) * (-(Nt-1)/2:(Nt-1)/2)' .* b_theta;

    % Complex amplitude
    beta = 1;

    % Noise covariance
    Q_inv = eye(Nr) / sigma2_R;

    % Fisher Information Matrix elements
    J11 = 2 * L * real(a_dot' * Q_inv * a_dot * abs(beta)^2 * real(b_theta' * R_X * b_theta) + ...
                       a_theta' * Q_inv * a_theta * abs(beta)^2 * real(b_dot' * R_X * b_dot));

    % CRB is inverse of Fisher Information
    CRB = 1 / max(J11, 1e-10); % Avoid division by zero
end

function [beampattern, R_X] = design_focused_beampattern(theta_grid, main_beam_range, P0, Nt)
    % Design focused beampattern with main beam in specified range

    theta_rad = theta_grid * pi/180;
    beampattern = zeros(size(theta_grid));

    % Find indices within main beam range
    main_beam_indices = find(theta_grid >= main_beam_range(1) & theta_grid <= main_beam_range(2));

    % Design simple focused beampattern
    % Higher gain in main beam, lower gain in sidelobes
    main_beam_gain = P0 * 0.8; % 80% of power in main beam
    sidelobe_gain = P0 * 0.2 / (length(theta_grid) - length(main_beam_indices)); % Remaining power in sidelobes

    beampattern(main_beam_indices) = main_beam_gain / length(main_beam_indices);
    beampattern(setdiff(1:length(theta_grid), main_beam_indices)) = sidelobe_gain;

    % Create corresponding covariance matrix (simplified)
    % In practice, this would be optimized using the weighted optimization problem
    R_X = eye(Nt) * P0/Nt; % Simplified for demonstration

    % Enhance the main beam direction
    main_beam_center_rad = mean(main_beam_range) * pi/180;
    b_main = exp(1j * pi * (-(Nt-1)/2:(Nt-1)/2)' * sin(main_beam_center_rad));
    R_X = R_X + 0.5 * P0 * (b_main * b_main') / Nt; % Add focused component

    % Normalize to maintain power constraint
    R_X = R_X * P0 / trace(R_X);
end
