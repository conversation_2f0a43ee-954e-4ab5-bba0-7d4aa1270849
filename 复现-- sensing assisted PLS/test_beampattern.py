#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test beampattern generation to verify [-90°, 90°] coverage
"""

import numpy as np
import matplotlib.pyplot as plt

def steering_vector(theta, N):
    """Calculate steering vector for ULA"""
    n = np.arange(N) - (N-1)/2
    return np.exp(1j * np.pi * n * np.sin(theta))

def design_wide_beam(theta_center_deg, beam_width_deg, Nt, iteration):
    """Design wide beam pattern"""
    # Complete angle grid from -90° to 90°
    theta_grid_deg = np.arange(-90, 90.5, 0.5)
    theta_grid = theta_grid_deg * np.pi/180
    theta_center = theta_center_deg * np.pi/180
    beam_width = beam_width_deg * np.pi/180
    
    beampattern = np.zeros_like(theta_grid)
    
    if iteration == 1:
        # Omnidirectional
        beampattern = np.ones_like(theta_grid)
    else:
        for i, theta_deg in enumerate(theta_grid_deg):
            theta_rad = theta_grid[i]
            
            # Array factor calculation
            n_indices = np.arange(Nt) - (Nt-1)/2
            psi = np.pi * np.sin(theta_rad)
            psi_0 = np.pi * np.sin(theta_center)
            u = (psi - psi_0) * Nt / (2 * np.pi)
            
            angle_diff = abs(theta_deg - theta_center_deg)
            
            if angle_diff <= beam_width_deg/2:
                # Main beam region
                if abs(u) < 1e-6:
                    array_factor = Nt
                else:
                    # Wide beam using windowed sinc
                    window_param = beam_width_deg / 15
                    window = np.sinc(u / window_param)
                    
                    if abs(np.sin(np.pi * u / Nt)) > 1e-10:
                        array_factor = window * np.sin(np.pi * u) / np.sin(np.pi * u / Nt)
                    else:
                        array_factor = window * Nt
                    
                    array_factor = abs(array_factor)
            else:
                # Sidelobe region
                sidelobe_level = 10**(-20/10)  # -20 dB
                
                if abs(u) < 1e-6:
                    array_factor = sidelobe_level * Nt
                else:
                    if abs(np.sin(np.pi * u / Nt)) > 1e-10:
                        array_factor = sidelobe_level * abs(np.sin(np.pi * u) / np.sin(np.pi * u / Nt))
                    else:
                        array_factor = sidelobe_level * Nt
            
            beampattern[i] = abs(array_factor)**2
        
        # Normalize
        beampattern = beampattern / np.max(beampattern)
    
    return beampattern, theta_grid_deg

def main():
    """Test beampattern generation"""
    Nt = 10
    theta_eve = -25  # degrees
    
    # Test different iterations with narrowing beam widths
    beam_widths = [40, 35, 25, 20, 15]  # degrees
    
    plt.figure(figsize=(12, 8))
    
    colors = ['g', 'b', 'r', 'm', 'c']
    linestyles = ['--', '-', '-', '-', '-']
    
    for i, beam_width in enumerate(beam_widths):
        iteration = i + 1
        beampattern, theta_grid = design_wide_beam(theta_eve, beam_width, Nt, iteration)
        
        # Convert to dB
        beampattern_dB = 10 * np.log10(beampattern + 1e-10)
        beampattern_dB = beampattern_dB - np.max(beampattern_dB)  # Normalize to 0 dB
        
        if i == 0:
            label = f'{i+1}st Iter (Omnidirectional)'
        else:
            label = f'{i+1}th Iter (Width: {beam_width:.0f}°)'
        
        plt.plot(theta_grid, beampattern_dB, 
                color=colors[i], linestyle=linestyles[i], 
                linewidth=2, label=label)
    
    # Mark Eve direction
    plt.axvline(x=theta_eve, color='k', linestyle=':', linewidth=2, label='Eve Direction')
    
    # Mark some CU directions
    for theta_cu in [40, 10, -30]:
        plt.axvline(x=theta_cu, color='g', linestyle=':', linewidth=1, alpha=0.5)
    
    plt.xlabel('θ (deg)')
    plt.ylabel('Beam gain (dB)')
    plt.title('Test: Wide Main Beam Patterns (Should cover [-90°, 90°])')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.xlim([-90, 90])
    plt.ylim([-30, 5])
    plt.xticks(np.arange(-90, 91, 30))
    
    plt.tight_layout()
    plt.savefig('test_beampattern.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Test completed. Check that:")
    print("1. X-axis covers [-90°, 90°]")
    print("2. Main beams are wide and centered at -25°")
    print("3. Main beam width decreases with iterations")
    print("4. Sidelobes are suppressed to ~-20dB")

if __name__ == "__main__":
    main()
