#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Algorithm Analysis for Sensing-Assisted Physical Layer Security
Detailed analysis of the ISAC algorithm from the paper

Paper: "Sensing-Assisted Eavesdropper Estimation: An ISAC Breakthrough in Physical Layer Security"
Authors: <AUTHORS>
"""

import numpy as np
import matplotlib.pyplot as plt
from reproduce_fig4 import calculate_CRB_omnidirectional, calculate_CRB_optimized, design_focused_beampattern

def analyze_algorithm_performance():
    """Analyze the performance of the sensing-assisted PLS algorithm"""
    
    print("=== Sensing-Assisted Physical Layer Security Algorithm Analysis ===\n")
    
    # System parameters
    Nt = 10
    Nr = 10
    L = 64
    P0_dBm = 35
    P0 = 10**((P0_dBm-30)/10)
    sigma2_R = 1.0
    
    # Analysis parameters
    SNR_range = np.arange(-30, 10, 2)  # SNR range for analysis
    theta_eve = -25 * np.pi/180  # Eve direction in radians
    
    print("1. CRB vs SNR Analysis")
    print("=" * 50)
    
    # Analyze CRB vs SNR
    CRB_omni = []
    CRB_opt = []
    
    for SNR_dB in SNR_range:
        # Omnidirectional CRB
        crb_o = calculate_CRB_omnidirectional(Nt, Nr, L, theta_eve, SNR_dB, sigma2_R)
        CRB_omni.append(crb_o)
        
        # Optimized CRB (simulate with focused beampattern)
        theta_grid = np.arange(-90, 90.5, 0.5)
        main_beam_range = [-30, -20]  # Focused around Eve
        _, R_X_opt = design_focused_beampattern(theta_grid, main_beam_range, P0, Nt, 3)
        crb_opt = calculate_CRB_optimized(Nt, Nr, L, theta_eve, SNR_dB, sigma2_R, R_X_opt)
        CRB_opt.append(crb_opt)
    
    # Plot CRB vs SNR
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.semilogy(SNR_range, np.sqrt(CRB_omni), 'b-o', label='Omnidirectional', linewidth=2)
    plt.semilogy(SNR_range, np.sqrt(CRB_opt), 'r-s', label='Optimized Beampattern', linewidth=2)
    plt.xlabel('SNR (dB)')
    plt.ylabel('Root-CRB (radians)')
    plt.title('Root-CRB vs SNR')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # Analyze main beam width evolution
    print("\n2. Main Beam Width Evolution")
    print("=" * 50)
    
    iterations = np.arange(1, 6)
    CRB_evolution = [0.000926, 0.000070, 0.000064, 0.000059, 0.000054]
    beam_width_evolution = [10.46, 10.46, 2.87, 2.74, 2.63]
    
    plt.subplot(2, 2, 2)
    plt.plot(iterations, beam_width_evolution, 'g-o', linewidth=2, markersize=8)
    plt.xlabel('Iteration')
    plt.ylabel('Main Beam Width (degrees)')
    plt.title('Main Beam Width Evolution')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 3)
    plt.semilogy(iterations, CRB_evolution, 'm-s', linewidth=2, markersize=8)
    plt.xlabel('Iteration')
    plt.ylabel('CRB')
    plt.title('CRB Convergence')
    plt.grid(True, alpha=0.3)
    
    # Analyze secrecy rate improvement (simplified model)
    print("\n3. Secrecy Rate Analysis")
    print("=" * 50)
    
    # Simplified secrecy rate calculation
    def calculate_secrecy_rate(CRB_val, SNR_dB):
        # Simplified model: better estimation leads to better secrecy
        estimation_quality = 1 / (1 + CRB_val * 1000)  # Normalize CRB
        base_rate = np.log2(1 + 10**(SNR_dB/10))  # Shannon capacity
        secrecy_rate = base_rate * estimation_quality * 0.5  # Simplified secrecy rate
        return secrecy_rate
    
    secrecy_rates = []
    for i, crb in enumerate(CRB_evolution):
        sr = calculate_secrecy_rate(crb, -22)  # Use SNR from paper
        secrecy_rates.append(sr)
        print(f"Iteration {i+1}: CRB = {crb:.6f}, Secrecy Rate = {sr:.2f} bits/s/Hz")
    
    plt.subplot(2, 2, 4)
    plt.plot(iterations, secrecy_rates, 'c-^', linewidth=2, markersize=8)
    plt.xlabel('Iteration')
    plt.ylabel('Secrecy Rate (bits/s/Hz)')
    plt.title('Secrecy Rate Evolution')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('algorithm_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\nImprovement Summary:")
    print(f"CRB improvement: {CRB_evolution[0]/CRB_evolution[-1]:.1f}x")
    print(f"Beam width reduction: {beam_width_evolution[0]/beam_width_evolution[-1]:.1f}x")
    print(f"Secrecy rate improvement: {secrecy_rates[-1]/secrecy_rates[0]:.1f}x")

def explain_algorithm_steps():
    """Explain the key steps of the algorithm"""
    
    print("\n" + "="*70)
    print("ALGORITHM EXPLANATION")
    print("="*70)
    
    print("""
The Sensing-Assisted Physical Layer Security Algorithm works as follows:

STEP 1: Initial Omnidirectional Probing
- ISAC base station emits omnidirectional waveform
- Uses CAML (Combined Capon and Approximate Maximum Likelihood) technique
- Estimates initial directions of potential eavesdroppers
- Calculates initial CRB (Cramér-Rao Bound) for estimation accuracy

STEP 2: Iterative Beampattern Optimization
- Formulates weighted optimization problem:
  * Minimize CRB of target/Eve estimation
  * Maximize secrecy rate with artificial noise (AN)
- Designs beampattern with wide main beam covering uncertainty region
- Main beam width determined by 3σ rule: ±3√CRB

STEP 3: Mutual Performance Enhancement
- Improved estimation accuracy → narrower main beam
- Focused beampattern → better SNR → lower CRB
- Better Eve knowledge → improved secrecy rate
- Process continues until convergence

KEY INNOVATIONS:
✓ Sensing functionality assists physical layer security
✓ Estimation uncertainty explicitly considered in beampattern design
✓ Mutual benefits between sensing and security functionalities
✓ Practical solution without requiring Eve's CSI

MATHEMATICAL FOUNDATION:
- Fisher Information Matrix (FIM) for CRB calculation
- Weighted optimization with fractional programming
- Beampattern constraints for robust operation
- Secrecy rate maximization with artificial noise
    """)

def demonstrate_key_equations():
    """Demonstrate key equations from the paper"""
    
    print("\n" + "="*70)
    print("KEY EQUATIONS DEMONSTRATION")
    print("="*70)
    
    print("""
1. STEERING VECTOR (Equation 7):
   a(θ) = [e^(-j(Nr-1)π sin θ/2), ..., e^(j(Nr-1)π sin θ/2)]^T

2. FISHER INFORMATION MATRIX (Equation 11-12):
   J = 2L * [Re(J11), Re(J12), -Im(J12); ...]
   
3. CRAMÉR-RAO BOUND (Equation 14-15):
   CRB(θ,β) = J^(-1)
   
4. SECRECY RATE (Equation 16):
   SR = min_{i,k} [R_CU^i - R_E^{k,i}]^+
   
5. WEIGHTED OPTIMIZATION (Equation 32):
   max ρ|J|/|J|_UB + (1-ρ)SR/SR_UB
   
6. BEAMPATTERN CONSTRAINTS (Equation 32b-32d):
   - Main beam power constraint
   - Sidelobe suppression
   - Wide beam coverage for uncertainty
    """)
    
    # Demonstrate numerical example
    print("\nNUMERICAL EXAMPLE:")
    print("-" * 30)
    
    Nt = 10
    theta = -25 * np.pi/180
    
    # Calculate steering vector
    t_indices = np.arange(-(Nt-1)/2, (Nt-1)/2 + 1)
    b_theta = np.exp(1j * np.pi * t_indices * np.sin(theta))
    
    print(f"For θ = -25°, Nt = {Nt}:")
    print(f"Steering vector magnitude: {np.abs(b_theta)[:5]}...")  # Show first 5 elements
    phases = np.angle(b_theta) * 180/np.pi
    print(f"Steering vector phase: {phases[:5]}...")  # Show first 5 elements

    # Calculate array factor
    array_factor = np.sum(b_theta)
    print(f"Array factor: {abs(array_factor):.2f}")

if __name__ == "__main__":
    analyze_algorithm_performance()
    explain_algorithm_steps()
    demonstrate_key_equations()
    
    print("\n" + "="*70)
    print("ANALYSIS COMPLETE")
    print("="*70)
    print("Files generated:")
    print("- figure4_reproduction.png: Main beampattern evolution")
    print("- algorithm_analysis.png: Performance analysis")
    print("- reproduce_fig4.py: Main reproduction script")
    print("- algorithm_analysis.py: This analysis script")
