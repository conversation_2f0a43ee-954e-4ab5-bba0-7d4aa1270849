#!/usr/bin/env python3
import zipfile
import xml.etree.ElementTree as ET
import re
import sys

def extract_text_from_pptx(pptx_file):
    """提取PPTX文件中的所有文本内容"""
    try:
        with zipfile.ZipFile(pptx_file, 'r') as zip_ref:
            # 获取所有幻灯片文件
            slide_files = [f for f in zip_ref.namelist() if f.startswith('ppt/slides/slide') and f.endswith('.xml')]
            slide_files.sort(key=lambda x: int(re.search(r'slide(\d+)\.xml', x).group(1)))
            
            print(f"\n{'='*60}")
            print(f"文件: {pptx_file}")
            print(f"{'='*60}")
            
            for i, slide_file in enumerate(slide_files, 1):
                try:
                    slide_content = zip_ref.read(slide_file)
                    root = ET.fromstring(slide_content)
                    
                    # 提取所有文本节点
                    texts = []
                    for elem in root.iter():
                        if elem.tag.endswith('}t') and elem.text and elem.text.strip():
                            texts.append(elem.text.strip())
                    
                    if texts:
                        print(f"\n--- 幻灯片 {i} ---")
                        for text in texts:
                            if text and len(text.strip()) > 0:
                                print(text)
                                
                except Exception as e:
                    print(f"处理幻灯片 {slide_file} 时出错: {e}")
                    
    except Exception as e:
        print(f"处理文件 {pptx_file} 时出错: {e}")

if __name__ == "__main__":
    files = [
        "安卫普公司介绍_20250221.pptx",
        "安卫普公司技术方向20250403.pptx", 
        "机载多模态无人机感知0709.pptx"
    ]
    
    for file in files:
        extract_text_from_pptx(file)
