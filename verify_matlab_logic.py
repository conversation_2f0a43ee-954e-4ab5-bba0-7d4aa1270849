#!/usr/bin/env python3
"""
Verify MATLAB code logic using Python/NumPy
This script tests the core mathematical operations to ensure they work correctly
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.linalg import svd, eig, cholesky
import warnings

def config():
    """Configuration parameters matching MATLAB config.m"""
    cfg = {}
    # Physical constants
    cfg['c'] = 3e8
    cfg['fc'] = 28e9
    cfg['lambda'] = cfg['c'] / cfg['fc']
    cfg['d'] = cfg['lambda'] / 2
    
    # Array and user parameters
    cfg['Nt'] = 16
    cfg['Nr_b'] = 2
    cfg['Ne'] = 4
    cfg['K'] = 2
    
    # Target angle
    cfg['theta0_deg'] = 10
    
    # Power and noise
    cfg['Ptot'] = 1
    cfg['sigma2'] = 1
    
    return cfg

def steering_vector(theta_rad, Nt, d, lambda_):
    """Generate array steering vector"""
    n = np.arange(Nt).reshape(-1, 1)
    a = np.exp(1j * 2 * np.pi * d / lambda_ * n * np.sin(theta_rad))
    return a

def beam_gain(Rx, a_theta):
    """Compute beam gain in direction of a_theta"""
    return np.real(a_theta.conj().T @ Rx @ a_theta)[0, 0]

def generate_channels(cfg, model='rayleigh'):
    """Generate channel realizations"""
    H_list = []
    
    if model == 'rayleigh':
        # Rayleigh fading
        for k in range(cfg['K']):
            H_real = np.random.randn(cfg['Nr_b'], cfg['Nt'])
            H_imag = np.random.randn(cfg['Nr_b'], cfg['Nt'])
            H_list.append((H_real + 1j * H_imag) / np.sqrt(2))
        
        He_real = np.random.randn(cfg['Ne'], cfg['Nt'])
        He_imag = np.random.randn(cfg['Ne'], cfg['Nt'])
        He = (He_real + 1j * He_imag) / np.sqrt(2)
        
    elif model == 'los':
        # LoS dominant
        for k in range(cfg['K']):
            theta_k = cfg['theta0_deg'] + 10 * k
            a_k = steering_vector(theta_k * np.pi/180, cfg['Nt'], cfg['d'], cfg['lambda'])
            H_list.append(np.ones((cfg['Nr_b'], 1)) @ a_k.T)
        
        theta_e = cfg['theta0_deg'] + 30
        a_e = steering_vector(theta_e * np.pi/180, cfg['Nt'], cfg['d'], cfg['lambda'])
        He = np.ones((cfg['Ne'], 1)) @ a_e.T
    
    return H_list, He

def safe_logdet(IplusX):
    """Stable computation of log(det(I + X))"""
    try:
        L = cholesky(IplusX, lower=False)
        return 2 * np.sum(np.log(np.diag(L)))
    except:
        eigenvals, _ = eig(IplusX)
        eigenvals = np.real(eigenvals)
        eigenvals[eigenvals < np.finfo(float).eps] = np.finfo(float).eps
        return np.sum(np.log(eigenvals))

def sum_rate(H_list, Rx, sigma2):
    """Compute sum rate for multiple users"""
    K = len(H_list)
    R_user = np.zeros(K)
    
    for k in range(K):
        Hk = H_list[k]
        I_plus_HRxH = np.eye(Hk.shape[0]) + Hk @ Rx @ Hk.conj().T / sigma2
        R_user[k] = safe_logdet(I_plus_HRxH) / np.log(2)
    
    R_sum = np.sum(R_user)
    return R_sum, R_user

def design_MRT(H_list, Ptot, Nt):
    """Maximum Ratio Transmission"""
    # Concatenate all channel matrices vertically
    H_all = np.vstack(H_list)
    
    F = H_all.conj().T / np.linalg.norm(H_all, 'fro')
    F = F * np.sqrt(Ptot)
    Z = np.zeros((Nt, Nt), dtype=complex)
    
    return F, Z

def test_basic_functionality():
    """Test basic functionality"""
    print("=== MATLAB Logic Verification ===")
    
    # Set random seed for reproducibility
    np.random.seed(20250820)
    
    # Test 1: Configuration
    print("\n[Test 1] Configuration...")
    cfg = config()
    print(f"  ✓ Nt = {cfg['Nt']} antennas")
    print(f"  ✓ K = {cfg['K']} users")
    print(f"  ✓ theta0 = {cfg['theta0_deg']} degrees")
    
    # Test 2: Steering vector
    print("\n[Test 2] Steering vector...")
    theta_rad = cfg['theta0_deg'] * np.pi/180
    a = steering_vector(theta_rad, cfg['Nt'], cfg['d'], cfg['lambda'])
    print(f"  ✓ Steering vector computed, size: {a.shape}")
    print(f"  ✓ Norm: {np.linalg.norm(a):.4f}")
    
    # Test 3: Channel generation
    print("\n[Test 3] Channel generation...")
    H_list, He = generate_channels(cfg, 'rayleigh')
    print(f"  ✓ Generated {len(H_list)} user channels")
    print(f"  ✓ H1 size: {H_list[0].shape}")
    print(f"  ✓ He size: {He.shape}")
    
    # Test 4: MRT precoder design
    print("\n[Test 4] MRT precoder design...")
    F, Z = design_MRT(H_list, cfg['Ptot'], cfg['Nt'])
    print(f"  ✓ F size: {F.shape}")
    print(f"  ✓ Z size: {Z.shape}")
    
    # Check power constraint
    Rx = F @ F.conj().T + Z
    power = np.trace(Rx).real
    print(f"  ✓ Total power: {power:.4f} (should be ≤ {cfg['Ptot']:.4f})")
    
    # Test 5: Rate computation
    print("\n[Test 5] Rate computation...")
    R_sum, R_user = sum_rate(H_list, Rx, cfg['sigma2'])
    print(f"  ✓ Sum rate: {R_sum:.4f} bits/s/Hz")
    print(f"  ✓ User rates: {R_user}")
    
    # Test 6: Beam pattern
    print("\n[Test 6] Beam pattern...")
    theta_grid = np.arange(-90, 91, 5)
    Pscan = []
    for theta_deg in theta_grid:
        theta_rad = theta_deg * np.pi/180
        a_theta = steering_vector(theta_rad, cfg['Nt'], cfg['d'], cfg['lambda'])
        P = beam_gain(Rx, a_theta)
        Pscan.append(P)
    
    Pscan = np.array(Pscan)
    print(f"  ✓ Scanned {len(theta_grid)} angles")
    print(f"  ✓ Max gain: {10*np.log10(np.max(Pscan)):.2f} dB")
    
    # Test 7: Check for common issues
    print("\n[Test 7] Checking for common issues...")
    
    # Check if matrices are positive semidefinite
    eigenvals_Rx = np.linalg.eigvals(Rx)
    min_eig = np.min(np.real(eigenvals_Rx))
    print(f"  ✓ Rx min eigenvalue: {min_eig:.6f} (should be ≥ 0)")
    
    # Check if channels have reasonable condition numbers
    for k, H in enumerate(H_list):
        cond_num = np.linalg.cond(H)
        print(f"  ✓ H{k+1} condition number: {cond_num:.2f}")
    
    print("\n=== All tests passed! ===")
    
    return True

if __name__ == "__main__":
    test_basic_functionality()
