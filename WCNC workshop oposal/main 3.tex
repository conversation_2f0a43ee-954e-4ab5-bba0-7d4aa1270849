\documentclass{article}
\usepackage[
    top=1.5cm,    % 上边距设置为 2cm
    bottom=1.5cm, % 下边距设置为 2cm
    left=2cm,   % 左边距设置为 2cm
    right=2cm,  % 右边距设置为 2cm
    % 或者，你可以使用 total 选项来设置文本区域的宽度和高度，并让 LaTeX 自动计算边距
    % total={17cm,25cm}, % 文本区域宽度17cm，高度25cm
    % 或者，只设置水平和垂直边距
    % hmargin=2cm, % 左右边距都是 2cm
    % vmargin=2cm, % 上下边距都是 2cm
]{geometry}
\usepackage{amsmath,amssymb}
\usepackage{authblk}
\usepackage{graphicx}
\usepackage[colorlinks=true,linkcolor=blue,urlcolor=blue]{hyperref} % For clickable links and citations
\usepackage{enumitem}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage[utf8]{inputenc}
\usepackage{booktabs} % For better table formatting (optional)



\begin{document}
\title{\large{Proposal for an IEEE TCCN Special Issue} \\ \huge{\textbf{Resilience and Trustworthiness for 6G Smart Wireless Environments}}}

\author{
Guest Editors: \\
Prof. George C. Alexandropoulos (Lead Guest Editor) \\
%\vspace*{-0.3cm}
Department of Informatics and Telecommunications \\
National and Kapodistrian University of Athens, Athens, Greece \\
email: <EMAIL> \\
%\vspace*{-0.5cm} % Adjust vertical spacing between editors if needed
\and
Dr. Nanchi Su \\
\vspace*{-0.3cm}
Guangdong Provincial Key Laboratory of Aerospace Communication and Networking Technology \\
Harbin Institute of Technology (Shenzhen), Shenzhen, China \\
email: <EMAIL> \\
%\vspace*{-0.5cm} % Adjust vertical spacing between editors if needed
\and
Prof. Christos Masouros \\
\vspace*{-0.3cm}
Department of Electronic and Electrical Engineering \\
University College London, London, UK \\
email: <EMAIL> \\
%\vspace*{-0.5cm} % Adjust vertical spacing between editors if needed
\and
Prof. Zhongxiang Wei \\
\vspace*{-0.3cm}
School of Electronics and Information Engineering, \\
Tongji University, Shanghai, China \\
email: z\<EMAIL> \\
%\vspace*{-0.5cm} % Adjust vertical spacing between editors if needed
\and
Dr. Ahmad Bazzi \\
\vspace*{-0.3cm}
 Engineering Division\\
New York University Abu Dhabi, Abu Dhabi, United Arab Emirates \\
email: <EMAIL> \\
%\vspace*{-0.5cm} % Adjust vertical spacing between editors if needed
\and
Prof. Aylin Yener \\
\vspace*{-0.3cm}
Department of Electrical and Computer Engineering \\
The Ohio State University, OH, USA \\
email: <EMAIL> \\
%\vspace*{-0.5cm} % Adjust vertical spacing between editors if needed
\and
Prof. Tse-Tin Chan \\
\vspace*{-0.3cm}
Department of Mathematics and Information Technology \\
The Education University of Hong Kong, Hong Kong SAR, China \\
email: <EMAIL>
}

\date{} % Omit date

\maketitle

This special issue is part of the activities of, and endorsed by, the IEEE ComSoc Emerging Technology Initiative on Integrated Sensing and Communication (ISAC-ETI).


\newpage
\section{Background and Motivation}

The sixth generation (6G) of wireless networks is poised to become the backbone of the intelligent information infrastructure of the future. By integrating advanced communications, sensing, computing, and control into a unified, adaptive, and programmable wireless environment, 6G is expected to support a wide range of transformative applications, including autonomous mobility, intelligent industrial systems, digital twins, edge artificial intelligence (AI), and immersive virtual worlds.

A key distinguishing feature of 6G, as acknowledged by major standardization bodies, including the International Telecommunication Union (ITU), 3rd Generation Partnership Project (3GPP), and Institute of Electrical and Electronics Engineers (IEEE), is its deep integration of AI and sensing capabilities directly into the communication fabric. Technologies such as integrated sensing and communications (ISAC), semantic-aware networking, near-field and terahertz transmission, extremely massive MIMO, and reconfigurable intelligent surfaces (RISs) are converging to create networks that are not only highly connected but also perceptive, context-aware, and adaptive. The convergence of the aforementioned technologies, however, fundamentally reshapes the security and privacy landscape of wireless systems. In 6G networks, data is not only transmitted, but also inferred, interpreted, and acted upon in real time and often autonomously. Communication and sensing signals now share waveforms, hardware, and spatial resources, creating new opportunities for attackers to infer sensitive information, inject misleading signals, generate harsh wave propagation conditions, or manipulate semantic meaning at the physical and medium access control layers. At the same time, AI-based inference models deployed across distributed edge devices are susceptible to model poisoning, inference leakage, and adversarial perturbations.

Conventional security architectures, which are largely built upon higher-layer cryptographic protocols or post-processing privacy filters, are insufficient in the aforementioned context. There is a pressing need to rethink security and trust from the ground up, beginning at the physical layer and extending seamlessly across all layers of the network stack. Moreover, these mechanisms must be resilient by design, capable of operating under uncertainty, adapting to dynamic threats, and recovering from failure without degrading core service performance. Despite the growing attention to AI-native wireless security and ISAC system design in recent literature, there remains a significant gap in research that takes a holistic, system-wide perspective on security for 6G networks—one that accounts for the unique vulnerabilities introduced by joint communication and sensing systems, on-the-fly programmable wireless environments, and cognitive intelligence embedded in the protocol stack. 

This special issue (SI) aims to address the emerging and critically important research area of resilient and trustworthy wireless networking. It particularly aims to bring together cutting-edge research on resilient and trustworthy security architectures and information processing schemes tailored for 6G smart wireless environments. While we emphasize the importance of physical-layer innovations, which serve as the foundation for secure and privacy-preserving 6G systems, we also welcome work on cross-layer, algorithmic, and hardware-oriented mechanisms that contribute to robust and trustworthy end-to-end wireless system designs. This SI is unique in its \textbf{holistic and forward-looking} focus on:
\begin{itemize}
    \item \textbf{Security challenges introduced by AI-native control planes,} including adversarial machine learning, trust calibration, and secure decision-making in intelligent networks;
    \item \textbf{Resilience of reconfigurable and semantic physical layers,} such as those using RISs, near-field propagation, and THz, where traditional cryptographic methods are insufficient;
    \item  \textbf{Cross-layer and cross-domain orchestration} of security across cloud-native, virtualized, and zero-trust infrastructures; and
    \item \textbf{System-level trade-offs} between performance, explainability, energy efficiency, and trustworthiness in emerging 6G applications, such as perceptive communications, federated analytics, and multi-modal sensing.
\end{itemize}

The core objective of this SI is not only to explore how 6G technologies introduce new security challenges, but also to uncover how these same technologies—such as AI, RISs, and semantic communications—can be leveraged to enhance security and trust. By encouraging interdisciplinary perspectives and system-level integration, this SI will provide a timely and comprehensive platform to shape the next generation of wireless security research.

\subsection*{\underline{References}}
\begin{enumerate}[label={[\arabic*]}]
    \item \textbf{G. C. Alexandropoulos}, K. D. Katsanos, M. Wen, and D. B. da Costa, ``Counteracting eavesdropper attacks through reconfigurable intelligent surfaces: A new threat model and secrecy rate optimization,'' \textit{IEEE Open Journal of the Communications Society}, vol. 4, pp. 1285-1302, 2023.
    \item A. Kunz, S. B. M. Baskaran, and \textbf{G. C. Alexandropoulos}, ``Lightweight security for ambient-powered programmable reflections with reconfigurable intelligent surfaces,'' \textit{IEEE Communications Standards Magazine}, to appear, 2025; DOI: 10.1109/MCOMSTD.2025.3573490.
    \item J. Xu, C. Yuen, C. Huang, N. Ul Hassan, \textbf{G. C. Alexandropoulos}, M. Di Renzo, and M. Debbah, ``Reconfiguring wireless environment via intelligent surfaces for 6G: Reflection, modulation, and security,'' \textit{Science China Information Sciences}, vol. 66, no. 3, 130304, 2023.
    \item S. Rezvani, P.-H.Lin, M. Le, \textbf{G. C. Alexandropoulos}, S. Stanczak, and E. A. Jorswieck, ``Physical-layer security with benign and malicious RISs,'' chapter in the upcoming book ``Reconfigurable Intelligent Surfaces for Wireless Communications: Modeling, Architectures, and Applications," Springer Nature, Singapore, 2025; edited by G. C. Alexandropoulos, A. Zappone, N. Shlezinger, M. Di Renzo, and Y. C. Eldar.
    \item K. Cumanan, \textbf{G. C. Alexandropoulos}, Z. Ding, and G. K. Karagiannidis, ``Secure communications with cooperative jamming: Optimal power allocation and secrecy outage analysis,'' \textit{IEEE Transactions on Vehicular Technology}, vol. 66, no. 8, pp. 7495–7505, 2017.
    \item D. Kompostiotis, D. Vordonis, V. Paliouras, and \textbf{G. C. Alexandropoulos}, ``Secrecy rate maximization in RIS-enabled OFDM wireless communications: The circuit-based reflection model case,'' in \textit{Proc. IEEE International Conference on Communications}, Rome, Italy, 2023.
    \item G. Stamatelis, A.-N. Kanatas, I. Asprogerakas, and \textbf{G. C. Alexandropoulos}, ``Single- and multi-agent private active sensing: A deep neuroevolution approach,'' in \textit{Proc. IEEE International Conference on Communications}, Denver, USA, 2024.
    \item D. Kompostiotis, D. Vordonis, V. Paliouras, and \textbf{G. C. Alexandropoulos}, ``Optimizing indoor RIS-aided physical layer security: A codebook-generation methodology and measurement-based analysis,'' in \textit{Proc. IEEE International Symposium on Personal, Indoor and Mobile Radio Communications}, Istanbul, Turkey, 2025.
    \item G. Rexhepi, H. S. Rou, G. T. F. de Abreu, and \textbf{G. C. Alexandropoulos}, ``Blinding the wiretapper: RIS-aided user occultation in the ISAC era,'' in \textit{Proc. IEEE Asilomar Signals, Systems and Computer Conference}, Pacific Grove, USA, 2025.
    \item  \textbf{N. Su}, F. Liu, \textbf{C. Masouros}, \textbf{G. C. Alexandropoulos}, Y. Xiong, and Q. Zhang, ``Secure ISAC MIMO systems: exploiting interference with Bayesian Cramér-Rao bound optimization," \textit{EURASIP Journal on Wireless Communications and Networking}, 2025, Art. no. 10.
    \item \textbf{N. Su}, F. Liu, and \textbf{C. Masouros}, ``Sensing-Assisted Eavesdropper Estimation: An ISAC Breakthrough in Physical Layer Security," \textit{IEEE Transactions on Wireless Communications}, vol. 23, no. 4, pp. 3162-3174, April 2024. \textbf{\textit{ - Top 3 Popular Article of IEEE TWC}}
    \item \textbf{N. Su}, F. Liu, \textbf{Z. Wei}, Y.-F. Liu, and \textbf{C. Masouros}, ``Secure Dual-Functional Radar-Communication Transmission: Exploiting Interference for Resilience Against Target Eavesdropping," \textit{IEEE Transactions on Wireless Communications}, vol. 21, no. 9, pp. 7238-7252, Sept. 2022.
    \item \textbf{N. Su}, F. Liu, and \textbf{C. Masouros}, ``Secure Radar-Communication Systems With Malicious Targets: Integrating Radar, Communications and Jamming Functionalities," \textit{IEEE Transactions on Wireless Communications}, vol. 20, no. 1, pp. 83-95, Jan. 2021.
    \item \textbf{Z. Wei}, \textbf{C. Masouros}, X. Zhu, P. Wang, and A. Petropulu, ``PHY Layer Anonymous Precoding: Sender Detection Performance and Diversity- Multiplexing Tradeoff," IEEE Transactions on Wireless Communications, vol. 23, no. 5, pp. 4531-4545, May 2024.
    \item \textbf{Z. Wei}, F. Liu, \textbf{C. Masouros}, \textbf{N. Su}, and A. P. Petropulu, ``Toward Multi-Functional 6G Wireless Networks: Integrating Sensing, Communication, and Security," \textit{IEEE Communications Magazine}, vol. 60, no. 4, pp. 65-71, April 2022.
    \item J. Zou, \textbf{C. Masouros}, F. Liu, and S. Sun, ``Securing the Sensing Functionality in ISAC Networks: An Artificial Noise Design," \textit{IEEE Transactions on Vehicular Technology}, vol. 73, no. 11, pp. 17800-17805, Nov. 2024.
    \item \textbf{Z. Wei}, P. Wang, A. Petropulu, \textbf{C. Masouros}, and S. Sun, ``Physical Layer Anonymous Communications in Trustworthy 6G: Fundamentals, Recent Advances, and Future Trends," \textit{IEEE Wireless Communications}, vol. 32, no. 2, pp. 26-32, April 2025.

    \item \textbf{Z. Wei}, \textbf{C. Masouros}, P. Wang, X. Zhu, J. Wang, and A. Petropulu, ``Physical Layer Anonymous Precoding Design: From the Perspective of Anonymity Entropy," \textit{IEEE Journal on Selected Areas in Communications}, vol. 40, no. 11, pp. 3224-3238, Nov. 2022.
    \item \textbf{A. Bazzi} and M. Chafii, ``Secure Full Duplex Integrated Sensing and Communications," \textit{IEEE Transactions on Information Forensics and Security}, vol. 19, pp. 2082-2097, 2024
    \item M. Mittelbach, R. F. Schaefer, M. Bloch, \textbf{A. Yener}, and O. Günlü, ``Sensing-assisted secure communications over correlated Rayleigh fading channels," \textit{Entropy}, vol. 27, no. 3, pp. 225, Mar. 2025.
    \item Y. E. Sagduyu, T. Erpek, \textbf{A. Yener}, and S. Ulukus, ``Will 6G Be Semantic Communications? Opportunities and Challenges From Task Oriented and Secure Communications to Integrated Sensing," \textit{IEEE Network}, vol. 38, no. 6, pp. 72-80, Nov. 2024.
    \item H. Pan, S. Yang, \textbf{T.-T. Chan}, Z. Wang, V. C. M. Leung, and J. Li, ``SC-PNC: Semantic Communication-Empowered Physical-Layer Network Coding," \textit{IEEE Transactions on Cognitive Communications and Networking}, vol. 10, no. 4, pp. 1160-1174, Aug. 2024.
    \item R. A. Chou and \textbf{A. Yener}, ``The Gaussian Multiple Access Wiretap Channel With Selfish Transmitters: A Coalitional Game Theory Perspective," \textit{IEEE Transactions on Information Theory}, vol. 70, no. 10, pp. 7432-7446, Oct. 2024.
    \item Y. E. Sagduyu, S. Ulukus, and \textbf{A. Yener}, ``Task-Oriented Communications for NextG: End-to-end Deep Learning and AI Security Aspects,'' \textit{IEEE Wireless Communications}, vol. 30, no. 3, pp. 52-60, June 2023
    \item O. Günlü, M. R. Bloch, R. F. Schaefer, and \textbf{A. Yener}, ``Secure Integrated Sensing and Communication," \textit{IEEE Journal on Selected Areas in Information Theory}, vol. 4, pp. 40-53, 2023.
    \item M. Bloch, O. Günlü, \textbf{A. Yener}, F. Oggier, H. V. Poor, and L. Sankar ``An Overview of Information-Theoretic Security and Privacy: Metrics, Limits and Applications," \textit{IEEE Journal on Selected Areas in Information Theory}, vol. 2, no. 1, pp. 5-22, Mar. 2021.
    \item Y. Xie, Y. Wen, X. Zhang, P. Lai, Z. Liu, H. Pan, and \textbf{T.-T. Chan}, ``Reflection Optimization for Covert Ambient Backscatter Systems Under Two Jamming Patterns," \textit{IEEE Internet of Things Journal}, vol. 12, no. 14, pp. 27728-27741, Jul. 2025.
\end{enumerate}


\section{Topic, Scope, and Brief Outline of the Proposed Special Issue}

As 6G wireless networks evolve towards deeply integrated and intelligent infrastructures, security and trustworthiness begin to constitute foundational design principles rather than afterthoughts. Unlike previous wireless network generations, 6G is characterized by the convergence of communications, sensing, computing, and control into a unified system. Emerging technologies, such as integrated sensing and communications (ISAC), semantic-aware protocols, reconfigurable intelligent surfaces (RISs), near-field and THz communications, as well as AI-native networking are driving this convergence.

While the latter innovations offer transformative wireless connectivity capabilities, they also introduce new and complex security challenges. The increased coupling between sensing and communications exposes novel attack paradigms, such as inference-based privacy breaches, signal and propagation manipulation, and semantic tampering. AI-driven network functions, while powerful, are vulnerable to poisoning and evasion attacks. In the meanwhile, the open and dynamic nature of 6G environments—including multi-tenant infrastructure, heterogeneous devices, and decentralized learning—calls for resilient, zero-trust security mechanisms at all layers of the protocol stack.

This SI aims to provide a focused platform for original research on the design, analysis, and validation of secure and trustworthy 6G wireless systems, with an emphasis on the interaction between security, resilience, intelligence, and adaptability. While we place special emphasis on physical-layer and cross-layer security mechanisms, we welcome contributions from across the overall system stack, including architecture, algorithms, learning, and hardware implementations. More specifically, we seek contributions that address the following overarching goals:
\begin{itemize}
	\item	Advanced theoretical foundations and modeling frameworks for security, privacy, and trust in intelligent, programmable, and reconfigurable 6G systems;
	\item	Secure and resilient protocols and architectures that span from the physical to the application layers, especially in dynamic, adversarial, and resource-constrained environments;
	\item Adaptive security mechanisms leveraging AI, learning, and context-awareness, while accounting for their own vulnerabilities; and
	\item Prototype and benchmark secure 6G systems through testbeds, field trials, and reproducible datasets, with relevance to practical deployment scenarios such as ISAC, UAV networks, or smart city infrastructure.
\end{itemize}
    
In line with the latter goals, the topics of interest include, but are not limited to, the following:
\begin{itemize}
   \item Security and privacy in integrated sensing and communications for 6G;
    \item Secure RIS-aided communications and anti-eavesdropping solutions;
    \item Physical-layer authentication and RF fingerprinting for secure 6G networks;
    \item AI-assisted physical-layer security and defenses against adversarial attacks;
    \item AI-based evasive active hypothesis testing;
    \item Semantic-aware physical-layer security solutions for intelligent communications;
    \item Physical-layer covert communication techniques and anti-detection strategies;
    \item Secure mmWave and THz communication systems in 6G;
    \item Physical-layer security for UAV-assisted wireless communications;
    \item Security challenges and solutions for near-field wireless communication systems;
    \item Secure MIMO techniques including holographic, massive, and XL antenna systems;
    \item Secure reconfigurable antenna technologies (e.g., fluid, movable, pinching, and rotatable antennas).
    \item Information-theoretic analysis of physical-layer security and privacy in integrated sensing and communications;
    \item Quantum and post-quantum techniques for secure physical-layer communications;
    \item Privacy-preserving federated and distributed learning at the physical layer;
    \item Differential privacy in physical-layer data transmission and sensing;
    \item Physical-layer cryptographic schemes for IoT and edge applications
    \item Cross-layer security designs for 6G;
    \item Trusted hardware platforms and secure RF front-end design;
    \item Security-oriented datasets, benchmarks, and reproducible evaluation frameworks; 
    \item New security metrics for 6G (e.g., anonymous/covert, differential privacy); and
    \item Large language models and generative AI for security.
\end{itemize}

The SI will also dovetail with ongoing IEEE efforts toward future networks—not only the IEEE Future Networks Initiative but also several active ComSoc Emerging Technology Initiatives that resonate strongly with the research themes of IEEE Transactions on Cognitive Communications and Networking. In particular, it aligns with the Integrated Sensing and Communication (ISAC) ETI, the Reconfigurable Intelligent Surfaces (RIS) ETI, the Machine Learning for Communications (MLC) ETI, and the Electromagnetic Signal and Information Theory (ESIT) ETI, thereby ensuring fertile cross-pollination with the latest standardization activities in 3GPP and ITU-T on the road to IMT-2030.

\section{Relationship with Other Recent Special Issues}
The guest editorial team has carefully reviewed SIs published in major journals of the IEEE Communications Society over the past three years. While security remains a consistently important topic, we have found that no recent IEEE SI has focused solely and systematically on the theme of resilience and trustworthiness for 6G smart wireless environments. The most thematically relevant issue is the ongoing 2024 IEEE JSAC SI on ``Secure Communication, Sensing, and Computation for Future Intelligent Networked Systems,'' which primarily emphasized distributed learning, over-the-air computation, and privacy in federated systems. However, this issue did not offer an architectural or system-wide exploration of security challenges arising from 6G-enabling technologies, such as semantic communications, ISAC, or reconfigurable wireless environments. In addition, past SIs, such as the 2022 IEEE Communications Magazine SI on ``Security for Emerging 6G Networks'' and the 2023 JSAC SI on ``Private and Covert Communications: Fundamentals, Methods, and Applications'' focused on specific protocol-level or information-theoretic aspects of security, without addressing the broader architectural, physical-layer, and cross-layer resilience concerns introduced by intelligent and programmable 6G environments.

To highlight the uniqueness within the TCCN scope, we note a remotely related special issue: the First Quarter 2020 TCCN SI on "Evolution of Cognitive Radio to AI-Enabled Radio and Networks." This SI explored the transition to AI-enabled cognitive systems and briefly touched on related security concerns, such as physical-layer attacks in cognitive radio systems. In contrast, our proposed SI takes a holistic approach to resilience and trustworthiness for 6G smart wireless environments, addressing emerging threats from technologies like ISAC, RIS, and semantic communications, with a strong emphasis on physical-layer and cross-layer defenses.

Another relevant example is the Second Quarter 2021 TCCN SI on "Intelligent Surfaces for Smart Wireless Communications," which focused on modeling, analysis, and protocols for RIS in smart communications. However, it did not delve into the security implications, such as anti-eavesdropping or adversarial robustness in RIS-enabled networks, which our SI aims to prioritize alongside broader 6G resilience challenges.

In contrast, our proposed SI focuses on the systematic design and analysis of security, privacy, and trust frameworks in the context of 6G networks that are AI-native, sensing-integrated, and infrastructure-reconfigurable. The SI emphasizes resilience under adversarial conditions, including signal-level attacks, model poisoning, and semantic manipulation, as well as the joint design of physical-layer and system-layer defenses. Moreover, the SI's scope includes hardware-aware and prototype-driven explorations, which are largely absent in previous efforts.

It is also noted that many prior SIs treat wireless security as a topic confined to cryptographic methods, higher-layer protocols, or specific threat models. Our SI, in contrast, addresses a new generation of challenges and opportunities that emerge when communications, sensing, AI, and programmable wireless infrastructure and environments are deeply co-designed. The SI's unique perspective is critical as 6G envisions multi-tenant, decentralized, and autonomous network behavior that must be protected by architectures built for security and trustworthiness from the ground up.

To the best of our knowledge, there is currently no IEEE TCCN or IEEE-wide SI dedicated to examining the intersection of security, intelligence, and reconfigurability in cognitive wireless networks from a cross-layer and architectural perspective. This combination of themes is aligned with the editorial scope of IEEE Transactions on Cognitive Communications and Networking, and has not been comprehensively covered in other recent IEEE SIs. The proposed issue will serve the TCCN community by providing a focused venue for interdisciplinary work on security and resilience in the era of intelligent and programmable smart wireless environments.

\section{Plan for Obtaining Quality Papers}
The topic of this SI has lately been garnering considerable attention. Researchers in both academia and industry are dedicating efforts to develop innovative wireless technologies for network resilience and trustworthiness. Consequently, there are numerous active research groups in this field that are potential sources of high-quality papers for this SI, as listed in Annex 1. We will employ various approaches to inform them about this SI, including Email, Facebook, Twitter, LinkedIn, WhatsApp, and WeChat. Notably, the proposed editorial team founded and leads the IEEE ComSoc ISAC Emerging Technology Initiative (ISAC-ETI), a global community that now connects more than 890 mailing-list subscribers—including 36 IEEE Fellows—and engages over 10 000 followers across WeChat, YouTube, and Bilibili. This platform will be used to circulate the Call for Papers not only to researchers in integrated sensing and communications (ISAC) but also to those working on RIS and security-oriented topics such as physical-layer secrecy, secure RIS configuration, and threat mitigation for perceptive networks. In parallel, we will distribute the announcement through the major mailing lists of IEEE ComSoc Technical Committees—including WTC, SPCE, TCGCC, and TCCN—to ensure broad visibility among specialists in wireless communications, signal processing, green communications, and network security, thereby attracting high-quality submissions at the nexus of ISAC, RIS, and security.

\section{Guest Editors}
The team of guest editors (GEs) comprises 7 experts with established and complementary track records in the area of this special issue.
\begin{itemize}
    \item George C. Alexandropoulos, National and Kapodistrian University of Athens, Greece.
    \item Nanchi Su, Harbin Institute of Technology (Shenzhen), China.
    \item Christos Masouros, University College London, UK.
    \item Zhongxiang Wei, Tongji University, China.
    \item Ahmad Bazzi, New York University (NYU) Abu Dhabi, United Arab Emirates \& NYU WIRELESS, NYU Tandon School of Engineering.
    \item Aylin Yener, The Ohio State University, USA.
    \item Tse-Tin Chan, The Education University of Hong Kong, Hong Kong SAR, China.
\end{itemize}
An excellent geographical balance is achieved with 7 experts from Europe, Asia, and USA, which would considerably enhance the practicality of the ISAC research and attract industrial researchers to submit their works to this SI. 

The GEs currently serve as the chairs and founding members of the IEEE ComSoc ISAC Emerging Technology Initiative (ISAC-ETI), which provides a platform to bring together academic and industrial researchers to share their ideas related to ISAC. It is worth mentioning that the GEs have collaboratively edited a book and a ComSoc Best Readings list on ISAC, as well as numerous special issues, workshops, special sessions, and tutorials related to ISAC in top-tier journals and flagship conferences. Some of these are listed below:
\begin{itemize}
    \item K. Meng, C. Masouros, and G. C. Alexandropoulos, "Smart Wireless Environments for Integrating Sensing and Communications," Springer Nature, Switzerland, to appear, 2026. - Ongoing book from \textbf{George C. Alexandropoulos}, \textbf{Christos Masouros}
    \item G. C. Alexandropoulos, A. Zappone, N. Shlezinger, M. Di Renzo, and Y. C. Eldar, "Reconfigurable Metasurfaces for Wireless Communications: Architectures, Modeling, and Optimization," Springer Nature, Singapore, to appear, 2025. - Ongoing book from \textbf{George C. Alexandropoulos}
    \item Springer Nature Book “Integrated Sensing and Communications” - Edited by \textbf{Christos Masouros}, chapters contributed by \textbf{Nanchi Su}
    \item IEEE Wireless Communications Special Issue on "Integrated Sensing and Communications for 6G" - \textbf{Christos Masouros}
    \item IEEE ICC 2024 Symposium on Selected Areas in Communications (SAC): Track on Integrated Sensing and Communications (ISAC) - \textbf{Christos Masouros}
    \item IEEE GLOBECOM 2025 23rd Workshop on Fluid Antenna System (FAS) for 6G - \textbf{George C. Alexandropoulos}
    \item IEEE ICC 2023 13th Workshop on Synergies of Communication, Localization, and Sensing Towards 6G - \textbf{George C. Alexandropoulos}
    \item IEEE ICC 2023 5th Workshop on Integrated Sensing and Communication (ISAC) - \textbf{Christos Masouros}
\end{itemize}


\section{Proposed Review Process}

All papers submitted to this special issue will undergo a rigorous review process adhering to the IEEE paper review policy. The Guest Editors possess extensive experience as (Guest) Editors for premier technical journals such as IEEE Journal on Selected Areas in Communications, IEEE Transactions on Communications, IEEE Transactions on Wireless Communications, IEEE Wireless Communications, IEEE Transactions on Signal Processing, IEEE Journal on Selected Topics in Signal Processing, IEEE Journal on Special Areas in Information Theory, IEEE Signal Processing Magazine, IEEE Wireless Communications Letters, and IEEE Communications Letters. The GEs have also made significant contributions to ISAC through high-impact publications and the organization of important workshops, special issues, and tutorials, as detailed above. Each paper will be assigned to at least three leading experts in the field as reviewers. There will be one review round, and conditional acceptance will only be granted to manuscripts requiring moderate revisions. A detailed but incomplete list of potential technical reviewers is provided in Annex 2.

\section{A Proposed Call-for-Papers}
Attached in Annex 3.

\section{Brief Biographies of the Seven Guest Editors}
\textbf{George C. Alexandropoulos} (Senior Member, IEEE) received the Engineering Diploma (Integrated M.S.c), M.A.Sc., and Ph.D. degrees in Computer Engineering and Informatics from the School of Engineering, University of Patras, Greece in 2003, 2005, and 2010, respectively. He has held senior research positions at various Greek universities and research institutes, and he was a Senior Research Engineer and a Principal Researcher at the Mathematical and Algorithmic Sciences Lab, Paris Research Center, Huawei Technologies France, and at the Technology Innovation Institute, Abu Dhabi, United Arab Emirates, respectively. He is currently an Associate Professor with the Department of Informatics and Telecommunications, School of Sciences, National and Kapodistrian University of Athens (NKUA), Greece and an Adjunct Professor with the Department of Electrical and Computer Engineering, University of Illinois Chicago, Chicago, IL, USA. His research interests span the general areas of algorithmic design and performance analysis for wireless networks with emphasis on multi-antenna transceiver hardware architectures, full duplex MIMO, active and passive RISs, ISAC, millimeter wave and THz communications, as well as distributed machine learning algorithms. He currently serves as an Editor for IEEE Transactions on Communications, IEEE Transactions on Green Communications and Networking, IEEE Transactions on Mobile Computing, IEEE Wireless Communications Letters, Frontiers in Communications and Networks, and the ITU Journal on Future and Evolving Technologies. Prof. Alexandropoulos is a Senior Member of the IEEE Communications, Signal Processing, Vehicular Technology, and Information Theory Societies, the Chair of the EURASIP Technical Area Committee on Signal Processing for Communications and Networking, as well as a registered Professional Engineer of the Technical Chamber of Greece. From 2022 to 2024, he was a Distinguished Lecturer of the IEEE Communications Society. He has participated and/or technically managed more than 15 European Union, international, and Greek research, innovation, and development projects, including the H2020 RISE‑6G, SNS JU TERRAMETA, SNS JU 6G-DISAC, and ESA PRISM projects dealing with RIS-empowered smart wireless environments, THz RISs, distributed ISAC, and RIS demonstration for localization and mapping, respectively. He was the recipient of the best Ph.D. thesis award 2010, IEEE Communications Society Best Young Professional in Industry Award 2018, EURASIP Best Paper Award of the Journal on Wireless Communications and Networking 2021, IEEE Marconi Prize Paper Award in Wireless Communications 2021, Best Paper Award from the IEEE GLOBECOM 2021 and IEEE VTC-Spring 2025, IEEE Communications Society Fred Ellersick Prizes 2023 and 2024, IEEE Communications Society Leonard G. Abraham Prize 2024, and NKUA’s Research Excellence Award for the academic year 2023-2024. More information is available at www.alexandropoulos.info. 





\vspace*{0.3cm}
\noindent\textbf{Nanchi Su} (Member, IEEE) is currently an Associate Research Fellow of Guangdong Provincial Key Laboratory of Aerospace Communication and Networking Technology, Harbin Institute of Technology (Shenzhen), Shenzhen, China. She received the B.Eng. and M.Eng. degrees from Harbin Institute of Technology (HIT), Harbin, China, in 2015 and 2018, respectively, and Ph.D. degree from University College London, London, UK, in 2023. Since 2023, she has also been serving as a Visiting Researcher at the Southern University of Science and Technology (SUSTech), where she actively collaborates on interdisciplinary research projects. Her research interests span a broad range of topics in wireless communications, including integrated sensing and communication (ISAC), network security, green communications, and space–air–ground integrated networks (SARGIN). She has authored over 10 peer-reviewed research papers published in prestigious journals and international conferences, including IEEE Transactions on Wireless Communications and flagship IEEE conferences such as Globecom and Asilomar. Among these, three papers have been recognized as highly cited. She has also contributed as a reviewer for several top-tier journals, including IEEE Transactions on Communications, IEEE Transactions on Signal Processing, IEEE Internet of Things Journal, and IEEE Wireless Communications Letters. She serves as a Technical Program Committee (TPC) member for numerous major IEEE conferences, such as IEEE Globecom, ICC, WCNC, and SPAWC.






\vspace*{0.3cm}
\noindent\textbf{Christos Masouros} (Fellow, IEEE) received the Diploma degree in Electrical and Computer Engineering from the University of Patras, Greece, in 2004, and MSc by research and PhD in Electrical and Electronic Engineering from the University of Manchester, UK in 2006 and 2009 respectively. In 2008 he was a research intern at Philips Research Labs, UK, working on the LTE standards. Between 2009-2010 he was a Research Associate in the University of Manchester and between 2010-2012 a Research Fellow in Queen's University Belfast. In 2012 he joined University College London as a Lecturer. He has held a Royal Academy of Engineering Research Fellowship between 2011-2016. Since 2019 he is a Full Professor of Signal Processing and Wireless Communications in the Information and Communication Engineering research group, Dept. Electrical and Electronic Engineering, and affiliated with the Institute for Communications and Connected Systems, University College London. His research interests lie in the field of wireless communications and signal processing with particular focus on Green Communications, Large Scale Antenna Systems, Integrated Sensing and Communications, interference mitigation techniques for MIMO and multicarrier communications. Between 2018-22 he was the Project Coordinator of the € 4.2m EU H2020 ITN project PAINLESS, involving 12 EU partner universities and industries, towards energy-autonomous networks. Between 2024-28 he will be the Scientific Coordinator of the €2.7m EU H2020 DN project ISLANDS, involving 19 EU partner universities and industries, towards next generation vehicular networks. He is a Fellow of the IEEE, a Fellow of the Asia-Pacific Artificial Intelligence Association (AAIA), and was the recipient of the 2023 IEEE ComSoc Stephen O. Rice Prize, co-recipient of the 2021 IEEE SPS Young Author Best Paper Award and the recipient of the Best Paper Awards in the IEEE GlobeCom 2015 and IEEE WCNC 2019 conferences. He is an IEEE ComSoc Distinguished lecturer 2024-2025, has been recognised as an Exemplary Editor for the IEEE Communications Letters, and as an Exemplary Reviewer for the IEEE Transactions on Communications. He is an Editor for IEEE Transactions on Wireless
Communications, the IEEE Open Journal of Signal Processing, and Editor-at-Large for IEEE Open Journal of the Communications Society. He has been an Editor for IEEE Transactions on Communications, IEEE Communications Letters, and a Guest Editor for a number of IEEE Journal on Selected Topics in Signal Processing and IEEE Journal on Selected Areas in Communications issues. He is a founding member and Vice-Chair of the IEEE Emerging Technology Initiative on Integrated Sensing and Communications (ISAC), Vice Chair of the IEEE Wireless Communications Technical Committee Special Interest Group on ISAC, and Chair of the IEEE Green Communications \& Computing Technical Committee, Special Interest Group on Green ISAC. He is a member of the IEEE Standards Association Working Group on ISAC performance metrics, and a founding member of the ETSI ISG on ISAC. He is the TPC chair for the IEEE ICC 2024 Selected Areas in Communications (SAC) Track on ISAC, and Chair of the "Integrated Imaging and Communications" stream in IEEE CISA 2024.

\vspace*{0.3cm}
\noindent\textbf{Zhongxiang Wei} (Senior Member, IEEE)  received the Ph.D. degree in Electrical and Electronics Engineering from the University of Liverpool, Liverpool, U.K., in 2017. From March 2016 to March 2017, he was with the Institution for Infocomm Research, Agency for Science, Technology and Research, Singapore, as a Research Assistant. From March 2018 to March 2021, he was with the Department of Electrical and Electronics Engineering, University College London, as a research associate. He is currently an associate professor in the College of Electronic and Information Engineering, Tongji University, China. He has authored and co-authored more than 100 research papers published on top-tier journals and international conferences. His research interests include anonymous communications, Internet of Things, and multi-antenna systems. He acted as a session/track/workshop chair, and tutorial speaker of various international flagship conferences, such as IEEE ICC, GLOBECOM, ICASSP, and WCNC. He acted as a leading guest editor of IEEE Internet-of-Things Journals, IEEE Open Journal on Vehicular Technology. He was a recipient of the Outstanding Students Abroad in 2018, best paper award of IWCMC in 2024, the A*STAR Research Attachment Programme (ARAP) in 2016.



\vspace*{0.3cm}
\noindent\textbf{Ahmad Bazzi} (Senior Member, IEEE) was born in Abu Dhabi, United Arab Emirates. He received his PhD degree in electrical engineering from EURECOM, Sophia Antipolis, France, in 2017, and the MSc degree (summa cum laude) in wireless communication systems (SAR) from Centrale Supélec, in 2014. He is currently a Research Scientist at the Wireless Research Lab of New York University (NYU) Abu Dhabi, and NYU WIRELESS, NYU Tandon School of Engineering, contributing to integrated sensing and communications (ISAC). Prior to that, he was the Algorithm and Signal Processing Team Leader at CEVA-DSP, Sophia Antipolis, leading the work on Wi-Fi (802.11ax) and Bluetooth (5.xx BR/BLE/BTDM/LR) high-performant (HP) PHY modems, OFDMA MAC schedulers, and RF-related issues. He is an inventor with multiple patents involving intellectual property of Wi-Fi and Bluetooth products, all of which have been implemented and sold to key clients. Since 2018, he has been publishing lectures on the YouTube platform under his name “Ahmad Bazzi”, where his channel contains mathematical, algorithmic, and programming topics, with over 270,000 subscribers and more than 17 million views, as of November 2024. He was awarded a CIFRE Scholarship from Association Nationale Recherche Technologies (ANRT) France, in 2014, in collaboration with RivieraWaves (now CEVA-DSP). He was nominated for Best Student Paper Award at IEEE International Conference on Acoustics, Speech, and Signal Processing (ICASSP) 2016. He received the Silver Plate Creator Award from YouTube, in 2022, for his 100,000 subscriber milestone. He was awarded an exemplary reviewer for the IEEE Transactions on Communications in 2022 and an exemplary reviewer for the IEEE Wireless Communications Letters in 2022. He served as technical program committee (TPC) member and a reviewer for many leading international conferences and journals. He was selected amongst top 200 Top Arab creators for 2023. His research interests include signal processing, wireless communications, artificial intelligence, statistics, and optimization.


\vspace*{0.3cm}
\noindent\textbf{Aylin Yener}(Fellow, IEEE) received the B.Sc. degrees in Electrical and Electronics Engineering and in Physics from Bogazici University, Istanbul, Turkey, the M.S. degree in Electrical and Computer Engineering from Rutgers University, New Brunswick, NJ, and the Ph.D. degree in Electrical and Computer Engineering from Rutgers University, New Brunswick, NJ. From 2000 to 2001, she was an Assistant Professor in the Electrical and Computer Engineering Department at Lehigh University. In 2002, she joined Penn State University as an Assistant Professor of Electrical Engineering, becoming Associate Professor in 2006, Professor in 2010, University Distinguished Professor in 2019, and Dean’s Fellow from 2017 to 2019. In 2008-2009, she was a Visiting Associate Professor in the Electrical Engineering Department at Stanford University, and in 2016-2018 she was a Visiting Professor in the same department. She also held a visiting position at Telecom Paris Tech in the summer of 2016. Since January 2020, she holds the Roy and Lois Chope Chair in Engineering at The Ohio State University, and is Professor of Electrical and Computer Engineering, Professor of Computer Science and Engineering, and Professor of Integrated Systems Engineering. Her research interests lie in the field of networked entities with a focus on next-generation connectivity of computing, communicating, and sensing entities (known as 6G), including smart environments, artificial intelligence, and security/privacy. She is known for introducing several "first papers" in communications and information theory that led to research areas including physical layer security (2005), energy harvesting wireless communication networks (2009, co-inventor), and semantic communications (2012). Her research has been supported by NSF, DARPA, NSA, ARL/ARO, DoT, and various industry and state entities. She is a Fellow of the IEEE, a Fellow of the American Association for the Advancement of Science (AAAS), a Fellow of the Asia-Pacific Artificial Intelligence Association (AAIA), and an elected member of The Science Academy, Turkey. She was the recipient of the 2025 IEEE Information Theory Society Joy Thomas Paper Award, the 2020 IEEE Communications Society Communication Theory Technical Achievement Award, the 2019 IEEE Communications Society Best Tutorial Paper Award, the 2018 IEEE Women in Communications Outstanding Achievement Award, the 2014 IEEE Marconi Prize Paper Award, and the 2010 IEEE ICC Best Paper Award. In 2017, she was a Clarivate Analytics Highly Cited Researcher. She has been a Distinguished Lecturer for the IEEE Communications Society (2018-2019), IEEE Information Theory Society (2019-2021), and IEEE Vehicular Technology Society (2017-2021), and has delivered over 80 technical keynotes and invited lectures. She is an Editor-in-Chief of IEEE Transactions on Green Communications and Networking (2022-2026), Area Editor for Security and Privacy in IEEE Transactions on Information Theory (2021-present), and Senior Editor for IEEE Journal on Selected Areas in Information Theory (2019-present) and IEEE Journal on Selected Areas in Communications (2016-present). She has held numerous volunteer roles for the IEEE Communications, Information Theory, Signal Processing, and Vehicular Technology Societies, including President of the IEEE Information Theory Society in 2020, Treasurer (2012-2014), Vice President, Past President, and Board of Governors member. She served on the AdCom for the IEEE Systems Council and as an elected member of the Nominations and Appointments Committee of the IEEE Technical Activities Board. In 2022, she was elected as IEEE Division IX Director Elect, serving as Director Elect in 2023 and Director in 2024-2025, placing her on the IEEE Board of Directors and as a voting member of the Technical Activities Board. She is the co-founder of the IEEE North American School of Information Theory, which has run annually since 2008, and leads the education and workforce development efforts of an NSF-funded national center. She has mentored 16 PhD students and 5 postdocs, who have advanced to professorships and top industry positions.


\vspace*{0.3cm}
\noindent\textbf{Tse-Tin Chan} (Member, IEEE) received the B.Eng. (First Class Hons.) and Ph.D. degrees in Information Engineering from The Chinese University of Hong Kong (CUHK), Hong Kong SAR, China, in 2014 and 2020, respectively. He is currently an Assistant Professor with the Department of Mathematics and Information Technology, The Education University of Hong Kong (EdUHK), Hong Kong SAR, China. From 2020 to 2022, he was an Assistant Professor with the Department of Computer Science, The Hang Seng University of Hong Kong (HSUHK), Hong Kong SAR, China. His research interests include wireless communications and networking, Internet of Things (IoT), age of information (AoI), and artificial intelligence (AI)-native wireless communications. He has authored more than 50 papers in leading journals and conferences, including IEEE TCOM, IEEE TMC, IEEE TAP, IEEE T-MTT, and IEEE TCCN, among others. He is the Principal Investigator of several externally funded research projects, with total funding exceeding USD 5 million, covering research topics such as ultra-reliable low-latency vehicle-to-everything (V2X) communications, time-sensitive wireless body-area networks, and intelligent wireless edge networks. He has served as a Technical Program Committee member and reviewer for flagship IEEE conferences, including IEEE ICC and IEEE GLOBECOM.


\section{Guest Editor Statement} 
The Guest Editors understand that guest editor-authored research paper submissions will no longer be accepted. As encouraged by TCCN, the guest editorial team commits to submitting tutorial and overview papers within the specified page limit for regular papers. The Guest Editors also declare that none of them is currently a Guest Editor on an active TCCN issue.

\appendix
\section*{Annex 1: A List of Potential Internationally-Leading Contributors}
\begin{enumerate}
    \item Sriram Vishwanath - University of Texas at Austin, USA
    \item Jeff Andrews - University of Texas at Austin, USA
    \item Sean Ramprashad - DoCoMo USA Labs, USA
    \item Ananthanarayanan Chockalingam - Indian Institute of Science, India
    \item Sundar Rajan - Indian Institute of Science, India
    \item Guy Vandenbosch - Katholieke Universiteit Leuven, Belgium
    \item Armin Wittneben - ETH, Switzerland
    \item Amos Lapidoth - ETH, Switzerland
    \item Helmut Bölcskei - ETH, Switzerland
    \item Giuseppa Alfano - Politecnico di Torino, Italy
    \item Giuseppe Caire - TU Berlin, Germany
    \item Andreas Molisch - University of Southern California, USA
    \item Ralf Müller - University of Erlangen-Nürnberg, Germany
    \item Merouane Debbah - Huawei, France
    \item Ross Murch - Hong Kong University of Science and Technology, Hong Kong
    \item Matthew R. McKay - Hong Kong University of Science and Technology, Hong Kong
    \item Kai-Kit Wong - University College London, UK
    \item Miguel Rodrigues - University College London, UK
    \item Bruno Clerckx - Imperial College London, UK
    \item Deniz Gunduz - Imperial College London, UK
    \item Mischa Dohler - King's College London, UK
    \item Zhiguo Ding - Lancaster University, UK
    \item Emad Alsusa - University of Manchester, UK
    \item Daniel So - University of Manchester, UK
    \item Shi Jin Southeast University, China
    \item Alberto Zanella - WiLAB, Italy
    \item Bjorn Ottersten - University of Luxemburg, Luxemburg
    \item Symeon Chaztinotas - University of Luxemburg, Luxemburg
    \item David Gesbert - EURECOM, France
    \item Peter J. Smith - University of Canterbury, New Zealand
    \item Iain B. Collings - CSIRO ICT Centre, Australia
    \item Robert Heath - University of California, San Diego, USA
    \item Michel Matthaiou - Queen's University Belfast, UK
    \item Tharm Ratnarajah - University of Edinburgh, UK
    \item John Thompson - University of Edinburgh, UK
    \item Mathini Sellathurai - Heriot Watt University, UK
    \item George Karagiannidis - Aristotle University of Thessaloniki, Greece
    \item Aris L. Moustakas - National and Kapodistrian University of Athens, Greece
    \item Angel Lozano - Universitat Pompeu Fabra, Spain
    \item Vincent Poor - Princeton University, USA
    \item Shlomo Shamai (Shitz) - Technion-Israel Institute of Technology, Israel
    \item A. Guillen i Fabregas - Universitat Pompeu Fabra, Spain
    \item Erik Larsson - Linkoping University, Sweeden
    \item Fredrik Tufvesson - Lund University, Sweden
    \item Nihar Jindal - Google Inc., USA
    \item Claude Desset - IMEC, Belgium
    \item Constantinos Papadias - Athens Information Technology, Greece
    \item Antonia M. Tulino - University of Naples Federico II, Italy
    \item Stephan ten Brink - University of Stuttgart, Germany
    \item Reinaldo Valenzuela - Bell Labs, USA
    \item Syed A. Jafar - University of California, Irvine, USA
    \item Ranjan K. Mallik - Indian Institute of Technology, India
    \item De-Chun Sun - Xi'an University, China
    \item Holger Boche - Technical University of Munich, Germany
    \item Wolfgang Utschick - Technical University of Munich, Germany
    \item Duk Kyung Kim - Inha University, South Korea
    \item Akbar M. Sayeed - University of Wisconsin-Madison, USA
    \item Inkyu Lee - Korea University, Korea
    \item Gerhard Fettweis - Technical University of Dresden, Germany
    \item Eduard Jorswieck - Technical University of Dresden, Germany
    \item Markus Rupp - Vienna University of Technology, Austria
    \item Robert Fischer - University of Ulm, Germany
    \item F. Adachi - Tohoku University, Japan
    \item Kwang Bok Lee - Seoul National University, Korea
    \item Mats Bengtsson - KTH, Sweden
    \item David J. Love - Purdue University, USA
    \item Qingyang (Rose) Hu - Utah State University, USA
    \item Jingon Joung - Institute for Infocomm Research, Singapore
    \item Chandra Murthy - Indian Institute of Science, Bangalore, India
    \item Jianwei Niu - Beihang University, China
    \item Joel Rodrigues - University of Beira Interior, Covilhã, Portugal
    \item Yuexin Peng - Beijing University of Posts and Telecommunications, China
    \item Himal A. Suraweera - University of Peradeniya, Sri Lanka
    \item Sennur Ulukus - University of Maryland, USA
    \item Yan Zhang - Simula Research Lab, Norway
    \item Xiangyun (Sean) Zhou - Australian National University, Australia
    \item Maryline Chetto - University of Nantes, France
    \item Zhangbing Zhou - China University of Geoscience (Beijing), China
    \item Paul Prucnal, Princeton University, USA
    \item João Barros, University of Porto, Portugal
    \item Kamran Arshad - University of Surrey, UK
    \item Alireza Attar - University of British Columbia, Canada
    \item Shengrong Bu - University of Glasgow, UK
    \item Maged Elkashlan - Queen Mary University of London, UK
    \item Oliver Holland - King's College London, UK
    \item Ekram Hossain - University of Manitoba, Canada
    \item Hai Jiang - University of Alberta, Canada
    \item Yevgeni Koucheryavy - Tampere University of Technology, Finland
    \item Long Le -MIT, USA
    \item Victor C.M. Leung - University of British Columbia, Canada
    \item Guowang Miao - Royal Institute of Technology, Sweden
    \item Dusit Niyato - Nanyang Technological University, Singapore
    \item Miao Pan - Texas Southern University, USA
    \item Kevin (Qixiang) Pang - General Dynamics, Canada
    \item Yi Qian - University of Nebraska, USA
    \item Tony Q. S. Quek - Singapore University of Technology and Design, Singapore
    \item Jelena Misic - Ryerson University, Canada
    \item Md. Mostafizur Rahman - University of Manitoba, Canada
    \item Xuemin (Sherman) Shen - University of Waterloo, Canada
    \item Biplab Sikdar - Rensselaer Polytechnic Institute, USA
    \item Wei Song - University of California, Berkeley, USA
    \item Helen Tang - DRDC-Ottawa, Canada
    \item Joseph Teo - Institute for InfoComm Research, Singapore
    \item Ping Wang -Nanyang Technological University, Singapore
    \item Yang Xiao - University of Alabama, USA
    \item Qin Xin - Simula Research Laboratory, Norway
    \item F. Richard Yu - Carleton University, Canada
    \item Xi Zhang - Texas A\&M University, USA
    \item Zhu Han - Univ. of Houston, USA
    \item Stefano Tomasin - Univ. of Padova, Italy
    \item Walid Saad - Virginia Tech, USA
    \item Veronica Belmega - ENSEA, France
    \item Arsenia Chorti - University of Essex, UK
    \item Dennis Goeckel - Massachusetts University, USA
    \item Y.-W. Peter Hong - National Tsing Hua University, Taiwan
    \item Gerhard Kramer - TU München, Germany
    \item Lifeng Lai - Worcester Polytechnic Institute, USA
    \item Ingmar Land - University of South Africa, South Africa
    \item Nicola Laurenti - Univ. of Padova, Italy
    \item Jemin Lee - Singapore University of Technology and Design, Singapore
    \item Ming Li - Utah State Univ., USA
    \item Shih-Chun Lin - National Taiwan University of Science and Technology, Taiwan
    \item Derrick Wing Kwan Ng - University of British Columbia, Canada
    \item Samir Perlaza INRIA, France
    \item Francesco Renna University College London, UK
    \item Rafael Schaefer - Princeton, USA
    \item Mikael Skoglund - KTH, Sweden
    \item Ravi Tandon - Virginia Tech, USA
    \item Xianbin Wang - University of Western Ontario, Canada
    \item Hui-Ming Wang - Xian Jiaotong Univ., China
    \item Nan Yang - Australian National University, Australia
    \item Jinhong Yuan - University of New South Whales
    \item Charalambos D. Charalambous- University of Cyprus, Cyprus
    \item Nicholas Laneman- University of Notre Dame, USA
    \item Natasha Devroye- University of Illinois at Chicago, USA
    \item Matthieu Bloch, Georgia Institute of Technology, USA
    \item Martin Haenggi, University of Notre Dame, USA
    \item Koji Ishibashi, The University of Electro-Communications, Japan
    \item Caijun Zhong, Zhejiang University, China
    \item Salman Durrani, The Australian National University, Australia
    \item Sonia Aissa, INRS, Canada
    \item Aria Nosratinia, University of Texas, USA
    \item Jean-Claude Belfiore, Telecom ParisTech, France
    \item Joseph Boutros, Texas A\&M, Qatar
    \item Athina Petropulu, Rutgers University, USA
    \item Athanasios G. Kanatas, University of Piraeus, Greece
    \item George Fischer, University of Erlangen-Nürnberg, Germany
    \item Marwa Chafii, New York University Abu Dhabi \& NYU WIRELESS, NYU Tandon School of Engineering
    \item Fan Liu, Southeast University
\end{enumerate}


\section*{Annex 2: A List of Potential Reviewers}
The following is a partial list of the reviewers for this TCCN special issue:
\begin{enumerate}
    \item Trung Q. Duong - Queen's University Belfast, UK
    \item Guangjie Han - Hohai University, China
    \item Qingyang (Rose) Hu - Utah State University, USA
    \item Jingon Joung - Institute for Infocomm Research, Singapore
    \item Dong Ku Kim - Yonsei University, Korea
    \item Jaime Lloret Mauri - Polytechnic University of Valencia, Spain
    \item Chih-Lin I - China Mobile Research Institute, China
    \item Rangarao Venkatesha Prasad - Delft University of Technology, The Netherlands
    \item Chandra Murthy - Indian Institute of Science, Bangalore, India
    \item Jianwei Niu - Beihang University, China
    \item Joel Rodrigues - University of Beira Interior, Covilhã, Portugal
    \item Yuexin Peng - Beijing University of Posts and Telecommunications, China
    \item Himal A. Suraweera - University of Peradeniya, Sri Lanka
    \item Sennur Ulukus - University of Maryland, USA
    \item Yan Zhang - Simula Research Lab, Norway
    \item Xiangyun (Sean) Zhou - Australian National University, Australia
    \item Maryline Chetto - University of Nantes, France
    \item Zhangbing Zhou - China University of Geoscience (Beijing), China
    \item Kamran Arshad - University of Surrey, UK
    \item Alireza Attar - University of British Columbia, Canada
    \item Shengrong Bu - University of Glasgow, UK
    \item Jun Cai - University of Manitoba, Canada
    \item Ranveer Chandra - Microsoft Research, WA, USA
    \item Peter Chong - Nanyang Technological University, Singapore
    \item Maged Elkashlan - Queen Mary University of London, UK
    \item Oliver Holland - King's College London, UK
    \item Ekram Hossain - University of Manitoba, Canada
    \item Hai Jiang - University of Alberta, Canada
    \item Yevgeni Koucheryavy - Tampere University of Technology, Finland
    \item Long Le -MIT, USA
    \item Xingang Liu - University of Electronic Science and Technology of China, China
    \item Amir Hamed Mohsenian Rad - University of California, Riverside, USA
    \item Victor C.M. Leung - University of British Columbia, Canada
    \item Guowang Miao -Royal Institute of Technology, Sweden
    \item Dusit Niyato - Nanyang Technological University, Singapore
    \item Miao Pan - Texas Southern University, USA
    \item Kevin (Qixiang) Pang - General Dynamics, Canada
    \item Yi Qian - University of Nebraska, USA
    \item Tony Q. S. Quek - Singapore University of Technology and Design, Singapore
    \item Jelena Misic - Ryerson University, Canada
    \item Md. Mostafizur Rahman - University of Manitoba, Canada
    \item Xuemin (Sherman) Shen - University of Waterloo, Canada
    \item Biplab Sikdar - Rensselaer Polytechnic Institute, USA
    \item Wei Song - University of California, Berkeley, USA
    \item Helen Tang - DRDC-Ottawa, Canada
    \item Joseph Teo - Institute for InfoComm Research, Singapore
    \item Ping Wang -Nanyang Technological University, Singapore
    \item Yang Xiao - University of Alabama, USA
    \item Qin Xin - Simula Research Laboratory, Norway
    \item F. Richard Yu - Carleton University, Canada
    \item Xi Zhang - Texas A\&M University, USA
    \item Zhu Han - Univ. of Houston, USA
    \item Stefano Tomasin - Univ. of Padova, Italy
    \item Manav R. Bhatnagar - IIT Delhi, India
    \item Veronica Belmega - ENSEA, France
    \item Marco Baldi - Univ. Marche, Italy
    \item Franco Chiaraluce - Univ. Marche, Italy
    \item Arsenia Chorti - University of Essex, UK
    \item Dennis Goeckel - Massachusetts University, USA
    \item Y.-W. Peter Hong - National Tsing Hua University, Taiwan
    \item Jing Huang - Qualcomm
    \item Gerhard Kramer - TU München, Germany
    \item Lifeng Lai - Worcester Polytechnic Institute, USA
    \item Ingmar Land - University of South Africa, South Africa
    \item Nicola Laurenti - Univ. of Padova, Italy
    \item Jemin Lee - Singapore University of Technology and Design, Singapore
    \item Ming Li - Utah State Univ., USA
    \item Shih-Chun Lin - National Taiwan University of Science and Technology, Taiwan
    \item Behrouz Maham - Univ. of Tehran, Iran
    \item Amitav Mukherjee - Ericsson
    \item Derrick Wing Kwan Ng - University of British Columbia, Canada
    \item Samir Perlaza INRIA, France
    \item Francesco Renna University College London, UK
    \item Aydin Sezgin - RU Bochum, Germany
    \item Pin-Hsun Lin - TU Dresden, Germany
    \item Rafael Schaefer - Princeton, USA
    \item Mikael Skoglund - KTH, Sweden
    \item Ravi Tandon - Virginia Tech, USA
    \item Xianbin Wang - University of Western Ontario, Canada
    \item Hui-Ming Wang - Xian Jiaotong Univ., China
    \item Nan Yang - Australian National University, Australia
    \item Jinhong Yuan - University of New South Whales
    \item Giuseppe Caire - TU Berlin, Germany
    \item A. Chockalingam, Indian Institute of Science, India
    \item I. B. Collings, CSIRO ICT Centre, Australia
    \item Merouane Debbah, Huawei, France
    \item Miltos Filippou, EURECOM, France
    \item Howard Huang, Alcatel Lucent
    \item Erik. G. Larsson, Linköping University, Sweden
    \item S. Lasaulce, CNRS and SUPELEC,
    \item B. K. Lau, Lund University, Sweden
    \item C. Lee Yonsei, University, Korea
    \item J. Leez, Samsung Electronics
    \item O. Leveque, EPFL, Switzerland
    \item Ying-Chang Liang, Institute for Infocomm Research
    \item Hien. Q. Ngo, Linköping University, Sweden
    \item V. K. Nguyen, Defence Science and Technology Organisation, Australia
    \item H. C. Papadopoulos, DOCOMO USA Lab
    \item B. Sundar Rajan, Indian Institute of Science, India
    \item Fredrik Rusek, Lund University, Sweden
    \item S. Sun, Institute for Infocomm Research
    \item Fredrik Tufvesson, Lund University, Sweden
    \item Wolfgang Utschick, University of Munchen, German
    \item Adrian Garcia, University College London, UK
    \item Pierluigi Amadori, University College London, UK
    \item Ang Li, Xi'an Jiaotong University, China
    \item Ka Lung Law, SenseTime, China
    \item Spiros Mikroulis, University College London, UK
    \item Anastiasios Papazafeiropoulos, Imperial College London, UK
    \item Ji-Woong Choi, Dgist, Korea
    \item Q. Zhou, HFUT, China
    \item J. P. Choi, Dgist, Korea
    \item Faheem Khan, U. Edinburgh, UK
    \item Sudip Biswas, U. Edinburgh, UK
    \item Ebtehal Yousif, U. Edinburgh, UK
    \item Tongyang Xu, University College London, UK
    \item Jie Tang, South China University of Technology, China
\end{enumerate}

\newpage
\section*{Annex 3: Proposed Call For Papers}
\begin{center}
\textbf{CALL FOR PAPERS} \\
\textit{IEEE Transactions on Cognitive Communications and Networking} \\
%\vspace*{0.1cm}
\textit{Special Issue on Resilience and Trustworthiness for 6G Smart Wireless Environments} \\
\end{center}
The sixth generation (6G) of broadband mobile networks is expected to revolutionize the way wireless systems perceive, connect, and act. Emerging applications, such as autonomous mobility, the metaverse, digital twins, and real-time control in Industry 5.0, will rely on the tight integration of diverse wireless network functionalities, including communications, sensing and localization, as well as computation. These capabilities will be supported by a complex interplay of technologies, including Integrated Sensing And Communications (ISAC), near-field communications with extremely large antenna arrays under new spectrum, semantic communications, Reconfigurable Intelligent Surfaces (RISs), and protocol stacks with native Artificial Intelligence (AI).

This expected technological convergence is, however, prone to a multitude of risks. Attacks in the upcoming 6G systems may expand across all network layers, from waveform-level spoofing and jamming to AI model poisoning, inference attacks, malicious edge nodes, and insecure hardware components. Noteworthy, many of these envisioned threats are unique to the adaptive, intelligent, and dynamically programmable nature of 6G, rendering traditional security architectures insufficient. This witnesses the urgent need for holistic, resilient, and trustworthy security frameworks that will account for both physical-layer innovations and their cross-layer implications.

This special issue focuses exactly on the latter need by offering a dedicated venue to explore next-generation resilient and trustworthy architectures tailored for 6G systems. Original research studies spanning from fundamental theoretical models and signal-level innovations to full-stack systems and hardware-focused designs, with a particular emphasis on physical layer and cross-layer resilience in smart wireless environments, are welcome. 

\textbf{Topics including, but not limited to, the following are solicited:} 
\setlist[itemize]{noitemsep, topsep=0.1pt, partopsep=0.1pt, parsep=0pt}
\begin{itemize}
    \item Security and privacy in integrated sensing and communications for 6G;
    \item Secure RIS-aided communications and anti-eavesdropping solutions;
    \item Physical-layer authentication and RF fingerprinting for secure 6G networks;
    \item AI-assisted physical-layer security and defenses against adversarial attacks;
    \item AI-based evasive active hypothesis testing;
    \item Semantic-aware physical-layer security solutions for intelligent communications;
    \item Physical-layer covert communication techniques and anti-detection strategies;
    \item Secure mmWave and THz communication systems in 6G;
    \item Physical-layer security for UAV-assisted wireless communications;
    \item Security challenges and solutions for near-field wireless communication systems;
    \item Secure MIMO techniques including holographic, massive, and XL antenna systems;
    \item Secure reconfigurable antenna technologies (e.g., fluid, movable, pinching, and rotatable antennas).
    \item Information-theoretic analysis of physical-layer security and privacy in integrated sensing and communications;
    \item Quantum and post-quantum techniques for secure physical-layer communications;
    \item Privacy-preserving federated and distributed learning at the physical layer;
    \item Differential privacy in physical-layer data transmission and sensing;
    \item Physical-layer cryptographic schemes for IoT and edge applications
    \item Cross-layer security designs for 6G;
    \item Trusted hardware platforms and secure RF front-end design;
    \item Security-oriented datasets, benchmarks, and reproducible evaluation frameworks; 
    \item New security metrics for 6G (e.g., anonymous/covert, differential privacy); and 
    \item Large language models and generative AI for security.
\end{itemize}
Prospective authors should submit their manuscripts following the IEEE TCCN guidelines at \url{https://www.comsoc.org/publications/journals/ieee-tccn} according to the following schedule:

\begin{table}[h]
    \centering
    \begin{tabular}{ll}
        \hline
        Manuscript Submission: TBD & \;\;\;\;\;\;\; First Review Completed: TBD \\
        \hline
        Revised Manuscript Due: TBD & \;\;\;\;\;\;\; Second Review Completed: TBD \\
        \hline
        Final Manuscript Due: TBD & \;\;\;\;\;\;\; Publication Date: TBD \\
        \hline
    \end{tabular}
\end{table}

Guest Editors:
Prof. George C. Alexandropoulos, National and Kapodistrian University of Athens, Greece, email: <EMAIL>

Dr. Nanchi Su, Harbin Institute of Technology (Shenzhen), China, email: <EMAIL>

Prof. Christos Masouros, University College London, UK, email: <EMAIL>

Prof. Zhongxiang Wei, Tongji University, China, email: z\<EMAIL>

Dr. Ahmad Bazzi, NYU Abu Dhabi, UAE, \& NYU WIRELESS, Brooklyn, USA, email: <EMAIL>

Prof. Aylin Yener, The Ohio State University, USA, email: <EMAIL>

Prof. Tse-Tin Chan, The Education University of Hong Kong, Hong Kong SAR, China, email: <EMAIL>


\end{document}