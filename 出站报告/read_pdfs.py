#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import PyPDF2
import glob

def read_pdf(file_path):
    """读取PDF文件内容"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            print(f"\n{'='*60}")
            print(f"文件: {os.path.basename(file_path)}")
            print(f"页数: {len(pdf_reader.pages)}")
            print(f"{'='*60}")
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        print(f"\n--- 第 {page_num} 页 ---")
                        print(page_text)
                        text += f"\n--- 第 {page_num} 页 ---\n{page_text}\n"
                except Exception as e:
                    print(f"读取第 {page_num} 页时出错: {e}")
            
            return text
            
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return ""

def main():
    # 获取当前目录下所有PDF文件
    pdf_files = glob.glob("*.pdf")

    if not pdf_files:
        print("当前目录下没有找到PDF文件")
        return

    print(f"找到 {len(pdf_files)} 个PDF文件:")
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"  {i}. {pdf_file}")

    # 让用户选择要读取的文件
    while True:
        try:
            choice = input(f"\n请选择要读取的文件 (1-{len(pdf_files)}) 或输入 'all' 读取所有文件: ").strip()
            if choice.lower() == 'all':
                for pdf_file in pdf_files:
                    read_pdf(pdf_file)
                break
            else:
                file_index = int(choice) - 1
                if 0 <= file_index < len(pdf_files):
                    read_pdf(pdf_files[file_index])
                    break
                else:
                    print("无效的选择，请重新输入")
        except ValueError:
            print("请输入有效的数字或 'all'")

if __name__ == "__main__":
    main()
