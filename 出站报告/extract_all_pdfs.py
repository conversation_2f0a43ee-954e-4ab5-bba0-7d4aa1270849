#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import PyPDF2
import glob

def extract_pdf_to_text(file_path, output_file):
    """提取PDF文件内容并保存到文本文件"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            output_file.write(f"\n{'='*80}\n")
            output_file.write(f"文件: {os.path.basename(file_path)}\n")
            output_file.write(f"页数: {len(pdf_reader.pages)}\n")
            output_file.write(f"{'='*80}\n\n")
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        output_file.write(f"\n--- 第 {page_num} 页 ---\n")
                        output_file.write(page_text)
                        output_file.write("\n")
                except Exception as e:
                    output_file.write(f"读取第 {page_num} 页时出错: {e}\n")
            
            output_file.write(f"\n{'='*80}\n\n")
            
    except Exception as e:
        output_file.write(f"读取文件 {file_path} 时出错: {e}\n")

def main():
    # 获取当前目录下所有PDF文件
    pdf_files = glob.glob("*.pdf")
    
    if not pdf_files:
        print("当前目录下没有找到PDF文件")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件，开始提取内容...")
    
    # 创建输出文件
    with open("所有PDF内容.txt", "w", encoding="utf-8") as output_file:
        output_file.write("PDF文件内容提取结果\n")
        output_file.write(f"提取时间: {os.popen('date').read().strip()}\n")
        output_file.write(f"总文件数: {len(pdf_files)}\n")
        output_file.write("="*80 + "\n")
        
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"正在处理 {i}/{len(pdf_files)}: {pdf_file}")
            extract_pdf_to_text(pdf_file, output_file)
    
    print("所有PDF内容已提取完成，保存在 '所有PDF内容.txt' 文件中")

if __name__ == "__main__":
    main()
