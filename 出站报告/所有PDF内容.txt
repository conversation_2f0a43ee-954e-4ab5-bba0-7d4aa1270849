PDF文件内容提取结果
提取时间: Sat Jul 26 14:10:34 CST 2025
总文件数: 7
================================================================================

================================================================================
文件: Toward_Multi-Functional_6G_Wireless_Networks_Integrating_Sensing_Communication_and_Security.pdf
页数: 7
================================================================================


--- 第 1 页 ---
65
IEEE Communications Magazine • April 2022 0163-6804/22/$25.00 © 2022 IEEEAbstrAct
Integrated sensing and communication (ISAC) 
has recently emerged as a candidate 6G technol-
ogy, aiming to unify the two key operations of the future network in a spectrum/energy/cost-effi-cient way. ISAC systems communicate and sense for targets using a common waveform, a common hardware platform, and ultimately the same net-work infrastructure. Nevertheless, the inclusion of information signaling in the probing waveform for target sensing raises challenges from the perspec-tive of information security. At the same time, the sensing capability incorporated in ISAC transmis -
sion offers unique opportunities to design secure ISAC techniques. This overview article discusses these unique challenges and opportunities for the next generation of ISAC networks. We first briefly discuss the fundamentals of waveform design for sensing and communication. Then we detail the challenges and contradictory objectives involved in securing ISAC transmission, along with state-of-the-art approaches to ensure security. We then identify the new opportunity of using the sensing capability to obtain knowledge target information as an enabling approach against the known weak-nesses of PHY security. Finally, we illustrate some low-cost secure ISAC architectures, followed by a series of open research topics. This family of sensing-aided secure ISAC techniques brings new insight on providing information security, with an eye on robust and hardware-constrained designs tailored for low-cost ISAC devices. 
IntroductIon  
The 6G network, not only an improvement or extension of existing communication technolo-gy but also a great paradigm revolution, is envi-sioned as the new engine of the future intelligent world. In addition to connecting communication nodes, 6G will support ubiquitous sensing, con-nectivity, and intelligence. Among the exciting features of 6G, sensing will rise from an auxilia-ry functionality to a basic service, providing an extra dimension of capability of the network [1]. This has prompted the recent research interest in integrated sensing and communication (ISAC), a technology that enables the integration of sens-ing and communication functionalities with a sin-gle transmission, a single device, and ultimately a single network infrastructure. By exploiting a common spectral, hardware platform, and signal processing framework, ISAC can improve spectral and energy efficiencies, thus addressing the prob-lem of spectrum congestion and at the same time reducing hardware and signaling costs, referred to as integration gain. Further, by exploiting the possibility to co-design the two functionalities, ISAC can enable communication-aided sensing and sensing-aided communication. Hence, it can considerably improve sensing and communica-tion performance, referred to as coordination gain. Benefiting from the above merits, ISAC can enable emerging applications, including enhanced localization and tracking, drone monitoring/man-agement, human activity recognition, vehicle platooning, environmental monitoring, protocols and network-level sensing, sensing-aided beam training/tracking/prediction, sensing-aided and resource allocation (e.g., cell handover, band -
width/beamwidth/power allocation).
Nevertheless, ISAC comes with unique security 
challenges arising due to the shared use of spec-trum and the broadcast nature of wireless trans-mission. The inclusion of information messages in the radar probing signal makes the communication susceptible to eavesdropping by the target. Indeed, the target that is being sensed can potentially exploit the information-bearing signal and detect the confidential message intended for the com-munication destinations [2]. This raises a unique and very interesting trade-off for the transmitter. On one hand, it wishes to illuminate the target by focusing power toward its direction, and on the other hand, it has to limit the useful signal power that reaches the target to prevent eavesdropping.
A possible solution to the aforementioned 
security challenge is to apply cryptographic tech -
niques at high layers of the network stack to encrypt the confidential data prior to transmission. However, such solutions have several limitations, such as a tedious secret key management/mainte-nance process, unprovable security performance in the presence of a computationally strong eaves-dropper (Eve), and difficulty in identifying a com-promised secret key. Physical layer (PHY) security, an information-theory-based methodology, could be a complementary approach for securing wire-Zhongxiang Wei, Fan Liu, Christos Masouros, Nanchi Su, and Athina P. Petropulu 
Zhongxiang Wei (corresponding author) is with Tongji University, China; Fan Liu (corresponding author) is with  
Southern University of Science and Technology, China; Nanchi Su and Christos Masouros are with University College London,  
United Kingdom; Athina P. Petropulu is with Rutgers University, United States.Digital Object Identifier:
10.1109/MCOM.002.2100972Toward Multi-Functional 6G Wireless 
Networks: Integrating Sensing, 
Communication, and Security MOBILE COMMUNICATIONS AND NETWORKS
Integrated sensing and com-
munication (ISAC) has recently 
emerged as a candidate 6G 
technology, aiming to unify the 
two key operations of the future 
network in a spectrum/energy/
cost-efficient way. ISAC systems 
communicate and sense for tar-
gets using a common waveform, 
a common hardware platform, 
and ultimately the same network 
infrastructure.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:06 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 2 页 ---
IEEE Communications Magazine • April 202266less transmission. By exploiting the channel vari-
ability between Eves and legitimate users (LUs), the signal quality that Eves receive can be degrad-ed to the degree that the Eves cannot extract the message even when they have full knowledge of the secret key [3]. Despite decades of research, the major limitation of a large class of PHY securi-ty solutions stems from either extremely optimistic or overly pessimistic assumptions with regard to what can be known about Eve. Some methods require full knowledge or statistical information of the Eves’ channels. Some methods do not require any knowledge on the Eves’ channels, such as transmitting artificial noise to jam the entire space except the legitimate destination. However, such methods do not make good use of the available bandwidth by transmitting a signal that does not bear communication information, as summarized in Table 1. There have also been recent works that monitor the changes caused by Eves’ inter-action with the radio frequency electromagnet-ic wave field at the PHY to infer Eves’ positions [4]. However, the sensing and communication in [4] are performed separately, and hence the information obtained by the Eves may be outdat -
ed, especially in high-mobility scenarios. Also, this 
TABLE 1.  A brief summary of the existing ISAC and PHY security designs.Relevant 
techniquesDesign principles Pros Cons Remarks
ISAC designSensing-centric designIntegrate communication into radar systemsSide-lobe based ISAC [7]
High compatibility to radar systemsLow data rate
• These security-agnostic techniques may not preserve the confidentiality of the data;  • Even high layer encryption/authentication can be applied, the PHY information contained in the probing waveform can be exploited by the Eve;  • Then the Eve is able to decipher the data.Index or generalized spatial modulation ISAC [6]
Communica-tion-centric designLeverage the existing communication waveform or protocols for sensing [8, 9]Use 802.11p communication waveform, or single carrier PHY frame of 802.11ad for sensingHigh compatibility to communication systemsPoor sensing performance
Use OFDM signals for detection
Joint designInvolves optimization of one system or the other, subject to certain constraints for communication or sensing accuracy [10]Optimize sensing subject to communication quality
High performance of sensing and communicationNeed dedicated waveform designOptimize communication, and meanwhile realize the sensing functionality
Optimize sum-weighted function 
of communication and sensing
Performance analysis by information theoryChannel coding for improving reception and channel estimation performance at the destination [12]
Theoretic trade-off analysis 
between the communication and sensing [11]
PHY secure designSensing-aided secure design [4]Exploit the EM wave change caused by the Eve; then proactively and causally infer the Eve’s path loss to assist secure designEncoder design for providing secrecy even when the Eve moves to improve its eavesdropping capabilityThe secrecy rate can be close to that obtained with hindsight, had the transmitter obtained the Eve’s condition non-causally Only theoretic analysis is givenThough the sensing and communication are performed on the same channel use, the optimal secure waveform is still unknownCharacterize the secrecy rate for any sequence of path loss for the Eve
Sensing agnostic secure design [3]Secure precodingExploit the channel disparity between the LU and Eve for sending signalsAdjustable secrecy rateNeed multiple antennas for exploiting spatial disparity
• Sensing is disabled;  • Rely either on extremely optimistic or pessimistic assumption with regards to the Eve’s condition;  • The transmitter is not capable of proactively sensing the Eves.Artificial NoiseSend isotropic AN toward the null-space of LUsNo requirement of Eve’s CSIImpracticality of network-level interference control
Inject spatial AN towards the direction of EvesMore energy-efficient than isotropic ANNeed Eve’s full or statistical CSI
PHY authenticationUse LUs PHY attributes for authenticationDifficult for Eve to impersonate; No requirement of Eve’s CSINeed agreement among the communication parties
Other hardware-efficient secure designConstellation rotation and noise aggregationLow complexity; No requirement of Eve’s CSIReduced throughput
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:06 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 3 页 ---
IEEE Communications Magazine • April 202267approach is not spectrally efficient, as spectral 
resources are dedicated for sensing only.
Interestingly, the joint sensing and com -
munication mechanism of ISAC ushers in new opportunities for secure design, where the addi-tional sensing functionality can serve as a support to facilitate the provision of security. Motivated by the aforementioned issue, this article over -
views the sensing-aided secure designs together with the characteristics of ISAC. Starting from the fundamentals of ISAC systems, we first examine a novel secure ISAC design. Then we discuss a practical robust secure ISAC design, where knowl-edge of the target and communication users is imperfectly obtained. Further, some hardware-ef-ficient secure ISAC architectures are reviewed. Open challenges are then identified, before con -
cluding this article.
the FundAmentAls  oF IsAc
As an early stage of ISAC, communication and radar spectrum sharing (CRSS) has been investi-gated from the perspective of spectrum sensing, dynamic spectrum access, and mutual interfer-ence mitigation [5] so that communication and radar systems can share the spectrum without sig-nificantly interfering with each other. As a further step, ISAC can realize not only spectral coexis-tence, but also the shared use of hardware plat-form and even network architecture, as shown in Fig. 1. In addition to providing communication and sensing functionalities, ISAC systems lend themselves to communication-aided sensing and sensing-aided communication functionalities. Let us start by discussing the fundamentals of ISAC and then elaborate on secure ISAC transmission.
sensIng  bAsIcs
While communication aims to accurately convey information to a receiver, sensing aims to extract target information from target echoes. Conse -
quently, the useful information for sensing is not in the sensing waveform but in the target return. Interestingly, since sensing and communication performance is evaluated by different key perfor-mance indicators, ISAC waveform design should take different metrics into consideration for imple-menting the dual functionalities. This typically incurs conflicting design objectives between sens-ing and communications, which need to be care-fully balanced as detailed in the next subsection.
WAveForm  desIgn  For IsAc
ISAC waveform designs can be categorized into sensing-centric, communication-centric, and joint designs, as summarized in Table 1.
Sensing-Centric Design: Sensing-centric 
design integrates communication messages into a classical sensing waveform, and hence has high compatibility with the radar architecture. Early sensing-centric design works include pulse inter-val modulation, where the interval between radar pulses is utilized for communication. There have also been designs that leverage the concepts of index modulation, or generalized spatial modula-tion for waveform design [6]. Another sensing-cen-tric design approach is to sense the target in the mainlobe of the radar beampattern, while embed-ding information in the beampattern sidelobes [7]. Nevertheless, since the communication symbols are generally embedded into the radar pulses, the sensing-centric design results in a low data rate, limited by the pulse repetition frequency of the radar, which is well below 5G/6G requirements.
Communication-Centric Design: Communi-
cation-centric designs leverage the standardized communication waveforms, protocols, and archi-tectures for sensing. For example, pilot signals and frame preambles that have good auto-correlation properties and are typically used for channel esti-mation or multi-user access, have recently been employed for sensing targets [8, 9]. Also, stan-dards-relevant communication waveforms, such as the IEEE 802.11p vehicular communication waveform, have been used for sensing targets in vehicular applications. These communication-cen-tric ISAC designs can realize sensing function -
ality without sacrificing the communication performance, thereby obtaining high data rate. However, the pilot signal, frame preambles, and communication waveforms are not dedicatedly designed for sensing. Accordingly, the main draw-back of communication-centric designs lies in the poor, scenario-dependent, and difficult-to-tune sensing performance.
Joint Design: In joint-design ISAC approach-
es, the beampattern is designed to meet an ideal radar beampattern, while ensuring a high sig-nal-to-interference-plus-noise ratio (SINR) at LUs for communications [10]. Also, the sum-weighted sensing and communication quality can also be exploited as an objective function, further leading to a Pareto-optimality of the multi-objective opti-mization. Apart from the optimization-oriented research, the joint design has also been investi-gated from the perspective of information theory, such as the channel coding design [11], as well as the theoretic trade-off between the transmission rate and sensing performance design [12]. Clear-ly, joint design involves dedicated optimization of both functionalities and enables scalable perfor-FIGURE 1. A generic joint communication and sensing design for ISAC. At the transmitter side, the modulated signal 
is manipulated by digital precoding at baseband, then passes through radio frequency (RF) chains, and finally is 
dissipated by antennas. On the other hand, while the reflected echo is analyzed for target detection, the sensing results also assist secure waveform design in a proactive and causal manner.
sensing & com 
vehicles
Sensing-ai ded Proact ive De sign
RF chai n
RF chai nDigita l
Precodi ng
001110
bits 
sequencemodulati onComm unica tion m odulephase shifters
RF chai n
RF chai n
Matched
filter
Radar 
signal 
processing 
Target de tection and 
estimation module
target
(Eve )LUs
clutter
echo 
signa l
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:06 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 4 页 ---
IEEE Communications Magazine • April 202268mance trade-offs between them. It enables flexi -
ble use of time, frequency, and spatial resources, 
thereby achieving both high throughput and sens-ing reliability.
In addition to academic research, there 
have been extensive industrial activities focus -
ing on ISAC, including the 3rd Generation Partership Project (3GPP) (e.g., S1-214036/  
214056/214100/214101, R1-2110894/2104724, and R2-210049), IEEE standards (e.g., 802.11bf WLAN Sensing, 802.15.22.3-2020, and 802.11-2020), and International Telecommunication Union (ITU) recommendations (e.g., ITU-T Y.4809 and ITU-T X.1080.2).
From duAl-FunctIonAl  to multI-FunctIonAl : 
IntegrAtIng  securIty  Into IsAc
In this section, we discuss security issues around ISAC and how the sensing functionality can be judiciously utilized for benefiting the provision of information security.
the unIque  securIty  chAllenges  And opportunItIes  oF IsAc
The ISAC transmitter needs to focus its power toward directions that contain targets and ensure that the target echo at the receiver has good enough signal-to-clutter-plus-noise ratio (SCNR) for achieving certain sensing performance. How -
ever, as the target might be Eve, the angle of the sensing beam that enters the SCNR objective is the same as the angle of Eve, as shown in Fig. 2. This implies that the target has high reception SINR on the embedded communication signal, which significantly increases the susceptibility of information to eavesdropping by the target. Therefore, one should carefully strike a trade-off  between sending sufficient power toward the target’s direction for sensing, while limiting the useful signal power at the target to prevent eaves-dropping. In the following, we examine recent research results of sensing-aided secure ISAC techniques.
The ISAC transmitter is able to sense the angle 
of arrival (AoA) of the echo signal reflected from the target, and infer the target’s position based on the received signal strength. Leveraging this round-trip channel, the wiretap channel from the ISAC transmitter to the target can be estimated. Hence, the proactively and casually obtained wiretap channel, or the target’s AoA as a mini -
mum, can be exploited to design a number of secure approaches including secure beamform-ing, artificial noise, and cooperative jamming, among many others.
The unique ISAC transmission requires the 
aforementioned approaches to be redesigned for achieving “in-band” dual functionality. As an exam-ple, in designing a secure dual functional transmis-sion, one can optimize the sensing performance by maximizing the echo signal’s SCNR at the ISAC’s receiver, while limiting the eavesdropping SINR at the target and at the same time guaranteeing the signal’s SINR at LUs above a certain threshold. The ISAC transmitter needs to 
focus its power toward direc -
tions that contain targets and 
ensure that the target echo at the receiver has good enough 
signal-to-clutter-plus-noise 
ratio (SCNR) for achieving 
certain sensing performance. 
However, as the target might 
be Eve, the angle of the 
sensing beam that enters the 
SCNR objective is the same as 
the angle of Eve
FIGURE 2. A practical scenario, where the target’s position is roughly sensed within an uncertainty interval, and the LUs’ channels are also imperfectly known: 
a) the width of the beampattern is adaptively manipulated in different scenarios for sensing the target; b) by proactively sensing the target, a high level of 
secrecy rate is achieved.
ISAC
Uncertainty  
interva l [-, ]
Robust 
waveformWider beampattern 
for sensing
same directio n
communication 
SINReavesdropping 
SINR
(b)(a)echo with  
SCNR
Angle (degree)Beam gain (dBm )
Communication SINR threshol dSecrecy rate (bit/s/Hz )      imperfect CSI and 10o target location uncertainty
---  perfect CSI and precise target location
 ---  perfect CSI and precise target location
      imperfect CSI and 10o target location uncertainty
      statistical CSI and 10o target location uncertainty
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:06 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 5 页 ---
IEEE Communications Magazine • April 202269This equivalently improves the value of the secrecy 
rate, calculated as the achievable rate difference between the LUs and the target. Alternatively, one can maximize the secrecy rate and meanwhile ensure the echo’s SCNR at the ISAC receiver for guaranteeing the radar’s functionality. Although designing a sensing-aided secure waveform is not convex in nature, due to the fractional-structured SINR and SCNR constraints of the sensing and communication functionalities, there have been extensive optimization tools for handling these typical fractional-structured optimizations in ISAC systems. Detailed discussions can be found in [2, 5, 10]. Note that in the rare case that the target and LU are in the same direction and both have strong line-of-sight (LoS) channels, their channels are strongly correlated. In this context, ensuring security at the PHY layer is extremely challenging, where secure authentication and encryption tech-niques are still needed at the higher layers.
robust  secure  IsAc W AveForm  desIgn
In practice, a target’s position may not always be perfectly obtained due to sensing error and finite detection resolution. For example, given N antennas arranged in uniform linear structure with half-wavelength spacing, the angular resolution is approximately calculated as 2/N  (in rad) [10], 
which means the targets within that angular inter -
val cannot be detected individually. When the tar-get’s position can only be roughly sensed within an angular region, a wider beam needs to be for-mulated toward that region to avoid missing the target. However, focusing the beam to a region of space inevitably leads to an increased possibili-ty of information leakage, giving rise to a need for robust secure waveform design.
When the target is only known to be locat -
ed within a certain angular region of space, the robust secure waveform can be obtained by min-imizing the sum of the target’s reception SINR at the possible locations in this angular interval. In this way, the achievable rate of the target can be upper-bounded, thus guaranteeing information security. On the other hand, when the LUs’ chan-nels are also not perfectly known by the ISAC transmitter, the channel estimation error can be generally formulated using bounded or unbound-ed error models [13]. With the bounded or unbounded error model, ensuring the LUs’ SINR can be further transformed into deterministic or probabilistic constraints, which can be handled readily by a series of established stochastic optimi-zation tools [13].
Let us consider the scenario shown in Fig. 2, 
where the possible angular interval of the target is [–5°, 5°], while the channel estimation error of the LUs follows Gaussian distribution with vari-ance 0.05. There are 4 LUs, and their SINR thresh-old is 40 dB. The power budget is 20 dBm. The objective of the secure waveform optimization is to suppress the targets’ reception SINR, subject to per-LU’s SINR requirement, while ensuring that the resulting waveform approximates the desired sensing beampattern. As observed in Fig. 2a, a narrow beampattern is obtained when the tar-get location is accurately sensed. Leveraging the proactively obtained location, the transmitter is able to manipulate the dissipated waveform to suppress the eavesdropping SINR of the target, thereby improving the secrecy rate in Fig. 2b. When the target’s location can only be imperfect-ly sensed, a wider beampattern is formed, direct-ing the same power over the possible region, with reduced power gain of the main beam. Neverthe-less, by suppressing the sum of the target’s SINR at the possible locations in the angular interval, a high level of secrecy rate is achieved, even if the ISAC transmitter only knows the statistics of the LUs’ channels.
hArdWAre  eFFIcIent  secure  IsAc d esIgn
At millimeter-wave band, a candidate frequency for 5G/6G systems, low-cost and power-consum-ing hardware is preferred. However, the hard -
ware limitations may jeopardize the sensing and communication performance, and importantly, the security of the transmission. A recent abun-dance of hardware-efficient techniques that have been developed for communication-only systems can be leveraged to design hardware-informed secure ISAC transmission [14]. On the feasibili -
ty of secure waveform with high hardware effi-ciency, one approach is to reduce the RF chains through analog architectures that involve phase shifters (PSs) and/or switchers, as illustrated in Fig. 1. This hybrid ISAC involves low-dimensional digital beamforming and high-dimensional analog beamforming. However, in both fully digital and hybrid ISAC, the required number of RF chains is no smaller than the total number of data streams for multi-user communications.
To remove the expensive and power-consum -
ing digital-to-analog converters (DACs), a more hardware-efficient secure ISAC technique, built on the concept of directional modulation (DM), is emerging, where parasitic antennas are used as main components in the transmitter. Aided by the LU’s channel state information (CSI), symbol modulation happens at the antenna level instead of the baseband level, and the received beam pattern at the LUs is treated as a spatial complex constellation point. In particular, the constructed signal of the LUs does not necessarily align with FIGURE 3. The DM uses power amplifiers (PAs)and parasitic antennas as main components, while CDA uses modula-
tors, linear combiners, and PAs as main components.
LOPower 
Divide r...
PAparasitic 
antennas
DM signa l genera tion
LU 
CI-aided 
QPSK
Modu-
lator
Modu-
lator
Com-
biner
... ...
PA
LO..
..2
QPSK2
QPSK1
LU 
1.. .... .... .... ..
.. .... .... .... .... .... ..
Eve
CDA signa l genera tion16QAM
..........................
..
Eve
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:06 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 6 页 ---
IEEE Communications Magazine • April 202270the desired symbols, but can be pushed away 
from the detection thresholds of demodulation, built on the concept of constructive interference (CI) regions [13]. An example is illustrated in Fig. 3 for quadrature phase shift keying (QPSK). Since the decision thresholds for QPSK are the real and imaginary axes, the constructed symbols (denot-ed by blue dots) at the LUs can be judiciously pushed away from both the real and imaginary axes, where the resultant increased distance with respect to the detection threshold benefits the LUs’ communication quality. In a similar vein, the symbols can be constructed for the LUs with high-er-order modulations. On the other hand, with the proactively obtained Eve’s information, one can intentionally locate Eve’s received symbols (denot-ed by red stars) into destructive regions of the signal demodulation, which further impedes Eve’s intercepting behavior at a symbol level.
Another hardware-efficient architecture, name-
ly constellation decomposition array (CDA), also has high potential for securing ISAC. A simplified block of the CDA is shown in Fig. 3, including the local oscillators, modulators, linear combiners, and PAs, but costly DACs are completely avoided [14]. Evidently, high-order quadrature amplitude modulation (QAM) can be treated as a vectorial combination of several low-order QAM/PSK sig-nals. For example, a 16-QAM signal can be seen as a combination of QPSK
1 and QPSK2 signals, 
where the superscript denotes the normalized Euclidean distance between two adjacent sym-bols. By properly controlling the array with the LU’s CSI, an LU can see a correct combination of the intended signal, while any Eve (including the sensing target) located at a different angle will obtain a distorted signal in demodulation. Also, since the CDA transmits low-order modulation signals with a low level of peak-to-power ratio, the stringent linearity requirement of the PAs is properly relaxed.
open chAllenges  And Future  Work
ISAC-relevant design is still broadly open, and the remaining challenges can benefit from the com-munications literature.
rAdAr  locAtIon  And IdentIty  prIvAcy -preservIng  desIgn
On the evolution road of ISAC design, the CRSS system still has its market. Designed to con -
trol mutual interference, there are parameters transformed to one system that contain implic-it information about the other. This raises priva-cy concerns for the two systems, and especially for military radar. Recent research has unveiled some machine-learning-based schemes, which exploit the information contained in the precoder to infer the radar’s location [15]. As a result, how to exchange parameters between radar and com-munication units without loss of each other’s pri-vacy while maintaining a minimum level of mutual interference remains an open challenge.
secure  IsAc d esIgn  For 5g/6g kpI s
In recent years, ultra-reliable low-latency and mas-sive device communications have received much attention in 5G/6G applications. Those applica-tions involve new metrics and protocols, including latency, reliability, grant-free massive access, short packets, and so on. Rethinking secure ISAC tech-niques to align with these stringent requirements, and also to maintain a low level of complexity and overhead is a fertile area of research.
on compAtIbIlIty  oF secure  IsAc And 5g nr
5G New Radio (NR) has standardized a series of waveforms, including but not limited to fil -
ter-OFDM (orthogonal frequency-division multi-plex), DFTS-OFDM, and FBMC-QAM. Also, 5G NR has also proposed adaptive wireless interface con-figuration, such as changeable frame structure and adaptive 15-120 kHz carrier spacing. With different communication environments and specific perfor-mance requirements, how do we leverage the flex-ible waveform specification and wireless interface configuration? Essential work is needed to bridge the gap between theory and implementation.
netWork  level IsAc d esIgn  And secure  perFormAnce  AnAlysIs
Networking design has been investigated for cel-lular communication systems, where coverage probability and ergodic capacity are analyzed in a systematic manner. This network-level investiga-tion advises networking planning and engineering design with an eye to the interests of the whole system. While the existing ISAC-related research is investigated in simple scenarios, considering the heterogeneity and high node density in future communication systems, the systematic ISAC designs need fundamental research.
IsAc For neW securIty  metrIcs
Apart from data confidentiality, the concept of security has been greatly generalized in 5G/6G communications, such as covertness and privacy. In some scenarios, users want to communicate with others covertly, referred to as low-probabili-ty-of-detection communication. Coordinated with sensing, it becomes easier to detect an intruding adversary’s information, which is then exploited to design a covert waveform to hide an ongoing com-munication. On the other hand, sensing may be leveraged by an adversary to violate users’ privacy, such as sensing pedestrians’ non-shared positions and trajectories, imaging users’ indoor activities. Hence, it is demanding to rethink the role of ISAC from the perspective of new security metrics.
conclusIons
This article discusses the exciting intersection of ISAC and security. Starting from the fundamentals of the ISAC, we first introduce the methodology of the waveform design for joint sensing and com-munication. Then we examine the sensing-aided secure ISAC techniques to prevent the confiden-tial signal embedded in the probing waveform from being eavesdropped on by the sensing target. Finally, the recent interest in robust and hardware-efficient secure ISAC is reviewed. This family of sensing-aided secure ISAC design offers a broad field of preserving information security in a proactive manner, which holds the promise of exciting research in the years to come.
AcknoWledgments
Z. Wei would like to acknowledge the financial support of the NSFC under Grant 62101384, as well as of the Chongqing Key Laboratory of Mobile Communication Technology under Grant cqupt-mct-202101. C. Masouros would like to This family of sensing-aided 
secure ISAC design offers 
a broad field of preserving 
information security in a 
proactive manner, which 
holds the promise of excit -
ing research in the years 
to come.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:06 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 7 页 ---
IEEE Communications Magazine • April 202271acknowledge the financial support of the EPSRC 
under Grant EP/R007934/1. A. P. Petropulu would like to acknowledge the financial support of the ARO under Grant W911NF2110071. Fan Liu would like to acknowledge the financial sup-port of NSFC under Grant 62101234. 
reFerences  
[1] Y. Cui et al., “Integrating Radio Sensing and Communications 
for Ubiquitous IoT: Applications, Trends and Challenges,” 
IEEE Network, vol. 35, no. 5, Sept./Oct. 2021, pp. 158–67. 
[2] N. Su, F. Liu, and C. Masouros, “Secure Radar-Communi-
cation Systems With Malicious Targets: Integrating Radar, Communications and Jamming Functionalities,” IEEE Trans. Wireless Commun., vol. 20, no. 1, Jan. 2021, pp. 83–95. 
[3] M. Bloch et al., “An Overview of Information-Theoretic Secu-
rity and Privacy: Metrics, Limits and Applications,” IEEE J. Sel. Topics Info. Theory, vol. 2, no. 1, Mar. 2021, pp. 5–22. 
[4] M. Tahmasbi, M. Bloch, and A. Yener, “Learning an Adver-
sary’s Actions for Secret Communication,” IEEE Trans. Info. Theory, vol. 66, no. 3, Mar. 2020, pp. 1607–24. 
[5] B. Li, A. P. Petropulu, W. Trappe, “Optimum Co-Design 
for Spectrum Sharing between Matrix Completion Based MIMO Radars and a MIMO Communication System,” IEEE Trans. Sig. Process., vol. 64, no. 7, July 2016, pp. 4562–75. 
[6] T. Huang et al., “MAJoRCom: A Dual-Function Radar Com-
munication System Using Index Modulation,” IEEE Trans. Signal Process., vol. 68, no. 5, May 2020, pp. 3423–38. 
[7] A. Hassanien et al., “Dual-Function Radar Communications: 
Information Embedding Using Sidelobe Control and Wave-form Diversity,” IEEE Trans. Signal Process., vol. 64, no. 8, Apr. 2016, pp. 2168–81. 
[8] P. Kumari, N. Myers, and R. W. Heath, “Adaptive and Fast 
Combined Waveform Beamforming Design for mmWave Automotive Joint Communication-Radar,” IEEE J. Sel. Topics Sig. Process., vol. 15, no. 4, June 2021, pp. 996–1012. 
[9] P. Kumari et al., “IEEE 802.11ad-Based Radar: An Approach 
to Joint Vehicular Communication-Radar System,” IEEE Trans. Vehic. Tech., vol. 67, no. 4, Apr. 2018, pp. 3012–27. 
[10] F. Liu et al., “Toward Dual-Functional Radar-Communica-
tion Systems: Optimal Waveform Design,” IEEE Trans. Signal Process., vol. 66, no. 16, Aug. 2018, pp. 4264–79. 
[11] M. Kobayashi, G. Caire, and G. Kramer, “Joint State Sens-
ing and Communication: Optimal Tradeoff for a Memo-ry-Less Case,” Proc. IEEE ISIT ’18, Vail, CO, pp. 111–15. 
[12] W. Zhang et al., “Joint Transmission and State Estimation: 
A Constrained Channel Coding Approach,” IEEE Trans. Info. Theory, vol. 57, no. 10, Oct. 2011, pp. 7084–95. 
[13] Z. Wei et al., “Multi-Cell Interference Exploitation: Enhanc-
ing the Power Efficiency in Cell Coordination” IEEE Trans. Wireless Commun., vol. 19, no. 1, Jan. 2020, pp. 547–62. 
[14] N. S. Mannem et al., “A mm-Wave Transmitter MIMO with 
Constellation Decomposition Array for Key-Less Physical Secured High-Throughput Links,” Proc. IEEE RFIC ’21 , Den-
ver, CO, pp. 199–202. 
[15] A. Dimas et al., “On Radar Privacy in Shared Spectrum Sce-
narios,” Proc. IEEE ICASSP ’19, Brighton, U.K.bIogrAphIes
Zhongxiang  Wei is an associate professor of Electronic and 
Information Engineering at Tongji University. He received his Ph.D. from the University of Liverpool (2017). He was a postdoc researcher at University College London (UCL) (2018–2021), United Kingdom, and was a research assistant at A*STAR Singa-pore (2016–2017). He has served as a TPC Chair/member of various international flagship conferences. He was a recipient of an Exemplary Reviewer of IEEE Transactions on Wireless Commu-nications , an Outstanding Self-Financed Students Abroad award 
in 2018, and the A*STAR Research Attachment Programme in 2016. His interests include MIMO systems, PHY security, and anonymous communication designs. 
Fan Liu is an assistant professor in the Department of Electronic 
and Electrical Engineering, Southern University of Science and Technology. He received his Ph.D. and BEng. degrees from Bei-jing Institute of Technology, China, in 2018 and 2013, respec-tively. He was a Marie Curie Research Fellow at UCL from 2018 to 2020. He is an Associate Editor of IEEE OJSP and IEEE Communications Letters, and a Guest Editor of the IEEE Journal on Selected Areas in Communications and IEEE Wireless Com-munications. He is also the Founding Academic Chair of the IEEE ComSoc ISAC Emerging Technology Initiative. He was the recipient of the 2021 IEEE SPS Young Author Best Paper Award, and the 2019 Chinese Institute of Electronics Best Ph.D. Thesis Award. His research interests include ISAC, vehicular networks, and mmWave communications. 
Christos  Masouros  is a professor of electrical and electronic 
engineering at UCL. He received his Ph.D. from the University of Manchester, United Kingdom (2009). His interests include wireless communications and signal processing with specialty in large-scale antenna systems and interference exploitation. He held a Royal Academy of Engineering Research Fellowship (2011–2016). He is co-author of the 2021 IEEE SPS Young Author Best Paper Award (F. Liu). He is an Editor and Guest Editor of IEEE Transactions on Wireless Ccommunications, IEEE 
Transactions on Communications, JSTSP, and the IEEE Journal on 
Selected Areas in Communications. He is Vice-Chair of the IEEE ComSoc ISAC Emerging Technology Initiative.
nanChi su [S’18] received her B.E. and M.E. degrees from Harbin 
Institute of Technology, China, in 2015 and 2018, respectively. She is currently pursuing a Ph.D. degree at UCL. Her research interests include CI design, PHY security, and ISAC signal processing. 
athina  P. P etro PuLu [F] is a Distinguished Professor of Electrical 
and Computer Engineering at Rutgers University. Her interests include radar signal processing and PHY security. She received the Presidential Faculty Fellow Award (1995) from NSF and the U.S. White House, and the 2012 IEEE Signal Processing Society (SPS) Meritorious Service Award. She is an AAAS Fellow. She is co-author of the 2005 IEEE Signal Processing Magazine Best Paper Award, the 2020 IEEE Signal Processing Society Young Author Best 
Paper Award (B. Li), the 2021 IEEE Signal Processing Society Young Author Best Paper Award (F. Liu), and the 2021 Aerospace and Electronic Systems Society Barry Carlton Best Paper Award. She is currently President-Elect of the IEEE Signal Processing Society. 
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:06 UTC from IEEE Xplore.  Restrictions apply. 

================================================================================


================================================================================
文件: 感知辅助安全.pdf
页数: 13
================================================================================


--- 第 1 页 ---
3162 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 23, NO. 4, APRIL 2024
Sensing-Assisted Eavesdropper Estimation: An
ISAC Breakthrough in Physical Layer Security
Nanchi Su
 ,Graduate Student Member, IEEE , Fan Liu
 ,Member, IEEE ,
and Christos Masouros
 ,Senior Member, IEEE
Abstract — In this paper, we investigate the sensing-aided
physical layer security (PLS) towards Integrated Sensing and
Communication (ISAC) systems. A well-known limitation of PLS
is the need to have information about potential eavesdroppers
(Eves). The sensing functionality of ISAC offers an enabling role
here, by estimating the directions of potential Eves to inform
PLS. In our approach, the ISAC base station (BS) firstly emits an
omnidirectional waveform to search for potential Eves’ directions
by employing the combined Capon and approximate maximum
likelihood (CAML) technique. Using the resulting information
about potential Eves, we formulate secrecy rate expressions,
which is a function of the Eves’ estimation accuracy. We then
formulate a weighted optimization problem to simultaneously
maximize the secrecy rate with the aid of the artificial noise (AN),
and minimize the Cramér-Rao Bound (CRB) of targets’/Eves’
estimation. By taking the possible estimation errors into account,
we enforce a beampattern constraint with a wide main beam
covering all possible directions of Eves. This implicates that
security needs to be enforced in all these directions. By improving
estimation accuracy, the sensing and security functionalities
provide mutual benefits, resulting in improvement of the mutual
performances with every iteration of the optimization, until
convergence. Our results avail of these mutual benefits and reveal
the usefulness of sensing as an enabler for practical PLS.
Index Terms — Integrated sensing and communication system,
sensing aided physical layer security, Cramér-Rao bound, secrecy
rate, artificial noise.
Manuscript received 15 October 2022; revised 28 March 2023 and 15 June
2023; accepted 7 August 2023. Date of publication 23 August 2023; date
of current version 11 April 2024. This work was supported in part by
the Engineering and Physical Sciences Research Council (EPSRC) under
Grant EP/S028455/1; in part by the National Natural Science Foundation
of China under Grant 62101234, Grant U20B2039, Grant 61831008, and
Grant 62027802; in part by the Young Elite Scientist Sponsorship Program
by the China Association for Science and Technology (CAST) under Grant
YESS20210055; and in part by the China Scholarship Council (CSC). The
associate editor coordinating the review of this article and approving it for
publication was M. C. Gursoy. (Corresponding author: Fan Liu.)
Nanchi Su is with the Guangdong Provincial Key Laboratory of Aerospace
Communication and Networking Technology, Harbin Institute of Technology
(Shenzhen), Shenzhen 518055, China, also with the Department of Electronic
and Electrical Engineering, Southern University of Science and Technology,
Shenzhen 518055, China, and also with the Department of Electronic and
Electrical Engineering, University College London, WC1E 7JE London, U.K.
(e-mail: <EMAIL>).
Fan Liu is with the Department of Electrical and Electronic Engineering,
Southern University of Science and Technology, Shenzhen 518055, China
(e-mail: <EMAIL>).
Christos Masouros is with the Department of Electronic and Electrical
Engineering, University College London, WC1E 7JE London, U.K. (e-mail:
<EMAIL>).
Color versions of one or more figures in this article are available at
https://doi.org/10.1109/TWC.2023.3306029.
Digital Object Identifier 10.1109/TWC.2023.3306029I. I NTRODUCTION
A. Background and Motivation
AS THE 5G wireless networks are being rolled-out world-
wide, emerging applications, such as connected cars,
smart factories, and digital twins, highlight the limitations
of existing network infrastructures [1]. These applications
demand both increasingly high-quality communication as well
as high accuracy and robustness of sensing, it is well-
recognized that the cooperation and co-design between com-
munication and radar systems will play a significant role in
the upcoming beyond 5G (B5G) and 6G eras.
At the early stage of the radar-communication (RadCom)
system studies, the two systems were conceived to spectrally
coexist with each other, thus easing the severe competition
over the scarce spectrum resources [2], [3]. In the forth-
coming B5G/6G eras, radio sensing and communications
(S&C) are both evolving towards higher frequency bands and
large-scale antenna arrays, which leads to striking similarities
between S&C systems in terms of hardware architecture, chan-
nel characteristics, and information processing pipeline [4].
In light of this, the research on the coexistence of radar
and communication systems has involved into dual-functional
radar communication (DFRC) systems. The joint design of
the S&C operations, in the form of Integrated Sensing and
Communications (ISAC), have been initially proposed in [5].
ISAC systems are expected to achieve higher spectral and
energy efficiencies, but most importantly, promote a new
paradigm of integration for attaining mutual benefits from a
co-design perspective, wherein the S&C functionalities can
mutually assist each other. Benefiting from these two advan-
tages, applications of ISAC have been extended to numerous
emerging areas, including smart manufacturing, environmental
monitoring, vehicular networks, as well as indoor services such
as human activity recognition.
With the evolution of cellular networks, the security in
mmWave ISAC systems is facing with great challenges
because of the shared use of the spectrum and the broad-
casting nature of wireless transmission [6]. On one hand, the
Rician channels are widely employed in mmWave frequencies,
containing the line of sight (LoS) component, which results
in an inescapable correlation with the sensing channel. This
is different from conventional physical layer security (PLS)
studies in communication systems with the independent and
identically distributed assumption between legitimate user
© 2023 The Authors. This work is licensed under a Creative Commons Attribution 4.0 License.
For more information, see https://creativecommons.org/licenses/by/4.0/

--- 第 2 页 ---
SU et al.: SENSING-ASSISTED EA VESDROPPER ESTIMATION: AN ISAC BREAKTHROUGH IN PLS 3163
channels and intercept channels [7], [8], [9]. On the other hand,
in dual-functional waveform design, the confidential informa-
tion intended for communication users (CUs) is embedded
in radar probing signals. This makes it susceptible to being
eavesdropped by the target of interest. In this case, a unique
and interesting conflict arises from the radar functionality side.
To be specific, the power is expected to be focused towards
targets of interest to improve detectability, while the useful
signal information has to be protected from being intercepted
by the targets, which are acknowledged as Eves, as each of
them is reckoned as a potential eavesdropper (Eve).
To secure confidential information in ISAC systems, exist-
ing approaches can be generally divided into the following
categories, i.e., 1) Cryptography and 2) PLS. Conventionally,
the security of communication systems is regarded as an
independent feature and addressed at the upper layers of the
protocol stack by deploying cryptographic technologies. The
studies of cryptography commonly assume that the physical
layer provides an error-free link [10], while the wireless links
are vulnerable to attacks in practice, which would result in a
high risk of information leakage. It is worth pointing out that
5G has already been a large-scale heterogeneous network with
multiple levels and weakly-structured architectures, which
makes it difficult to distribute and manage secret keys [11].
Also, complicated encryption/decryption algorithms cannot be
straightforwardly applied considering the power consumption
in 5G networks. Furthermore, even if the data is encrypted,
the detection of a wireless link from a potential eavesdrop-
per can reveal critical information. In contrast to complex
cryptographic approaches, signal processing operations of PLS
are usually simple with little additional overheads. A major
limitation of PLS is the need to obtain some information
for the potential Eves. This ranges from full CSI, to an
SNR estimate of Eve’s link, or Eve’s direction as a mini-
mum. This difficult-to-obtain information often renders PLS
impractical.
To our best knowledge of existing literature, ISAC security
has been studied in more complex scenarios in recent years.
To be specific, the PLS was concerned with the non-orthogonal
multiple access (NOMA)-ISAC system by maximizing the
sum secrecy rate for multiple users via artificial jamming,
where the superimposed signal for NOMA users can be
concurrently employed for target detection [12]. Moreover,
Reconfigurable Intelligent Surfaces (RIS) have been applied
to enhance ISAC security [13], [14], [15]. In [14], the authors
deployed an active RIS and designed the optimization problem
to maximize the achievable secrecy rate of the system by
jointly designing the radar receive beamformers, the active RIS
reflection coefficients matrix, and the transmit beamformers.
This work proved that the deployment of active RIS improves
the secrecy performance compared with the passive RIS or no-
RIS case. Also, the aerial eavesdroppers (AE) were considered
in [16], where the ISAC BS emitted waveforms to track and
jam the AE, which achieved a higher secrecy rate and better
fairness performance. The optimization problem was formu-
lated to jointly design radar signal and receiver beamformer
for improving the secrecy performance based on the tracking
information.More relevant to this work, security in ISAC systems was
initially studied in [17], where MIMO radar transmits two
different signals, carrying desired information and false infor-
mation, respectively, both of which are employed for sensing.
Optimization problems were designed to maximize the secrecy
rate for safeguarding communication data. As studied in [18]
and [19], the dual-functional base station (BS) detects targets
and transmits information to CUs simultaneously, where each
of the targets is regarded as a potential eavesdropper. In this
scenario, the artificial noise (AN)-aided secure beamforming
design enables the secure information transmission from the
BS to CUs in ISAC systems. Specifically, AN is generated
at the transmitter side to deteriorate the received signal at
each target/Eve, thus the decoding capability of which is
destructed. To avoid the redundant power consumption caused
by the added AN, the research in [20] proposed a symbol-level
precoding algorithm to exploit constructive interference (CI)
to aid detection from the legitimate users, and destructive
interference (DI) to inhibit detection from the target/Eve. More
recently, the encryption keys mechanism has been applied in
PLS, where the filter band-based PLS algorithm was proposed
to enable key generation by decomposing the received signal in
parallel sub-bands, namely chirp modulation [21]. This method
secured ISAC systems by improving the secret key generation
rate efficiently, which however depends on the radio channel
characteristics. Additionally, the information-theoretic study
in [22] considered mitigating information leakage between
sensing and communication operations in the ISAC system,
where the inner and outer bounds for the secrecy-distortion
region were derived under the assumption of perfect and partial
output feedback.
B. Contributions
We note that in the above works on secure ISAC transmis-
sion, the radar and communication systems work individually
over separate end-goals rather than cooperating with each
other. To further promote the integration of S&C functionali-
ties to improve the security of the ISAC systems, we propose
a novel approach to ensure the PLS for communication data
transmission, which is assisted by the sensing functionality.
At the first stage, the dual-functional access point (AP) emits
an omnidirectional waveform for Eve detection, which then
receives echoes reflected from both CUs and Eves located
within the sensing range. Suppose that all CUs are cooperative
users. That is, the location information of each is acknowl-
edged to the AP. Thus, it is possible to obtain angle estimates
of Eves contained in the reflected echo by removing known
CUs’ angles. The estimation performance is measured by the
Cramér-Rao Bound (CRB) [23].
In the next stage, we formulate a weighted optimization
problem to minimize the CRB of targets/Eves and maximize
the secrecy rate, subject to beampattern constraints as well as
a transmit power budget. A key novelty in this setup is that the
channel information in the secrecy rates, is a function of the
sensing performance. Specifically, to avoid any false dismissal
detection, the main lobe of the beampattern is designed to
be wide, with a width depending on the estimation accuracy.

--- 第 3 页 ---
3164 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 23, NO. 4, APRIL 2024
Afterwards, by improving estimation accuracy, the sensing and
security functionalities provide mutual benefits, resulting in
improvement of the mutual performances with every iteration
of the optimization, until convergence.
Within this scope, the contributions of our work are sum-
marized as follows :
•We present a sensing-assisted PLS algorithm for the ISAC
systems, where the sensing and secrecy performance are
measured by the CRB and the secrecy rate, respectively.
In particular, we first perform target detection via emit-
ting an omnidirectional waveform. Then, we formulate
a beamforming design problem that jointly improves the
sensing accuracy and communication secrecy rate.
•We analyze the lower bound of CRB and the upper bound
of the secrecy rate in our proposed ISAC system.
•We propose an alternative optimization algorithm that
iteratively maximizes the determinant of the Fisher Infor-
mation Matrix (FIM) and the secrecy rate with the aid
of the AN. Specifically, the secrecy rate is updated with
improved accuracy of the Eves’ angle estimation.
•To improve the robustness of the proposed method,
we further take into account the uncertainty of Eve’s
location. In such cases, the main beam of the sensing
beampattern is designed to be sufficiently wide to cover
the possible angular region where an Eve may appear
with high probability. This region is indicated by the CRB
value obtained from the previous iteration.
•We design a fractional programming (FP) algorithm to
solve the proposed weighted optimization problem and
verify the efficiency of the solver for both single-Eve and
multi-Eve detection.
C. Organization
This paper is organized as follows. Section II gives the
system model. Benchmark schemes including AN design
techniques with unknown and statistically known Eve channel
information are given in Section III. Section IV presents
the approach to estimating Eves’ parameters. Bounds for the
metrics CRB and secrecy rate are given in Section V and
the weighted optimization problem is accordingly designed for
Eves’ parameters estimation and communication data security
in Section VI. Section VII provides numerical results, and
Section VIII concludes the paper.
Notations: Unless otherwise specified, matrices are denoted
by bold uppercase letters (i.e., X), vectors are repre-
sented by bold lowercase letters (i.e., x), and scalars are
denoted by normal font (i.e., α). Subscripts indicate the
location of the entry in the matrices or vectors (i.e., si,jand
lnare the (i, j)-th and the n-th element in Sandl, respec-
tively). tr (·)andvec (·)denote the trace and the vectorization
operations. (·)T,(·)Hand(·)∗stand for transpose, Hermi-
tian transpose and the complex conjugate of the matrices,
respectively. diag (·)represents the vector formed by the diag-
onal elements of the matrices and rank (·)is rank operation.
∥·∥,∥·∥∞and∥·∥Fdenote the l2norm, infinite norm and
the Frobenius norm respectively. E{·}denotes the statistical
expectation.
Fig. 1. Architecture of the proposed secure ISAC system assisted by the
sensing functionality.
II. S YSTEM MODEL
We consider a mmWave ISAC system equipped with
co-located antennas and let NtandNrdenote the number of
transmit antennas and receive antennas, where the base station
communicates with Icommunication users (CUs) and detects
Ktargets/Eves simultaneously as depicted in Fig. 1. Note that
the targets of interest are considered to be malicious, which
intend to intercept the confidential information from the AP
to the CUs. We assume the BS has knowledge of the CUs and
their channels, and has no knowledge of the Eves.
A. Communication Signal Model and Metrics
Let the rows of X∈CNt×Ldenote the transmit waveforms,
where Lis the number of time-domain snapshots. By trans-
mitting the dual-functional waveforms to ICUs, the received
signal matrix at the receivers can be expressed as
YC=HX+ZC, (1)
where ZC∈CI×Lis the additive white Gaussian noise
(AWGN) matrix and with the variance of each entry being σ2
C.
H= [h1,h2, . . . ,hI]H∈CI×Ntrepresents the communica-
tion channel matrix, which is assumed to be known to the BS,
with each entry being independently distributed. Following the
typical mmWave channel model in [20] and [24], we assume
thathiis a slow-fading block Rician fading channel. The
channel vector of the i-th user can be expressed as
hi=rvi
1 +vihLoS
L,i+r
1
1 +vihNLoS
S,i, (2)

--- 第 4 页 ---
SU et al.: SENSING-ASSISTED EA VESDROPPER ESTIMATION: AN ISAC BREAKTHROUGH IN PLS 3165
where vi>0is the Rician K-factor of the i-th user, hLoS
L,i=√Ntat(ωi,0)is the LoS deterministic component. a(ωi,0)
denotes the array steering vector, where ωi,0∈
-π
2,π
2
is
the angle of departure (AOD) of the LoS component from the
BS to the user i[24], [25]. The scattering component hNLoS
S,i
can be expressed as hNLoS
S,i=q
Nt
LpPLp
l=1ci,lat(ωi,l), where
Lpdenotes the number of propagation paths, ci,l∼CN (0,1)
is the complex path gain and ωi,l∈
-π
2,π
2
is the AOD
associated to the (i, l)-th propagation path.
The waveform Xin (1) can be expressed as
X=WS+N, (3)
where W∈CNt×Iis the dual-functional beamforming matrix
to be designed, each row of S∈CI×Ldenotes the i-th unit-
power data stream intended to CUs, and N∈CNt×Lis the
AN matrix generated by the transmitter to interfere potential
eavesdroppers. We assume that N∼ CN (0,RN), where
RN⪰0denotes the covariance matrix of the AN that is
to be designed. We further assume that the data streams are
approximately orthogonal to each other, yielding
1
LSCSH
C≈II×I. (4)
Note that (4) is asymptotically achievable when Lis suffi-
ciently large. Then, we denote the beamforming matrix as
W= [w1, . . . ,wI], where each column wiis the beamformer
for the i-th CU. Accordingly, the SINR of the i-th user is given
as
SINRCU
i=hH
iwi2
IP
m=1,m̸=ihH
iwm2+hH
iRNhi+σ2
C
=tr
˜Hi˜Wi
IP
m=1,m̸=itr
˜Hi˜Wm
+tr
˜HiRN
+σ2
C,(5)
where we denote ˜Hi=hihH
iand˜Wi=wiwH
i.
B. Radar Signal Model
We here consider targets of interest associated with a
particular range bin. Targets in adjacent range bins contribute
as interference to the range bin of interest [26]. By emitting the
waveform Xto sense Eves, the reflected echo signal matrix
at the BS receive array is given as
YR=KX
k=1a(θk)βkbH(θk)X+ZR, (6)
where a(θ)∈CNr×1andb(θ)∈CNt×1represent the
steering vectors for the receive and transmit arrays, which
are assumed to be a uniform linear array (ULA) with
half-wavelength antenna spacing. βkis the complex amplitude
of the k-th Eve. We assume the number of antennas is even
and define the receive steering vector as
a(θ) =h
e−jNr−1
2πsinθ, e−jNr−3
2πsinθ,···, ejNr−1
2πsinθiT
.
(7)It is noted that we choose the center of the ULA antennas as
the reference point. To this end, it is easy to verify that
aH(θ)˙ a(θ) = 0 . (8)
Finally, ZRdenotes the interference and the AWGN term.
We assume that the columns of ZRare independent and
identically distributed circularly symmetric complex Gaussian
random vectors with mean zero and a covariance matrix
Q=σ2
RI.
Similar to the expression in (5), the eavesdropping SINR
received at the k-th Eve regarding the i-th CU is written as
SINRE
k,i=|αk|2bH(θk)˜Wib(θk)
|αk|2bH(θk)
IP
¯m=1,
¯m̸=i˜W¯m+RN
b(θk) +σ2
0,
(9)
where αkdenotes the complex path-loss coefficient of the k-th
target and σ2
0denotes the covariance of AWGN received by
each Eve.
For simplicity, the reflected echo signal given in (6) can be
recast as
Y=A(θ)ΛBH(θ)X+ZR, (10)
where we denote A(θ) = [ a(θ1), . . . ,a(θK)],B(θ) =
[b(θ1), . . . ,b(θK)], andΛ=diag(βk).
C. CRB and Secrecy Rate
In this subsection, we elaborate on the radar detection and
communication security metrics. Particularly, the target/Eve
estimation is measured by the CRB, which is a lower bound
on the variance of unbiased estimators [27], and the security
performance is evaluated by the secrecy rate.
In the multi-Eve detection scenario, the CRB with respect
to the unknown Eve parameters θ1, . . . , θ Kandβ1, . . . , β K
was derived in [28] in detail, and the FIM for θk,∀kas well
as real and imaginary parts of βk,∀kis given as
J= 2L
Re (J11) Re ( J12)−Im (J12)
ReT(J12) Re ( J22)−Im (J22)
−ImT(J12)−ImT(J22) Re ( J22)
,(11)
where the elements of the matrix in (11) are given
in (12), shown at the bottom of the next page, with
⊙denoting the Hadamard (element-wise) matrix prod-
uct, and ˙A =h
∂a(θ1)
∂θ1∂a(θ2)
∂θ2. . .∂a(θK)
∂θKi
,˙B =h
∂b(θ1)
∂θ1∂b(θ2)
∂θ2. . .∂b(θK)
∂θKi
. Also, the covariance matrix RX
is given as
RX=1
LXXH=WWH+RN
=IX
i=1˜Wi+RN. (13)
As per the above, the corresponding CRB matrix is expressed
as
CRB (θ,β) =J−1(14)

--- 第 5 页 ---
3166 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 23, NO. 4, APRIL 2024
and
CRB (θ) =
J−1
11
CRB (β) =
J−1
22+
J−1
33. (15)
Moreover, the achievable secrecy rate at the legitimate user
is defined as the difference between the achievable rates at the
legitimate receivers and the eavesdroppers. Thus, we give the
expression of the worst-case secrecy rate as [19] and [29]
SR
˜Wi,RN
= min
i,k
RCU
i−RE
k,i+, (16)
where RCU
i,∀iandRE
k,∀krepresent the achievable trans-
mission rate of the i-th CU and the k-th Eve, which can be
expressed as (17a) and (17b), respectively.
RCU
i
˜Wi,RN
= log 
1 +SINRCU
i
(17a)
RE
k,i
˜Wi,RN
= log 
1 +SINRE
k,i
. (17b)
III. B ENCHMARK SCHEMES : ISOTROPIC AN-A IDED
SECURE BEAMFORMING AND EVE-AWARD AN D ESIGN
In the scenario considered with no knowledge of the Eves,
a typical method to avoid the information inception is to
transmit AN. To be specific, partial transmit power is allocated
to emit the AN to interfere with the Eves, where the AN
is isotropically distributed on the orthogonal complement
subspace of CUs’ channels [30]. To elaborate on this, we firstly
take the l-th snapshot as a reference, i.e., (1) is simplified as
yC[l] =Hx[l] +zC[l]. (18)
where x[l] =Ws[l]+n[l]. For simplicity, the snapshot index
lwill be omitted in the following descriptions. We further
rewrite the AN vector nas
n=V¯ n, (19)
where V=P⊥
H=INt−HH
HHH−1Hdenotes the
orthogonal complement projector of the H, and ¯ nis the
zero-mean colored noise vector with a covariance matrix
R¯n=E
¯ n¯ nH	
[31], [32]. Accordingly, the covariance
matrix is given as
¯Rx=IX
i=1˜Wi+VR ¯nVH. (20)
Then, the received signal vector of legitimate CUs is written
as
yC=HWs +zC. (21)It is noted that the AN does not interfere with the CUs’
channels and the SINR of the i-th user is given as
SINRCU
i=tr
˜Hi˜Wi
IP
m=1,m̸=itr
˜Hi˜Wm
+σ2
C. (22)
Likewise, the eavesdropping SINR of the k-th Eve on the i-th
CU is given as
SINRE
k,i=E
gH
kwis	
E

gH
kIP
˜m=1,
˜m̸=iw˜ms

+E
gH
kn	
+σ2
0
=gH
k˜Wigk
gH
kIP
˜m=1,
˜m̸=i˜W˜mgk+gH
kVR ¯nVHgk+σ2
0
=tr
Gk˜Wi
tr
GkIP
˜m=1,
˜m̸=i˜W˜m
+tr(GkVR ¯nVH) +σ2
0,
(23)
where gkdenotes the channel from the transmitter to the k-
th Eve. Note that the covariance matrix of the colored noise
vector, i.e., R¯n, is set as the identity matrix when Eves’
channels are unknown to the ISAC BS.
A. AN Refinement Based on Eves’ Information
The AN design could be further refined if more informa-
tion about Eve’s channels gkis known to the BS. In this
case, we assume that the instantaneous channel realizations
of Eves are known to the transmitter, which is defined as
Gk=E
gkgH
k	
=¯ gk¯ gH
k+σ2
G,kINt, where ¯ gkandσ2
G,kINt
denote the mean and covariance matrix of gk, respectively.
In particular, to obtain a fair comparison with our approach
that assumes no Eves’ information, we consider the extreme
setting that Gk=σ2
g,kINt,σ2
g,k>0. Besides, we assume
thatgkandsare independent and identically distributed
(i.i.d.) [33]. To this end, the expression of the secrecy rate
can be accordingly obtained as given in Section II-C, which
is written as
SRIST= min
i,kh
log
1 +SINRCU
i
−log
1 +SINRE
k,ii+
.
(24)
J11=
˙AHQ−1˙A
⊙ 
Λ∗BHR∗
XBΛ
+
˙AHQ−1A
⊙
Λ∗BHR∗
X˙BΛ
+
AHQ−1˙A
⊙
Λ∗˙BHR∗
XBΛ
+
 
AHQ−1A
⊙
Λ∗˙BHR∗
X˙BΛ
(12a)
J12=
˙AHQ−1A
⊙ 
Λ∗BHR∗
XB
+ 
AHQ−1A
⊙
Λ∗˙BHR∗
XB
(12b)
J22= 
AHQ−1A
⊙ 
BHR∗
XB
(12c)

--- 第 6 页 ---
SU et al.: SENSING-ASSISTED EA VESDROPPER ESTIMATION: AN ISAC BREAKTHROUGH IN PLS 3167
In light of the above assumptions, the secrecy rate maximiza-
tion problem with the omnidirectional beampattern design is
given as
max
˜Wi,R¯nSRIST
s.t.¯RX=P0
NtINt
˜Wi⪰0,R¯n⪰0,∀i. (25)
Note that the non-convexity of the problem above only lies in
the objection function, while it can be regarded as a typical
secrecy rate maximization problem, which has been solved
efficiently as studied in [34] and [35]. We further apply the
eigenvalue decomposition or Gaussian randomization proce-
dure to make sure the resulting beamforming matrix ˜Wiis
rank-1. The simulation results will be given in Section VII as
benchmarks.
IV. E VES’ PARAMETERS ESTIMATION
To avoid redundancy, we briefly present the method
to estimate amplitudes and angles of Eves based on our
signal models proposed in Section II, namely the com-
bined Capon and approximate maximum likelihood (CAML)
approach [36], [37]. Specifically, Capon is initially applied to
estimate the peak directions, and then approximate maximum
likelihood (AML) is used to estimate the amplitudes of all
Eves.
We firstly give the expression of signal model Y[38], where
we let ˆθk, k= 1, . . . , K denote the estimated Eves’ directions.
Similar to the receive signal model in (6), we here have
Y=A∗
ˆθ
ˆΛBT
ˆθ
X+˜Z, (26)
where ˆΛ=diagh
β
ˆθ1
, . . . , β
ˆθKi
and˜Zdenotes the
residual term. By employing the AML algorithm, the estimate
of amplitudes can be written in a closed form given as [37]
β=1
Lh 
AHT−1A
⊙
BHˆR∗
XBi−1
·vecd 
AHT−1YXHB∗
, (27)
where vecd(·) denotes a column vector with the elements being
the diagonal of a matrix and
T=LˆR−1
LYXHB∗
BTˆRXB∗−1
BTXYH,(28)
where ˆRis the sample covariance of the observed data samples
andˆR=1
LYYH.
At the first step of the Eve parameter estimation, we design
our transmission so that the AP emits an omnidirectional
waveform, which is usually employed by the MIMO radar
for initial probing. Thus, the covariance matrix is given as
˜RX=P0
NtINt. The CRBs for angles and amplitudes of
targets can be accordingly calculated by substituting ˜RX
into (12) and (15), where we denote them as CRB 0
ˆθ
and CRB 0
ˆβ
. Assume that the probability density function
(PDF) of the angle estimated error is modeled as Gaussian
distribution, zero mean and a variance of CRB 0
ˆθ
. That
Fig. 2. Spatial spectral estimates with CAML approach, when Eves locate at
θ1=−25◦, θ2= 15◦(blue lines), and CUs locate at θ3= 40◦, θ4= 10◦
andθ5=−30◦(green lines). (a) SNR=20 dB. (b) SNR=-15 dB, where the
red dashed lines denote Eves’ real directions and amplitudes. Note that the
CUs’ information is known to the BS as they are assumed to be cooperative
receivers.
is, E est,k∼CN
0,CRB 0
ˆθk
, where E est,k denotes the
angle estimation error of the k-th Eve. As a consequence, the
probability that the real direction of the k-th Eve falls in the
range Ξ(0)
k=
ˆθk−3r
CRB 0
ˆθk
,ˆθk+ 3r
CRB 0
ˆθk
is
approximately 0.9973 [39]. Thus, the main lobe width of the
radar beampattern will be initially designed as Ξ(0), and then
it will be iteratively updated based on the optimized CRB.
For clarity, we present the spatial spectrum of the direction
of angle (DOA) estimation by deploying the CAML technique
in Fig. 2. It is assumed that two Eves are located at θ1=
−25◦, θ2= 15◦(denoted by blue lines) and three CUs locate
atθ3= 40◦, θ4= 10◦, θ5=−30◦(denoted by green lines),
with the modulus of complex amplitudes β1= 1, β2= 5, β3=
4, β4= 5 andβ5= 2, where directions of CUs are known
to the transmitter. Fig. 2(a) and Fig. 2(b) demonstrate the
CAML performance when SNR =20dB and SNR =−15dB,
respectively. It is noted that the CAML approach estimates
the DOA precisely when SNR is 20dB, while errors of the
angle estimation happen when the SNR decreases to -15 dB.
To further illustrate the performance of the CAML estimation
method, the root mean square error (RMSE) versus the SNR of
the echo signal is shown in Fig. 3 with the CRB as a baseline.
As expected, the CRB is shown as the lower bound of the
RMSE obtained by CAML estimation, in particular, the CRB
gets tight in the high-SNR regime.
V. B OUNDS FOR CRB AND SECRECY RATE
The design of a weighted optimization between the radar
CRB and the communication secrecy rate presents the chal-
lenge that the two performance metrics have different units
and potentially different magnitudes. To overcome this chal-
lenge we need to normalize them each with their respective
upper/lower bound. To obtain these bounds, in this section
we present the CRB minimization problem and the secrecy
rate maximization problem with the system power budget
constraint. Considering the further design of the weighted

--- 第 7 页 ---
3168 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 23, NO. 4, APRIL 2024
Fig. 3. Target/Eve estimation performance by applying CAML method, with
the CRB obtained by omnidirectional beampattern design as a benchmark.
objective function in the following section, the CRB minimiza-
tion problem can be approximated as the FIM determinant
maximization problem. To this end, the optimal solutions
generate the upper bounds of the FIM determinant and the
secrecy rate, both of which will be employed to normalize the
metrics in Section VI.
A. Upper-Bound of the FIM Determinant
We denote ηas the sensing parameters, thus the MSE can be
expressed as M(η)≜En
(η−ˆη) (η−ˆη)To
⪰J−1. For the
m-th parameter ηmto be estimated, it has En
∥ηm−ˆηm∥2o
≥

J−1
mm[40]. Thus, it is common to minimize the trace or
the determinant of the CRB matrix, i.e., tr 
J−1
orJ−1.
Since the CRB matrix is the inverse of the FIM matrix, the
problem of minimizingJ−1is equivalent to maximizing |J|,
which is given as [28]
max
˜Wi,RN|J| (29a)
s.t.RN⪰0,˜Wi⪰0,∀i (29b)
tr IX
i=1˜Wi+RN!
=P0, (29c)
where P0denotes the power budget of the proposed system.
It is noted that the optimization above is convex and can be
efficiently solved by CVX toolbox [41], [42]. Consequently,
by substituting the optimal ˜Wi,RNin (11), the upper-bound
of FIM determinant is obtained.
B. Secrecy Rate Bound
To derive the upper bound of the secrecy rate, we only
consider the communication security metric in this subsection.
Assuming that the CSI is perfectly known to the BS, the
secrecy rate maximization problem can be formulated as
SR⋆= max
˜Wi,RNmin
i,kSR
˜Wi,RN
(30a)
s.t.(29b),(29c). (30b)It is noted that the non-convexity lies in the objective function
of (30), which makes the optimization problem above difficult
to solve. To resolve this issue, we introduce an auxiliary
variable b, where (30) has the same optimal solutions as the
reformulation below
SR⋆= max
˜Wi,RN,bmin
i,kh
RCU
i
˜Wi,RN
−logbi
s.t.
log
1 +|αk|2bH(θk)˜Wib(θk)
|αk|2bH(θk)
IP
¯m=1,
¯m̸=i˜W¯m+RN
b(θk) + 1

≤logb,∀k, i
(29b),(29c). (31)
The above problem can be simply relaxed into a convex SDP
problem. For brevity, we refer readers to [35] for more details.
VI. W EIGHTED OPTIMIZATION FOR EVES’ ESTIMATION
AND SECURE COMMUNICATION
In this section, we propose a normalized weighted optimiza-
tion problem that reveals the performance tradeoff between
the communication security and Eve parameters estimation.
Additionally, recall that the ISAC access point firstly emits
an omnidirectional beampattern as given in Section IV, where
imprecise angles of Eves have been obtained at the given SNR,
with the angular uncertainty interval of the k-th Eve is denoted
asΞ(0)
k. To reduce angle estimation errors, we also take the
wide main beam design into account, which covers all possible
directions of Eves.
A. Problem Formulation
To achieve the desired tradeoff between the communication
data security and the radar estimation CRB, while taking the
estimation errors of Eves’ angles and the system power budget
into account, we formulate the weighted optimization problem
as follows
max
˜Wi,RNρ|J|
|J|UB+ (1−ρ)SR
SRUB(32a)
s.t.bH(ϑk,0)RXb(ϑk,0)−bH(ϑk,p)RXb(ϑk,p)≥γs,
∀ϑk,p∈card(Ψk),∀k
(32b)
bH(ϑk,n)RXb(ϑk,n)≤(1 +α)bH(ϑk,0)RXb(ϑk,0),
∀ϑk,n∈card(Ωk),∀k (32c)
bH(ϑk,n)RXb(ϑk,n)≥(1−α)bH(ϑk,0)RXb(ϑk,0),
∀ϑk,n∈card(Ωk),∀k (32d)
(29b),(29c), (32e)
where|J|UBand SR UBdenote the upper bounds of the FIM
matrix determinant and the secrecy rate which were obtained
in Section V, respectively. γsdenotes the given threshold to

--- 第 8 页 ---
SU et al.: SENSING-ASSISTED EA VESDROPPER ESTIMATION: AN ISAC BREAKTHROUGH IN PLS 3169
Algorithm 1 Iterative Optimization of the CRB and the
Secrecy Rate
Initialization: Ξ(0)
kobtained from initial target/Eve estimation
and CRB in Section IV; r= 1
1:repeat
2:Ω(r)
k= Ξ(r−1)
k,Ψ(r)
kis accordingly obtained;
3: substitute Ω(r)
kandΨ(r)
kinto problem (32);
4: repeat
5: solve problem (32) by FP algorithm;
6: until find the optimal c∈
min
i1 +P0∥hi∥2−1
,1
which generates the maximum value of the objective
function deploying the golden search;
7: the optimal variables ˜W⋆
i,R⋆
Nare obtained;
8: calculate the CRB r
ˆθ
and the secrecy rate in the r-th
iteration;
9:Ξ(r)
kcan be accordingly obtained;
10: update r=r+ 1,
11:until Convergence.
constrain the power of the sidelobe. 0≤ρ≤1denotes
the weighting factor that determines the weights for the Eve
estimation performance and the secrecy rate. αdenotes a given
scalar associated with the wide main beam fluctuation. ϑk,nis
then-th possible direction of the k-th Eve, ϑk,0is the angle
which was estimated by the algorithm proposed in Section IV.
ΩkandΦkdenote the main beam region and sidelobe
region, respectively. Note that card (·)denotes the cardinality
of(·).
Remark 1: It is important to highlight that the secrecy rate
given by (16) is a function of the estimation accuracy of
Eve’s parameters, including θkandαk. Accordingly, beyondthe tradeoff in the weighted optimization in this section, the
improvement in the sensing performance directly results in an
improvement in the secrecy performance.
B. Efficient Solver
To tackle problem (32), we firstly recast the complicated
secrecy rate term in the objective function. For simplicity,
we denote Σi=PI
m=1tr
˜Hi˜Wm
and rewrite the opti-
mization problem as (33), shown at the bottom of the page.
According to [35], the weighted optimization problem can be
recast as (34), shown at the bottom of the page, by introducing
the scalar b.
It is noted that the min operator only applies to the second
term of the objective function of problem (34). According
to the Fractional Programming (FP) algorithm [43], the opti-
mization problem can be further reformulated by replacing
the fraction term with the coefficient z, which is given
as
max
˜Wi,RN,y,zρ
|J|UB|J|+1−ρ
2SRUBz (35a)
s.t. 2yir
Σi+tr
˜HiRN
+ 1
−y2
i
b
Σi−tr
˜Hi˜Wi
+tr
˜HiRN
+ 1
≥z,
∀i(35b)
(34b),(32b),(32c),(32d)and(32e), (35c)
where ydenotes a collection of variables y =
{y1, . . . , y I}. Referring to [35], let c=1
b, where
c∈
min
i1 +P0∥hi∥2−1
,1
. Thus, problem (35)
can be rewritten as (37) (next page) by replacing bwith c,
max
˜Wi,RNρ
|J|UB|J|+1−ρ
SRUBmin
i,k,n
RCU
i
˜Wi,RN
−log
1 +|αk|2bH(ϑk,n)˜Wib(ϑk,n)
|αk|2bH(ϑk,n)
IP
¯m=1,
¯m̸=i˜W¯m+RN
b(ϑk,n) + 1

+
,
ϑk,n∈card(Ωk),∀k, i (33a)
s.t.(32b),(32c),(32d)and(32e). (33b)
max
˜Wi,RNmin
i
ρ
|J|UB|J|+1−ρ
2SRUBΣi+tr
˜HiRN
+ 1
b
Σi−tr
˜Hi˜Wi
+tr
˜HiRN
+ 1
 (34a)
s.t.|αk|2bH(ϑk,n)˜Wib(ϑk,n)
|αk|2bH(ϑk,n)
IP
¯m=1,
¯m̸=i˜W¯m+RN
b(ϑk,n) + 1≤b−1,∀ϑk,n∈card(Ωk),∀k, i (34b)
(32b),(32c),(32d)and(32e). (34c)

--- 第 9 页 ---
3170 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 23, NO. 4, APRIL 2024
Fig. 4. Beampatterns for the scenario of single Eve angle esti-
mation, where the main beam width narrows over each iteration,
ϑ1,0=−25◦, I= 3, K= 1, P0=35 dBm, SNR =−22 dB.
Fig. 5. Beampatterns for the scenario of two Eves to be estimated,
illustrating the circumstance when the main lobes overlap at the first iteration,
ϑ1,0=−25◦, ϑ2,0= 15◦, I= 3, K= 2, P0= 35 dBm, SNR =−22 dB.
and the optimal yican be found in the following closed form
yi=cr
Σi+tr
˜HiRN
+ 1
Σi−tr
˜Hi˜Wi
+tr
˜HiRN
+ 1. (36)
Note that problem (37), shown at the bottom of the next
page, can be efficiently solved by the CVX toolbox [41], [42].
Given the interval of c, the optimal variables. ˜W⋆
i,R⋆
N, z⋆can
be consequently obtained by performing a one-dimensional
search over c, such as uniform sampling or the golden
search [44]. To this end, the optimal CRB⋆and SR⋆can
be accordingly calculated. The computational complexity of
solving problem (37) at each iteration is O 
N6.5
t
according
to [45].
To further generalize the problem above and simplify the
objective function, we equivalently consider the determinant
minimization problem of PHJ−1Pby introducing the matrix
P, where Passociates with activated Eves with the dimension
ofPis3K×3. For example, when the CRB minimization
is only associated with the first Eve, the first, the (K+ 1) -th,
Fig. 6. The secrecy rate analysis versus Eve’s location uncertainty with
various power budgets, where the AN design techniques with no information
of Eves’ channels and with known Gkare denoted by dotted lines and dashed
lines, respectively. ϑ1,0=−25◦, I= 3, K= 1,SNR =−15dB.
and the (2K+ 1) -th rows are the first, second, and third rows
of the identity matrix I3×3, respectively [28]. Then, by noting
that the inequality Υ−1≥PHJ−1Pis equivalent to Υ≥
ΥPHJ−1PΥ, and based on the Schur-complement condition,
problem (32) can be recast as
max
˜Wi,RN,z,Υρ
|J|UB|Υ|+1−ρ
2SRUBz
s.t.
Υ ΥPH
PΥ J
⪰0
(36b),(36c)and(36d). (38)
Similarly, the determinant maximization problem above is
convex and readily solvable. For clarity, the above procedure
has been summarized in Algorithm 1.
VII. N UMERICAL RESULTS
In this section, we provide the numerical results to evaluate
the effectiveness of the proposed sensing-aided secure ISAC
system design. We assume that both the ISAC BS and the radar
receiver are equipped with uniform linear arrays (ULAs) with
the same number of elements with half-wavelength spacing
between adjacent antennas. In the following simulations, the
number of transmit antennas and receive antennas are set as
Nt=Nr= 10 serving I= 3 CUs, the frame length is
set as L= 64 , the noise variance of the communication
system is σ2
C= 0 dBm. We assume that the complex
path-loss coefficient is constant over the observation interval
and modeled as a complex Gaussian distributed with mean
zero and variance of ¯σ2
αk∝1
d2
k, where dkis the distance
between the BS and the k-th target [46].
Resultant beampatterns of the proposed sensing-aided ISAC
security technique are shown in Fig. 4 and Fig. 5, which
demonstrate the single-Eve (located at ϑ1,0=−20◦) scenario
and multi-Eve scenario (located at ϑ1,0=−25◦, ϑ2,0= 15◦),
respectively. Note that the Rician factor is set as vi= 0.1for
generating a Rician channel with a weak LoS component,
aiming to alleviate the impact on the radar beampattern caused

--- 第 10 页 ---
SU et al.: SENSING-ASSISTED EA VESDROPPER ESTIMATION: AN ISAC BREAKTHROUGH IN PLS 3171
Fig. 7. Convergence with iterations when SNR =−15 dB and SNR =−22 dB. I= 3, K= 1, P0= 35 dBm. (a) Convergence of root-CRB of amplitude
estimation; (b) Convergence of root-CRB of angle estimation; (c) Convergence of the secrecy rate.
by the channel correlation, and αis set as α= 0.05.
To verify the efficiency of the proposed approach, the received
SNR of the echo signal is set as SNR=-22 dB, which is
defined as SNR =|β|2LP0
σ2
R. The ISAC BS first transmits an
omnidirectional beampattern for Eve estimation, with the aid
of the CAML technique, which is denoted by green dashed
lines in Fig. 4 and Fig. 5. It is referred to as the first iteration
and the CRB can be accordingly calculated. Then, to ensure
that Eves stay within the angle range of main lobes, we design
a beampattern with a wide main beam with a beamwidth
determined by the CRB obtained from the last iteration,
which has been elaborated in Section VI. By updating the
CRB iteratively, the main lobes get narrow and point to the
directions of Eves, as illustrated by the rest of the lines in
Fig. 4 and Fig. 5. In the simulations, we repeat the weighted
optimization problem until the CRB and the secrecy rate
both convergence to a local optimum. The beampatterns also
indicate that the main beam gain grows with the main lobe
width getting narrow. Besides, Fig. 5 shows that the power
towards Eves of interest gets lower compared with the single-
Eve scenario, while it still outperforms the omnidirectional
beampattern design.
In Fig. 6, we investigate the secrecy rate versus the main
beam width with different power budget P0, and the bench-
marks are given in dashed lines and dotted lines which are
obtained by the AN design techniques with knowledge of
Gkand with no information of Eves’ channels as given in
Sec III, respectively. Generally, the secrecy rate gets higher
with the increase of the power budget and it is obvious that
the proposed algorithm outperforms benchmark methods. It isworthwhile to stress that the proposed weighted optimization
(32) is implemented with no information on Eves. Note that
the secrecy rate increases first and then decreases with the
expansion of Eve’s location uncertainty. The initial increase is
because the gain of the beam towards the target/Eve of interest
decreases with the growth of the main beam width, resulting in
the deterioration of the eavesdropping SINRE
k,i. With respect to
the expression in (16), the secrecy rate improves when SINRE
k,i
reduces. However, the power budget constraint becomes tight
when the main beam keeps being expanded. This indicates
that more power is allocated to the Eve estimation, thus, the
secrecy rate decreases. Additionally, when the main beam is
wider, the transmission needs to secure the data over a wider
range of angles, which is reflected in an SR expression with
high channel uncertainty. Particularly, when the power budget
is low, for example, P0= 25 dBm, we note that the secrecy
rate monotonically decreases with the growth of ∆θ, while the
weighted optimization problem is infeasible due to the power
budget limit when the ∆θis larger than 5 degree.
Fig. 7 illustrates the convergence of the CRB and the
secrecy rate of the proposed algorithm. The benchmark in
Fig. 7 (c) is generated following the AN design techniques in
Section III, where the covariance of AWGN received by Eves
is set as σ2
0= 0 dBm.1It is noted that the performance of
metrics converges after five iterations when SNR =−22 dB,
1In the isotropical AN designs, i.e., the benchmark schemes, we deploy the
omnidirectional waveform to ensure the sensing performance, where we have
¯RX=P0
NtINtaccording to problem (25). As the CRB matrix is a function
of the covariance matrix, the resultant root-CRB of the benchmark schemes
is equal to the value at the first iteration as shown in Fig. 6.
max
˜Wi,RN,y,zρ
|J|UB|J|+1−ρ
2SRUBz (37a)
s.t. 2cyir
Σi+tr
˜HiRN
+ 1−y2
i
Σi−tr
˜Hi˜Wi
+tr
˜HiRN
+ 1
≥cz,∀i (37b)
c|αk|2bH(ϑk,n)˜Wib(ϑk,n)≤(1−c)
|αk|2bH(ϑk,n)
IX
¯m=1,
¯m̸=i˜W¯m+RN
b(ϑk,n) + 1
,∀ϑk,n∈card(Ωk),
∀k, i (37c)
(32b),(32c),(32d)and(32e). (37d)

--- 第 11 页 ---
3172 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 23, NO. 4, APRIL 2024
Fig. 8. The secrecy rate analysis versus the number of CUs, with various
power budgets. K= 1,SNR =−15dB.
Fig. 9. Tradeoff between the CRB and the secrecy rate with different power
budgets. ϑ1,0=−25◦, I= 3, K= 1,SNR =−15dB.
while the convergence requires fewer iterations at higher
SNR. Additionally, the secrecy rate obtained by the proposed
algorithm converges to 8.9 bit/s/Hz and 9.1 bit/s/Hz when
SNR =−22 dB and SNR =−15 dB, which outperforms
the isotropical AN methods.
Moreover, it is illustrated in Fig. 8 that the secrecy rate
decreases with the growth of the CUs’ number, given different
power budgets P0. Note that a higher power budget achieves
better security performance. Particularly, the secrecy rate can-
not be ensured if the ISAC system serves more than 5 CUs
when P0= 25 dBm. In Fig. 9, we consider the performance
tradeoff between the target/Eve estimation and communication
data security with different power budgets by varying the
weighting factor ρ. We note that higher P0results in a better
performance of the estimation metric, i.e., root-CRB of the
amplitude and the angle. Additionally, with the increase in
secrecy rate, the CRB grows as well, which demonstrates the
deterioration of Eve’s angle estimation accuracy.
Furthermore, we consider a scenario including one CU and
one Eve for exploiting impacts on security and sensing metrics
resulting from the angle difference between the CU and the
Fig. 10. Beampatterns for the scenario when the CU and the Eve
both locate at −20◦, narrowing with each iteration until convergence.
I= 1, K= 1,SNR =−22dB,P0= 35 dBm.
Fig. 11. Secrecy rate and root-CRB of angle performances versus
uncertain angular interval of the target/Eve, with various angle differ-
ences between the Eve and the CU, where the CU locates at −20◦.
I= 1, K= 1,SNR =−15dB,P0= 35 dBm.
Eve. In this case, the Rician channel model with a strong
LoS component is deployed, i.e., vi= 7 in (2), and the
CU is assumed to locate at −20◦. Resultant beampatterns
are shown in Fig. 10 when the Eve is at −20◦as well. It is
demonstrated that the main beam width converges after four
iterations and the generated angle root-CRB at the second
iteration is lower than the case of a weak Rician channel,
which is validated in Fig. 11. Fig.11 illustrates the analysis
of the secrecy rate and the root-CRB of angle with various
angle difference. Generally speaking, with the expansion of the
uncertain angular interval ∆θ, both of the metrics deteriorated.
The secrecy rate decreases when the Eve and the CU directions
get closer, while the performance of the CRB improves since
the tradeoff is revealed in Fig. 9.
VIII. C ONCLUSION
In this paper, we have considered the sensing-aided secure
ISAC systems, where the dual-functional BS emitted wave-
forms to estimate the amplitudes and the directions of poten-
tial eavesdroppers and send confidential communication data

--- 第 12 页 ---
SU et al.: SENSING-ASSISTED EA VESDROPPER ESTIMATION: AN ISAC BREAKTHROUGH IN PLS 3173
to CUs simultaneously. The proposed design has promoted
the cooperation between sensing and communication rather
than conventionally individual functionalities. The weighted
optimization problem has been designed to optimize the nor-
malized CRB and secrecy rate while constraining the system
power budget. Our numerical results have demonstrated that
the secrecy rate was enhanced with the decreasing CRB in
both single and multi-Eve scenarios.
REFERENCES
[1] X. You et al., “Towards 6G wireless communication networks: Vision,
enabling technologies, and new paradigm shifts,” Sci. China Inf. Sci. ,
vol. 64, no. 1, pp. 1–74, Nov. 2020.
[2] Z. Feng, Z. Fang, Z. Wei, X. Chen, Z. Quan, and D. Ji, “Joint radar and
communication: A survey,” China Commun. , vol. 17, no. 1, pp. 1–27,
Jan. 2020.
[3] L. Zheng, M. Lops, Y . C. Eldar, and X. Wang, “Radar and communi-
cation coexistence: An overview: A review of recent methods,” IEEE
Signal Process. Mag. , vol. 36, no. 5, pp. 85–99, Sep. 2019.
[4] F. Liu et al., “Integrated sensing and communications: Toward dual-
functional wireless networks for 6G and beyond,” IEEE J. Sel. Areas
Commun. , vol. 40, no. 6, pp. 1728–1767, Jun. 2022.
[5] Y . Cui, F. Liu, X. Jing, and J. Mu, “Integrating sensing and communi-
cations for ubiquitous IoT: Applications, trends, and challenges,” IEEE
Netw. , vol. 35, no. 5, pp. 158–167, Sep. 2021.
[6] Z. Wei, F. Liu, C. Masouros, N. Su, and A. P. Petropulu, “Toward multi-
functional 6G wireless networks: Integrating sensing, communication,
and security,” IEEE Commun. Mag. , vol. 60, no. 4, pp. 65–71, Apr. 2022.
[7] Y . Liu, Z. Qin, M. Elkashlan, Y . Gao, and L. Hanzo, “Enhancing
the physical layer security of non-orthogonal multiple access in large-
scale networks,” IEEE Trans. Wireless Commun. , vol. 16, no. 3,
pp. 1656–1672, Mar. 2017.
[8] Z. Qin, Y . Liu, Z. Ding, Y . Gao, and M. Elkashlan, “Physical layer
security for 5G non-orthogonal multiple access in large-scale networks,”
inProc. IEEE Int. Conf. Commun. (ICC) , May 2016, pp. 1–6.
[9] J. M. Hamamreh, H. M. Furqan, and H. Arslan, “Classifications and
applications of physical layer security techniques for confidentiality: A
comprehensive survey,” IEEE Commun. Surveys Tuts. , vol. 21, no. 2,
pp. 1773–1828, 2nd Quart., 2019.
[10] R. Melki, H. N. Noura, M. M. Mansour, and A. Chehab, “A survey
on OFDM physical layer security,” Phys. Commun. , vol. 32, pp. 1–30,
Feb. 2019.
[11] L. Sun and Q. Du, “Physical layer security with its applications in
5G networks: A review,” China Commun. , vol. 14, no. 12, pp. 1–14,
Dec. 2017.
[12] D. Li, Z. Yang, N. Zhao, Z. Wu, Y . Li, and D. Niyato, “Joint precoding
and jamming design for secure transmission in NOMA-ISAC networks,”
inProc. 14th Int. Conf. Wireless Commun. Signal Process. (WCSP) ,
Nov. 2022, pp. 764–769.
[13] B. Yang et al., “Reconfigurable intelligent computational surfaces: When
wave propagation control meets computing,” 2022, arXiv:2208.04509 .
[14] A. A. Salem, M. H. Ismail, and A. S. Ibrahim, “Active reconfigurable
intelligent surface-assisted MISO integrated sensing and communication
systems for secure operation,” IEEE Trans. Veh. Technol. , vol. 72, no. 4,
pp. 4919–4931, Apr. 2023.
[15] A. M. Elbir, K. V . Mishra, M. R. B. Shankar, and S. Chatzinotas,
“The rise of intelligent reflecting surfaces in integrated sensing and
communications paradigms,” IEEE Netw. , early access, Dec. 26, 2022,
doi: 10.1109/MNET.128.2200446.
[16] P. Liu, Z. Fei, X. Wang, J. A. Zhang, Z. Zheng, and Q. Zhang, “Securing
multi-user uplink communications against mobile aerial eavesdropper
via sensing,” IEEE Trans. Veh. Technol. , vol. 72, no. 7, pp. 9608–9613,
Jul. 2023.
[17] A. Deligiannis, A. Daniyan, S. Lambotharan, and J. A. Chambers,
“Secrecy rate optimizations for MIMO communication radar,” IEEE
Trans. Aerosp. Electron. Syst. , vol. 54, no. 5, pp. 2481–2492, Oct. 2018.
[18] J. Chu, R. Liu, Y . Liu, M. Li, and Q. Liu, “AN-aided secure beamform-
ing design for dual-functional radar-communication systems,” in Proc.
IEEE/CIC Int. Conf. Commun. China (ICCC Workshops) , Jul. 2021,
pp. 54–59.[19] N. Su, F. Liu, and C. Masouros, “Secure radar-communication systems
with malicious targets: Integrating radar, communications and jam-
ming functionalities,” IEEE Trans. Wireless Commun. , vol. 20, no. 1,
pp. 83–95, Jan. 2021.
[20] N. Su, F. Liu, Z. Wei, Y .-F. Liu, and C. Masouros, “Secure dual-
functional radar-communication transmission: Exploiting interference
for resilience against target eavesdropping,” IEEE Trans. Wireless Com-
mun. , vol. 21, no. 9, pp. 7238–7252, Sep. 2022.
[21] S. Dwivedi, M. Zoli, A. N. Barreto, P. Sen, and G. Fettweis, “Secure
joint communications and sensing using chirp modulation,” in Proc. 2nd
6G Wireless Summit (6G SUMMIT) , Mar. 2020, pp. 1–5.
[22] O. Günlü, M. Bloch, R. F. Schaefer, and A. Yener, “Secure joint
communication and sensing,” 2022, arXiv:2202.10790 .
[23] S. M. Kay, Fundamentals of Statistical Signal Processing: Estimation
Theory . Upper Saddle River, NJ, USA: Prentice-Hall, 1993.
[24] L. Zhao, G. Geraci, T. Yang, D. W. K. Ng, and J. Yuan, “A tone-
based AoA estimation and multiuser precoding for millimeter wave
massive MIMO,” IEEE Trans. Commun. , vol. 65, no. 12, pp. 5209–5225,
Dec. 2017.
[25] X. Hu, C. Zhong, X. Chen, W. Xu, and Z. Zhang, “Cluster grouping
and power control for angle-domain mmWave MIMO NOMA systems,”
IEEE J. Sel. Topics Signal Process. , vol. 13, no. 5, pp. 1167–1180,
Sep. 2019.
[26] J. Li and P. Stoica, “MIMO radar with colocated antennas,” IEEE Signal
Process. Mag. , vol. 24, no. 5, pp. 106–114, Sep. 2007.
[27] F. Liu, Y .-F. Liu, A. Li, C. Masouros, and Y . C. Eldar, “Cramér–Rao
bound optimization for joint radar-communication beamforming,” IEEE
Trans. Signal Process. , vol. 70, pp. 240–253, 2022.
[28] J. Li, L. Xu, P. Stoica, K. W. Forsythe, and D. W. Bliss, “Range compres-
sion and waveform optimization for MIMO radar: A Cramér–Rao bound
based study,” IEEE Trans. Signal Process. , vol. 56, no. 1, pp. 218–232,
Jan. 2008.
[29] M. F. Hanif, L.-N. Tran, M. Juntti, and S. Glisic, “On linear precod-
ing strategies for secrecy rate maximization in multiuser multiantenna
wireless networks,” IEEE Trans. Signal Process. , vol. 62, no. 14,
pp. 3536–3551, Jul. 2014.
[30] B. Hassibi and T. L. Marzetta, “Multiple-antennas and isotropically
random unitary inputs: The received signal density in closed form,” IEEE
Trans. Inf. Theory , vol. 48, no. 6, pp. 1473–1484, Jun. 2002.
[31] W.-C. Liao, T.-H. Chang, W.-K. Ma, and C.-Y . Chi, “QoS-based transmit
beamforming in the presence of eavesdroppers: An optimized artificial-
noise-aided approach,” IEEE Trans. Signal Process. , vol. 59, no. 3,
pp. 1202–1216, Mar. 2011.
[32] B. Fang, Z. Qian, W. Shao, and W. Zhong, “Precoding and artificial
noise design for cognitive MIMOME wiretap channels,” IEEE Trans.
Veh. Technol. , vol. 65, no. 8, pp. 6753–6758, Aug. 2016.
[33] Q. Li, Y . Yang, W.-K. Ma, M. Lin, J. Ge, and J. Lin, “Robust cooperative
beamforming and artificial noise design for physical-layer secrecy in
AF multi-antenna multi-relay networks,” IEEE Trans. Signal Process. ,
vol. 63, no. 1, pp. 206–220, Jan. 2015.
[34] Z. Chu, H. Xing, M. Johnston, and S. Le Goff, “Secrecy rate optimiza-
tions for a MISO secrecy channel with multiple multiantenna eaves-
droppers,” IEEE Trans. Wireless Commun. , vol. 15, no. 1, pp. 283–297,
Jan. 2016.
[35] Q. Li and W.-K. Ma, “Spatially selective artificial-noise aided transmit
optimization for MISO multi-eves secrecy rate maximization,” IEEE
Trans. Signal Process. , vol. 61, no. 10, pp. 2704–2717, May 2013.
[36] A. Jakobsson and P. Stoica, “Combining Capon and APES for estimation
of spectral lines,” Circuits, Syst., Signal Process. , vol. 19, no. 2,
pp. 159–169, Mar. 2000.
[37] L. Xu, J. Li, and P. Stoica, “Target detection and parameter estimation
for MIMO radar systems,” IEEE Trans. Aerosp. Electron. Syst. , vol. 44,
no. 3, pp. 927–939, Jul. 2008.
[38] J. Li, P. Stoica, and Z. Wang, “On robust Capon beamforming and diago-
nal loading,” IEEE Trans. Signal Process. , vol. 51, no. 7, pp. 1702–1715,
Jul. 2003.
[39] V . Chandola, A. Banerjee, and V . Kumar, “Anomaly detection: A survey,”
ACM Comput. Surv. , vol. 41, no. 3, pp. 1–58, Jul. 2009.
[40] P. Tichavsky, “Posterior Cramér–Rao bound for adaptive harmonic
retrieval,” IEEE Trans. Signal Process. , vol. 43, no. 5, pp. 1299–1302,
May 1995.
[41] M. Grant and S. Boyd. (2014). CVX: MATLAB Software for Disciplined
Convex Programming . [Online]. Available: http://cvxr.com/cvx
[42] S.-P. Wu, L. Vandenberghe, and S. Boyd. (1996). Software for Deter-
minant Maximization Problems—User’s Guild . [Online]. Available:
http://www.stanford.edu/~boyd/maxdet

--- 第 13 页 ---
3174 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 23, NO. 4, APRIL 2024
[43] K. Shen and W. Yu, “Fractional programming for communication
systems—Part I: Power control and beamforming,” IEEE Trans. Signal
Process. , vol. 66, no. 10, pp. 2616–2630, May 2018.
[44] D. P. Bertsekas, “Nonlinear programming,” J. Oper. Res. Soc. , vol. 48,
no. 3, p. 334, 1997.
[45] A. Ben-Tal and A. Nemirovski, Lectures on Modern Convex Optimiza-
tion: Analysis, Algorithms, and Engineering Applications . Philadelphia,
PA, USA: SIAM, 2001.
[46] Z. Yu, J. Li, Q. Guo, and J. Ding, “Efficient direct target localization
for distributed MIMO radar with expectation propagation and belief
propagation,” IEEE Trans. Signal Process. , vol. 69, pp. 4055–4068,
2021.
Nanchi Su (Graduate Student Member, IEEE)
received the B.E. and M.E. degrees from the
Harbin Institute of Technology, Heilongjiang, China,
in 2015 and 2018, respectively, and the Ph.D.
degree from University College London, London,
U.K., in 2023. She is currently a Visiting Scholar
with the Guangdong Provincial Key Laboratory of
Aerospace Communication and Networking Tech-
nology, Harbin Institute of Technology (Shenzhen),
Shenzhen, China, and with the Department of Elec-
tronic and Electrical Engineering, Southern Univer-
sity of Science and Technology, Shenzhen. Her research interests include
integrated sensing and communication systems (ISAC), constructive interfer-
ence design, physical-layer security, radar signal processing, convex optimiza-
tion, and situational awareness. She is a TPC Member of various flagship
IEEE/ACM conferences, such as IEEE ICC and IEEE GLOBECOM.
Fan Liu (Member, IEEE) received the B.Eng. and
Ph.D. degrees from the Beijing Institute of Tech-
nology (BIT), Beijing, China, in 2013 and 2018,
respectively. He is currently an Assistant Professor
with the Department of Electronic and Electrical
Engineering, Southern University of Science and
Technology (SUSTech). He has previously held
academic positions with University College Lon-
don, London, U.K., first as a Visiting Researcher
from 2016 to 2018, and then as a Marie Curie
Research Fellow from 2018 to 2020. His research
interests include the general area of signal processing and wireless communi-
cations, and in particular in the area of integrated sensing and communications
(ISAC). He is a member of the IMT-2030 (6G) ISAC Task Group. He was a
recipient of the 2023 IEEE ComSoc Stephan O. Rice Prize, the 2023 IEEE
ICC Best Paper Award, the 2021 IEEE Signal Processing Society Young
Author Best Paper Award, the 2019 Best Ph.D. Thesis Award of the Chinese
Institute of Electronics, and the 2018 EU Marie Curie Individual Fellowship.
He has ten publications selected as the IEEE ComSoc Besting Readings
in ISAC. He is the Founding Academic Chair of the IEEE ComSoc ISAC
Emerging Technology Initiative (ISAC-ETI), an Associate Editor of the IEEE
COMMUNICATIONS LETTERS and the IEEE O PEN JOURNAL OF SIGNAL
PROCESSING , and the Guest Editor of the IEEE J OURNAL ON SELECTED
AREAS IN COMMUNICATIONS , IEEE W IRELESS COMMUNICATIONS ,IEEE
Vehicular Technology Magazine , and China Communications . He was also anOrganizer and the Co-Chair of numerous workshops, special sessions, and
tutorials in flagship IEEE/ACM conferences, including ICC, GLOBECOM,
ICASSP, and MobiCom. He is the TPC Co-Chair of the 2nd–4th IEEE Joint
Communication and Sensing Symposium (JC&S), the Track Chair of the ISAC
Track of the IEEE GLOBECOM 2023 Selected Areas in Communications
Symposium, and the Track Co-Chair of the IEEE WCNC 2024. He was listed
in the World’s Top 2% Scientists by Stanford University for citation impact
in 2021 and 2022.
Christos Masouros (Senior Member, IEEE)
received the Diploma degree in electrical and
computer engineering from the University of Patras,
Greece, in 2004, and the M.Sc. (by research)
and Ph.D. degrees in electrical and electronic
engineering from The University of Manchester,
U.K., in 2006 and 2009, respectively.
In 2008, he was a Research Intern with Philips
Research Labs, U.K. From 2009 to 2010, he was
a Research Associate with The University of
Manchester. From 2010 to 2012, he was a
Research Fellow with Queen’s University Belfast. He has held a Royal
Academy of Engineering Research Fellowship from 2011 to 2016. In 2012,
he joined University College London, as a Lecturer. Since 2019, he has
been a Full Professor of signal processing and wireless communications
with the Information and Communication Engineering Research Group,
Department of Electrical and Electronic Engineering, and affiliated with
the Institute for Communications and Connected Systems, University
College London. From 2018 to 2022, he was the Project Coordinator
of the e4.2m EU H2020 ITN Project PAINLESS, involving 12 EU
partner universities and industries, towards energy-autonomous networks.
During 2024–2028, he will be the Scientific Coordinator of the e2.7m
EU H2020 DN Project ISLANDS, involving 19 EU partner universities
and industries, towards next-generation vehicular networks. His research
interests include wireless communications and signal processing with a
particular focus on green communications, large scale antenna systems,
integrated sensing and communications, interference mitigation techniques
for MIMO, and multicarrier communications. He is a founding member and
the Vice-Chair of the IEEE Emerging Technology Initiative on Integrated
Sensing and Communications (SAC), the Vice Chair of the IEEE Wireless
Communications Technical Committee Special Interest Group on ISAC,
and the Chair of the IEEE Green Communications & Computing Technical
Committee, Special Interest Group on Green ISAC. He is the TPC Chair
of the IEEE ICC 2024 Selected Areas in Communications (SAC) Track on
ISAC. He was a recipient of the 2023 IEEE ComSoc Stephen O. Rice Prize
and the Best Paper Award from the IEEE GLOBECOM 2015 and IEEE
WCNC 2019 conferences. He was a co-recipient of the 2021 IEEE SPS
Young Author Best Paper Award. He has been recognized as an Exemplary
Editor of the IEEE C OMMUNICATIONS LETTERS and as an Exemplary
Reviewer of the IEEE T RANSACTIONS ON COMMUNICATIONS . He is an
Editor of IEEE T RANSACTIONS ON WIRELESS COMMUNICATIONS and the
IEEE O PEN JOURNAL OF SIGNAL PROCESSING . He is an Editor-at-Large
of IEEE O PEN JOURNAL OF THE COMMUNICATIONS SOCIETY . He has
been an Editor of IEEE T RANSACTIONS ON COMMUNICATIONS and IEEE
COMMUNICATIONS LETTERS . He has been the Guest Editor of a number of
IEEE J OURNAL ON SELECTED TOPICS IN SIGNAL PROCESSING and IEEE
JOURNAL ON SELECTED AREAS IN COMMUNICATIONS issues.

================================================================================


================================================================================
文件: 符号及与编码.pdf
页数: 15
================================================================================


--- 第 1 页 ---
7238 IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 21, NO. 9, SEPTEMBER 2022
Secure Dual-Functional Radar-Communication
Transmission: Exploiting Interference for
Resilience Against Target Eavesdropping
Nanchi Su ,Graduate Student Member, IEEE ,F a nL i u ,Member, IEEE , Zhongxiang Wei, Member, IEEE ,
Ya-Feng Liu ,Senior Member, IEEE , and Christos Masouros ,Senior Member, IEEE
Abstract —We study security solutions for dual-functional
radar communication (DFRC) systems, which detect the radartarget and communicate with downlink cellular users inmillimeter-wave (mmWave) wireless networks simultaneously.Uniquely for such scenarios, the radar target is regarded asa potential eavesdropper which might surveil the informationsent from the base station (BS) to communication users (CUs),that is carried by the radar probing signal. Transmit waveformand receive beamforming are jointly designed to maximizethe signal-to-interference-plus -noise ratio (SINR) of the radar
under the security and power budget constraints. We apply aDirectional Modulation (DM) approach to exploit constructiveinterference (CI), where the known multiuser interference (MUI)can be exploited as a source of useful signal. Moreover, to fur-ther deteriorate the eavesdropping signal at the radar target,we utilize destructive interference (DI) by pushing the receivedsymbols at the target towards the destructive region of the signal
Manuscript received 9 July 2021; revised 28 December 2021; accepted
25 February 2022. Date of publication 1 7 March 2022; date of current version
12 September 2022. This work was supported in part by the Engineering
and Physical Sciences Research Council under Project EP/R007934/1 andProject EP/S026622/1; in part by the Na tional Natural Science Foundation of
China (NSFC) under Grant 62101234, Grant 12021001, and Grant 62101384;
and in part by the Chongqing Key Laboratory of Mobile Communica-
tion Technology under Grant cqupt-mct -202101 and the China Scholarship
Council (CSC). An earlier version of this paper was presented in part
at the IEEE International Workshop on Signal Processing Advances in
Wireless Communications (SPAWC), Lucca, Italy, September 2021 [DOI:
10.1109/SPAWC51858.2021.9593096] and in part at the IEEE AsilomarConference on Signals, Systems, and Computers, Paciﬁc Grove, CA, USA,
October 2021 [DOI: 10.1109/I EEECONF 53345.2021.9723251]. The associate
editor coordinating the review of this article and approving it for publicationwas W. P. Tay. (Corresponding author: Fan Liu.)
Nanchi Su is with the Department of Electronic and Electrical Engi-
neering, University College London, London WC1E 7JE, U.K., and alsowith the Department of Electronic and Electrical Engineering, SouthernUniversity of Science and Technol ogy, Shenzhen 518055, China (e-mail:
<EMAIL>).
Fan Liu is with the Department of Electronic and Electrical Engineering,
Southern University of Science a nd Technology, Shenzhen 518055, China
(e-mail: <EMAIL>).
Zhongxiang Wei is with the College of Electronic and Informa-
tion Engineering, Tongji University, Shanghai 201804, China (e-mail:<EMAIL>).
Ya-Feng Liu is with the State Key Laboratory of Scientiﬁc and
Engineering Computing, Institute of Computational Mathematics and Sci-entiﬁc/Engineering Computing, Academy of Mathematics and SystemsScience, Chinese Academy of Sci ences, Beijing 100190, China (e-mail:
yaﬂ****************).
Christos Masouros is with the Department of Electronic and Electrical
Engineering, University College London, London WC1E 7JE, U.K. (e-mail:
<EMAIL>).
Color versions of one or more ﬁgures in this article are available at
https://doi.org/10.1109/TWC.2022.3156893.
Digital Object Identiﬁer 10.1109/TWC.2022.3156893constellation. Our numerical results verify the effectiveness of the
proposed design showing a secure transmission with enhancedperformance against benchmark DFRC techniques.
Index Terms —Dual-functional radar-communication system,
millimeter-wave, physical layer security, direction modulation,constructive interference, fractional programming.
I. I NTRODUCTION
A. Background and Motivation
WIRELESS spectrum is getting increasingly congested
due to the tremendous growth of wireless connections
and mobile devices, which results in high auction price of
the available frequency bands. According to [1], the Spanish
government raised a total of 438 million for the sale of 5G
frequencies. On the other hand, the government of South Koreapaid $3.3 billion for the 3.5 GHz and 28 GHz bands in 5G
network. To address the increasing need for extra spectrum,
the radar bands, which are largely overlapped with thoseof major communication applications, have been envisioned
as potentially exploitable spectral resources. In fact, given
the overlapped frequencies, as well as the more and more
similar RF front-end designs between radar and communica-
tions, the shared use of the spectrum or even the hardwareplatform between both functionalities becomes a promising
solution to improve the efﬁcien cy and reduce the costs. This
has given rise to the development of the Dual-functionalRadar-Communication (DFRC) system in recent years [2]–[6].
In many emerging applications, DFRC systems are expected
to meet the demand for location- awareness as a new paradigm,
for example, in intelligent transportation systems [7].
In DFRC systems, the transmitted waveform is speciﬁcally
designed as to serve for both purposes of target sensing
and wireless communication, which raises unique security
challenges. Intuitively, the radar beampattern is designed toconcentrate the radiation power towards the direction of targets
of interest so as to improve the detection performance. Since
the probing DFRC signal also carries information for the
communication users, the target, as a potential eavesdropper,
e.g., an unauthorized vehicle or UA V , could readily surveil theinformation intended for communication users (CUs). To this
end, new physical layer (PHY) security solutions are required
for the dual functional operation in security-critical DFRCdesigns.
1536-1276 © 2022 I EEE. Personal u se is perm itted, but republication/redistri bution requires IEEE permission.
See https://www.ieee.org/publications/rights/index.html for more information.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 2 页 ---
SUet al. : SECURE DFRC TRANSMISSION: EXPLOITING INTERFERENC E FOR RESILIENCE AGAINST TARGET EA VESDROPPING 7239
Methods to secure the wireless communication systems
are widely investigated over t he past decades. Pioneered
by Wyner [8], beamformer and precoder are designed
to ensure the quality-of-service (QoS) at legitimate userswhile limiting the signal strength received at the potential
eavesdroppers [9]–[12], which aims to maximize the signal-
to-interference-plus-noise ratio (SINR) difference betweenthe two types of users, and accordingly yields an optimal
secrecy rate (SR). On top of that, artiﬁcial noise (AN)
is generated to further deteriorate the received signals at
eavesdroppers [13]–[17]. AN-aided scheme is proved to be
efﬁcient especially when the channel statement informa-tion (CSI) of eavesdroppers is unknown or partially known
to the base station (BS) [18], [19].
In conventional beamforming designs, AN indeed degrades
SINR at both CUs and eavesdroppers, which requires higher
power budget to ensure the QoS. In view of the redun-
dant power consumption caused by AN, directional mod-
ulation (DM) has attracted growing research attentions as
an emerging hardware efﬁcient approach to secure wirelesscommunication systems in recent years [20]–[22]. The DM
transmitter sends conﬁdential information to the CUs such that
the malicious eavesdroppers cannot intercept the transmittedmessages [23]. Unlike the SR based methods, DM technique
adjusts the amplitude and phase of the symbols at the users of
interest directly while scrambling the symbols in other unde-
sired directions, which implies that the modulation happens
at the antenna level instead of at the baseband level. As aresult, a low symbol error rate (SER) can be endorsed at
the CUs, while the received symbols of the eavesdropper are
randomized in the signal constellation. Since the expensive andpower-consuming radio frequency (RF) chains and digital-to-
analog converter (DAC) deployed in conventional beamform-
ing design are not required, the DM based scheme is efﬁcient
on aspects of both cost and energy. The DM approach is
based on the pronciples of exploiting constructive interference(CI) [24]–[27], where the received signal is not necessary to
be aligned with the intended symbols, but is pushed away from
the detection thresholds of the signal constellation.
In this relevant line of CI res earch, recent studies focus on
exploiting CI through symbol-l evel precoding, which exploits
known multiuser interference (MUI) as useful power by push-
ing the received signal away from the detection bound of
the signal constellation. Also, it is provable that CI-basedprecoding designs beneﬁt the data secrecy. In particular, the CI
and AN can be jointly exploite d to design secure beamformer
under the assumption of perfect or imperfect CSI [27], [28],which was proved to outperform the conventional AN-aided
secrecy optimization. In addition to increasing the secrecy,
the generated AN was exploited to be constructive to energyharvesting in [27]. AN-aided CI precoding designs were pro-
posed in [29]–[31], where a deterministic robust optimization
algorithm was presented in [29] and a probabilistic optimiza-
tion method was presented in [30], respectively. Furthermore,
the work of [31] expanded the scenario to more practicalcases where the CSI of eavesdropper is totally unknown.
In [20], practical transmitter designs were exploited when the
CUs’ channel is correlated with or without the eavesdropper’schannel. We note that while all the above approaches are
designed for the classical PHY security scenario involving
legitimate users and external eavesdroppers, none of these
apply to the unique DFRC scenarios where the target ofinterest may be a potential eavesdropper.
To address the security issue raised in the DFRC systems,
in [32], the MIMO radar was designed to transmit a mixtureof two different signals, includi ng desired information for the
intended users and a pseudorandom distortional waveform
to confuse the eavesdropper, both of which are used for
detecting the target. In this context, several optimizations were
designed, namely target return SINR maximization, transmitpower minimization, and SR maximization, where the former
two designs keep the SR above a given threshold. In [33],
a uniﬁed system including passive radar and communicationsystem has been studied. To ensure the SR at CUs, the
optimization problem was designed to maximize the SINR
at passive radar with an SR threshold constraint. Furthermore,
an AN-aided method deployed in DFRC systems was proposed
in [16], where the BS serves communication users and detectsa target simultaneously. To secure the communication data via
optimized SR, the signal-to-noise ratio (SNR) was minimized
at the target while ensuring the SINR at each desired user.
To the best of our knowledge, all the existing studies
on DFRC security are based on SR maximization, with the
assumption of Gausssian symbol transmission and perfect
or imperfect CSI knowledge. To address DFRC security in
broader scenarios, it is worth studying the CI based waveformdesign for the reason that a) MUI is commonly treated as a
detrimental impact that needs to be mitigated, while it becomes
beneﬁcial and further contributes to the useful signal powerin CI design; b) CI based precoding can support a larger
number of data streams with a signiﬁcantly improved SER
performance [34].
B. Contributions
We propose several designs, which aim at maximizing the
receive SINR of radar in secure DFRC systems. Speciﬁcally,
we consider a MU-MISO DFRC BS which serves CUs and
detects a point-like target simultaneously, where the transmitwaveform and the receive beamformer are jointly designed
to improve PHY security following the CI approach. Note
that the target is treated as a potential eavesdropper. As afurther consideration on comm unication data secrecy, MUI is
designed to be constructive at the CUs, while disrupting the
data at the radar target, which deteriorates the target receive
signals and thus increases the SER at the target. Throughout
this paper, the proposed problems above are ﬁrstly studied inan ideal scenario where the ta rget location is known to the
BS, and are then extended to the more practical case where
the location is uncertain to the BS.
Within this scope, the contributions of our work are sum-
marized as follows :
•We design the transmit waveform and receive beam-
former jointly for the secure DFRC system, where theDM technique is employed to maximize the received
SINR of the radar system under the constraints of power
budget and CI for security.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 3 页 ---
7240 IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 21, NO. 9, SEPTEMBER 2022
•We propose a fractional programming (FP) algorithm
to solve the radar SINR maximization problem, and
compare the resulting performance with benchmark tech-
niques, and alternative solvers including semideﬁniterelaxation (SDR), and successive Quadratically Con-
strained Quadratic Program (SQ) methods.
•We investigate the problem under the practical condition
of target location uncertainty, where the DFRC waveform
is designed to maximize the minimum radar SINR within
a given angular interval that the targets might fall into.
•We further consider an advanced secure CI design for theproposed DFRC system, where the MUI is designed tobe constructive to CUs, while destructive to the target.
Remark 1: Comparing with the research in [16], the radar
detection scenario is extended to be more general in practical
by taking the presence of clutter into account. Regarding
the MUI, in our previous work [16] and general block-level
designs, it is commonly treated as a detrimental impact to be
mitigated. While in this work, we consider an approach whereinterference becomes beneﬁcial and further contributes to the
useful signal power in CI-based design, which is a symbol-
level precoding method. Moreover , the CI-based securitydesign avoids the redundant power consumption comparing
with the AN-aided method.
C. Organization
This paper is organized as follows. Section II gives the sys-
tem model. The waveform optimization problem is designedwith the guarantee of PHY security by adopting CI method
in Section III and Section IV, when the target location
is known to the BS perfectly or imperfectly, respectively.
In Section V, PHY security is further considered by con-
structing the received signal at the target into the destructiveregion. Section VI provides numerical results, and Section VII
concludes the paper.
Notations: Unless otherwise speciﬁed, matrices are
denoted by bold uppercase letters (i.e., X), vectors are rep-
resented by bold lowercase letters (i.e., x), and scalars are
denoted by normal font (i.e., α). Subscripts indicate the loca-
tion of the entry in the matrices or vectors (i.e., s
i,jandlnare
the(i,j)-th and the n-th element in Sandl, respectively). tr (·)
andvec (·)denote the trace and the vectorization operations.
(·)T,(·)Hand(·)∗stand for transpose, Hermitian transpose
and complex conjugate of th e matrices, respectively. diag (·)
represents the vector formed by the diagonal elements of the
matrices and rank (·)is rank operation. /bardbl·/bardbl,/bardbl·/bardbl∞and/bardbl·/bardblF
denote the l2norm, inﬁnite norm and the Frobenius norm
respectively. E{·}denotes the statistical expectation.
II. S YSTEM MODEL
We consider a DFRC MU-MISO system with a BS equipped
withNTtransmit antennas and NRreceive antennas, which
is serving Ksingle-antenna users and detecting a point-like
target simultaneously. As shown in Fig.1, the target can beregarded as a potential eavesdropper which might intercept the
information sent from the BS to legitimate users. Due to the
existence of Iclutter sources, the target return is interfered at
Fig. 1. (a) DFRC System imposed pot ential eavesdropper, which might
eavesdrop the information from the access point (AP) to CUs. (b) Secure
DFRC system.
the BS’s receiver. Additionally, the communication channel is
considered to be a narrowband slow time-varying block fading
Rician fading channel. Based on the assumptions above, belowwe elaborate on the radar and communication signal models.
A. Radar Signal Model
Letx∈C
NT×1denote the transmit signal vector, the
received waveform at the receive array is given as
r=α0U(θ0)x/bracehtipupleft
/bracehtipdownright/bracehtipdownleft
/bracehtipupright
signal+I/summationdisplay
i=1αiU(θi)x
/bracehtipupleft
/bracehtipdownright/bracehtipdownleft
 /bracehtipupright
signal - dependent clutter+z/bracehtipupleft/bracehtipdownright/bracehtipdownleft/bracehtipupright
noise, (1)
where α0andαidenote the complex amplitudes of the target
and the i-th interference source, θ0andθiare the angle of the
target and the i-th signal-dependent clutter source, respectively,
andz∈CNR×1is the additive white Gaussian noise (AWGN)
vector, with the variance of σ2
R.U(θ)is the steering matrix
of uniform linear array (ULA) antenna with half-wavelength
spaced element, deﬁned as
U(θ)=ar(θ)aT
t(θ), (2)
where at(θ)=1
√
NT/bracketleftbig
1,ejπsinθ,···,ejπ(NT−1) sin θ/bracketrightbigTand
ar(θ)=1
√
NR/bracketleftbig
1,ejπsinθ,···,ejπ(NR−1) sin θ/bracketrightbigT. Then, the
output of the ﬁlter can be given as
rf=wHr
=α0wHU(θ0)x+I/summationdisplay
i=1αiwHU(θi)x+wHz,(3)
wherew∈CNR×1denotes the receive beamforming vector.
Accordingly, the output SINR can be expressed as
SINR rad=/vextendsingle/vextendsingleα0wHU(θ0)x/vextendsingle/vextendsingle2
wHI/summationtext
i=1|αi|2U(θi)xxHUH(θi)w+wHwσ2
R
=μ/vextendsingle/vextendsinglewHU(θ0)x/vextendsingle/vextendsingle2
wH(Σ(x)+INR)w, (4)
where μ=|α0|2/slashBig
σ2
R,Σ(x)=I/summationtext
i=1biU(θi)xxHUH(θi),
andbi=|αi|2/slashBig
σ2
R.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 4 页 ---
SUet al. : SECURE DFRC TRANSMISSION: EXPLOITING INTERFERENC E FOR RESILIENCE AGAINST TARGET EA VESDROPPING 7241
Sincexis the intended information signal, the received
signal at target (eavesdropper’s receiver) can be given as
yR=α0aH
t(θ0)x+e, (5)
where e∼C N/parenleftbig
0,σ2
T/parenrightbig
denotes the AWGN. Then, eavesdrop-
ping SNR at radar target can be expressed as
SNR T=/vextendsingle/vextendsingleα0aH
t(θ0)x/vextendsingle/vextendsingle2
σ2
T. (6)
B. Communication Signal Model
The received signal at the k-th CU can be written as
yk=hH
kx+nk, (7)
wherehk∈CNT×1denotes the multiple -input-si ngle-output
(MISO) channel vector between the BS and the k-th CU.
Similarly, nkis the AWGN of the CU kwith the variance of
σ2
Ck. Following the typical mmWave channel proposed in [35],
we assume that hkis a slow time-varying block Rician fading
channel, i.e., the channel is constant in a block but varies
slowly from one block to another. Thus, the channel vectorof the k-th user can be expressed as a combination of a
deterministic strongest line-of-sight (LoS) channel vector and
a multiple-path scattered channel vector, which is expressedas
h
k=/radicalbigg
vk
1+vkhLoS
L,k+/radicalbigg
1
1+vkhNLoS
S,k, (8)
where vk>0is the Rician K-factor of the k-th user, hLoS
L,k=√
NTat(ωk,0)is the LoS deterministic component. a(ωk,0)
denotes the array steering vector, where ωk,0∈/bracketleftbig
-π
2,π
2/bracketrightbig
is
the angle of departure (AOD) of the LoS component fromthe BS to user k[35], [36]. The scattering component h
NLoS
S,k
can be expressed as hNLoS
S,k=/radicalBig
NT
LL/summationtext
l=1ck,lat(ωk,l),w h e r e L
denotes the number of propagation paths, ck,l∼C N (0,1)
is the complex path gain and ωk,l∈/bracketleftbig
-π
2,π
2/bracketrightbig
is the AOD
associated to the (k,l)-th propagation path.
Additionally, we note that the intended symbol varies
at a symbol-by-symbol basis in CI precoding designs. Let
skdenote the intended symbol of the k-th CU, which is
M-PSK modulated. To this end, we deﬁne sk∈A M,w h e r e
AM=/braceleftbig
am=ej(2m−1)φ,m=1,···,M/bracerightbig
,φ=π/M,a n d
Mdenotes the modulation order.
III. SINR radMAXIMIZATION WITHKNOWN
TARGET LOCATION
With the knowledge of precise target location, in this
section, we design the transmit waveform aiming at maxi-mizing the received radar SINR and subject to the informa-
tion secrecy constraint in the wireless communication system
deploying CI method. For clarity, we remark here that the
known target location is quite a typical assumption in the radar
literature, especially for target tracking algorithm designs. Thiscan be interpreted as to optimize the transmit waveform and
receive beamformer towards a sp eciﬁc direction of interest,
or to track the movement of the target with predicted locationFig. 2. QPSK illustration. (a) Relaxed phase DM. (b) Rotation by arg
/A0
s∗
k
/A1
.
information inferred from the previous estimates. Note that this
also applies to the clutter sources, whose angles are assumed
to be pre-estimated.
In light of the above system setting, we then propose two
algorithms to tackle the optimization problem, namely, the SQ
method proposed in Section III-B and the FP method proposedin Section III-C. Finally, the SDR approach is adopted to
analyze the upper-bound performance, and is presented in
Section III-D.
A. Problem Formulation
As demonstrated in [37], the study of the DM technique
can be based on strict phase and relaxed phase constraints. For
the strict phase-based waveform design, the received signal y
k
should have exactly the same phase as the induced symbol
of the k-th CU (i.e., sk), which constrains the degrees of
freedom (DoFs) in designing the waveform x. Hence, inspired
by the concept of CI [25], [38], the optimization problem isproposed to locate the received symbol for each CU within
a constructive region rather than restrict the symbol in the
proximity of the constellation point, namely the relaxed phase
based design.
The CI technique has been widely investigated in the recent
work. To avoid deviating our focus, we will omit the derivation
of the CI constraints, and refer the reader to [25] for more
details. Since CI-based waveform design aims to transform theundesirable MUI into useful power by pushing the received
signal further away from the M-PSK decision boundaries,
all interference contributes to t he useful received power [39].
Herewith, the SNR of the k-th user is expressed as
SNR
k=/vextendsingle/vextendsinglehH
kx/vextendsingle/vextendsingle2
σ2
Ck. (9)
With the knowledge of the channel information, all CUs’
data, as well as the location of target and clutter resources is
readily available at the transmitter, we formulate the following
optimization problem aiming at maximizing the SINR of thetarget return
max
w,xSINR rad
s.t./bardblx/bardbl2≤P0/vextendsingle/vextendsinglearg/parenleftbig
hH
kx/parenrightbig
−arg(sk)/vextendsingle/vextendsingle≤ξ,∀k,
SNR k≥Γk,∀k, (10)
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 5 页 ---
7242 IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 21, NO. 9, SEPTEMBER 2022
where P0denotes the transmit power budget, Γkis the given
SNR threshold, and ξis the phase threshold where the noise-
less received symbols are supposed to lie.
As illustrated in Fig. 2, by taking one of the QPSK
constellation points as an example, the constructive region is
given as the green area. In Fig. 2(a), ¯ykdenotes the noise-
excluding signal and the SNR related scalar γkis the threshold
distance to the decision region of the received symbol at the
k-th CU. Then, in order to express the constructive region
geometrically, we rotate t he noise-free received signal ¯ykand
project it onto real and imaginary axes, which is illustrated in
Fig. 2(b). By noting |sk|=1, the rotated signal ˜ykcan be
given in the form of
˜yk=(yk−nk)s∗
k
|sk|=hH
kxs∗
k
=˜hH
kx, (11)
where ˜hk=hks∗
k. Let us represent Re (˜yk)=R e/parenleftBig
˜hH
kx/parenrightBig
andIm(˜yk)=I m/parenleftBig
˜hH
kx/parenrightBig
. Then, the SINR radmaximization
problem (9) can be recast as [25]
max
w,xSINR rad (12a)
s.t./bardblx/bardbl2≤P0 (12b)/vextendsingle/vextendsingle/vextendsingleIm/parenleftBig
˜hH
kx/parenrightBig/vextendsingle/vextendsingle/vextendsingle≤/parenleftBig
Re/parenleftBig
˜hH
kx/parenrightBig
−/radicalBig
σ2
CkΓk/parenrightBig
tanφ,∀k
(12c)
where φ=±π/M. Till now, the constraints (12b) and (12c)
are both convex. In particular, the CI constraint (12c) is
essentially linear with respect to the variable x. In the follow-
ing subsections, we will transform the non-convex objectivefunction to tackle the optimization problem.
B. Solve (12) by SQ Approach
It is noted that problem (12) is still non-convex since
the clutter is signal-dependent, where the quadratic form ofoptimizing variable xis included in both numerator and
denominator. To address this issue, in this section we develop
an SQ approach to extract a suboptimal solution. Firstly, note
that problem (12) can be viewed as the classical minimum
variance distortionless response (MVDR) beamforming prob-lem with respect to w, which can be expressed as a function
ofxas
w=[Σ(x)+I]
−1U(θ0)x
xHUH(θ0)[Σ(x)+I]−1U(θ0)x. (13)
By substituting (13) into (4), the optimization problem (12)
can be rewritten as [40], [41]
max
xxHΦ(x)x
s.t.12 (b)and12 (c), (14)
where Φ(x)=UH(θ0)[Σ(x)+I]−1U(θ0)is a positive-
semideﬁnite SINR matrix. To solve problem (14), we adoptthe sequential optimization algorithm (SOA) presented in [41].
To be speciﬁc, let us ﬁrstly ignore the dependence of Φ(x)
onx, i.e., ﬁxing the signal-dependent matrix Φ(x)=Φforag i v e n x. To start with, we initialize Φ=Φ
0,w h e r e Φ0is
a constant positive-semideﬁnite matrix. By using SOA, the
waveform xis optimized iteratively with the updated Φtill
convergence. By doing so, in each SOA iteration we solve thefollowing problem
max
xxHΦx
s.t.12 (b)and12 (c). (15)
Note that problem (15) is eas ily converted to a convex
Quadratically Constrained Quadratic Program (QCQP) prob-
lem by recasting the signal-independent matrix Φto be
negative-semideﬁnite as follows [40]
max
xxHQx
s.t.12 (b)and12 (c), (16)
where Q=(Φ−λI),λ≥λmax(Φ),w h e r e λmax(Φ)
is the largest eigenvalue of Φ1It is straightforward to see
thatQis negative-semideﬁnite, thus the objective function
is concave, and then it can be tackled efﬁciently by CVX
toolbox [42]. Here, we denote w∗andx∗as the optimal
receive beamformer and wavefor m, respectively. Furthermore,
as the expression given in (13), the receive beamforming
vector w∗can be updated by the optimal waveform x∗.
Therefore, the suboptimal solutions are obtained until conver-gence by updating xandwiteratively. The generated solution
will serve as a baseline in Section VI named as SQ. For
clarity, we summarize the SQ approach in Algorithm 1. The
computational complexity of solving problem (16) at each
iteration is given by O/parenleftBig
N
3
T√
K/parenrightBig
[43].
In SQ approach, we note that the reformulation of the
objective function in (16) actually relaxes the one given
in (15). To be speciﬁc, we have xHQx=xH(Φ−λI)x=
xHΦx−λxHx, while the power constraint (12b) indicates
thatxHxin the second term is not constant. In the following
subsection, we adopt FP algorithm to solve problem (15),which aims to tackle the problem without a relaxation in the
objective function.
C. Solve (12) by FP Approach
The original radar SINR maxi mization problem can also be
written as
max
xμ/vextendsingle/vextendsinglewHU(θ0)x/vextendsingle/vextendsingle2
wH(Σ(x)+INR)w
s.t.12 (b)and12 (c). (17)
We note that the non-convexity lies only in the objective
function in the problem above, and one can stay in the
convex feasible region by exploiting various linear iteration
schemes. Thus, it can be solved by converting the objective
function into its linear approximation form. Following the
1Note that the objective function (16) can be transformed into the form of
Rayleigh quotient, which is a typical optimization formulation with a closed-
form solution. However, it does not admit a simple closed-form solution due to
CI constraints. Accordingly, the Rayle igh quotient theorem cannot be trivially
applied to solve the optimization problem.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 6 页 ---
SUet al. : SECURE DFRC TRANSMISSION: EXPLOITING INTERFERENC E FOR RESILIENCE AGAINST TARGET EA VESDROPPING 7243
Algorithm 1 SQ Algorithm for Solving Problem (12)
Input: P0,hk,σ2
Ck,σ2
R,θi,θ0,α0,bi,Γk,∀k,∀i,ε > 0,a n d
the maximum iteration number mmax
Output: x
1. Reformulate problem (12) by (16).
2. Initialize the positive-semideﬁnite matrix Φ0,m=1.
while m≤mmax and/vextendsingle/vextendsingleSINRm
rad−SINRm−1
rad/vextendsingle/vextendsingle≥εdo
3. Calculate Qm−1, solve problem (16) to obtain the
optimal waveform xm.
4. Update Φmbyxm.
5. Transform Φminto the negative-semideﬁnite matrix
Qm.
6.m=m+1.
end while
Dinkelbach’s transform of FP problem presented in [44],
we ﬁrstly reformulate the objective function as
max
xμ/vextendsingle/vextendsinglewHU(θ0)x/vextendsingle/vextendsingle2−uwH(Σ(x)+INR)w
s.t.12 (b)and12 (c). (18)
Here, the objective function is still non-concave because of the
ﬁrst term. To proceed with optim ization problem (18), let us
ﬁrstly denote f(x)=/vextendsingle/vextendsinglewHU(θ0)x/vextendsingle/vextendsingle2. Then, we approximate
the objective function f(x)by its ﬁrst-order Taylor expansion
with respective to xatx/prime∈D,w h e r e Ddenotes the feasible
region of (17).
f(x)≈f(x/prime)+∇fH(x/prime)(x−x/prime)
=f(x/prime)+,Re/parenleftbigg/parenleftBig
2/parenleftBig
x/primeHUH(θ0)w/parenrightBig
UH(θ0)w/parenrightBigH
×(x−x/prime)/parenrightbigg
, (19)
where ∇f(·)denotes the gradient of f(·). For simplicity,
we omit the constant term f(x/prime)and denote
g(x)=R e/parenleftBigg/parenleftbigg
2/parenleftbigg
xm−1H
UH(θ0)w/parenrightbigg
UH(θ0)w/parenrightbiggH
×/parenleftbig
x−xm−1/parenrightbig/parenrightBigg
. (20)
Herewith, the m-th iteration of the FP algorithm can
be obtained by solving the following convex optimization
problem
max
xμg(x)−uwH(Σ(x)+INR)w
s.t.12 (b)and12 (c), (21)
where xm−1∈D is the point obtained at the (m−1)-th
iteration. The optimal solution xm∈D can be obtained by
solving problem (21), and then the receive beamformer wm
can be obtained by substituting xmin (13). Furthermore, uis
an auxiliary variable, which is updated iteratively by
um+1=μ/vextendsingle/vextendsinglewHU(θ0)xm/vextendsingle/vextendsingle2
wH(Σ(xm)+INR)w. (22)It is easy to prove the convergence of the algorithm given the
non-increasing property of uduring each iteration [44]. For
clarity, we summarize the above in Algorithm 2. We note that
the computational complexity of solving problem (21) at each
iteration is given by O/parenleftBig
N3
T√
K/parenrightBig
[43].
D. Upper Bound Performance
In this subsection, we derive a new optimization problem
to analyze the upper bound performance of problem (12).
According to the reformulation given in problem (14), the
objective function is equivalent to
y(x)=xHUH(θ0)[Σ(x)+I]−1U(θ0)x. (23)
It is obvious that Σ(x)+I/followsequalI, and thereby, [Σ(x)+I]−1/precedesequal
I, which indicates that y(x)≤xHUH(θ0)U(θ0)x.S ow e
ﬁrstly relax the objective function as
max
xxHUH(θ0)U(θ0)x
s.t.12 (b)and12 (c). (24)
It is noted that problem (24) is an inhomogeneous QCQP [45]
problem. We ﬁrstly deﬁne X=xxHand let
˜X=/bracketleftbiggXx
xH1/bracketrightbigg
. (25)
Afterwards, problem (24) can be recast as
max
x,Xtr/parenleftBig
XˆU0/parenrightBig
s.t.˜X/followsequal0,rank/parenleftBig
˜X/parenrightBig
=1
12(b)and12(c), (26)
where ˆU0=UH(θ0)U(θ0). Note that problem (26) is
readily to be solved by the SDR technique [46]. To start
with, we relax the above optimization problem by dropping
the rank-1 constraint, yielding
max
x,Xtr/parenleftBig
XˆU0/parenrightBig
s.t.˜X/followsequal0
12(b)and12(c). (27)
Problem (27) is convex and can be optimally solved. Here,
we deﬁne X∗andx∗as the approximate solution to the
problem above. By substituting the X∗in the objective
function in (25), the optimal objective value is an upper
bound of the optimal value in problem (12). Note that the
computational complexity of solving problem (27) is given asO/parenleftbig
N
4
T√
2NT+K/parenrightbig
[43]. For clarity, the computational com-
plexities of proposed algorithms in Sec. III are summarized in
Table I.
Remark 2: In problem (27), the constraint ˜X/followsequal0implies
X/followsequalxxH. Based on the relaxations above, we have the
following inequalities
tr/parenleftBig
X∗ˆU0/parenrightBig
≥tr/parenleftBig
x∗x∗HˆU0/parenrightBig
≥x∗HΦ(x∗)x∗
Therefore, the objective value in (27) is larger than the
achievable SINR rad, of which performance is presented as the
upper bound of radar receive SINR in our simulation results.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 7 页 ---
7244 IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 21, NO. 9, SEPTEMBER 2022
TABLE I
COMPLEXITY ANALYSIS
Algorithm 2 The Proposed FP Algorithm for Solving Problem
(12)
Input: P0,hk,σ2
Ck,σ2
R,θi,θ0,α0,bi,Γk,∀k,∀i,ε > 0,a n d
the maximum iteration number mmax
Output: x
1. Reformulate the objective function as given in (21).
2. Initialize x0∈D randomly, m=1.
while m≤mmax and/vextendsingle/vextendsingleSINRm
rad−SINRm−1
rad/vextendsingle/vextendsingle≥εdo
3. Solve problem (21) to obtain the optimal waveform xm.
4. Obtain the receive beamformer wmby substituting xm
in (13).
5. Update uby (22).
6.m=m+1.
end while
IV . SINR radMAXIMIZATION WITHTARGET LOCATION
UNCERTAINTY
In a practical target tracking s cenario, the target location
is not perfectly known to the BS due to its movement and
random ﬂuctuation, and we therefore consider the scenario
where a rough estimation of the target’s angle is available atthe BS. That is, the target is assumed to locate in an uncertain
angular interval. In the following waveform design, we aim to
maximize the minimum SINR
radwith regard to all possible
locations within the interval, while taking CI technique and
power budget into account. Finally, an efﬁcient solver is
proposed to tackle the worst-case optimization problem.
A. Problem Formulation
Let us denote the uncertain interval as Ψ=
[θ0−Δθ,θ0+Δθ]. It is noteworthy that the target from
every possible direction should be taken into account
when formulating the optimization problem. To this end,we therefore consider the following worst-case problem,
which is to maximize the minimum SINR
radwith respect
to all the possible target locations within Ψ.F o rt h es a k eo f
simplicity, let θp∈card(Ψ) denote the p-th possible location
in the given region, where card (·)represents the cardinality
of(·).
max
xmin
θp∈card(Ψ)μ/vextendsingle/vextendsinglewHU(θp)x/vextendsingle/vextendsingle2
wH(Σ(x)+INR)w
s.t.12 (b)and12 (c). (28)
Note that the problem above is non-convex since the point-
wise maximum of concave functions is not convex. In the fol-
lowing subsection, we will work on solving the problem (28).B. Efﬁcient Solver
As is detailed in [44], the straightforward extension of
Dinkelbach’s transform which is deployed in Section III does
not guarantee the equivalence to problem (28). Thus, we give
the equivalent quadratic transformation of the the max-min-
ratio problem (28), which is rewritten as
max
x,umin
βp∈card(Ψ)2up/radicalBig
μ|wHU(θp)x|2
−u2
pwH(Σ(x)+INR)w
s.t.12 (b)and12 (c). (29)
Here, we denote uas a collection of variables {u1,···,
uP},up∈R. The objective above is a sequence of ratios
forθp∈card(Ψ). To proceed,2we rewrite problem (29) in
an epigraph form by introducing the variable a, a∈R,w h i c h
yields the following formulation
max
x,u,aa (30a)
s.t.2up/radicalBig
μ|wHU(θp)x|2−u2
pwH(Σ(x)+INR)w≥a,
∀θp∈card(Ψ) (30b)
12 (b)and12 (c). (30c)
By observing problem (30), it is noted that the constraint (30b)
is non-convex. To tackle the pr oblem, likewise, we substitute
μ/vextendsingle/vextendsinglewHU(θp)x/vextendsingle/vextendsingle2in the ﬁrst term of (30b) with its ﬁrst-order
Taylor expansion approximation with respective to xatx/prime∈D
as is given in (19), which is expressed as
max
x,u,aa
s.t.2up/radicalbigg
μRe/parenleftBig/parenleftbig
2/parenleftbig
x/primeHUH(θp)w/parenrightbig
UH(θ0)w/parenrightbigH(x−x/prime)/parenrightBig
−u2
pwH(Σ(x)+INR)w≥a,∀θp∈card(Ψ)
12 (b)and12 (c). (31)
It is noted that at the m-th iteration, x/primein problem (31) denotes
xm−1∈D, which is the point obtained at the (m−1)-th
iteration. When the optimal waveform xis obtained, the
variable upcan be updated by the following closed form as
um+1
p=/radicalBig
μ|wHU(θp)xm|2
wH(Σ(xm)+INR)w. (32)
Now, problem (31) can be solved by interior point
methods at a worst-case computational complexity of
O/parenleftbig
N3
T√
Ψ0+K+1/parenrightbig
at each iteration [47], where we denote
Ψ0as the number of elements in card (Ψ). For clarity, the pro-
posed method of solving (28) is summarized in Algorithm 3.
V. C I P RECODING WITHDESTRUCTIVE INTERFERENCE TO
THE RADAR RECEIVER
In this section, we consider the information transmission
security of the DFRC system. We assume that the communi-
cation users are legitimate, and treat the point-like target as a
2As given in the expression (4), it can be found that the objective function
is independent with the amplitude coefﬁcient α0, therefore, when the target
location is imperfectly known, the uncertainty of amplitude can be neglected
in the problem formulation.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 8 页 ---
SUet al. : SECURE DFRC TRANSMISSION: EXPLOITING INTERFERENC E FOR RESILIENCE AGAINST TARGET EA VESDROPPING 7245
Algorithm 3 The Proposed Algorithm f or Solving Multiple-
Ratio FP Problem (28)
Input: P0,hk,σ2
Ck,σ2
R,θi,θ0,α0,bi,Γk,Δθ,∀k,∀i,ε > 0,
and the maximum iteration number mmax
Output: x
1. Reformulate the problem by (29).
2. Transform the problem to epigraph form following (30).
3. Reformulate the non-convex constraint by (31).
4. Initialize x0∈D randomly, m=1.
while m≤mmax and||um−um−1|| ≥εdo
5. Solve problem (31) to obtain the optimal waveform xm.
6. Obtain the receive beamformer wmby substituting xm
in (13).
7. Update uby (32).
8.m=m+1.
end while
Fig. 3. The constructive and destructive region division for QPSK.
potential eavesdropper which might surveille the information
from BS to CUs. Accordingly, in the following design, we aim
to maximize the SINR at radar receiver like the proposed
formulation in Section III and Section IV, while conﬁning
the received signal at the target into the destructive regionof the constellation, in order to ensure the PHY security for
DFRC transmission. This problem will be studied under the
circumstances that target loca tion is known to the BS perfectly
and imperfectly, respectively.
A. With Knowledge of Precise Target Location
In prior work with respect to DM technique, such as
algorithms proposed in [37], the problems are designed based
on the CSI of legitimate users, where the symbols received
by potential eavesdroppers are scrambled due to the channel
disparity. However, PHY security cannot be explicitly guar-
anteed in this way. To be speciﬁc, Taking QPSK modulationas an example, the intended symbol can be intercepted with
a
1
4probability at the target when the target’s channel is
independent with the CUs’ channels, while more importantly,the probability of the target intercepting increases when the
target and CUs’ channels are correlated. The simulation result
will be shown in Section VI.
While the CI-based precoding guarantees low SER at CUs,
we still need to focus on the detection performance at the
target in order to prevent the transmit information from being
decoded. Thus, the following problem is designed to improvethe SER at the target. In detail, we deﬁne the region out
of the constructive region as destructive region and aims at
restricting the received signal of the potential eavesdropper in
the destructive area.
We ﬁrstly take s
1as a reference. Likewise, the received
noise-excluding signal at the target can be expressed as
˜yR=(yR−e)s∗
1
|s1|=α0aH
t(θ0)xs∗
1
=α0˜aH
t(θ0)x, (33)
where ˜aH
t(θ0)=aH
t(θ0)s∗
1. Accordingly, the destructive
region can be described by
|Im(˜yR)|≥/parenleftbigg
Re (˜yR)−/radicalBig
σ2
TΓT/parenrightbigg
tanφ. (34)
where the scalar ΓTdenotes the desired maximum SNR for
the potential eavesdropper and/radicalbig
σ2
TΓTcorresponds to γein
Fig. 3. As illustrated in Fig. 3, the destructive region can be
divided to three zones and the inequality (34) holds when any
one of the following constraints is fulﬁlled.
zone1:
Re/parenleftbig
α0˜aH
t(θ0)x/parenrightbig
−/radicalBig
σ2
TΓT≤0 (35a)
zone2:
Im/parenleftbig
α0˜aH
t(θ0)x/parenrightbig
≥/parenleftbigg
Re/parenleftbig
α0˜aH
t(θ0)x/parenrightbig
−/radicalBig
σ2
TΓT/parenrightbigg
tanφ
andRe/parenleftbig
α0˜aH
t(θ0)x/parenrightbig
>/radicalBig
σ2
TΓT (35b)
zone3:
−Im/parenleftbig
α0˜aH
t(θ0)x/parenrightbig
≥/parenleftbigg
Re/parenleftbig
α0˜aH
t(θ0)x/parenrightbig
−/radicalBig
σ2
TΓT/parenrightbigg
tanφ
andRe/parenleftbig
α0˜aH
t(θ0)x/parenrightbig
>/radicalBig
σ2
TΓT. (35c)
For simplicity, we denote (35) as destructive interference (DI)
constraints. By taking the full region of destructive interference
into consideration, the optimization problem can be formulated
as
max
xμ/vextendsingle/vextendsinglewHU(θ0)x/vextendsingle/vextendsingle2
wH(Σ(x)+INR)w
s.t.12 (b)and12 (c)
35 (a)or35 (b)or35 (c). (36)
Note that problem (36) is again an FP problem, which can be
converted to
max
xμg(x)−uwH(Σ(x)+INR)w
s.t.12 (b)and12 (c)
35 (a)or35 (b)or35 (c). (37)
One step further, since all of the constraints given in (35) are
linear, the reformulation above can be tackled following the
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 9 页 ---
7246 IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 21, NO. 9, SEPTEMBER 2022
solving method proposed in Section III-C. Then, the formu-
lation (37) is converted into a convex optimization problem
which includes three subproblems. By solving the problems
above, we can obtain optimal waveforms x∗
1,x∗
2,x∗
3. Then,
we substitute each of them in the objective function, the one
resulting in maximum SINR radwill be the ﬁnal solution to
problem (36).
B. With Target Location Uncertainty
In this subsection, we study the scenario where target
location is known imperfectly. Similar to Section IV, the
target is assumed to locate within a given angular interval
Ψ=[ θ0−Δθ,θ0+Δθ]andβp∈card(Ψ) denotes the
p-th possible target angle. In order to guarantee the secrecy,
we conﬁne the received signal a t every possible angle in the
destructive area. Hence, the problem is given as follows
max
xmin
θp∈card(Ψ)μ/vextendsingle/vextendsinglewHU(θp)x/vextendsingle/vextendsingle2
wH(Σ(x)+INR)w(38a)
s.t./bardblx/bardbl2≤P0 (38b)/vextendsingle/vextendsingle/vextendsingleIm/parenleftBig
˜hH
kx/parenrightBig/vextendsingle/vextendsingle/vextendsingle≤/parenleftBig
Re/parenleftBig
˜hH
kx/parenrightBig
−/radicalBig
σ2
CkΓk/parenrightBig
tanφ,∀k
(38c)/vextendsingle/vextendsingleIm/parenleftbig
α0˜aH
t(βp)x/parenrightbig/vextendsingle/vextendsingle
≥/parenleftbigg
Re/parenleftbig
α0˜aH
t(βp)x/parenrightbig
−/radicalBig
σ2
TΓT/parenrightbigg
tanφ,∀p, (38d)
which is however not convex. When we take the possi-
ble target locations into account, the approach proposed
in Section V-A would be complicated and time consuming.Therefore, in order to reduce the computational complexity,
we solve problem (38) following the given steps below. Firstly,
it is noteworthy that (38d) holds when any one of the following
inequalities is satisﬁed for each p.
Im/parenleftbig
α
0˜aH
t(βp)x/parenrightbig
≥/parenleftbigg
Re/parenleftbig
α0˜aH
t(βp)x/parenrightbig
−/radicalBig
σ2
TΓT/parenrightbigg
tanφ,∀p (39a)
−Im/parenleftbig
α0˜aH
t(βp)x/parenrightbig
≥/parenleftbigg
Re/parenleftbig
α0˜aH
t(βp)x/parenrightbig
−/radicalBig
σ2
TΓT/parenrightbigg
tanφ,∀p. (39b)
One step further, according t o big-M continuous relaxation
method proposed in [48], we introduce binary variables ηp∈
{0,1},∀pand a sufﬁciently large constant Ω>0,t h e
reformulated either-or constraints in (39) can be converted to
/parenleftbigg
Re/parenleftbig
α0˜aH
t(βp)x/parenrightbig
−/radicalBig
σ2
TΓT/parenrightbigg
tanφ−Im/parenleftbig
α0˜aH
t(βp)x/parenrightbig
−ηpΩ≤0,∀p (40a)/parenleftbigg
Re/parenleftbig
α0˜aH
t(βp)x/parenrightbig
−/radicalBig
σ2
TΓT/parenrightbigg
tanφ+I m/parenleftbig
α0˜aH
t(βp)x/parenrightbig
−(1−ηp)Ω≤0,∀p. (40b)
Note that in the either-or constraints above, (40a) is active
whenηp=0, which corresponds to (39a), and (40b) is fulﬁlled
anyway due to the sufﬁciently large constant Ω. Likewise,when ηp=1, (40b) is activated. Accordingly, problem (38)
can be recast as [39]
max
xmin
θp∈card(Ψ)μ/vextendsingle/vextendsinglewHU(θp)x/vextendsingle/vextendsingle2
wH(Σ(x)+INR)w
s.t.12 (b),12 (c),40 (a)and40 (b)
ηp∈{0,1},∀p, (41)
We ﬁrstly reformulate the problem into the following equiva-
lent form
min
xmax
βp∈card(Ψ)wH(Σ(x)+INR)w
μ|wHU(βp)x|2
s.t.12 (b),12 (c),40 (a)and40 (b)
ηp∈{0,1},∀p, (42)
Henceforth, we will work on solving (42). Based on the
formulation proposed in Section IV, we ﬁrstly give the epi-
graph form of problem (42), which is shown in (43), at thebottom of the next page. It is noted that (43) is a mixed-integer
optimization problem with no polynomial-time computational
complexity. To reach a lower complexity, we give the equiv-alent form of the above problem as [39], [49]
min
x,ηp,aa+ω/parenleftBigg2Δθ+1/summationdisplay
p=1ηp−2Δθ+1/summationdisplay
p=1η2
p/parenrightBigg
s.t.43 (b)
12 (b),12 (c),40 (a)and40 (b)
0≤ηp≤1,∀p, (44)
where ωdenotes a large penalty factor for penalizing the
objective function for any ηpthat is not equal to 0 or 1. The
problem above can solved by successive convex approxima-
tion (SCA) method ﬁrstly aiming to obtain the optimal ηp.
Then,x,acan be tackled by optimal ηpiteratively following
FP algorithm. To start with, we initially let s(ηp)=2Δθ+1/summationtext
p=1η2
p,
and the ﬁrst-order Taylor expansion of s(ηp)is given as
˜s/parenleftbig
ηp,η/prime
p/parenrightbig
≈2Δθ+1/summationdisplay
p=1/parenleftbig
η/prime
p/parenrightbig2+22Δθ+1/summationdisplay
p=1η/prime
p/parenleftbig
ηp−η/prime
p/parenrightbig
.(45)
Herewith, problem (44) is solvable by adopting SCA algorithm
so as to generate the optimal ηp. Eventually, the reformulation
is given in (47), shown at the bottom of the next page, where
nis the iteration index of ηp. To tackle this problem, ηpis
updated until convergence, and then the optimal waveform x
can be obtained by updating up,∀piteratively by
um+1
p=/radicalbig
wH(Σ(xm)+INR)w
μ|wHU(θp)xm|2. (46)
Let us denote the number of itera tions required for generating
the optimal ηpbyNn. Accordingly, the total complexity of
can be given as O/parenleftbig
4NnN6
TΨ0/parenrightbig
by reserving the highest order
term [47]. For simplicity, the proposed method of solving
problem (41) is summarized in Algorithm 4.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 10 页 ---
SUet al. : SECURE DFRC TRANSMISSION: EXPLOITING INTERFERENC E FOR RESILIENCE AGAINST TARGET EA VESDROPPING 7247
Algorithm 4 The Proposed Algorithm for Solving the Mixed-
Integer Optimization Problem (41)
Input: P0,hk,σ2
Ck,σ2
R,θi,bi,θ0,α0,Δθ,Γk,∀k,∀i,ε >
0,ε0>0, and the maximum iteration number mmax
Output: x
1. Reformulate the problem by (43).
2. Transform the problem to epigraph form following (31).
3. Initialize η0
p∈[0,1],x0∈D randomly, n=1,m=1.
while m≤mmax and||um−um−1|| ≥εdo
4. When xis ﬁxed, solve problem (47) iteratively by
updating ηn
puntil/vextendsingle/vextendsingle/vextendsingle/vextendsingle/vextendsingle2Δθ+1/summationtext
p=1ηn−1
p/parenleftbig
ηp−ηn−1
p/parenrightbig/vextendsingle/vextendsingle/vextendsingle/vextendsingle/vextendsingle<ε
0.
5. Fix the optimal η∗
p, solve problem (47) to obtain the
optimal waveform xm.
6. Obtain the receive beamformer wmby substituting xm
in (13).
7. Update uby (46).
8.m=m+1.
end while
VI. N UMERICAL RESULTS
In this section, we evaluate the proposed methods via Monte
Carlo based simulation results given as follows. We assume
that both the DFRC BS and the radar receiver are equipped
with uniform linear arrays (ULAs) with the same number
of elements with half-wavele ngth spacing between adjacent
antennas. In the following simulations, the power budget isset as P
0=3 0 dBm and the Rician coefﬁcient is given as
vk=1. The target is located at θ0=0◦with a reﬂecting
power of |α0|2=1 0 dB and clutter sources are located at
θ1=−50◦,θ2=−20◦,θ3=2 0◦,θ4=5 0◦reﬂecting a power
of|α1|2=|α2|2=|α3|2=|α4|2=2 0 dB. The SNR threshold
ΓTis set as −1dB as default unless it is presented speciﬁcally.
A. The Resultant Beampattern
The resultant beampattern is ﬁrstly given in Fig. 4 with
different number of DFRC BS antennas, where we set theFig. 4. Optimized beampatterns with different numbers of DFRC BS
antennas, where the beamformer design approach proposed in [50] is set asbenchmarks and K=5.
DFRC precoder design proposed by Chen et al. [50] as the
benchmark, namely ‘DFRC-PD’, and the proposed methods
in this paper are denoted as ‘DFRC-CI’ and ‘DFRC-CI-DI’ in
our results, respectively. The SNR threshold Γk,∀kis ﬁxed as
15dB. The nulls at the locations of clutter sources are clearlyillustrated. It can be observed that the performance of beam-
pattern gets better from the viewpoint of the radar, and the
main beam width decreases with the increasing number of BSantennas. Additionally, comparing with the beamformer design
method proposed in [50], the peak to sidelobe ratio (PSLR)
of the resultant beampattern generated from our proposed
waveform design method is higher, and it can be found that the
null in the main beam is mitigated in our design. It can alsobe noted that the beampattern generated by the CI-DI method
overlaps with the one obtained by employing CI constraints
only.
Furthermore, when the radar target location is not known
to the BS perfectly, the generated beampattern is shown
as Fig. 5 with different angular interval of possible target
locations. It is noteworthy that the power gain of main beam
reduces with the expansion of target location uncertaintyinterval.
min
x,ηp,aa (43a)
s.t.2up/radicalBig
wH(Σ(x)+INR)w−u2
pμRe/parenleftbigg/parenleftBig
2/parenleftBig
x/primeHUH(θp)w/parenrightBig
UH(θ0)w/parenrightBigH
(x−x/prime)/parenrightbigg
≤a,∀θp∈card(Ψ) (43b)
12 (b),12 (c),40 (a)and40 (b) (43c)
ηp∈{0,1},∀p. (43d)
min
x,ηp,aa+ω/parenleftBigg2Δθ+1/summationdisplay
p=1ηp−˜s/parenleftbig
ηp,ηn−1
p/parenrightbig/parenrightBigg
s.t.2up/radicalBig
wH(Σ(x)+INR)w−u2
pμRe/parenleftbigg/parenleftBig
2/parenleftBig
xm−1HUH(θp)w/parenrightBig
UH(θ0)w/parenrightBigH/parenleftbig
x−xm−1/parenrightbig/parenrightbigg
≤a,∀p
12 (b),12 (c),40 (a)and40 (b)
0≤ηp≤1,∀p. (47)
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 11 页 ---
7248 IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 21, NO. 9, SEPTEMBER 2022
Fig. 5. The resultant beampattern with different angular interval. NT=
NR=1 0,K=5.
Fig. 6. Convergence analysis.
B. Radar SINR Performance
In this subsection, we evaluate the performance of radar
receive SINR versus SNR threshold of the communication
system, number of CUs, and target location uncertainty. Firstly,
Fig. 6 illustrates the convergence analysis of the proposed
methods. It can be found that the algorithm converges fast
when the target location is precisely known to the BS.The optimal solution is generated with 5 iterations with the
knowledge of precise target location, while it converges with
around 9 iterations when the target location is uncertain.
The average performance of the tradeoff between the given
SNR threshold of CU and the SINR of radar is illustrated
in Fig. 7, including benchmark algorithms. Speciﬁcally, withrespect to the benchmarks, SQ denotes the method proposed
in [40], SDR without Gaussian Rand denotes the upper bound
of the objective function as we have given in Section III, D.
To satisfy the rank-1 constraint, Gaussian randomization pro-
cedure is commonly required, and the simulation result ofwhich is given in Fig. 7 denoted as ‘SDR after Gaussian
Rand’. It is found that the received SINR of radar increases
with the growth of Γ
kwhen we adopt SQ method and theFig. 7. The performance of radar SINR versus CU’s SNR with different
solving methods, NT=NR=1 0,K=5.
Fig. 8. The received SINR of radar versus the number of CUs with different
number of DFRC BS antennas.
Fig. 9. Average SINR of radar versus angular interval of target location
uncertainty for different angluar differences Δφbetween the target and
communication users, NT=NR=1 0,K=5.
SDR technique after Gaussian randomization procedure, while
SINR raddecreases when we deploy the other methods. This is
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 12 页 ---
SUet al. : SECURE DFRC TRANSMISSION: EXPLOITING INTERFERENC E FOR RESILIENCE AGAINST TARGET EA VESDROPPING 7249
Fig. 10. The constellation of received signals with DI constraints when the ta rget location is known to the BS precisely, where the received signal at C Us
and the target are denoted by blue dots and red dot s, respectively. QPSK and 8PSK modulated signal, NT=NR=1 0,K=5.
for the reason that the optimized system power increases with
the growth of Γk, which is less than the given power budget
P0, under the circumstance when SQ method or SDR solver
with Gaussian randomization procedure is deployed. That is,
the SQ approach and SDR after Gaussian randomization fail to
formulate an appropriate tradeo ff between the radar system and
the communication system. Moreover, the proposed waveform
design method reaches a higher SINR radcomparing with the
beamformer design in [50], especially when Γkis above 22dB.
Furthermore, the radar recei ve SINR is deteriorated when
the destructive interference constraints are taken into account.Fig. 8 depicts the radar SINR versus the number of CUs with
different number of BS antennas, which reveals the tradeoff
between radar and communication system. It can be also noted
that the receive SINR of the radar system gets lower when DI
constraints are taken into account.
In Fig. 9, we explore the effect of correlation between
the target and CU LoS channels in the radar eavesdropping
performance with various angular uncertainty interval Δθ
when the angle difference between the CU and the target
(i.e. ‘Δφ’i nF i g .9 )v a r i e sf r o m 0.5
◦to25◦. It indicates the
tradeoff between SINR radand target uncertainty. In addition,
it can be found that the radar SINR is slightly impacted by
the CU location when the angle difference is larger than 15◦.
C. Communications Security Performance
The distribution of received symbols at CUs (denoted by
blue markers) and the target (denoted by blue markers) isshown in Fig. 10, where QPSK and 8PSK modulated symbols
are taken as examples. It illustrates that the received symbols
are randomized at the target when only CI is considered,Fig. 11. SER of CU versus SNR threshold Γkwith different number of
antennas equipped by BS when target location is known precisely. K=5.
while the signals received by the target are conveyed into the
destructive region when deploying DI constraints. In Fig.11,the average SER of CUs versus threshold SNR Γ
kis depicted
when the BS is equipped with different number of antennas,
with and without DI constraints, respectively. It is found thatthe SER decreases with the growth of Γ
k. Furthermore, when
the received symbols at the target are constructed in the
destructive region, CUs decode the received symbols with a
lower probability, which means the SER performance of the
CUs is deteriorated to some extend when DI constraints aretaken into account.
Furthermore, in Fig. 12, we take one CU as a reference
to evaluate the SER performance of the radar target versus
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 13 页 ---
7250 IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 21, NO. 9, SEPTEMBER 2022
Fig. 12. SER at the target versus the a ngle difference between the target
and the CU with and without DI constraint when target location is known
precisely. K=5,NT=NR=1 0 .
angle difference between the target and the CU. It is noted that
target decode probability converges to 0.75 with the increasing
angular difference from the CU to the target when only CI
constraint is considered. For generality, the simulation result
is obtained on average of target location ranging in the angularinterval/bracketleftbig
−
π
2,π
2/bracketrightbig
. Moreover, it can be found that the SER at the
target increases obviously when the DI constraints are consid-
ered, which is close to 1 when the angle difference is gettinglager. Thus, it indicates that the deployment of DI method
prevents the radar target from eavesdropping communication
data efﬁciently.
VII. C
ONCLUSION
In this paper, we have considered the problem of secure
DFRC transmission and proposed a solution based on CI.
We have further extended our approach to enforce destructive
interference to the target as potential eavesdropper, to furtherenhance security. Our numerical results have demonstrated
that FP algorithms outperform the results generated from
benchmark algorithms. Moreover, we observe that the DIconstraints can effectively d eteriorate the SER performance
at the radar target, thus providing a secure solution for the
unique DFRC scenarios.
Given the fact that the orthogonal frequency division
multiplexing (OFDM) technique is the key enabler for 4Gand 5G wireless networks, it is of interest to conduct the
OFDM waveform in the secure DFRC system to overcome
the frequency-selective fading of the wideband MIMO sys-tems with one multi-antenna DFRC BS and multiple single-
antenna user equipments, which will be studied in our future
work.
R
EFERENCES
[1] S. Kinney. (2018). Update on Global 5G Spectrum Auctions . Accessed:
Aug. 21, 2018. [Online]. Available: http://https://www.rcrwireless.com/20180821/5g/5g-spectrum-auctions[2] Y . Zhang, Q. Li, L. Huang, and J. Song, “Waveform design for joint
radar-communication system with multi-user based on MIMO radar,” inProc. IEEE Radar Conf. (RadarConf) , May 2017, pp. 0415–0418.
[3] F. Liu, C. Masouros, A. P. Petropulu, H. Grifﬁths, and L. Hanzo, “Joint
radar and communication design: Appli cations, state-of-the-art, and the
road ahead,” IEEE Trans. Commun. , vol. 68, no. 6, pp. 3834–3862,
Jun. 2020.
[ 4 ]F .L i u ,C .M a s o u r o s ,A .L i ,H .S u n ,a n dL .H a n z o ,“ M U - M I M O
communications with MIMO radar: From co-existence to joint transmis-
sion,” IEEE Trans. Wireless Commun. , vol. 17, no. 4, pp. 2755–2770,
Apr. 2018.
[5] F. Liu, L. Zhou, C. Masouros, A. Li, W. Luo, and A. Petropulu,
“Toward dual-functional radar-communication systems: Optimal wave-
form design,” IEEE Trans. Signal Process. , vol. 66, no. 16,
pp. 4264–4279, Aug. 2018.
[6] X. Yuan et al. , “Spatio-temporal power optimization for MIMO joint
communication and radio sensing systems with training overhead,” IEEE
Trans. Veh. Technol. , vol. 70, no. 1, pp. 514–528, Jan. 2021.
[7] A. Hassanien, M. G. Amin, E. Aboutanios, and B. Himed, “Dual-
function radar communication systems: A solution to the spectrum
congestion problem,” IEEE Signal Process. Mag. , vol. 36, no. 5,
pp. 115–126, Sep. 2019.
[8] A. D. Wyner, “The wire-tap channel,” Bell Syst. Tech. J. , vol. 54, no. 8,
pp. 1355–1387, May 1975.
[9] T. Lv, H. Gao, and S. Yang, “Secrecy transmit beamforming for
heterogeneous networks,” IEEE J. Sel. Areas Commun. , vol. 33, no. 6,
pp. 1154–1170, Jun. 2015.
[10] S. Gong, C. Xing, Z. Fei, and S. Ma, “Millimeter-wave secrecy
beamforming designs for two-way amplify-and-forward MIMO relaying
networks,” IEEE Trans. Veh. Technol. , vol. 66, no. 3, pp. 2059–2071,
Mar. 2017.
[11] L. Liu, R. Zhang, and K.-C. Chua , “Secrecy wireless information and
power transfer with MISO beamforming,” IEEE Trans. Signal Process. ,
vol. 62, no. 7, pp. 1850–1863, Apr. 2014.
[12] Y . Yang, C. Sun, H. Zhao, H. Long, and W. Wang, “Algorithms
for secrecy guarantee with null space beamforming in two-way relaynetworks,” IEEE Trans. Signal Process. , vol. 62, no. 8, pp. 2111–2126,
Apr. 2014.
[13] W. Zhang, J. Chen, Y . Kuo, and Y . Z hou, “Artiﬁcial-noise-aided optimal
beamforming in layered physical layer security,” IEEE Commun. Lett. ,
vol. 23, no. 1, pp. 72–75, Jan. 2019.
[14] Z. Kong, S. Yang, D. Wang, and L. Hanzo, “Robust beamforming and
jamming for enhancing the physical la yer security of full duplex radios,”
IEEE Trans. Inf. Forensics Security , vol. 14, no. 12, pp. 3151–3159,
Dec. 2019.
[15] W. Wang, K. C. Teh, and K. Li, “Artiﬁcial noise aided physical
layer security in multi-antenna small-cell networks,” IEEE Trans. Inf.
Forensics Security , vol. 12, no. 6, pp. 1470–1482, Jun. 2017.
[16] N. Su, F. Liu, and C. Masouros, “ Secure radar-communication systems
with malicious targets: Integrating radar, communications and jam-
ming functionalities,” IEEE Trans. Wireless Commun. , vol. 20, no. 1,
pp. 83–95, Jan. 2021.
[17] Q. Li, M. Hong, H.-T. Wai, Y .-F. Liu, W.-K. Ma, and Z.-Q. Luo,
“Transmit solutions for MIMO wiretap channels using alternating opti-
mization,” IEEE J. Sel. Areas Commun. , vol. 31, no. 9, pp. 1714–1727,
Sep. 2013.
[18] H. Wang et al. , “Intelligent reﬂecting surfaces assisted secure transmis-
sion without eavesdropper’s CSI,”
IEEE Signal Process. Lett. , vol. 27,
pp. 1300–1304, 2020.
[19] H. He, P. Ren, Q. Du, and H. Lin, “Joint feedback and artiﬁcial
noise design for secure communications over fading channels with-out eavesdropper’s CSI,” IEEE Trans. Veh. Technol. , vol. 66, no. 12,
pp. 11414–11418, Dec. 2017.
[20] Z. Wei, C. Masouros, and F. Liu, “Secure directional modulation with
few-bit phase shifters: Optimal and iterative-closed-form designs,” IEEE
Trans. Commun. , vol. 69, no. 1, pp. 486–500, Jan. 2021.
[21] S. Yan, N. Yang, G. Geraci, R. Malaney, and J. Yuan, “Optimization
of code rates in SISOME wiretap channels,” IEEE Trans. Wireless
Commun. , vol. 14, no. 11, pp. 6377–6388, Nov. 2015.
[22] E. Baghdady, “Directional si gnal modulation by means of switched
spaced antennas,” IEEE Trans. Commun. , vol. 38, no. 4, pp. 399–403,
Apr. 1990.
[23] F. Shu et al. , “Directional modulation: A physical-layer security solution
to B5G and future wireless networks,” IEEE Netw. , vol. 34, no. 2,
pp. 210–216, Mar. 2020.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 14 页 ---
SUet al. : SECURE DFRC TRANSMISSION: EXPLOITING INTERFERENC E FOR RESILIENCE AGAINST TARGET EA VESDROPPING 7251
[24] C. Masouros, M. Sellathurai, and T. Ratnarajah, “Vector perturbation
based on symbol scaling for limited feedback MISO downlinks,” IEEE
Trans. Signal Process. , vol. 62, no. 3, pp. 562–571, Feb. 2014.
[25] C. Masouros and G. Zheng, “Exploiting known interference
as green signal power for downlink beamforming optimization,”
IEEE Trans. Signal Process. , vol. 63, no. 14, pp. 3628–3640,
Jul. 2015.
[26] F. Liu, C. Masouros, A. Li, T. Ratnarajah, and J. Zhou, “MIMO
radar and cellular coexistence: A power-efﬁcient approach enabled by
interference exploitation,” IEEE Trans. Signal Process. , vol. 66, no. 14,
pp. 3681–3695, Jul. 2018.
[27] M. R. A. Khandaker, C. Masouros, K. Wong, and S. Timotheou,
“Secure SWIPT by exploiting constructive interference and artiﬁ-cial noise,” IEEE Trans. Commun. , vol. 67, no. 2, pp. 1326–1340,
Feb. 2019.
[28] M. R. A. Khandaker, C. Masouros, and K.-K. Wong, “Constructive
interference based secure precoding: A new dimension in physicallayer security,” IEEE Trans. Inf. Forensics Security , vol. 13, no. 9,
pp. 2256–2268, Sep. 2018.
[29] Z. Wei, C. Masouros, and F. Liu, “I nterference exploitation based secure
transmission for distributed antenna systems,” in Proc. ICC - IEEE Int.
Conf. Commun. (ICC) , May 2019, pp. 1–6.
[30] Z. Wei and C. Masouros, “Robust secure precoding and antenna selec-
tion: A probabilistic optimization approach for interference exploita-tion,” in Proc. IEEE Int. Conf. Acoust., Speech Signal Process.
(ICASSP) , May 2019, pp. 2442–2446.
[31] Z. Wei and C. Masouros, “Device-centric distributed antenna trans-
mission: Secure precoding and ante nna selection with interference
exploitation,” IEEE Internet Things J. , vol. 7, no. 3, pp. 2293–2308,
Mar. 2020.
[32] A. Deligiannis, A. Da niyan, S. Lambotharan, and J. A. Chambers,
“Secrecy rate optimizations for MIMO communication radar,” IEEE
Trans. Aerosp. Electron. Syst. , vol. 54, no. 5, pp. 2481–2492,
Oct. 2018.
[33] B. K. Chalise and M. G. Amin, “Performance tradeoff in a uniﬁed system
of communications and passive radar: A secrecy capacity approach,”
Digit. Signal Process. , vol. 82, pp. 282–293, Nov. 2018.
[34] A. Li, C. Masouros, X. Liao, Y . Li, and B. Vucetic, “Multiplexing more
data streams in the MU-MISO down link by interference exploitation
precoding,” in Proc. IEEE Wireless Commun. Netw. Conf. (WCNC) ,
May 2020, pp. 1–6.
[35] L. Zhao, G. Geraci, T. Yang, D. W. K. Ng, and J. Yuan, “A tone-
based AoA estimation and multiuser precoding for millimeter wave
massive MIMO,” IEEE Trans. Commun. , vol. 65, no. 12, pp. 5209–5225,
Dec. 2017.
[36] X. Hu, C. Zhong, X. Chen, W. Xu, and Z. Zhang, “Cluster grouping
and power control for angle-domain mmWave MIMO NOMA systems,”
IEEE J. Sel. Topics Signal Process. , vol. 13, no. 5, pp. 1167–1180,
Sep. 2019.
[37] A. Kalantari, M. Soltanalian, S. Ma leki, S. Chatzinotas, and B. Otter-
sten, “Directional modulation via symbol-level precoding: A way toenhance security,” IEEE J. Sel. Topics Signal Process. , vol. 10, no. 8,
pp. 1478–1493, Dec. 2016.
[38] C. Masouros and E. Alsusa, “Dynamic linear precoding for the
exploitation of known interference in MIMO broadcast systems,”IEEE Trans. Wireless Commun. , vol. 8, no. 3, pp. 1396–1404,
Mar. 2009.
[39] Q. Xu, P. Ren, and A. L. Swindl ehurst, “Rethinking secure pre-
coding via interferenc e exploitation: A smart eavesdropper perspec-
tive,” IEEE Trans. Inf. Forensics Security , vol. 16, pp. 585–600,
2021.
[40] O. Aldayel, V . Monga, and M. Rangaswamy, “Successive QCQP
reﬁnement for MIMO radar waveform design under practical con-
straints,” IEEE Trans. Signal Process. , vol. 64, no. 14, pp. 3760–3774,
Jul. 2016.
[41] G. Cui, H. Li, and M. Rangaswamy, “MIMO radar waveform design
with constant modulus and similarity constraints,” IEEE Trans. Signal
Process. , vol. 62, no. 2, pp. 343–353, Jan. 2014.
[42] M. Grant and S. Boyd, CVX: MATLAB Software for Disciplined Convex
Programming . 2008.
[43] A. Ben-Tal and A. Nemirovski, Lectures on Modern Convex Optimiza-
tion: Analysis, Algorithms, and Engineering Applications . Philadelphia,
PA, USA: SIAM, 2001.[44] K. Shen and W. Yu, “Fractional programming for communication
systems—Part I: Power control and beamforming,” IEEE Trans. Signal
Process. , vol. 66, no. 10, pp. 2616–2630, Mar. 2018.
[45] L. Vandenberghe and S. Boyd, “Semideﬁnite programming,” SIAM Rev. ,
vol. 38, no. 1, pp. 49–95, 1996.
[46] J. Park and S. Boyd, “General he uristics for nonconvex quadratically
constrained quadratic programming,” 2017, arXiv:1703.07870 .
[47] Y . Nesterov and A. Nemirovskii, Interior-Point Polynomial Algorithms
in Convex Programming . Philadelphia, PA, USA: SIAM, 1994.
[48] Y . Cheng, M. Pesavento, and A. Ph ilipp, “Joint network optimization
and downlink beamforming for CoMP transmissions using mixed integer
conic programming,” IEEE Trans. Signal Process. , vol. 61, no. 16,
pp. 3972–3987, Aug. 2013.
[49] D. W. K. Ng, Y . Wu, and R. Schober, “P ower efﬁcient resource allocation
for full-duplex radio distributed antenna networks,” IEEE Trans. Wireless
Commun. , vol. 15, no. 4, pp. 2896–2911, Apr. 2016.
[50] L. Chen, F. Liu, J. Liu, and C. Masouros, “Composite signalling for
DFRC: Dedicated probing signal or not?” 2020, arXiv:2009.03528 .
Nanchi Su (Graduate Student Member, IEEE)
received the B.E. and M.E. degrees from the HarbinInstitute of Technology, Harbin, Heilongjiang,China, in 2015 and 2018, respectively. She is cur-
rently pursuing the Ph.D. degree with the Infor-
mation and Communications Engineering ResearchGroup, Department of Electronic and Electrical
Engineering, University College London, London,
U.K. Her research interests include integrated sens-ing and communication systems, constructive inter-ference design, physical-layer security, radar signal
processing, and convex optimization. She is a TPC Member of various
international conferences, such as IEEE ICC.
Fan Liu (Member, IEEE) r eceived the B.Eng. and
Ph.D. degrees from the Beijing Institute of Tech-nology (BIT), Beijing, China, in 2013 and 2018,respectively.
From 2016 to 2018, he was a Visiting Researcher
at the University College London (UCL), and then asa Marie Curie Research Fellow from 2018 to 2020.
He is currently an Assistant Professor with the
Department of Electrical and Electronic Engineer-ing, Southern University of Science and Technology
(SUSTech). He has ten publications selected as
IEEE ComSoc Besting Readings in ISAC. His research interests include signal
processing and wireless communicati ons, and in particularly in the area of
integrated sensing and communications (ISAC).
He is a member of the IMT-2030 (6G) ISAC Task Group. He was a recipient
of the IEEE Signal Processing Soci ety Young Author Best Paper Award
in 2021, the Best Ph.D. Thesis Award of Chinese Institute of Electronicsin 2019, and the EU Marie Curie Individual Fellowship in 2018. He was
an Organizer and the Co-Chair of numerous workshops, special sessions,
and tutorials in ﬂagship IEEE confer ences, including ICC, GLOBECOM,
ICASSP, and SPAWC. He is the Founding Academic Chair of the IEEE
ComSoc ISAC Emerging Technology Initiative (ISAC-ETI), an Associate
Editor of the IEEE O
PEN JOURNAL OF SIGNAL PROCESSING and the
IEEE C OMMUNICATIONS LETTERS , a Lead Guest Editor of the IEEE
JOURNAL ON SELECTED AREAS IN COMMUNICATIONS , Special Issue on
“Integrated Sensing and Communication,” and a Guest Editor of the IEEE
WIRELESS COMMUNICATIONS , Special Issue on “Integrated Sensing and
Communications for 6G.” He is also the TPC Co-Chair of the Second IEEEJoint Communication and Sensing Symposium (JC&S). He will serve as the
Track Co-Chair for the IEEE WCNC 2024. He was listed in the World’s Top
2% Scientists by Stanford University for citation impact in 2021. He has beennamed as an Exemplary Reviewer of IEEE TWC/TCOM/COMML for ﬁve
times.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 15 页 ---
7252 IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 21, NO. 9, SEPTEMBER 2022
Zhongxiang Wei (Member, IEEE) r eceived the
Ph.D. degree in electrical and electronics engineer-ing from the University of Liverpool, Liverpool,
U.K., in 2017. From March 2016 to March 2017,
he was with the Institution for Infocomm Research,Agency for Science, Technology and Research,Singapore, as a Research Assistant. From March
2018 to March 2021, he was with the Department
of Electrical and Electronics Engineering, UniversityCollege London, as a Research Associate. He is cur-
rently an Associate Professor at Tongji University,
China. He has authored and coauthored more than 60 papers published ontop-tier journals and international confer ences. His research interests include
anonymous communications, constructive interference design, and millimeter
wave communications. He was a recipient of an Exemplary Reviewer of the
IEEE TWC in 2016, the Outstanding Se lf-Financed Students Abroad in 2018,
and the A*STAR Research Attachment Programme (ARAP) in 2016. He has
acted as a TPC Chair/Member or the Session Chair of various international
conferences, such as IEEE ICASSP, ICC, and GLOBECOM.
Ya-Feng Liu (Senior Member, IEEE) r eceived the
B.Sc. degree in applied mathematics from XidianUniversity, Xi’an, China, in 2007, and the Ph.D.degree in computational mathematics from the Chi-
nese Academy of Sciences (CAS), Beijing, China,
in 2012.
During his Ph.D. study, he was supported by
the Academy of Mathematics and Systems Sci-
ence (AMSS), CAS, to visit Prof. Zhi-Quan (Tom)
Luo at the University of Minnesota (Twins Cities)from 2011 to 2012. After his graduation, he joined
the Institute of Computational Mathematics and Scientiﬁc/Engineering Com-
puting, AMSS, CAS, in 2012, where he became an Associate Professorin 2018. His main research interests include non-linear optimization andits applications to signal processing, wi reless communications, and machine
learning. He is an Elected Member of the Signal Processing for Commu-
nications and Networking Technical Committee (SPCOM-TC) of the IEEESignal Processing Society. He received the Best Paper Award from the IEEE
International Conference on Communi cations (ICC) in 2011, the Chen Jingrun
Star Award from the AMSS in 2018, the Science and Technology Award forYoung Scholars from the Operations Re search Society of China in 2018, and
the 15th IEEE ComSoc Asia-Paciﬁc Outstanding Young Researcher Award
in 2020. He currently serves as an Editor for the IEEE T
RANSACTIONS ON
WIRELESS COMMUNICATIONS and an Associate Editor for the IEEE S IGNAL
PROCESSING LETTERS and the Journal of Global Optimization .
Christos Masouros (Senior Member, IEEE)
received the Diploma degree in electrical andcomputer engineering from the University of Patras,
Greece, in 2004, and the M.Sc. by research
and Ph.D. degrees in electrical and electronicengineering from The University of Manchester,Manchester, U.K., in 2006 and 2009, respectively.
In 2008, he was a Research Intern at Philips
Research Labs, U.K. From 2009 to 2010, he wasa Research Associate with The University of
Manchester. From 2010 to 2012, he was a Research
Fellow at Queen’s University Belfast. In 2012, he joined University CollegeLondon as a Lecturer. He has held a R oyal Academy of Engineering
Research Fellowship from 2011 to 2016. Since 2019, he has been a
Full Professor of signal processing and wireless communications at the
Information and Communication Engineering Research Group, Departmentof Electrical and Electronic Engineering, and afﬁliated with the Institute for
Communications and Connected Systems, University College London. His
research interests include wireless communications and signal processing
with particular focus on green communications, large scale antenna systems,integrated sensing and communications , interference mitigation techniques
for MIMO, and multi-carrier communications. He was a recipient of the
Best Paper Awards from the IEEE GlobeCom 2015 and IEEE WCNC2019 conferences and a co-recipient of the 2021 IEEE SPS Young Author
Best Paper Award. He has been recognized as an Exemplary Editor of
the IEEE C
OMMUNICATIONS LETTERS , and an Exemplary Reviewer
of the IEEE T RANSACTIONS ON COMMUNICATIONS . He is an Editor
of IEEE T RANSACTIONS ON WIRELESS COMMUNICATIONS and the
IEEE O PEN JOURNAL OF SIGNAL PROCESSING , and an Editor-at-Large
of IEEE O PEN JOURNAL OF THE COMMUNICATIONS SOCIETY .H eh a s
been an Editor of IEEE T RANSACTIONS ON COMMUNICATIONS and
IEEE C OMMUNICATIONS LETTERS and a Guest Editor of a number
of IEEE J OURNAL ON SELECTED TOPICS IN SIGNAL PROCESSING and
IEEE J OURNAL ON SELECTED AREAS IN COMMUNICATIONS ISSUES .
He is also a Founding Member and the Vice-Chair of the IEEE Emerging
Technology Initiative on Integrated Sensing and Communications, the IEEE
Special Interest Group on Integrated Sensing and Communications (ISAC),
and the Chair of the IEEE Special Interest Group on Energy HarvestingCommunication Networks.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:20 UTC from IEEE Xplore.  Restrictions apply. 

================================================================================


================================================================================
文件: 2024-NSFC-深圳校区-苏南池.pdf
页数: 41
================================================================================


--- 第 1 页 ---
申请代码 F0105
接收部门
收件日期
接收编号 **********
    
国家自然科学基金
申 请 书
（2 0 2 4 版）
资助类别： 青年科学基金项目
亚类说明：
附注说明：
项目名称： 基于物理层安全的通感内生安全架构与网络性能动态适配机制
申 请 人： 苏南池 BRID： 09007.00.62796
办公电话： 0755-26404115
依托单位： 哈尔滨工业大学
通讯地址： 广东省深圳市南山区深圳大学城哈工大校区信息楼L1214室
邮政编码： 518055 单位电话： 0451-86414151
电子邮箱： <EMAIL>
国家自然科学基金委员会NSFC 2024

--- 第 2 页 ---
基本信息
申
请
人
信
息姓        名 苏南池 性别女出生
年月1993年08月 民族汉族
学        位 博士 职称无
是否在站博士后 是 电子邮箱 <EMAIL>
办公电话 0755-26404115 国别或地区 中国
申请人类别 依托单位全职
工   作   单   位 哈尔滨工业大学/哈尔滨工业大学（深圳）
主 要 研 究 领 域 无线通信安全，通信感知一体化，态势感知
依
托
单
位
信
息名        称 哈尔滨工业大学
联   系   人 王雪 电子邮箱 <EMAIL>
电        话 0451-86414151 网站地址 http://kjc.hit.edu.cn/
合
作
研
究
单
位
信
息单 位 名 称
项
目
基
本
信
息项目名称 基于物理层安全的通感内生安全架构与网络性能动态适配机制
英文名称Endogenous Security Architecture and Network Performance Dynamic
Adaptation Mechanism Based on Physical Layer Security for Integrated
Sensing and Communication
资助类别 青年科学基金项目 亚类说明
附注说明
申请代码 F0105.移动通信 F0102.信息系统与系统安全
研究期限 2025年01月01日 -- 2027年12月31日 研究方向：高能效通信
申请直接费用 30.0000万元
研究属性 目标导向类基础研究
中文关键词 高能效资源分配；通信感知融合网络；一体化波形设计；内生安全；网络弹性
英文关键词High-Efficiency Resource Allocation; Integrated Sensing and
Communications; Dual-functional Waveform; Endogenous Security;
Network ResilienceNSFC 2024
第 1 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 3 页 ---
中
文
摘
要通感技术是实现6G业务沉浸化、智慧化、全域化发展的重要战略之一，随着高速数据传输
、广泛网络覆盖和先进智能服务的需求急剧增加，6G网络会更加复杂开放且具备更多数据形式
。但海量数据处理及广泛物联网设备接入造成的信息监听、隐私泄露以及网络防御能力有限等
为信息大数据量、高频率交互的通感网络带来安全挑战。针对上述问题，本项目以构建通感融
合系统内生安全架构为目标，基于物理层安全技术和通感一体化波形设计，探索通信感知网络
安全和隐私的表征方法，明晰通信、感知和安全的相互作用机理，解决通信-感知-安全集成网
络系统的多维性能动态适配问题。从内生先验信息、鲁棒性安全应对方案、网络弹性分析三个
维度综合提升通感系统的通信数据安全和感知数据隐私保障，并优化系统服务性能和安全资源
分配策略。研究成果将保障6G网络系统信息高可靠传输，有助于实现更为安全、智能和高效的
通信网络环境，促进社会信息化进程向更高层次演进。
英
文
摘
要The integration of sensing and communication technology stands as a pivotal
strategy for realizing the immersive, intelligent, and all-encompassing evolution
of 6G services. Amidst the rapidly growing demands for high-speed data
transmission, broad network coverage, and sophisticated intelligent services, the
6G network is set to become increasingly complex and open, accommodating an
ever-expanding array of data types. Nevertheless, the challenges of processing
voluminous data and facilitating widespread IoT device connectivity introduce
significant security concerns, including susceptibility to eavesdropping,
potential privacy breaches, and constrained network defense capabilities,
particularly in an integrated sensing and communication network distinguished by
vast data quantities and frequent interactions. To address these challenges, this
project is dedicated to developing a comprehensive endogenous security
architecture for the integrated sensing system, grounded in physical layer
security technology and cohesive sensing waveform design. This initiative seeks to
identify effective methods for delineating the security and privacy landscape of
communication-sensing networks, elucidate the intricate interplay among
communication, sensing, and security, and tackle the complex issues of
multi-dimensional performance dynamic adaptation within the integrated network
system encompassing communication, sensing, and security. Utilizing intrinsic
prior knowledge, deploying robust security measures, and conducting network
resilience analysis, the project aims to significantly bolster the security of
communication data and the privacy protection of sensing data, all while enhancing
system service efficiency and refining strategies for the allocation of resources.
The findings of this research will guarantee the transmission of information with
high reliability within the 6G network system, contributing to the establishment
of a communication network environment that is not only safer and more intelligent
but also significantly more efficient. This, in turn, will propel the advancement
of the societal informatization process to unprecedented.NSFC 2024
第 2 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 4 页 ---
报告正文  
参照以下提纲撰写，要求内容翔实、清晰，层次分明，标题突出 。
请勿删除或改动下述提纲标题及括号中的文字。  
（一）立项依据与研究内容 （建议 8000字以内） ：  
1. 项目的立项依据 （研究意义、国内外研究现状及发展动态分
析，需结合科学研究发展趋势来论述科学意义；或结合国民经济和社
会发展中迫切需要解决的关键科技问题来论述其应用前景。 附主要参
考文献目录） ；  
1.1 研究背景及 科学意义  
随着中国在全球通信技术领域的迅速崛起， 我国的研究与开发焦点已经转向
了下一代移动通信网络——6G 。2019年11月， 中国正式启动 6G技术研发工作，
成立了国家 6G技术研发（IMT -2030 6G）推进工作组和总体专家组[1]，标志着
对移动通信产业发展和科技创新的深度投入，以及对移动通信与信息安全领域
关键技术问题的探索和解决的决心。6G 网络预期将是一个开放而复杂的“ 巨系
统”，其应用场景和指标体系预计将实现数量级的提升，标志着从基站和网络设
备中心的传统通信模式，向通信、计算、感知与能源等多方面的深度整合转变，以更全面地适应未来智能化应用的需求
[2]。 
通信感知深度融合被视为 6G重要的战略性技术路径之一。通信感知一体化
的概念正日益受到学术界、工业界和标准化机构的关注，并被 ITU-R 正式纳入  
6G 的六大使用场景[3]。通感技术中， 通信系统不仅仅是信息传递的媒介，它还
能充当传感器的角色，通过探索无线信号的传输、反射和散射特性来“ 感知”物理
世界，从而提供全新的用户体验。 同时，利用无源感知技术实现的高精度定位、
成像和环境重构能够显著提升通信效率， 这类技术可以 归纳为“感知辅助通
信”[4]。这种集成了通信与感知的先进系统将促进各种创新应用的发展，包括精
确的定位技术、高清成像、模式识别，以及实时的 3D映射等[1]，这些应用将加
速自动驾驶、智能城市建设、工业自动化、数字医疗和沉浸式 XR体验等领域的
发展。这一切都强调了 6G网络作为一个开放复杂的巨系统的本质，以及感知通
信融合在实现未来通信技术革新中的核心地位。 
通信和感知的深度融合在推动智能应用发展的同时， 也为信息安全和隐私带
来了新的挑战。通感融合系统中潜在的安全问题如图 1所示。首先，从通信的NSFC 2024
第 3 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 5 页 ---
角度，骤增的应用类型和数据量使得通信系统端到端加密更加复杂，需要更高
效的加密算法来满足服务性能和安全性的双重需求。其次，感知数据往往包含
大量敏感信息，如用户位置、生物特征信息和图像信息等，这些信息的收集和传输增加了数据泄露的风险。因此，随着网络功能的多维扩展，保护用户数据
的安全和隐私需要更加综合和动态的策略。
针对面向 6G的通感网络所具备的动
态性和异构性，安全策略和隐私保护机制需要具备高度的灵活性和适应性 ，使
开放复杂的系统具有半确定性或 者确定性 。 
 
  
 
 
 
 
  
 
  
 
图1 存在安全和隐私威胁的通信感知融合系统场景 
当前移动通信系统把安全作为一种独立的技术，依靠“ 补丁式”“外挂式” ，所
采用的安全手段主要围绕加密、认证及访问控制等传统机制， 从外部抵御不同
种类的恶意攻击这些机制在 1G到5G
网络中的有效性，主要得益于网络架构的
相对固定和应用场景的有限复杂度。 面向 6G的通信感知网络安全需求远远超出
了传统安全方案的应对范围，尤其是在物理层技术方面。传统安全措施，如加
密协议和应用层的 HTTPS、传输层的 SSH等，主要关注数据在传输 过程中的
加密和身份验证，而对于物理层的安全威胁和挑战则考虑不足。面对 6G通信感
知网络的新特性和安全需求， 则需要重新审视物理层安全技术的角色和发展。
目前面向 6G的通信感知融合系统安全设计瓶颈具体表现为： 
（1）物理层安全 高度依赖 外部反馈获取 窃听信道 先验信息 成为通感内生安
NSFC 2024
第 4 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 6 页 ---
全发展瓶颈 ：随着窃听技术的不断进步，攻击者可以更加精准地捕获和分析从
物理层泄露的信息，这使得传统依赖于固定先验信息的安全策略变得不再有效。
同时，高动态的移动环境和复杂的城市布局，这导致窃听者的行为和信道条件难以预测，例如在动态网络环境下的用户设备频繁变换连接基站， 这时由于传
统安全方案针对特定类型的威胁设计，无法全面应对新兴的多样化攻击。
 
（2）感知、通信和安全多功能集成系统 影响了安全应对方案的鲁棒性 ：6G
通感网络将在更加动态和不确定的环境中运行，一体化波束设计必须不断适应
环境变化，保持波束设计的鲁棒性。 同时，在感知、通信和安全多功能集成的
系统中，每个功能的需求可能会相互冲突。一体化波束设计在试图满足这一多
功能集成系统的所有需求时，难以在保证安全的同时，还能维持通信和感知最
优性能，这种性能之间的折衷对波束设计的鲁棒性构成了极大挑战。 
（3）网络的复 杂化和数据形式的多样化 降低了系统资源分配 的高效性 ：多
功能集成系统要求在通信、感知和安全之间动态分配有限的资源，如频谱、功率和计算能力。传统安全方案往往需要固定的资源分配，难以适应动态变化的
需求。同时，对于需要实时处理的大数据应用，如智慧城市中的视频监控系统需要实时分析大量视频流，同时保证数据传输的安全性，这对传统安全方案更
是一个巨大的挑战。
 
 
图2 通信、感知、安全从分立到集成 
综合上述难点分析， 面向 6G的通信感知网络 的安全架构设计 、安全方案 鲁
棒性、资源分配高效性 ，均受到网络环境高动态、数据形式复杂、系统 高度依
赖先验信息等外在因素与 内源性缺陷联合制约，使得通信感知融合系统的内生
安全发展遭遇瓶颈。 其本质原因在于， 当前研究未能将通信感知融合系统与物
理层安全设计 理论良好衔接， 进而无法 形成集通信、感知 与内生安全为一体的
准确性系统。 网络内生安全在系统设计之初进行系统级的安全考量， 设计和实
施6G通感网络时固有的、全面的安全措施， 这些措施旨在保护网络免受各种安NSFC 2024
第 5 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 7 页 ---
全威胁和攻击，同时确保用户隐私和数据安全， 使系统自身具备免疫能力，不
仅能被动地抵御恶意攻击，还能主动地消灭各种安全风险。 因此，基于物理层
安全理论 开展通信 、感知与安全深度融合的设计方案相关 研究，是推动 6G系统
实现更为安全、智能和高效的通信网络环境 的关键所在 ，具有重要的研究意义
和研究价值。  
物理层安全技术旨在通过利用信道的固有安全属性如随机性、 多样性和时变
性，在物理层实现加密和认证。6G 通感系统的信道特点体现在其对高频段的利
用和对复杂环境的适应性，这导致了显著的时频双弥散特性， 信道特征在时间
和频率维度上的变化复杂 ，因此，设计与信道状态信息相结合的安全机制可以
确保窃听者无法截取信息 ，推动了物理层技术 与通感系统的结合 。另一方面 ，
通感系统的数据形式多样性 为系统内生先验信息 赋能，其独有特性 为物理层安
全使能的 通信、感知和安全一体化集 成系统设计提供了可行的契机 。 
当前，我国关于“6G 网络中通信感知由分立走向一体”相关技术已经开展
了初步研究, 有一定的研究基础。 面对海量数据传输、多样化的数据形式以及高
度动态的开放复杂网络环境所带来的数据安全和隐私风险，应及时开展全面的
安全策略更新和隐私保护措施的实施。 针对大规模复杂通感融合 网络中存在的
个人隐私泄露 、通信信息 监听、抵御未知风险能力差 等安全问题 ，围绕着 内生
先验信息 、安全方案鲁棒性 、动态资源分配 三个方面提出创新 的研究思路和可
行的技术方案， 实现通信感知网络 的内生安全 ，综合提升系统抵御 风险、消灭
风险的能力，推动无线网络在保障信息安全和隐私的前提下飞速发展。  
1.2 国内外研究现状  
本项目针对面向 6G的通信感知融合网络内生安全技术展开研究，从通信感
知融合系统中安全和隐私的风险、内生安全技术设计、内生安全赋能的网络弹性分析三个方面回顾国内外研究现状和发展动态，分析开放复杂的 6G系统的内
生安全技术研究的必要性和紧迫性。 
1.2.1 
通感系统中安全和隐私的 风险研究现状  
随着通信感知的深度融合技术的发展， 同时执行通信和感知任务的系统为多
种应用提供了前所未有的便利和效率。然而，通感技术的发展在保护用户隐私
和系统安全方面也带来了新的挑战。 
当前，众多基站利用电磁波形成了广阔的无线网络， 电磁信号的存在范围十
分广泛， 通感技术的应用可能对用户对隐私的产生威胁[6]。具体来讲， 感知功能NSFC 2024
第 6 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 8 页 ---
主要分为三个层面：检测、估计和识别。检测关注的是确认目标是否存在的信
息获取；估计则是在目标已知的情况下，通过感知来获取目标的位置、速度等
具体参数；识别则进一步获取关于目标身份、特征、状态等更加详尽的信息[7-9]。
显然，当感知目标为人类时，这三层感知就可以像摄像头一样记录人的每个动
作。然而和摄像头相比，电磁波可以在黑暗中或即便有墙体阻隔的情况下继续
发挥感知作用。例如， 文献[ 10]通过太赫兹频段的通感技术，成功实现了对受限
空间环境的重建和目标成像。这意味着，即便个人身处封闭空间，也难以逃避电磁波的监视，这种全方位的监控使 用户隐私几乎无所遁形。此外， 如果存在
恶意终端捕获了通感设备的回波信号， 且该设备拥有强大的处理能力，则直接
构成了对隐私的侵犯
[11]。 
通感一体化基站的任务之一是将功率集中投向包含目标的方向， 同时确保在
接收端，目标回波信号的信干噪比足够高，以满足既定的感知性能要求。然而，
当目标可能是潜在的窃听者时，问题变得复杂。由于感知 波束设计时，最大化
信干噪比 比中包含的 角度与目标的角度相同，这就意味着目标对嵌入通信信号
的接收信干 噪比非常高，从而很大程度地增加了信息被窃听的可能性[12-14]。例
如，文献 [15]中针对此场景展开了研究， 文中将感知目标看做通信信息的潜在窃
听者，当系统没有针对目标接收处不做任何干预时 ，目标有很大概率成功获取
通信信息。 此外，在上行通信过程中通信用户可能会接入伪基站，导致电磁信
号泄漏给未授权的感知目标，这也是通感系统中存在的安全威胁之一[16,17]。 
综上所述，通信感知融合系统的研究近年已经取得了可观的进展， 但是随之
带来的安全风险在 通感系统的设计中也要考虑在内。首先，对于高动态变化的
信道，需要通过探究一体化波形增益的本质来设计适应性强、复杂度低且安全
可靠的确定性系统。同时，对于通信感知融合网络的安全问题的研究还处于初
级阶段，如何针对通感网络中数据类型多样、 数据交互频繁的特征设计可靠的
安全方案，抵御未知的风险是是突破研究瓶颈的关键问题。 
1.2.2 通感系统内生安全 研究现状  
 内生安全理论由国家数字交换系统工程技术研究中心团队提出[18-21]，是利用
具有动态、异质和冗余属性的内生安全结构来实现内生安全功能。这一理论的
核心在于，它能够从一个异构且具备冗余的执行体中，动态地选取一组执行者
来完成系统所需的功能。这种方法不仅增加了系统对攻击者的不确定性，也大
大提高了系统的可靠性。通过这种方式，系统不仅能够抵御已知的安全威胁，NSFC 2024
第 7 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 9 页 ---
更重要的是，它还能有效防御未知的安全威胁和攻击手段。因此，该机制使得
攻击者难以理解系统的结构和运行机制，从而难以发起有效的攻击。 
 
图3 网络安全发展特征与内生安全发展阶段 
无线系统内生安全的不同之处在于，由于电磁波传播的折射、散射、衍射、
反射等效应，使得无线通道自然具有动态、异质和冗余的内生安全属性。无线内生安全是一种不同于传统安全机制的共生关系，安全内在融合与无线系统，
可以共生发展。例如，信道状态信息估计技术不仅可以更好地抵御随机信道衰
落，而且还可以更好地利用信道状态信息来对信号进行混淆，提高内生安全性。
同样，对于信道编码技术，一个良好的编码方法不仅可以提高通信系统的鲁棒
性，还可以帮助实现物理层安全传输技术。 
融合感知功能的无线通信系统中，安全问题可以分为两个方面，包括通信信
息安全和感知信息的隐私。在复杂多变的开放性应用场景中会产生大量数据交互，提升系统内信息安全和隐私的风险。利用信道状态信息设计保证通信信息
安全近年被广泛研究
[22-25]，其中包括通过在发送端加入人工噪声的手段开降低
非法用户端的信干噪比，从而降低其成功解码信息的概率[26]。此外，方向性调
制、共线性干 扰设计也是作为通信信息传输安全的有效手段被广泛研究[27]。感
知隐私方面，罗格斯大学团队分析了在通信雷达同频共存的环境下，感知信息
的隐私风险[28-31]，其中包括基于信道信息获取感知雷达位置、基于机器学习估
计雷达方向等，并提出了在非法接收端不采用基于机器学习理论时，雷达方位
信息隐私保护机制。 
对于通信感知融合系统的内生安全研究目标在于， 感知和通信等数据信息大
量交互的高动态环境中，如何提升该开放系统的确定性。由上述研究现状可以
看出，该方向的研究仍处于初级阶段，首先，在系统设计中需要分别考虑通信
安全和感知隐私，然而当前在通信感知融合系统的感知隐私安全相关研究几近NSFC 2024
第 8 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 10 页 ---
空白，如何建立统一性能指标，量化通信安全和感知隐私，是设计通感融合系
统内生安全的关键。此外，深入研究保障系统内生安全机制，设计低功耗、低
复杂性、高效益的内生安全算法仍有待探索。 1.2.3 
内生安全 赋能的多维性能适配 网络弹性  
网络弹性工程是由美国、 欧洲等发达国家和地区响应数字化转型和新兴网络
安全挑战而实施的技术措施。它基于网络弹性标准，旨在建立数字技术的准入门槛，通过同时加强应用服务和设备供应两端，提升数字基础设施和产品的安
全性
[32]。美国自 2007年起，通过一系列政策文件和指令，将网络弹性纳入国家
安全战略，强调提高关键基础设施的安全与弹性，并在 2023年提出将安全措施
内置于体系结构和设计中。欧盟自 2008年开始发布多份政策文件，推动网络弹
性的实践应用，并通过立法要求销售于欧盟的数字化设备和软件满足强制性安
全标准。 2023年11月，欧盟就《网络弹性法案》技术议题达成一致，准备提交
审议[33]。英国则在 2022年发布了多项政策文件，将网络弹性作为国家网络战略
的重要组成部分，明确提出建立国防弹性网络体系的愿景，并推进相关立法。
这些措施体现了各自在提高网络弹性方面的战略布局和实施细节，旨在建立更安全、有弹性的数字生态系统，以应对网络攻击和提高国家网络安全水平
[34]。 
当前，国际上普遍采用的网络弹性工程方法面临几个技术挑战，包括处理未
知威胁的能力不足、缺少集成多种技术的框架、以及安全性评估的能力有限。文献[ 32]中表明， 内生安全可以解决这三大难题。该技术旨在增强网络弹性在预
防、抵御、恢复、适应四 个既定目标维度上的能力。针对通信感知融合网络，
通信服务质量、感知准确性和数据安全性的性能权衡和理论边界分析可以为网络弹性的刻画提供有力的理论基础支撑，现有文献仅针对通信和感知性能理论
边界，例如，文献[ 35-36]通过制定一个通用的通信感知 权衡优化问题来优化输
入数据分布来研究时间- 频率权衡。此外，文献[ 37]通过将通信感知波形视为感
知信号模型中的随机但已知的干扰参数，并表征克拉美罗界- 速率区域，分析 了
时间-频率权衡。在文献[ 38]中，作者基于不同的输入信号分布，从信息论的角
度考虑了通信感知融合系统的时分复用方案，并提出了容量- 失真区域来表征 通
信感知系统性能权衡。上述融合感知功能的无线网络无法保证信息的安全和隐私，无法保证信息交互的安全性和可靠性，仍然是不确定的系统。 
内生安全赋能的通 感网络弹性工程研究关键在于建立统一量化体系刻画网
络服务性能和安全之间的弹性关系，为深入探索通信、感知和安全三者之间的NSFC 2024
第 9 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 11 页 ---
相互影响及权衡机理提供理论依据。目前，内生安全赋能的通信感知融合网络
理论内涵和可达合作增益尚不明确，因此由内生安全赋能的通信感知融合网络
性能权衡和理论边界分析是整体网络弹性的量化表征至关重要的研究基础。    
1.3 主要参考文献  
[1] 郎平, 田大新. 面向 6G 的车联网关键技术[J]. 中兴通讯技术, 2021, 27(2): 13.  
[2] 王晓云, 段晓东, 孙滔. 平台化服务网络 ——新型移动通信系统架构研究[J]. 电信
科学, 2023, 39(1): 20- 29. 
[3] XIE F. 6G network architecture: a survey[J]. ZTE technology journal, 2023, 29(5): 28- 37. 
[4] 何佳, 周知 , 李先进, 等. 面向 6G 的通信感知一体化: 基于无线电波的感知与感
知辅助通信 [J]. 信息通信技术与政策, 2022, 48(9): 9.  
[5] 程强, 刘姿杉. 电信网络智能化发展现状与未来展望[J]. 信息通信技术与政策, 2020, 
46(9): 16.  
[6] Lu S, Liu F, Li Y , et al. Integrated sensing and communications: Recent advances and ten 
open chall enges[J]. IEEE Internet of Things Journal, 2024.  
[7] Zhang J A, Rahman M L, Wu K, et al. Enabling joint communication and  radar sensing in 
mobile networks —A survey[J]. IEEE Communications Surveys & Tutorials, 2021, 24(1): 
306-345.  
[8] Gini F, Rangaswamy M. Knowledge -Based Radar Detection, Tracking, and 
Classification[J]. 2008.  
[9] Liu F, Masouros C, Petropulu A P, et al. Joint radar and communication design: 
Applications, state -of-the-art, and the road ahead[J]. IEEE Transactions on Communications, 
2020, 68(6): 3834- 3862.  
[10] Jia H E, Zhi Z, Xianjin L I, et al. 6G integrated sensing and communication: wireless 
sensing and sensing assisted communication[J]. Information and Communications 
Technology and Policy, 2022, 48(9): 9. , 
[11] Mavroudis V , Hao S, Fratantonio Y , et al. On the privacy and security of the ultrasound 
ecosystem[J]. Proceedings on Privacy Enhancing Technologies, 2017, 2017(2): 95-112.  
[12] Chu J, Liu R, Li M, et al. Joint secure transmit beamforming designs for integrated 
sensing and communication systems[J] . IEEE Transactions on Vehicular Technology, 2022.  
[13] Su N, Liu F, Masouros C, et al. Secure ISAC MIMO S ystems: Exploiting Interference 
With Bayesian Cram \'er-Rao Bound Optimization[J]. arXiv preprint arXiv:2401.16778, 2024.  NSFC 2024
第 10 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 12 页 ---
[14] Khan W U, Lagunas E, Ali Z, et al. Opportunities for physical layer security in UA V 
communication enhanced with intelligent reflective surfaces[J]. IEEE Wireless 
Communications, 2022, 29(6): 22-28.  
[15] Su N, Liu F, Masouros C. Sensing-assisted eavesdropper estimation: An ISAC 
breakthrough in physical layer security[J]. IEEE Transactions on Wireless Communications, 
2023.  
[16] Duan Z,  Yang X, Gong Y , et al. Covert Communication in Uplink NOMA Systems under 
Channel Distribution Information Uncertainty[J]. IEEE Communications Letters, 2023.  
[17] Qu K, Li X, Guo S. Privacy and Security in Ubiquitous Integrated Sensing and 
Communication: Threats, Challenges and Future Directions[J]. arXiv preprint 
arXiv:2308.00253, 2023.  
[18] Guo W, Wu Z, Zhang F, et al. Scheduling sequence control meth od based on sliding 
window in cyberspa ce mimic defense[J]. IEEE Access, 2019, 8: 1517 -1533.  
[19] Hu H, Wu J, Wang Z, et al. Mimic defense: a designed‐in cybersecurity defense 
framework[J]. IET Information Security, 2018, 12(3): 226-237.  
[20] Jin L, Hu X, Lou Y , et al. Introduction to wireless endogenous secur ity and safety: 
Problems, attributes, structures and  functions[J]. China Communications, 2021, 18(9): 88- 99. 
[21] Ji X, Wu J, Jin L, et al. Discussion on a new paradigm of endogenous security towards 
6G networks[J]. Frontiers of Information Technology & Electronic Engineering, 2022, 23(10): 
1421- 1450.  
[22] Mitev M, Chorti A, Poor H V , et al. What physical layer security can do for 6g 
security[J]. IEEE Open Journal of Vehicular Technology, 2023.  
[23] Feng  K, Li X, Han Y , et al. Physical layer  security enhanc ement exploiting intelligent 
reflecting surface[J]. IEEE Communications Letters, 2020, 25(3): 734-738.  
[24] Makarfi A U, Rabie K M, Kaiwartya O, et al. Physical  layer security in vehicular 
networks with reconfigurable intelligent surfaces[C]//2020 IEEE 91st Vehicular Technology 
Conference (VTC2020-Spring). IEEE, 2020: 1 -6. 
[25] Zhang C, Jia F, Zhang Z, et al. Physical layer security designs for 5G NOMA systems 
with a stronger near -end internal eavesdropper[J]. IEEE Transactions on Vehicular 
Technology, 2020 , 69(11): 13005-13017.  
[26] Su N, Liu F, Masouros C.  Secure radar -communication systems with malicious targets: NSFC 2024
第 11 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 13 页 ---
Integrating radar, communications and jamming functionalities[J]. IEEE Transactions on 
Wireless Communications, 2020, 20(1): 83-95.  
[27] Su N, Liu F, Wei Z, et al. Secure dual-functional radar -communication transmission: 
Exploiting interference for resilience against target eavesdropping[J]. IEEE Transactions on Wireless Communications, 2022, 2 1(9): 7238-7252.  
[28] Li B, Petropulu A P. Joint trans mit designs for coexistence of MIMO wireless 
communi cations and sparse sensing radars in clutter[J]. IEEE Transactions on Aerospace and 
Electronic Systems, 2017 , 53(6): 2846- 2864.  
[29] Li B, Petropulu A P, Trappe W. Optimum co -design for spectrum sharing between matrix 
completion based MIMO radars and a MIMO communication system[J]. IEEE Transactions 
on Signal Processing, 2016, 64(17): 4562-4575.  
[30] Dimas A, Clark M A, Li B, et al. On radar privacy in shared spectrum 
scenarios[C]//ICASSP 2 019-2019 IEEE International Conference on Acoustics, Speech and 
Signal Processing (ICASSP). IEEE, 2019: 7790 -7794.  
[31] Dimas A, Li B, Clark M, et al. Spectrum sharing between radar and communication 
systems: Can the privacy of the radar be preserved?[C]//2017 51st Asilomar Conference on 
Signals, Systems, and Computers. IEEE, 2017: 1285 -1289.  
[32] Wu J, Zou H, Xue X, et al. Cyber Resilience Enabled by Endogenous Security and 
Safety: Vision, Techniques, and Strategies[J] . Strategic Study of Chinese Academy of 
Engineering, 2024, 25(6): 106-115.  
[33] Car P, De Luca S. EU Cyber  resilience act[J]. EPRS, European Parliament, 2022.  
[34] UK G O V . National cyber strategy 2022[J]. 2022.  
[35] Xiong Y , Liu F, Lops M. Generalized deterministic -random tradeoff in integrated sensing 
and communications: The sensing-optimal operating point[J]. arXiv preprint 
arXiv:2308.14336, 2023.  
[36] Zhang Y , Aditya S, Clerckx B. Input Distribution Optimization in OFDM Dual-Function 
Radar -Communicatio n Systems[J]. arXiv preprint arXiv:2305.06635, 2023.  
[37] Xiong Y , Liu F, Cui Y , et al. On the fundamental tradeoff of integrated sensing and 
communications under Gaussian channels[J]. IEEE Transactions on Informat ion Theory, 
2023.  
[38] Ahmadipour M, Kobayashi M, Wigger M, et al. An information-theoretic approach to NSFC 2024
第 12 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 14 页 ---
joint sensing and communication[J]. IEEE Transactions on Information Theory, 2022.  
2. 项目的研究内容、研究目标，以及拟解决的关键科学问题 （此
部分为重点阐述内容） ； 
2.1 研究内容  
本项目针对 6G开放融合、异构共存、智能互联的网络特性引发的更多未知
复杂安全威胁，研究面向 6G的通信感知融合网络内生安全问题，从通信感知融
合系统架构切入，探索如何通过物理层技术实现通感系统内生安全，并深入分
析内生安全赋能的通感融合系统的网络弹性。拟从 
 基于通感系统特性获取先验信息 
 物理层技术使能的通感系统内生安全 
 内生安全赋能的多维性能适配系统网络弹性分析 
三个层面展开研究。以上三个研究内容联系紧密、层层递进。研究内容一中提
出的网络架构是研究内容二的基础，研究内容二针对研究内容一中的系统提出
内生安全技术方案，研究内容三针对研究内容二提出的方案深入分析网络弹性。
下面针对具体的研究内容进行详细阐述。 
 
 
 
 
 
  
 
  
 
图4 总体研究内容、目标和关键科学问题之间的关系 
2.1.1 
研究内容一： 基于通感系统特性获取 先验信息  
通信感知一体化技术通过深度整合通感功能， 旨在提高无线与硬件资源的使
用效率。 波束赋形技术在通感系统中具有很大的发挥潜力，它逐步成为很多新
型平台的技术要求。对于通信和感知的性能指标，往往会产生波形设计上的矛
NSFC 2024
第 13 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 15 页 ---
盾，因此在通感融合系统下进行波束赋形具有一定的挑战性。 因此，本项目基
于通信感知融合系统架构研究物理层增强技术，具体包括： 1）通感系统一体化
鲁棒性波形设计。利用同一电磁信号和硬件平台完成通信和感知两项任务，由
此提升频谱效率、降低开销以满足 6G的高数据量交互需求。 同时，在考虑系统
中通信信道信息及感知先验信息不确定性的情况下，设计高性能、高鲁棒性的波束赋形以保证通感系统的服务质量指标； 2）通信感知融合系统安全与隐私性
能刻画。 通感系统的内生安全问题分别从通信安全和感知隐私两个方面考虑，
建立合理 性能指标 刻画系统 安全性能 是评估本项目研究 采取的技术手段的基
础； 3）通感辅助弥补系统内源性缺陷。传统通信中信道信息获取需要依赖导频
信号发送及反馈，在时延和能耗等方面产生了额外的开销，并且在发送导频的
过程中可能会造成信息泄露。 在感知功能的辅助下， 利用确定性信道建模方法，
从多源传感器中提取通信信道状态信息，实现基于感知辅助的低开销、高精度
的通信信道状态估计。同时复用感知数据，作为安全设计的重要先验信息。 
2.1.2  
研究内容二：物理层技术使能的通感系统内生安全  
传统通信系统安全依赖于“外挂式”和“补丁式”技术，这些技术有效性与
安全问题的成因和威胁机制的精准掌控程度强相关，不仅开销很大，而且无法
完全对未知的威胁产生有效防御。内生安全设计不依赖 (但不排斥) 攻击者先验
知识或精确感知与行为分析的前提下, 有效抑制基于内生 安全共性问题的“ 已知
的未知”随机性扰动及“ 未知的未知” 不确定网络攻击。为此，本项目研究保障通
感系统的物理层安全技术，具体包括：1）感知辅助迭代优化物理层安全。通过
感知功能获取窃听者信道信息，建立加权优化模型，权衡感知功能估计精确度
和物理层安全性能； 2）方向性调制合法接收信息。通过合法用户与基站的合作
获取信道状态信息，设计一体化波束，使得系统授权合法用户端收到正确信息，
而其他用户接收随机信息，该方法无需窃听信道先验信息； 3）基于信息论的
感知隐私保密。考虑合法通信用户在通信感知融合系统中作为双站雷达的非法
接收端，解析获取感知目标位置，利用互信息差刻画隐私安全性能表征，设计
优化问题在保证服务质量的同时防止感知数据的泄露。 
2.1.3 研究内容三：内生安全赋能的 多维性能适配 系统网络弹性分析  
通感网络的内生安全设计使得一个开放复杂的巨系统成为了确定系统， 但是
系统中存在“开放与安全”的矛盾性，即在一定的功率限度内通信、感知和安全的服务性能之间存在权衡关系。在大部分通感一体化研究中，或者采用泛化NSFC 2024
第 14 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 16 页 ---
的信干噪比、互信息等衡量通感性能，或者采用通信速率和估计误差等分别衡
量通信和感知性能，未能建立精确统一的性能评价指标。因此，本项目研究内
生安全设计赋能的多维性能适配系统网络弹性， 具体包括： 1）统一通感服务质
量性能评价指标。基于速率失真理论的容量失真函数的概念刻画给定估计误差
下所需的最小信息速率，建立信息量和估计量之间的等价关系； 2）通感系统整
体性能指标刻画。描述在给定感知精度条件下，可达到的通信容量及通信和感
知性能的最优实现水平，为进行通感整体性能和安全性能之间的权衡提供理论
基础； 3）内生安全赋能的通感系统可达性能边界分析。基于不同的系统资源分
配方案，分析通感系统性能和安全性能的权衡，为通信感知融合系统的内生安
全设计提供理论支持。 
2.2 研究目标  
本项目总体研究目标是采用物理层技术实现通信感知融合系统的内生 安
全，以应对由无线网络内源性缺陷产生的共性和本源安全问题，提升系统抵御
未知安全威胁的能力。遵循建立系统架构、提出通感系统内生安全物理层技术、
提出联合性能指标并通过性能权衡分析网络弹性的技术路线，探索物理层技术如何保证通感系统的安全和隐私、感知功能如何辅助物理层安全技术摆脱对窃
听信道先验信息的依赖以实现内生性。具体目标如下： 
研究目标 （1）：在达到通信感知服务用户质量要求的 前提下， 通过利用感
知功能获取的数据作为系统内生先验信息， 摆脱物理层安全设计对额外反馈机
制的依赖，实现内生安全设计。  
研究目标 （2）：通过通信感知网络架构建模以及鲁棒性一体化波形设计，
实现鲁棒性强的物理层内生安全设计， 安全机制随 动态信道环境而变化， 解决
安全方案与高动态复杂环境不适配的问题。  
研究目标 （3）：通过分析通信、感知、安全各性能之间的权衡， 建立系统
高效适配机制， 根据实际需要设计性能折衷方案， 解决系统内服务性能与安全
性能冲突所导致的低能效问题  
2.3  拟解决的关键科学问题  
本项目从内生先验信息、 鲁棒性安全应对方案、高效资源分配三个维度上，
逐步探索并明确通信、感知和安全一体化集成系统中的高效动态性能适配问题，
进而为开放复杂的巨系统大量数据交互传输提供具有高效性和可靠性 的安全和
隐私保障。具体表述如下： NSFC 2024
第 15 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 17 页 ---
拟解决的关键科学问题 ：通信 -感知 -安全集成 网络系统的多维性能动态适配 。 
通信、感知 、安全一体化的集成系统的 基于物理层技术理论，设计实现系
统的内生安全， 旨在大幅度提升复杂开放的通感融合巨系统 抵御未知风险的能
力。 
首先，通信感知实现“ 深度融合、 功能辅助、 信息共享” 还处于初期探索
阶段，通信信息和感知数据的多重协作机制仍有很大的研究空间， 但是更深度
的通感协作势必会带来通信感知数据边界模糊的问题，一旦系统中出现恶意获
取信息的终端设备，将会造成大规模数据窃听和隐私泄露，因此设计安全方案
既可以支持多重数据交互，也能保障数据安全成为了挑战性问题。 
与此同时， 对于向智能化、全场景 、全连接、全频谱方向发展的高动态巨
系统，传统的“ 外挂式” 安全技术由于其 成本高、灵活性差， 已经无法满足巨
系统的安全需求， 因此探索安全技术和通感系统特定的衔接点 ，针对通感巨系
统设计适应其动态性的内生安全方案成为了另一个难点。 
最后，在多功能集成的通信、 感知、安全系统中，性能之间会出现相互 “冲
突”，例如，当系统中安全性能到达最优状态时，通信和感知的服务质量不一定
会满足用户质量需求， 因此，在通感系统内生安全设计中对通信、感知和安全
的性能权衡问题同样是一个难点。 
本项目为应对上述问题难点， 面向通感融合系统设计具有高度适应性的内
生安全方案，探索安全技术与通感系统的衔接点，并明晰通信、感知和安全高
效动态适配机制。 
 
图5 核心技术路线图 
本项目拟解决的关键科学问题与研究内容和研究目标之间的逻辑关系如图 4
所示，核心技术路线场景示意图如图 5所示。项目立足于面向 6G的通信感知融NSFC 2024
第 16 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 18 页 ---
合网络内生安全技术研究， 围绕内生先验信息、鲁棒性安全应对方案、高效资
源分配三个维度研究通信、感知和安全的高效动态适配机制。 本项目设置的 研
究内容和蕴含的科学问 题之间紧密联系、相互支撑，相关研究成果可以推动网
络安全技术发展，为飞速发展的通信技术提供信息安全可靠保障。 
3. 拟采取的研究方案及可行性分析 （包括研究方法、技术路线、
实验手段、关键技术等说明） ；  
3.1 研究方法  
 本项目拟沿着基础理论构建、 方案设计以及仿真实验性能分析层层递进的研
究思路， 设计集通信、感知、安全一体的稳定系统。 具体采用物理层技术，从
通信信息和感知信息的安全和隐私方面分别考虑，不通过通感系统外部反馈获
取恶意用户的先验信息， 以此实现通感系统的内生安全，并设计通信、感知、
安全性能指标， 分析一体化系统的性能权衡和内生安全赋能的多维性能适配系
统网络弹性。 
3.2 技术路线  
本项目根据三个研究内容的特点，按图 6所示的技术路线，详细阐述如下：  
 
图6 项目总体技术路线 
3.2.1 基于通感系统特性获取 先验信息  
本研究内容中，通信感知融合系统在通信和雷达共享频谱、共享硬件平台、
共享功率的基础上设计通感一体化波形， 从而提升无线频谱资源利用率、降低
开销，并为系统提供通信感知深度合作的性能增益。 在通感融合基础上， 设计
提升系统的鲁棒性以及感知辅助弥补内源性缺陷。 NSFC 2024
第 17 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 19 页 ---
 
图7 研究内容（1）技术路线 
（1）通感系统 一体化鲁棒性波形设计  
 通信感知 一体化波形设计关键在于如 何设计波形同时满足通信与感知的服
务质量，并进一步深度融合以产生新的增益。 根据系统实际需求， 一种通信感
知波形设计可以写成如下加权优化问题形式 
 ()()() maximize  1
subject to  CSρρ+−
∈XXX
X (1) 
其中 X表示通感一体化波形矩阵， () CX表示通信性能指标，包 括误码率、 信道
容量等， () SX表示感知性能指标，包括 估计精度、检测 概率等，均 为关于波形
X的函数。 ρ表示给定的加权因子， 决定系统对通信和感知性能优化的权重 ，
表示可行域，实际设计中包括系统功率约束等条件。上述优化问题适用于通信
和感知性能指标表达式较为简单的情况，否则会很大程度增加该问题求解的复杂性。 
 当考虑系统中先验信息无法精确已知的情况， 需要针对系统进行鲁棒性强的
一体化波形设计。此时通信感知性能表达式较为复杂， 设计中优化通式可以等
价表示为如下形式 
 ()
()maximize  
subject to  ,  C
S ∈XX
XX (2) 
具体场景中，合法用户信道和感知信道的信道状态估计误差模型分别表示为： 
 合法用户信道信息估计误差 
已知误差范围的不精确信道估计：估计信道可以采用加性信道误差模型NSFC 2024
第 18 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 20 页 ---
表示，即第 i个通信用户的信道可以写作 
 i ii= +hhe   (3) 
    其中ih表示发送端估计信道，ie表示信道不确定度，分布于如下球面集合 
 { }2 2,i i i i iε ℑ= ≤ ∀ ee  (4) 
 感知目标（窃听者）信道估计误差 
在感知通信融合网络中， 感知信道是关于感知目标角度的函数。 实际上，
感知目标的角度估计通常也伴随着估计误差。因此，在该场景中，虽然发送
端可以通过感知功能获取窃听信道状态信息，但信道信息仍存在估计误差。  
基于上述通信和感知信道先验信息获取不精确的情况， 鲁棒性一体化波形设计
表达式可以写为如下形式 
 ()
()()()()
()()()()()
()()()()()00
00
00ˆ maximize  ,
subject to  ,
                 1,
                 1,
                 C
HH
x mx m m
HH
kxk x k
HH
kxk x kC
θ θ θ θ τθ
θ θ αθ θθ
θ θ αθ θθ− ≥ ∀ ∈Ω
≤+ ∀∈ Φ
≥− ∀∈ Φ
∈XHX
a Ra a Ra
a Ra a Ra
a Ra a Ra
W (5) 
其中 ˆ
CH表示通信估计信道，1H
xL=R XX 表示感知端协方差矩阵， ()θa表
示指向矢量。 ˆϑ表示估计角度， 当目标角度估计不准确时，假设估计误差为 θ∆，
则有0ˆ ˆ, θ ϑ θϑ θ∈ −∆ +∆，式中 ˆ ˆ,ϑ θϑ θΦ −∆ +∆ 表示设计波形的主瓣角度
区间。由此可以看出上式优化问题最大化通信服务质量性能，同时约束条件一
至三设计生成宽主瓣波形以 覆盖目标所有可能角度，从而保证目标一定落在主
瓣的角度区间内， 保证感知性能。 求解优化问题 （5）可以采用 S-过程原理重构
通信性能表达式，从而转化为凸问题求解。 
（2）系统安全和 隐私性能 刻画  
 通感融合系统的内生安全设计需要从通信和感知两个方面考虑， 分别对其构
建性能刻画指标是关键性问题。 
 通信信息安全性能刻画 
融合感知功能的通信系统中，双功能基站发送波束用于感知目标，同时携带
发送给通信用户通信信息， 系统中接收通信信息的通信用户为 合法终端，其他
感知设备为潜在非法终端。具体应用场景如图所示。 信息论中，香农- 哈特利定
理定义了信道容量的概念，即在噪声存在的情况下，通过特定带宽的信道可靠NSFC 2024
第 19 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 21 页 ---
传输通信信息的最高速率的严格上限。由此任何离散无记忆信道的容量定义为
输入 X和输出 Y之间的最大互信息 ();I XY可以表示为 
 
()() max ;
pXI XY C=  (6) 
上式表示，在输入 X上给定功率约束 P的情况下，具有加性高斯白噪声的高斯
信道的信道容量可以用香农公式表示，即 ()2
Gau 2 log 1 CP σ = + ，其中2σ是噪声
方差。基于上述信道容量引入了一个用于衡量通信安全的基本保密度量标准。在物理层安全研究中，保密容量通常作为一个关键的评估指标，定义如下 
 
()()() { } max ; ;spXC IX Y IX Z= −  (7) 
其中 X是发送信号， Y和Z分别是用户和窃听者端的接收信号。 基于上述理论，
为了更方便地评估保密性并简化计算，在物理层安全研究中通常应用保密率评
估安全性能，保密速率通常被 视为保密容量的下限。假设输入信号符合高斯分
布，可达保密率表达式为  
[]s ceR RR+= −  (8) 
其中[]+表示{} max ,0，cR表示发送端到合法用户的信道传输率，eR表示发送端
到窃听者的信道传输率。 在不考虑信道衰落的前提下，通常采用保密率或保密
容量来衡量物理层安全的设计性能。 
图8 通感系统中感知隐私风险 
 感知信息隐私性能刻画 
对于感知和通信功能一体化的基站，当感知端采用双站雷达时，双功能波束
经过目标反射的 信号可能同时被通信用户和感知合法接收端接收，此时通信用
户即可以解析出目标的位置，从而造成目标位置的隐私泄露。该场景如图 8所
示，为衡量接收器的感知性能，采用接收到的雷达信号和目标响应矩阵之间的
NSFC 2024
第 20 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 22 页 ---
互信息作为雷达感知的性能指标，合法雷达接收器的感知互信息可以表示为 
 ()( ) ;| ; |SS S S IIθ= Y X YH X  (9) 
同理，通信用户和感知信息的互信息可以写为 ( ) ;|ccIYH X 。其中，SH和CH分
别对应二者的信道响应矩阵 。根据上述信息模型和互信息表达式，互信息差
( )( ) ;| ;|SS cc II − YH X YH X 即可表示感知隐私性能指标。 
（3）通感辅助弥补系统内源性缺陷  
由通信感知安全的性能刻画中， 安全和隐私性能指标都是关于信道信息的函
数，因此可以说明信道信息是 保证网络安全的重要先验信息。 然而，对于系统
中的恶意终端设备，现有的研究中通常假设其信道信息在发送端已知或部分已
知，实际中系统必须依赖由恶意终端设备参与的额外的反馈来获取其信道信息，
这是几乎不可能实现的， 这个问题就是网络中的内源性缺陷。 对于通感融合系
统，在感知功能的辅助下可以弥补此缺陷，具体如下： 
 
图9 通感系统中通信信息安全风险 
通信安全 ：对于图 9中所示场景，感知目标被看作窃听终端，所以系统设计中
需要防止其解码保密信息。 在通信感知融合系统中，感知功能可以发送全向波
束，利用回波进行角度估计 。由于基站到目标的信道（ 窃听信道） 是关于角度
的函数，由角度估计即可获取窃听信道信息。具体回波可以写作 
 ()()()H
rtθβθ θ= + Xa a SZ  (10) 
其中()tθa和()rθa分别表示发送天线和接收天线的指向向量， ()βθ表示从角度
θ回波信号的幅度， S表示发送信号， Z表示包括干扰和噪声等剩余项。 待估计
参数一般包括角度信息和幅度信息，其中角度信息可以采用 Capon算法，幅度
信可以采用渐进最大似然估计（AML ）算法估计。 
感知隐私 ：在如图 8所示的场景中，由于该通信用户（非法接收端）与基站进
行合法通信，通信感知双功能融合基站也很容易获取通信用户位置。 NSFC 2024
第 21 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 23 页 ---
 综合通信和感知两方面的信息安全两个方面来看， 利用物理层技术保障通信
感知融合网络安全隐私可以摆脱利 用额外反馈获取窃听信道信息， 有助于突破
物理层安全中的瓶颈问题， 构成一个集通信、感知、安全为一体的具有稳定性
和准确性的系统。 
3.2.2 物理层技术使能的通感系统内生安全  
 
图10 研究内容（2）技术路线 
（1）感知辅助迭代优化物理层安全  
不同于传统的物理层安全对窃听信道的信息获取方式， 章节 3.2.1中阐述了
在通信感知网络中，感知功能可以辅助获取窃听信道状态信息。 
图11 Capon算法性能及克拉美罗下界 
 
采用 Capon算法的目标角度估计精度与系统的信噪比关系如图 11所示。在
NSFC 2024
第 22 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 24 页 ---
低信噪比的情况下，目标角度估计误差明显增大。此外， 图中也给出了在不同
信噪比下，发送端发射全向波束进行目标探测时的角度估计方均根误差的克拉
美罗下界 （CRB）。假设目标角度概率分布服从以估计角度为均值，以方均根误
差下界为估计方差的高斯分布，则 第k个目标落在角度区间
()() ()0
00ˆ ˆˆ ˆ 3 CRB , 3 CRBk k kk k θ θθ θΞ= − +的概率为 0.9973。 
基于目标角度估计结果， 如何提升感知通信融合系统的通信信息传输安全，
同时兼顾感知服务质量是设计的关键。值得注意的是，该问题中通信安全性能
指标（保密率）是关于感知服务质量（角度估计精确度）的函数，因此可以通
过迭代优化的方式，逐步提升通信物理层安全性能，并分析感知服务质量和通
信信息安全之间的权衡。具体优化问题可以表示为 
 ()
()()()()
()()()()()()
()()()()()(),0 ,0 , , ,
, , ,0 ,0 ,
,,0
0
0,
0,m
 ,
                   1,
                   1,
         t
      
 u
 ax  1
s bject  
 o    HH
k x k km x km km
HH
kp x kp k x k kp
HH
kp x kp x k kps
kR
θ θ θ θ τθ
θθ
θρρ
αθ θ θ
θ θ α θθ− ≥ ∀ ∈Ω
≤+
+∀
Ξ∈
≥− ∀−
Ξ
∈Wn
a Ra a Ra
a Ra a RaJ
a Ra a Ra
12,  ∈∈Wn (11) 
其中()0
kΞ表示由第一阶段估计得出的目标角度区间， 作为优化问题设计的波形主
瓣宽度。 ρ表示给定的加权系数，当 0ρ=时该问题退化为通信安全最优问题。
问题（ 11）为非凸问题，本项目拟采用分式规划、半定松弛、凸近似等方法获得
问题的最优解或次优解，记问题的解为()() *1 *1, Wn 。然后将问题（1 1）的解带回
Fisher信息矩阵 J即可求得相应的 CRB，记为 () 1ˆ CRBkθ，当 0ρ≠时，显然
()() 10ˆ ˆ CRB CRBkkθθ< ，这意味着目标估计精确度提升，下一次迭代中的波形的
主瓣宽度随之相应变窄， 所以在下一次迭代中将表达式 （11）中主瓣角度范围区
间()0
kΞ更新为()() ()1
11ˆ ˆˆ ˆ 3 CRB , 3 CRBk k kk k θ θθ θΞ= − +。依据上述步骤迭代解决
优化问题，直至主瓣角度范围在第 t次迭代中收敛至()t
kΞ，即得到物理层安全和
目标估计 CRB联合优化的最终解()() *1 *1,tt++Wn 。 
（2）基于方向性调制 的符号级预编码  
方向性调制算法的特点在于物理层安全的设计不依赖于窃听信道信息， 根据
发送端获取的合法信道信息或合法信道部分信息， 对输入信息进行符号级预编NSFC 2024
第 23 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 25 页 ---
码，是实现通信感知融合系统内生安全的有效手段。 方向性调制设计具体可以
表示为 
 ()()
() ()arg [ ] arg
Re [ ] ReH
kk
H
k kkls
lsξ −≤
≥Γhx
hx (12) 
其中[]lx表示第 l个时隙的发送波束， ξ表示经接收端去除噪声的接收信号与发
送信号在星座图上相位差门限值，kΓ表示通信服务质量 （即用户 k处信干噪比）
门限值。 
图12 贡献性干扰区域划分 
贡献性干扰设计在方向性调制机制的基础上， 进一步设计优化问题，将多用
户干扰转化为贡献性信息， 很大程度提升系统的能效。 由此贡献性干扰设计机
制下，接收用户信噪比可以表示为 
 []{}2
2SNRH
k
k
kl
σ=hx
 (13) 
其中2
kσ表示系统噪声方差， 对于不同信息调制方式，贡献性干扰区域划分如图
12所示（绿色区域） 。基于贡献性干扰机制的方向性调制， 感知网络内生安全优
化问题可以表达为 
 ()
()()( )2maximize   
subject to Im Re tan ,HH
k k kkS
σφ ≤ −Γ ∈XX
hX hX X   (14) 
其中() SX表示感知性能指标，  *
k kk s=hh，kh为基站到第 k个用户的信道，ks为
向第 k个用户发送的信息， 对于 M-PSK调制的输入信号， Mφπ=±。优化问题
（14）中的设计是基于发送端已知的合法信道信息设计， 因此合法用户收到的
信息是正确的星座图分布， 而系统中非法终端设备收到的信息则是随机分布。
优化后在合法接收端和非法接收端的星座图如图 13所示，图中以 QPSK和8PSK
NSFC 2024
第 24 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 26 页 ---
调制为例， 对两种调制方式分别选择其中一个星座点 ，可以看出非法用户所接
收的信号是完全随机的。 由此，贡献性干扰机制可以在 未知窃听信道信息的前
提下，有效地保护传输信息安全。 
 
 
  
 
  
 
图13 合法接收端和非法接收端的星座图 
（3）基于信息论的感知隐私保密  
对于图 8中所示场景，设计中反射经过目标后，在合法的雷达接收端和通
信端的接收信号可以分别表示为 
 ()r rr rθ= +Y H XZ  (15) 
 ()c cc cθ= +Y H XZ  (16) 
其中 ()2~ 0,rr σ ZI 和 ()2~ 0,cc σ ZI 分别表示合 法接收端和通信用户端的高
斯加性白噪声， =X WS表示发送信号矩阵， ()rrθH和()ccθH分别对应二者 的
信道响应矩阵，通常情况下假设rcθθ≠。为衡量接收器的感知性能，采用接收到
的雷达信号和目标响应矩阵之间的互信息作为雷达感知的性能指标，合法雷达
接收器的感知互信息可以表示为  
 ()( )
() ( )2;| ; |
                    log det
rrr r rrr
H
rhIIθ
σ−=
= +⊗Y X YH X
I R XX I (17) 
其中 ()() vec vec
rH
h rrR HH ，同理通信用户和感知信息的互信息可以写为 
 ( ) () ( )2; | log det
cH
ccc ch I σ−= +⊗ Y H X I R XX I  (18) 
根据上述信息模型和互信息表达式，可以通过最大化互信息差以确保感知隐私
安全，同时满足通信感知服务质量等约束条件，优化问题表示为 
 ma ximize 
subject to xrc
xII−
∈R
R (19) 
其中xR为发送信号的协方差矩阵。在系统总功率一定的情况下，当 互信差最大
NSFC 2024
第 25 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 27 页 ---
化时，可以保证感知服务性能同时降低非法接收端获取目标位置信息的能力。
优化问题（ 19）是复杂非凸优化问题，可以采用连续凸近似等方法解决该优化
问题。 
综上所述， 物理层技术可以分别从通信安全和感知隐私角度防止各自信息被
窃听，因此可以作为构建通信感知融合系统的内生安全的有效手段。 
3.2.3 内生安全赋能的通感系统 多维性能适配 系统网络弹性分析  
 
图13 研究内容（3）技术路线 
（1）通感系统 联合性能指标刻画  
 随着通信感知功能的融合演进路径逐渐清晰， 设计目标转向了通信与感知整
体性能的联合提升，而非仅仅关注单一方面的性能增强。这意味着感知功能和通信功能互为补充，紧密融合，共同优化。 
在此背景下，传统的单一性能指标已不足以全面评估系统的综合“ 效用”。因
此需要引入新的性能指标，以综合反映通信和感知的相互辅助作用及其对系统
整体性能的影响。基于通信感知功能常用 的性能指标， 系统联合性能评价指标
可以如下定义。 
通感效率指标 ：通感效率指标定义为信道容量与参数估计误差之比， 用于刻画
在单位感知误差条件下的最大信道容量，在给定系统发送信噪比为 γ的前提下，
表达式可以写作 
 ()
()C
CRBXEXγ
γ
γω=+ (20) 
其中() CXγ表示当信噪比为 γ时通信的信道容量， () CRB Xγ表示当信噪比为 γ
时的参数估计克拉美罗下界（包括角度、幅度、速度等参数） ，ω表示给定常数NSFC 2024
第 26 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 28 页 ---
以限制当克拉美罗下界的 值较小时，通感系统效率 E的最大值。 由上述表达式
可以看出，当通信信道容量上升或感知估计精确度提升（即 CRB减小），Eγ会
随之增大。 因此， Eγ可以有效表征通感系统效率， 并且 Eγ越大系统联合效率越
高。 
通感效用指标 ：用于刻画给定资源 分配方案的 情况下，系统对可用功率的利用
程度，包含可达通信信道容量和可达感知估计精度，具体可以表示为  
 ()
()()()
()LB
UBC CRB+1C CRBXXUXXγγ
γ
γγηη= −  (21) 
其中[]0,1η∈表示给定的加权因子， 用于刻画通信和感知在系统中的比重。
()UBC Xγ表示纯通信系统的可达信道容量， ()LBCRB Xγ表示纯感知系统设计中克
拉美罗下界可达最小值。 依据通感效用的定义，通信感知功能不同比重下，其
性能扰动对系统的效用指标影响也不同。 因此， Uγ可表征系统对于通信和感知
的最大可用性能的利用率，并且 Uγ越大，系统能效越高。 
综上可以看出， 当新指标在达到系统效用最大化时，通信和感知的性能可能
都不处于各自的最优状态。这标志着通感深度融合网络面临的是一个多目标联
合优化的挑战。引入通感系统整体评估指标，一方面可以表征通信和感知性能之间的折中关系，另一方面有助于打破独立优化时的多维资源限制。  
（2）“开放性”与“安全性”性能权衡  
 在通信、 感知、安全集成系统中，通信和感知的性能指标用于衡量系统的服
务质量， 安全和隐私的性能指标用于衡量系统的可靠性。 在给定功率和信噪比
的前提下， 根据不同的需求 ，在设计中所分配的资源也会有相应的不同。 因此
分析系统内性能权衡为满足实际需求的进一步设计提供重要理论基础。  基于通感系统联合性能指标的提出， 可通过构建如下形式优化问题具体分析
内生安全系统的网络弹性 
 ()()() maximize  1
subject to  ABρρ+−
∈XXX
X (22) 
其中() AX表示通感联合性能指标， () BX表示安全或隐私性能指标。 []0,1ρ∈表
示给定的加权系数，通过不同的加权系数取值可以分析系统服务性能和安全性
能之间的性能权衡。 
3.3可行性分析  
本项目立足 6G网络的内生安全需求，针对通信感知融合网络的开放性而衍NSFC 2024
第 27 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 29 页 ---
生出的更复杂安全风险展开研究，沿着通感架构设计、内生安全方案以及安全
网络弹性分析路线层层递进，紧跟下一代无线网络需求，设计集通信、感知、
安全为一体的具有稳定性和准确性的系统。项目的可行性分析主要包括以下三个方面。 
（1
） 立题目标明确 ：本项目立足 6G网络内生安全需求，解决安全技术发展滞
后于通信网络 技术的问题， 具有理论研究价值和社会经济价值 。本项目
研究结果将 为无线网络发展提供 信息安全基础 ，有助于推进实现““ 全频
谱、全球覆盖、全应用、强安全” 的6G愿景。 
（2） 研究方案布局合理。 本项目紧密围绕通信感知融合系统的内生安全，引
出关键科学问题，设计 通信、感知和安全一体化集成系统中的高效动态
性能适配机制 ，沿着完善理论基础、构建优化问题、分析系统性能的技
术路线，设计提出面向复杂开放的通感系统的 内生安全和隐私方案。 研
究内容的设置基于当前无线通信和感知领域的热点问题和发展趋势，是现有理论方案的深入延伸和自然拓展。在解决相关问题时已有明确的技
术方案和思路，研究过程中不存在不可逾越的理论和技术瓶颈。
 
（3） 前期研究基础扎实 。申请人近年来一直从事感知通信融合网络相关技术
研究，尤其在信息传输安全方向具有较深的成果积累。具 体而言，申请
人在通信感知融合网络信息安全设计、波束赋形 等方向发表期刊和会议
论文 11篇，截止目前（2024 年2月）引用量 360余次， 以第一作者身
份在 IEEE  TWC发表论文 3篇，其中 2019年发表的《 Secure 
radar -communication systems with malicious targets: Integra ting radar, 
communications and jamming functionalities 》文章首次提出了通信感知融
合网络中的通信信息安全 保障方案，文章引用量 150余次。除此之外，
申请人为专著“Integrated Sensing and Communicati ons”撰写章节综述通
信感知融合系统的安全和隐私。本项目的研究内容是申请人前期研究成
果的进一步拓展和深化，申请人有能力克服研究过程中的技术困难，保
证研究方案的顺利实施并达到预期目标。 
（4） 平台与团队建设完善。申请人依托哈尔滨工业大学 电子与信息工程学院
以及“广东省空天通信与网络技术重点实验室” 。哈尔滨工业大学信息与
通信工程深圳研究院承担多项大型科研项目。实验室具备较为完善的通
信与微波工程试验设备。 申请人所在团队已经在无线通信领域深耕多年，NSFC 2024
第 28 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 30 页 ---
能够为本项目提供有效的理论支撑。此外，申请人所在团队与鹏城实验
室宽带通信部建立了良好的人员设备资源共享机制，本项目可共享鹏城
实验室宽带通信部的部分实验资源，对项目的实施过程提供补充支撑。 
4. 本项目的特色与创新之处；  
本项目瞄准 “针对复杂开放的通信感知融合网络提出高效动态适配的安全方
案”这一研究课题瓶颈，开展通信、感知、安全一体化集成系统方案研究， 力
求在保证通信感知服务质量的同时，通信感知数据实现安全交互。 基于物理层
安全理论，从内生先验信息、 鲁棒性安全应对方案、高效资源分配三个维度，
联合预编码优化设计， 明晰性能权衡与折衷机制，综合提升通感融合复杂网络
中服务性能与安全性能的动态适配高效性， 满足下一代无线网络发展的安全保
障需求。本项目的特色与创新之处具体如下： 
通感系统与物理层安全 衔接：探究通感深度融合机理， 在保证通信和感知
服务质量需求的基础上， 通过利用感知功能获取的信息实现系统架构内生先验
信息获取， 摆脱依赖外部反馈为系统带来的额外开销和风险。内生先验信息作
为通信感知网络和物理层安全的衔接点， 1）物理层安全利用通信感知系统获取
信息的多样性作为先验信息， 突破物理层研究中的重要瓶颈问题——依赖额外
反馈获取窃听信道先验信息； 2）通信感知利用物理层安全的灵活性和动态适配
性，不受信道环境动态变化的制约， 将复杂开放的巨系统设计成为集 通信、感
知、安全为一体的具有稳定性和准确性的系统。 
通信、感知、安全的高效动态适配机制 ：本项目在设计动态适配的基础上，
进一步性能权衡机制， 系统中的通信、感知、安全性能之间的关系并设计性能
折衷方案， 为资源高效性提供理论基础。 系统中的通信、感知、安全可以归为
两类性能， 其中通信感知作为服务性能， 研究中基于此提出系统联合性能指标，
包括通感效率指标和通感效用指标。联合性能指标的提出简化了 系统性能指标
的定义， 将通信、感知、安全之间的权衡问题转化为“ 开放”与“安全”折衷
设计问题， 联合加权协同优化设计， 为系统根据实际需求设计高效的动态适配
方案提供理论基础。  
这一设计在 6G网络发展中，为 信息和数据的可靠传输提供了基础的保障方
案。 
5. 年度研究计划及预期研究结果 （包括拟组织的重要学术交流
活动、国际合作与交流计划等） 。  NSFC 2024
第 29 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 31 页 ---
5.1 年度研究计划  
（1）2025 年1月1日 -- 2025年12月31日 
 针对融合感知功能的 6G网络特性展开研究，广泛阅读国内外关于通信感知
融合系统特性、其潜在的安全风险和技术瓶颈等领域的相关文献，与国内外相
关课题组联系并进行调研。分析不同融合方式，建立人工噪声辅助的通信感知
信息传输安全的理论框架。在此基础上，发表 SCI检索期刊论文 1~2 篇，参加
无线通信、信号处理国际旗舰会议 1 次。 
（2）2026 年1月1日 -- 2026年12月31日 
 针对感知辅助提升物理层安全开展研究， 深度分析通信感知融合系统信道特
性，以通信信息传输安全和感知服务质量为核心，构造性能权衡的优化函数，
并探究高效的问题求解方案。 在此基础上，发表发表 SCI检索期刊论文 1~2 篇，
参加无线通信、信号处理国际旗舰会议 1 次。 
（3）2027 年1月1日 -- 2027年12月31日 
 针对通信感知融合网络的感知隐私展开研究，在广泛阅读国内外相关文献，
总结提取融合网络中感知信息窃取方式， 分别针对通信感知频谱共享和感知通
信一体化融合网络设计相应隐私保障方案。在此基础上，发表 SCI检索期刊论
文1~2 篇，参加无线通信、信号处理国际旗舰会议 1 次。 
5.2 预期研究成果  
 技术指标： 1. 提出合理方案，采用 物理层技术分别从通信信息和感知数据
两个方面 实现通信感知融合网络的内生安全； 2. 提出通信感知系统 联合性
能指标，并分析通感系统中服务性和安全性的性能权衡。 
 学术指标：发表 SCI检索期刊论文 4~6 篇，其中 IEEE TCOM ，IEEE TWC
等本领域顶级期刊不少于 3 篇，申请发明专利 1~3 项。 
 人才培养：协助培养相关领域研究生 2 ~3名。 
 国际交流合作计划： 以本项目作为合作契机进一步扩展国际交流的深度和广
度。 计划参加 3次以上无线通信、 信号处理和雷达领域的国际权威学术会议，
如IEEE International  Conference on Communications (ICC) ，IEEE Globecom
和IEEE Radar Conference 等。拟与英国伦敦大学学院 Christos Masouros 教
授，英国皇家工程院院士 Lajos Hanzo 教授和雅典国立和卡波迪斯特里安大
学George C. Alexandropoulos 教授等无线通信/ 信号处理领域世界知名专家
继续保持紧密合作，在研究过程中定期进行学术访问交流，共同推进研究进NSFC 2024
第 30 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 32 页 ---
度。 
（二）研究基础与工作条件  
1. 研究基础 （与本项目相关的研究工作积累和已取得的研究工
作成绩） ；  
申请人长期从事通信感知系统的波束赋形设计、物理层安全研究，并取得了
一定的创新性成果，在通信和感知系统设计方面累积了丰富的研究基础和研究
经验。 具体而言， 申请人已经在本领域发表期刊和会议论文 11篇， 截止目前 （2024
年2月）引用量 360余次。此外，申请人参加 IEEE GLOBECOM 、ICC 等国际
通信旗舰会议， 受邀参加 SPAWC、Asilomar、EUSIPCO 等国际会议， 开展学术
交流并作口头报告。担任 IEEE Trans. Wireless Commun., IEEE Trans. Commun., 
和IEEE Trans. Signal  Processing 等十余个期刊/ 会议审稿人，与国内外学者保持
着良好的学术交流和合作。下面将从三个方面介绍研究基础。  人工噪声辅助下的波束赋形研究方面， 主要致力于研究通信感知融合网络与
传统通信网络在物理层安全方面设计差异， 定义在通信感知融合系统中的安
全性能指标， 在人工噪声辅助下， 基于波束赋形理论设计安全方案保护通信
信息安全， 为研究内容一的一体化波形设计提供充分的研究基础。其中代表
性工作有： 
[1] Nanchi Su, Fan Liu, and Christos M asouros. "Secure radar -communication 
systems with malicious targets: I ntegrating radar, communications and jamming 
functionalities."  IEEE Transacti ons on Wireless Communications  20.1 (2020): 
83-95. 
[2] Nanchi Su, Fan Liu, and Christos Masouros. "Enhancing the physical layer 
security of dual -functional radar communication systems."  2019 IEEE Global 
Communications Conference (GLOBECOM) . IEEE, 2019. 
[3] Zhongxiang Wei , Fan Liu, C hristos  Masouros, Nanchi Su ,  Athina Petropulu. 
"Toward multi- functional 6G wirele ss networks: Integrating sensing, 
communication, and security."  IEEE C ommunications Magazine  60.4 (2022): 
65-71. 
其中代表性工作 [1]首次提出 通信感知融合系统中 通信信息安全设计方案 ，
截至2024年3月，文章引用量 达到 150余次。 
 通信感知系统内生先验信息方面， 在毫米波背景下，研究通信感知信道相似NSFC 2024
第 31 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 33 页 ---
性对保密性能和感知服务的影响， 为单目标感知扩展到多目标感知的场景提
供理论依据， 设计采用分式规划、 连续凸近似等算法解决复杂非凸优化问题，
并分析对比优化性能，这也为研究内容二积累了经验。其中代表性工作有：  
[4] Nanchi Su, Fan Liu, and Christos Masouros. "Sensing -assisted eavesdropper 
estimation: An ISAC breakthrough in physical layer security."  IEEE Transactions 
on Wireless Communications  (2023).  
[5] Nanchi Su, Fan Liu, and Christos Masouros. "Sensing -assisted physical layer 
security." WSA & SCC 2023; 26th International ITG Workshop on Smart 
Antennas and 13th Conference on Systems, Communications, and Coding. VDE, 
2023. 
[6] Nanchi Su, Fan Liu, and Christos Masouros. "Secure Integrated Sensing and 
Communication  Systems with the Assistance of Sensing Functionality."  2023 
31st European Signal Processing Conference (EUSIPCO) . IEEE, 2023. 
 通感内生安全系统性能权衡方面， 主要贡献在于对已有感知通信频谱共享环
境下存在的雷达隐私窃取风险进行研究， 针对非基于学习机制的窃听模式提
出相应的隐私保障方案， 有效防止在恶意窃听端已知信道信息的情况下估计
出雷达所在位置。 在此基础上，深入分析系统中服务性能与安全性能之间性
能权衡和折衷。 申请人在此场景对信息安全隐私和性能权衡的研究基础，为
本项目的研究内容三中研究场景的扩展提供有力的支撑。 其中代表性的工作
有： 
[7] Nanchi Su, Fan Liu, Christos Masouros, Ahmed Al Hilli. "Security and 
Privacy in ISAC Systems."  Integrated Sensing and Communications . Singapore: 
Springer Nature Singapore, 2023. 477- 506. 
[8] Nanchi Su, Fan Liu, Zhongxiang Wei, Ya -Feng Liu, Christos Masouros . 
"Secure dual -functional radar -communication transmission: Exploiting 
interference for resilience against target eavesdropping."  IEEE Transactions on 
Wireless Communications  21.9 (2022): 7238- 7252. 
[9] Nanchi Su, Zhongxiang Wei, Christos Masouros . "Secure dual -functional 
radar -communication system via exploiting known interference in the presence of 
clutter."  2021 IEEE 22nd International Workshop on Signal Processing Advances 
in Wireless Communications (SP AWC) . IEEE, 2021. NSFC 2024
第 32 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 34 页 ---
[10] Nanchi Su, Fan Liu, Christos Masouros, Tharmalingam Ratnarajah, Athina 
Petropulu. "Secure Dual -functional Radar -Communication Transmission: 
Hardware -Efficient Design."  2021 55th Asilomar Conference on Signals, Systems, 
and Computers. IEEE, 2021. 
2. 工作条件 （包括已具备的实验条件，尚缺少的实验条件和拟
解决的途 径，包括利用国家实验室、国家重点实验室和部门重点实验
室等研究基地的计划与落实情况） ；  
本项目依托单位为哈尔滨工业大学。申请人所在深圳校区通信工程研究中
心长期从事无线通信、空间通信、网络优化、人工智能、信号与信息处理技术 、
芯片设计与安全等 研究。申请人所在团队为广东省空天通信与网络技术重点实
验室，广东省空天通信与网络技术重点实验室面向国家网络强国与航天强国的
重大战略需求，及大湾区推动卫星应用装备和空天信息技术发展的战略需求，
聚焦深空探测通信、大规模卫星网络及无人自主通信系统等电子信息与航空航
天领域的重大科技问题。 该实验室面向空间信息网络领域的基础设施较为完善，
现有专用实验室面积 1000 平方米，拥有各类通用、专用测试测试仪器超过100 
台（套） 。 其中包括中星10 号Ku 频段收发天线2 套，实验用无人机5 套，USRP 
软件无线电平台（USRP -2930、USRP -2974）35 套，LabVIEW 软件套装1 套，
R&S 的信号分析系统（FSQ26 ） ，泰克的高性能示波器（DPO070804 ） 、频谱分
析仪（RSA3308A ）、逻辑分析仪（TLA5204B ） ，以及安捷伦的信号发生器
（E8267D）等高端设备，总价值超过3000 万元，2022 年底计划建成天基物联
网分布式数据中心（具有≥ 3000T的存储能力，≥ 5000 颗计算核心，可支持 ≥
300 物理节点的10Gbps 高速数据交换机组） ，具备良好的科研平台基础保障。 
重点实验室主任为张钦宇教授，团队成员在职共计36 人，近三年科研团队
产出了面向深空探测- 测控-通信任务的高可靠信息传输技术、面向空- 天-海广域
覆盖网络的高时效组网与传输技术、基于集群运动的无人机可重构编队通信与网络技术等重要研究成果，解决了深空超远距离可靠通信、卫星广域覆盖网络
实时互联、无自主系统高效组网传输等技术难题。基于以上研究成果，获得国家发明专利授权20余项，国家行业标准1项，发表高水平学术论文60余篇，获得
3项领域内优秀论文奖励，实现5 项关键技术的成果转化。科研团队承担省部级
以上纵向科研项目25项，包含国家自然科学基金重点项目、重大仪器研制项目、
国家重点研发计划等国家重大科研项目，在研经费超过8000 万。实验室具有高NSFC 2024
第 33 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 35 页 ---
水平科技人才8人，包含国家杰青及国家海内外高层次人才计划入选者。实验室
广泛开展科研合作，设立开放基金10项，服务10家企业单位，获得3项技术应用
证明。 
此外，申请人与伦敦大学学院的Christos  Masouros 教授、英国爱丁堡大学的
Tharm Ratnarajah 教授、美国罗格斯大学的Athina Petropu lu教授、雅典国立和卡
波迪斯特里安大学Georg e C. Alexandropoulos 教授和南方科技大学刘凡教授等知
名专家学者建立了良好的合作关系，共同发表了多篇学术论文，为本项目开展
国内外学术交流与合作提供了良好的条件。 
综上，本项目团队拥有良好的实验条件和科学仪器、强大的科研创新载体
和丰富的科研项目经验，可为本项目的科研活动开展、总体思路设计、技术方
案实施和项目成果验证提供充分的平台支撑。 
3. 正在承担的与本项目相关的科研项目情况 （申请人正在承担
的与本项目相关的科研项目情况， 包括国家自然科学基金的项目和国
家其他科技计划项目，要注明项目的资助机构、项目类别、批准号、
项目名称、 获资助金额、 起止年月、 与本项目的关系及负责的内容等） ；  
无。 
4. 完成国家自然科学基金项目情况 （对申请人负责的前一个已
资助期满的科学基金项目（项目名称及批准号）完成情况、后续研究
进展及与本申请项目的关系加以详细说明。 另附该项目的研究工作总结摘要（限
500字）和相关成果详细目录） 。  
无。 
（三）其他需要说明的情况  
1. 申请人同年申请不同类型的国家自然科学基金项目情况（列
明同年申请的其他项目的项目类型、项目名称信息，并说明与本项目之间的区别与联系 ）。 
无。 
2. 具有高级专业技术职务（职称）的申请人是否存在同年申请
或者参与申请国家自然科学基金项目的单位不一致的情况； 如存在上
述情况，列明所涉及人员的姓名， 申请或参与申请的其他项目的项目
类型、项目名称、单位名称、上述人员在该项目中是申请人还是参与NSFC 2024
第 34 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 36 页 ---
者，并说明单位不一致原因。 
无。 
3. 具有高级专业技术职务（职称）的申请人是否存在与正在承
担的国家自然科学基金项目的单位不一致的情况；如存在上述情况，
列明所涉及人员的姓名，正在承担项目的批准号、项目类型、项目名
称、单位名称、起止年月，并说明单位不一致原因。  
无。 
4. 其他。  
无。 
 
NSFC 2024
第 35 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 37 页 ---
2024版
苏南池
 
( BRID: 09007.00.62796 )
 
简历
 
哈尔滨工业大学,
 
哈尔滨工业大学（深圳）,
 
无
教育经历：
(
1
) 
2018-09 至
 
2023-04,
 
University College London,
 
Electrical and Electronic Engineering,
 
博
士
(
2
) 
2016-09 至
 
2018-07,
 
哈尔滨工业大学,
 
信息与通信工程,
 
硕士
(
3
) 
2011-09 至
 
2015-07,
 
哈尔滨工业大学,
 
通信工程,
 
学士
博士后工作经历：
(
1
) 
2023-11 至
 
今,
 
在站,
 
哈尔滨工业大学，哈尔滨工业大学（深圳）
科研与学术工作经历（博士后工作经历除外）：
无
曾使用其他证件信息：
无
近五年主持或参加的国家自然科学基金项目/课题：
无
近五年主持或参加的其他科研项目/课题（国家自然科学基金项目除外）：
(
1
) 
European Union’s Horizon 2020 research and innovation programme,
 
MSCA-ITN-ETN,
 
812991,
H2020-MSCA-ITN-2018,
 
2018-10 至 
2022-09,
 
2940万元,
 
结题,
 
参与
(
2
) 
Engineering and Physical Sciences Research Council (EPSRC),
 
Research Grant,
 
EP/R007934/1,
Exploiting interference for physical layer security in 5G networks,
 
2018-02 至 
2021-04,
 
558万元,
结题,
 
参与
代表性研究成果和学术奖励情况（填写代表性论文时应根据其发表时的真实情况如实规范列
出所有作者署名，并对本人署名情况进行标注，包括：①作者署名按姓氏排序；②唯一第一
作者；③共同第一作者；④唯一通讯作者；⑤共同通讯作者；⑥其他情况）：
一、代表性论著（请在“申请书详情”界面，点开“人员信息”-“代表性成果”卡片查看对
应的全文）：
(
1
) 
Nanchi Su
Nanchi Su
; Fan Liu; Zhongxiang Wei; Ya-Feng Liu; Christos Masouros 
; 
Secure Dual-
Functional Radar-Communication Transmission: Exploiting Interference for Resilience Against
Target Eavesdropping
, 
IEEE Transactions on Wireless Communications
, 
2022
, 
21 
(9)
: 7238-
7252      (
期刊论文
期刊论文
)  ( 本人标注: 唯一第一作者 )
(
2
) 
Nanchi Su
Nanchi Su
; Fan Liu; Christos Masouros 
; 
Secure Radar-Communication Systems With Malicious
Targets: Integrating Radar, Communications and Jamming Functionalities
, 
IEEE Transactions on
Wireless Communications
, 
2021
, 
20
(1)
: 83-95      (
期刊论文
期刊论文
)  ( 本人标注: 唯一第一作者 )
(
3
) 
Nanchi Su
Nanchi Su
; Fan Liu; Christos Masouros 
; 
Sensing-Assisted Eavesdropper Estimation: An ISAC
Breakthrough in Physical Layer Security
, 
IEEE Transactions on Wireless Communications
, 
2023
,
1
(1)
: 1-1      (
期刊论文
期刊论文
)  ( 本人标注: 唯一第一作者 )NSFC 2024
第 36 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 38 页 ---
(
4
) 
Nanchi Su
Nanchi Su
; Fan Liu; Christos Masouros 
; 
Enhancing the Physical Layer Security of Dual-
Functional Radar Communication Systems
, 
2019 IEEE Global Communications Conference (GLOBECOM)
,
Waikoloa, HI, USA
, 
2019-12-9
至
2019-12-13
      (
会议论文
会议论文
)  ( 本人标注: 唯一第一作者 )
(
5
) 
Nanchi Su
Nanchi Su
; Fan Liu; Christos Masouros; Tharmalingam Ratnarajah; Athina Petropulu 
; 
Secure
Dual-functional Radar-Communication Transmission: Hardware-Efficient Design
, 
2021 55th Asilomar
Conference on Signals, Systems, and Computers
, 
Pacific Grove, CA, USA
, 
2021-10-31
至
2021-11-
3
      (
会议论文
会议论文
)  ( 本人标注: 唯一第一作者 )
二、论著之外的代表性研究成果和学术奖励：
无
NSFC 2024
第 37 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 39 页 ---
附件信息
序号 附件名称 备注 附件类型
1TWC 1 一作期刊，TWC 代表性论著
2TWC 2 一作期刊，TWC 代表性论著
3TWC 3 一作期刊，TWC 代表性论著
4Conf Globecom 代表性论著
5Invited Conf 会议邀约投稿 代表性论著
NSFC 2024
第 38 页
国家自然科学基金申请书 2024版
版本：24110306153148766

--- 第 40 页 ---
项目名称： 基于物理层安全的通感内生安全架构与网络性能动态适配机制
资助类型： 青年科学基金项目
申请代码： F0105.移动通信
国家自然科学基金项目申请人和参与者承诺书
为了维护国家自然科学基金项目评审公平、公正，共同营造风清气正的科研生态，本人 在此郑重
严格遵守《中华人民共和国科学技术进步法》《国家自然科学基金条例》《关于进一步加强科承诺：
研诚信建设的若干意见》《关于进一步弘扬科学家精神加强作风和学风建设的意见》《关于加强科技
伦理治理的意见》以及科技部、自然科学基金委关于科研诚信建设有关规定和要求；申请材料信息真
实准确，不含任何涉密信息或敏感信息，不含任何违反法律法规或违反科研伦理规范的内容；在国家
自然科学基金项目申请、评审和执行全过程中，恪守职业规范和科学道德，遵守评审规则和工作纪
律，杜绝以下行为：
（一）抄袭、剽窃他人申请书、论文等科研成果或者伪造、篡改研究数据、研究结论；
（二）购买、代写申请书；购买、代写、代投论文，虚构同行评议专家及评议意见；购买实验数
据；
（三）违反成果发表规范、署名规范、引用规范，擅自标注或虚假标注获得科技计划等资助；
（四）在项目申请书中以高指标通过评审，在项目计划书中故意篡改降低相应指标；
（五）以任何形式探听或散布尚未公布的评审专家名单及其他评审过程中的保密信息；
（六）本人或委托他人通过各种方式和途径联系有关专家进行请托、游说、“打招呼”，违规到
评审会议驻地窥探、游说、询问等干扰评审或可能影响评审公正性的行为；
（七）向工作人员、评审专家等提供任何形式的礼品、礼金、有价证券、支付凭证、商业预付
卡、电子红包，或提供宴请、旅游、娱乐健身等任何可能影响评审公正性的活动；
（八）违反财经纪律和相关管理规定的行为；
（九）其他弄虚作假行为。
如违背上述承诺，本人愿接受国家自然科学基金委员会和相关部门做出的各项处理决定，包括但
不限于撤销科学基金资助项目，追回项目资助经费，向社会通报违规情况，取消一定期限国家自然科
学基金项目申请资格，记入科研诚信严重失信行为数据库以及接受相应的党纪政务处分等。
申请人签字：
国家自然科学基金申请书 2024版

--- 第 41 页 ---
项目名称： 基于物理层安全的通感内生安全架构与网络性能动态适配机制
资助类型： 青年科学基金项目
申请代码： F0105.移动通信
国家自然科学基金项目申请单位承诺书
为了维护国家自然科学基金项目评审公平、公正，共同营造风清气正的科研生态， 本单位郑重承
申请材料中不存在违背《中华人民共和国科学技术进步法》《国家自然科学基金条例》《关于进诺：
一步加强科研诚信建设的若干意见》《关于进一步弘扬科学家精神加强作风和学风建设的意见》《关
于加强科技伦理治理的意见》以及科技部、自然科学基金委关于科研诚信建设有关规定和要求的情
况；申请材料符合《中华人民共和国保守国家秘密法》和《科学技术保密规定》等有关法律法规和规
章制度要求，不含任何涉密信息或敏感信息；申请材料不含任何违反法律法规或违反科研伦理规范的
内容；申请人符合相应项目的申请资格；依托单位、合作研究单位、申请人及主要参与者不在限制申
报、承担或参与财政性资金支持的科技活动的期限内；在项目申请和评审活动全过程中，遵守有关评
审规则和工作纪律，杜绝以下行为：
（一）以任何形式探听或公布未公开的项目评审信息、评审专家信息及其他评审过程中的保密信
息，干扰评审专家的评审工作；
（二）组织或协助申请人/参与者向工作人员、评审专家等给予任何形式的礼品、礼金、有价证
券、支付凭证、商业预付卡、电子红包等；宴请工作人员、评审专家，或组织任何可能影响科学基金
评审公正性的活动；
（三）支持、放任或对申请人/参与者抄袭、剽窃、重复申报、提供虚假信息（含身份和学术信
息）等不当手段申报国家自然科学基金项目疏于管理；
（四）支持或协助申请人/参与者采取“打招呼”“围会”等方式影响科学基金项目评审；
（五）其他违反财经纪律和相关管理规定的行为。
如违背上述承诺，本单位愿接受自然科学基金委和相关部门做出的各项处理决定，包括但不限于
停拨或核减经费、追回项目已拨经费、取消本单位一定期限国家自然科学基金项目申请资格、记入科
研诚信严重失信行为数据库以及主要责任人接受相应党纪政务处分等。
依托单位公章:
日期： 年月日
国家自然科学基金申请书 2024版

================================================================================


================================================================================
文件: 人工噪声辅助通感安全.pdf
页数: 13
================================================================================


--- 第 1 页 ---
IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 20, NO. 1, JANUARY 2021 83
Secure Radar-Communication Systems
With Malicious Targets: Integrating
Radar, Communications and
Jamming Functionalities
Nanchi Su ,Graduate Student Member, IEEE ,F a nL i u ,Member, IEEE ,
and Christos Masouros ,Senior Member, IEEE
Abstract — This article studies the physical layer security in
a multiple-input-multiple-output (MIMO) dual-functional radar-communication (DFRC) system, which communicates with down-link cellular users and tracks radar targets simultaneously. Here,the radar targets are considered as potential eavesdropperswhich might eavesdrop the information from the communicationtransmitter to legitimate users. To ensure the transmissionsecrecy, we employ artiﬁcial noise (AN) at the transmitter andformulate optimization problems by minimizing the signal-to-noise ratio (SNR) received at radar targets, while guaranteeingthe signal-to-interference-plus-noise ratio (SINR) requirement atlegitimate users. We ﬁrst consider the ideal case where boththe target angle and the channel state information (CSI) areprecisely known. The scenario is further extended to more generalcases with target location uncertainty and CSI errors, where wepropose robust optimization approaches to guarantee the worst-case performance. Accordingly, the computational complexityis analyzed for each proposed method. Our numerical resultsshow the feasibility of the algorithms with the existence ofinstantaneous and statistical CSI error. In addition, the secrecyrate of secure DFRC system grows with the increasing angularinterval of location uncertainty.
Index Terms — Dual-functional radar-communication system,
secrecy rate, artiﬁcial noise, channel state information.
I. I NTRODUCTION
THE increasing spectrum congestion has intensiﬁed the
efforts in dynamic spectrum licensing and soon spectrum
is to be shared between radar and communication applications.
Manuscript received September 9, 2019; revised February 26, 2020,
June 28, 2020, and August 24, 2020; accepted September 5, 2020. Date
of publication September 17, 2020; date of current version January 8,2021. This work was supported in part by the European Union’s Horizon2020 Research and Innovation Programme through the Marie Skłodowska-
Curie Grant 793345, in part by the Engineering and Physical Sciences
Research Council (EPSRC) of the U.K. under Grant EP/R007934/1 andGrant EP/S026622/1, in part by the U.K. MOD University Defence Research
Collaboration (UDRC) in Signal Processing, and in part by the China Schol-
arship Council (CSC). This article was presented in part at the IEEE GlobalCommunications Conference (GLOBECOM), Hawaii, USA, December 2019.The associate editor coordinating the re view of this article and approving it
for publication was S. Buzzi. (Corresponding author: Fan Liu.)
The authors are with the Department of Electronics and Electrical Engi-
neering, University College London, London WC1E 7JE, U.K. (e-mail:
<EMAIL>; <EMAIL>; <EMAIL>).
Color versions of one or more of the ﬁgures in this article are available
online at https://ieeexplore.ieee.org.
Digital Object Identiﬁer 10.1109/TWC.2020.3023164Govermental organizations such as the US Department of
Defence (DoD) have a documented requirement of releas-
ing 865 MHz to support telemetry by the year of 2025,
but only 445 MHz is available at present [1]. As a result,
the operating frequency bands of communication and radar
are overlapped with each other [2], which leads to mutual
interference between two systems. Furthermore, both systems
have been recently given a common spectrum portion by theFederal Communication Commission (FCC) [3]–[5]. To enable
the efﬁcient usage of the spectrum, research efforts are well
underway to address the issue of communication and radar
spectrum sharing (CRSS).
Aiming for realizing the spectral coexistence of individual
radar and communication systems, several interference miti-
gation techniques have been proposed in [6]–[11]. As a step
further, dual-functional radar-communication (DFRC) systemthat is capable of realizing not only the spectral coexis-
tence, but also the shared use of the hardware platform, has
been regarded as a promising research direction [12]–[15].
It is noteworthy that the DFRC technique has already been
widely explored in numerous civilian and military applica-tions, including 5G vehicular network [16], WiFi based indoor
positioning [17], low-probability-of-intercept (LPI) commu-
nication [18] as well as the advanced multi-function radiofrequency concept (AMRFC) [19].
In the DFRC system, radar and communication function-
alities are realized by a well-designed probing waveform
that carries communication signalling and data. Evidently,
this operation implicates secur ity concerns, which are largely
overlooked in the relevant DFRC literature. It is known that
typical radar requires to focus the transmit power towards
the directions of interest to obtain a good estimation ofthe targets. Nevertheless, in the case of DFRC transmission,
critical information embedded in the probing waveform could
be leaked to the radar targets, which might be potential
eavesdroppers at the adversary’s side. To this end, it is
essential to take information security into consideration for theDFRC design. In the communication literature, physical layer
security has been widely investigated, where the eavesdrop-
pers’ reception can be crippled by exploiting transmit degreesof freedom (DoFs) [20]. MIMO secrecy capacity problems
1536-1276 © 2020 I EEE. Personal u se is perm itted, but republication/redistri bution requires IEEE permission.
See https://www.ieee.org/publications/rights/index.html for more information.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 2 页 ---
84 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
were considered in [21]–[23]. Besides, another meaningful
technique for enabling physical layer secrecy was presented
in [20], [24], namely artiﬁcial noise (AN) aided transmission.
Furthermore, the AN generation algorithm studied in [24], [25]were with the premise of publicly known channel state infor-
mation (CSI) in a fading environment, where AN lies in
null-space of transmission channel. As the perfect CSI is notavailable accurately in practice, AN will leak into the signal
space and deteriorate the use ful SNR. To tackle the AN in the
design of transmission techniques with imperfect CSI, robust
schemes have been investigated in recent work [26], [27].
Since these techniques are conceived to address the PHY layersecurity for communication-only scenarios, they are unable to
be straightforwardly applied to the secure DFRC transmis-
sion. Moreover, some concurrent AN-aided studies employedcooperative jammers to improve secure communication
[28], [29]. Cooperative jamming is to implement user cooper-
ation for wireless networks with secrecy constraints.
Given the dual-functional nature of the DFRC systems,
the secrecy issue can be addressed on the aspect of eitherradar or communication. From the perspective of the radar
system, existing works focus on the radar privacy mainte-
nance [8], [30], [31]. A functional architecture was presentedin [8] for the control center aiming at coordinating the coop-
eration between radar and communication while maintaining
the privacy of the radar system. In [30], obfuscation tech-
niques have been proposed to counter the inference attacks
in the scenario of spectrum sharing between military radarsand commercial communication systems. Besides, the work
of [31] showed the probability for an adversary to infer radar’s
location by exploiting the communication precoding matrices.On the other hand, the works of [32], [33] have studied
the secrecy problems from the viewpoint of communications.
In [32], the MIMO radar transmits two different signals simul-
taneously, one of which is embedded with desired information
for the legitimate receiver, the other one consists of false infor-mation to confuse the eavesdroppers. Both of the signals are
used to detect the target. Several optimization problems were
presented, including secrecy rat e maximization, target return
signal-to-noise ratio (SNR) maximization and transmit power
minimization. Then, a uniﬁed joint system of passive radar
and communication systems was considered in [33], where
the communication receivers might be eavesdropped by the
target of passive radar. As the radar system typically addressesuncooperative or even malicious targets, it is essential to
guarantee the physical layer security in the safety-critical
DFRC systems. To guarantee the secrecy of legitimate userin the communication system, the optimization problem was
designed to maximize the SNR at the passive radar receiver
(RR) while keeping the secrecy r ate above a certain threshold.
While the aforementioned approaches are well-designed by
sophisticated techniques, the AN-aided physical layer security
remains to be explored for the DFRC systems under practical
constraints.
To the best of our knowledge, most of the present works
regarding secure transmission in DFRC system rely on the
assumption of precisely known channel state information (CSI)
at the transmitter. To address the beamforming design ina more general context, we take the imperfect CSI into account
in our work, which includes instantaneous and statistical
CSI with norm-bounded errors. Moreover, the well-known
S-procedure and Lagrange dual function have been adoptedto reformulate the optimization problem, which can be solved
by Semi-deﬁnite Relaxation (SDR) approach. In addition to
the CSI issues, we also explore the radar-speciﬁc targetuncertainty, where we employ a robust adaptation technique
for target tracking.
Accordingly, in this article, we propose several optimization
problems aiming at ensuring information transmission security
of the DFRC system. To be speciﬁc, we consider a MIMODFRC base station (BS) that is serving multiple legitimate
users while detecting targets. It should be noted that these
targets are assumed to be potential eavesdroppers. Moreover,spatially focused AN is employed in our methods. Throughout
the paper, we aim to minimize the SNR at the target while
ensuring the signal-to-interference-plus-noise ratio (SINR) at
each legitimate user. Within this scope, we summarize our
contributions as follows:
•We ﬁrst consider the ideal scenario under the assumptionsof perfect CSI and known precise location of targets. The
beampattern is formed by appr oaching to a given bench-
mark radar beampattern. By doing so, the formulated
optimization problem can be ﬁrstly recast as Fractional
programming (FP) problem [34], and then solved bythe SDR.
•We investigate the problem under the practical conditionof target location uncertainty, where we formulate abeampattern with a given angular interval that the targets
might fall into.
•We impose the imperfect communication CSI to theoptimization in addition to the above constraints, where
worst-case FP problems are fo rmulated to minimize the
maximum SNR at the target with bounded CSI errors.
•We consider the statistical CSI, which is more practicaldue to signiﬁcantly reduced feedback requirements [35].To tackle this scenario, we f urther formulate the eaves-
dropper SNR minimization problem considering the error
bound of statistical CSI.
•We derive the computational complexity for eachproposed algorithm.
Moreover, for clarity, some important insights are listed as
follows:
•When there is uncertainty on the target direction,the DFRC beam width inevita bly needs to be widened,
hence increasing the range of angles where the signal can
be eavesdropped. Accordingly, ensuring security requires
higher AN power, which impacts the power allocated tosignal transmission, to transmit conﬁdential information;
•When the peak to sidelobe ratio (PSLR) requirement forthe radar increases with a ﬁxed power budget, more power
is spent on satisfying the radar requirement. Accordingly,
the reduced power budget for communications deterio-rates the secrecy rate;
•When the CSI is only imperfectly or statistically known,with ﬁxed power budget, the secrecy rate deteriorates.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 3 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 85
Fig. 1. Dual-functional Radar-Communi cation system detecting target which
comprise a potential eavesdropper.
The system achieves higher secrecy rate when both the
target location and CSI are precisely known.
This article is organized as follows. Section II gives the system
model. The optimization problems based on perfect CSI are
addressed in Section III and IV for precise location and uncer-
tain direction of targets, respectively. In Section V and VI,more general context of imperfect CSI is considered, which
addresses issues with imperfect CSI under norm-bounded and
statistical errors, respectivel y. Section VII provides numerical
results, and Section VIII concludes the paper.
Notations : Unless otherwise speciﬁed, matrices are denoted
by bold uppercase letters (i.e., H), vectors are represented by
bold lowercase letters (i.e., x), and scalars are denoted by
normal font (i.e., α). Subscripts indicate the location of the
entry in the matrices or vectors (i.e., s
i,jandlnare the (i,j)-th
and the n-th element in Sandl, respectively). tr (·)and
vec (·)denote the trace and the vectorization operations. (·)T,
(·)H,(·)∗and(·)†stand for transpose, Hermitian transpose,
complex conjugate and Moore-Penrose pseudo-inverse of thematrices, respectively. diag (·)represents the vector formed
by the diagonal elements of the matrices and rank (·)is rank
operator. /bardbl·/bardbl,/bardbl·/bardbl
∞and/bardbl·/bardblFdenote the l2norm, l∞and
the Frobenius norm respectively. E{·}denotes the statistical
expectation. [·]+denotes max{·,0}.
II. S YSTEM MODEL
We consider a dual-functional MIMO DFRC system, which
consists of a DFRC base station, legitimate users and target
which is a potential eavesdropper, as shown in Fig. 1. TheDFRC system is equipped with uniform linear array (ULA) of
Nantennas, serving Ksingle-antenna users, while detecting
point-like target, which is a single-antenna eavesdropper. For
convenience, the multi-antenna transmitter, the legitimate users
and the target will be referred as Alice, Bobs and Everespectively.
A. Signal Model
In the scenario shown in Fig. 1, the DFRC base station Alice
intends to send conﬁdential information to single-antenna
legitimate users, i.e. Bobs, with the presence of the potential
eavesdropper, i.e. Eve. The received symbol vector at Bobsand Eve can be respectively modeled as
y=Hx+z
r=αa
H(θ)x+e, (1)
where H=[h1,h2,···,hK]T∈CK×Nis the channel
matrix, x∈CNis the transmitted signal vector, αrepresents
the complex path-loss coefﬁcient, θis the angle of tar-
get,a(θ)=/bracketleftbig
1ej2πΔs i n( θ)···ej2π(N−1)Δ sin( θ)/bracketrightbigT∈CN×1
denotes the steering vector of the transmit antenna array, with
Δbeing the normalized interval between adjacent antennas.
zandeare the noise vector and scalar, respectively, with
zi∼C N/parenleftbig
0,σ2
zi/parenrightbig
,e∼C N/parenleftbig
0,σ2
e/parenrightbig
.
Consider AN-aided transmit beamforming, the transmit
vectorxcan be written as
x=Ws+n (2)
wheres∈CKis the desired symbol vector of Bobs, where
we assume E/bracketleftbig
ssH/bracketrightbig
=I,W=[w1,w2,···,wK]∈CN×K
is the beamforming matrix, each column of the beamforming
matrixWrepresents the beamforming vector of each user, nis
an artiﬁcial noise vector1generated by Alice to avoid leaking
information to Eves. It is assumed that n∼C N (0,RN).
Additionally, we assume that the desired symbol vector sand
the artiﬁcial noise vector nare independent with each other.
According to [9], it is presumed that the above signal is
used for both radar and communication operations, where each
communication symbol is considered as a snapshot of a radar
pulse. Then, the covariance matrix of the transmitted dual-functional waveform
2can be given as
RX=E/bracketleftbig
xxH/bracketrightbig
=K/summationdisplay
i=1Wi+RN, (3)
where Wi/defineswiwH
i. Then, the beampattern can be
expressed as
Pbp=aH(θ)RXa(θ). (4)
B. Metrics
To evaluate the performance of the system, we deﬁne a
number of performance metrics in this subsection. Initially,
based on the aforementioned system model, the SINR of the
i-th user can be written as
SINR i=E/bracketleftBig/vextendsingle/vextendsinglehT
iwisi/vextendsingle/vextendsingle2/bracketrightBig
/summationtextK
k/negationslash=i,k=1E/bracketleftBig/vextendsingle/vextendsinglehT
iwksk/vextendsingle/vextendsingle2/bracketrightBig
+E/bracketleftBig/vextendsingle/vextendsinglehT
in/vextendsingle/vextendsingle2/bracketrightBig
+σ2zi
=hT
iWih∗i
/summationtextK
k/negationslash=i,k=1/parenleftbig
hT
iWkh∗i/parenrightbig
+/parenleftbig
hT
iRNh∗i/parenrightbig
+σ2zi.(5)
1Note that the generated AN is colored Gaussian noise, which has favorable
auto-correlation properties, and will hence impose negligible impact on
reconstructing the target at the radar receiver.
2In general radar system, the transmitted waveform is required to be known
at the receiver side in order to estimate the target. As the waveform changessymbol by symbol in DFRC systems, overhead generated by the sharing of
the transmitted waveform with the receiver is signiﬁcant. Thus, monostatic
radar is assumed to be employed in our system model rather than the bistaticradar, i.e., the transmitter and the receiver are collocated.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 4 页 ---
86 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
Equation (5) can be simpliﬁed
SINR i=tr/parenleftbig
h∗
ihTiWi/parenrightbig
/summationtextK
k/negationslash=i,k=1tr/parenleftbig
h∗
ihTiWk/parenrightbig
+tr/parenleftbig
h∗
ihTiRN/parenrightbig
+σ2zi.
(6)
The achievable transmission rate of legitimate users is given as
RCi=l o g2(1 + SINR i). (7)
Likewise, based on the given signal model in (2) and (3),
SNR at Eve can be given as [36]
SNR E=|α|2aH(θ)/summationtextK
i=1Wia(θ)
|α|2aH(θ)RNa(θ)+σ2e. (8)
In practice, the precise location of the target is unlikely to be
known in advance. Herewith, we d eﬁne a region of target loca-
tion angular uncertainty, which is given as [θ0−Δθ,θ0+Δθ],
withΔθbeing the uncertainty region given a priori. This
scenario will be elaborated in S ection IV . Then, the achievable
transmission rate of Eve can be expressed as
RE=l o g2(1 + SNR E). (9)
Additionally, the transmit power is expressed as
Pt=tr(RX). (10)
Given the achievable transmission rates of Bobs and Eve,
the worst-case secrecy rate of the system is deﬁned as3[37]
SR=m i n
i[RCi−RE]+. (11)
Note that since we focus on optimizing the covariance
matrices of both the DFRC signal and the AN, the range
sidelobe is not considered in this article due to its reliance onsymbol-level waveform designs, which is designated as our
future work.
C. Channel Model With Imperfect CSI and Statistical CSI
1) Imperfect CSI: According to [38], an additive channel
error model of i-th downlink user can be formulated as h
i=
˜hi+ei,w h e r e ˜hiis the estimated channel vector known
at Alice, and eidenotes the channel uncertainty within the
spherical region /Ifracturi={ei|/bardblei/bardbl2≤μ2
i}.
2) Statistical CSI: As the statistical CSI is known to the
BS instead of instantaneous CSI, we rewrite the SINR of the
i-th user as
SINR i=tr/parenleftBig
˜RhiWi/parenrightBig
/summationtextK
k/negationslash=i,k=1tr/parenleftBig
˜RhiWk/parenrightBig
+tr/parenleftBig
˜RhiRN/parenrightBig
+σ2z,(12)
where ˜Rhi=E/braceleftbig
h∗
ihTi/bracerightbig
denotes the i-th user’s down-
link channel covariance matrix with uncertainty. As a result,
the true channel covariance matrix can be modeled as Rhi=
˜Rhi+Δi,∀i,w h e r e Δi,∀iare the estimated error matrices.
The Frobenius norm of the error matrix of the i-th user is
assumed to be upper-bounded by a known constant δi,w h i c h
can be expressed as /bardblΔi/bardbl≤δi.
3The secrecy is deﬁned as the worst-case secrecy against all potential Bob-
Eve pairs in a multi-user multi-Eve scenario. We minimize the targets eaves-
dropping SNR subject to users SNR thresholds, expressed as the minimum
difference between the users and the Eves rates as we indicate in the followingsections.III. M INIMUM SNR OFEVEWITHPREMISE OF PERFECT
CSI AND TARGET DIRECTION
In this section, we aim to enhance the secrecy rate by
minimizing the Eves SNR while guaranteeing the required
SINR thresholds for the legitimate users, i.e. the SINR of
Bobs. Firstly, the formulated optimization problem is formu-
lated based on the assumption that the channel informationfrom Alice to Bobs in the communication system is perfectly
known. Meanwhile, the precise direction of the detected target
is assumed to be known to the transmitter, which will befurther relaxed by considering the uncertainty in the targets
location in the later sections. The complexity analysis is given
at the end of this section.
A. Problem Formulation
Let us ﬁrstly consider the SNR
Eminimization problem,
which should guarantee: a) individual SINR requirement ateach legitimate user, b) transmit power budget and c) a desired
radar spatial beampattern. It is noteworthy that the time-
domain metric peak-to-average power ratio (PAPR) in the
DFRC system can be explicitly dealt with by including PAPR
constraints in our optimization problems such as PAPR mini-mization convex optimization method adopted in [39], or con-
stant modulus (CM) constraints in [12], [40]–[42]. To keep
the focus on the secrecy aspect, PAPR is not considered inour optimization problem throughout this article, which is
designated as our future work. Note that an ideal radar beam-
pattern should be obtained before designing the beamforming
and artiﬁcial noise, which can be generated by solving the
following constrained least-squares (LS) problem [9], [43] asan example
min
η,RdM/summationdisplay
m=1/vextendsingle/vextendsingleηPd(θm)−aH(θm)Rda(θm)/vextendsingle/vextendsingle2
s.t.tr(Rd)=P0,
Rd/followsequal0,Rd=RH
d,
η≥0, (13)
where ηis a scaling factor, P0represents the transmission
power budget, {θm}M
m=1denotes an angular grid covering
the detection angular range in [−π/2,π/2],a(θm)denotes
steering vector, Pd(θm)is the desired ideal beampattern gain
atθm,Rdrepresents the desired wave form covariance matrix.
Given a covariance matrix Rdthat corresponds to a well-
designed MIMO radar beampattern, the fractional program-
ming optimization problem of minimizing SNR Ecan be
formulated as
min
Wi,RN|α|2aH(θ0)/summationtextK
i=1Wia(θ0)
|α|2aH(θ0)RNa(θ0)+σ2e, (14a)
s.t./bardblRX−Rd/bardbl2≤γbp, (14b)
SINR i≥γb,∀i, (14c)
tr(RX)=P0, (14d)
Wi=WH
i,Wi/followsequal0,∀i, (14e)
rank(Wi)=1,∀i, (14f)
RN=RH
N,RN/followsequal0, (14g)
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 5 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 87
where the constraints Wi=WH
i,Wi/followsequal0,rank(Wi)=
1,∀i,are equivalent to constraining Wi=wiwH
i[20].θ0
represents the direction of Eve known at Alice,4γbpis the
pre-deﬁned threshold that constraints the mismatch betweendesigned covariance matrix R
Xand the desired Rd,a n d
ﬁnally γbdenotes the predeﬁned SINR threshold of each
legitimate user.
For simplicity, we deﬁne the set of I×Ipositive semi-
deﬁnite matrices as P=/braceleftbig
A|A∈CI×I,AT=A,A/followsequal0/bracerightbig
.
Accordingly, (14e) and (14g) can be written as Wi∈P,∀i
andRN∈P, respectively. To solve the problem (14), let us
employ the SDR approach by omitting the rank (Wi)=1
constraint in (14f), based on which we relax the optimization
problem as
min
Wi,RN|α|2aH(θ0)/summationtextK
i=1Wia(θ0)
|α|2aH(θ0)RNa(θ0)+σ2e, (15a)
s.t./bardblRX−Rd/bardbl2≤γbp, (15b)
SINR i≥γb,∀i, (15c)
tr(RX)=P0, (15d)
Wi∈P,∀i, (15e)
RN∈P. (15f)
By noting the fact that problem (15) is still non-convex due to
the fractional objective function, we propose in the following
an iterative approach to solve the problem efﬁciently.
B. Efﬁcient Solver
Following [34], (15) is single-ratio FP problem, which can
be solved by employing the Dinkelbach’s transform demon-
strated in [44], where the globally optimal solution can be
obtained by solving a sequence of SDPs. To develop thealgorithm, we ﬁrstly introduce a scaling factor c=SNR
E,
which is an auxiliary variable. We then deﬁne two scal-
ing variables UandV, which are nonnegative and positive
respectively, where U=|α|2aH(θ)/summationtextK
i=1Wia(θ),∀i,V=
|α|2aH(θ)RNa(θ)+σ2
e. As a result, the FP problem (15) is
equivalent to
min
Wi,RNU−cV, (16a)
s.t./bardblRX−Rd/bardbl2≤γbp, (16b)
SINR i≥γb,∀i, (16c)
tr(RX)=P0, (16d)
Wi∈P,∀i, (16e)
RN∈P, (16f)
where ccan be iteratively updated by
c[iter+1 ]=U[iter]
V[iter], (17)
4The MIMO radar is assumed to be with two working modes including
searching and tracking. In the search mode, the radar transmits a spatiallyorthogonal waveform, which formulate s the omni-directional beampattern.
Potential targets can be searched via the beampattern. Then, the radar is
able to track potential targets via transmitting directional waveforms. Thus,the precise location is available to be known at Alice.where iteris the index of iteration. For clarity, we summarize
the above in Algorithm 1. According to [34], it is easy
to prove the convergence of the algorithm given the non-
increasing property of cduring each iteration. It is noted
that the SDR approach generates an approximated solution
to the optimization problem (14) by neglecting the rank-one
constraint. Accordingly, eigenvalue decomposition or Gaussianrandomization techniques are commonly employed to obtain
a suboptimal solution. In our case, it is noteworthy that the
solution obtained from the SDR solver can be guaranteed to
be rank-1
5[45].
Algorithm 1 Alogrithm for Solving FP Problem (15)
Input: H,a(θ0),σ2
e,σ2
z,α ,γ bp,γb,P0,it er max≥2,ε
Output: W(iter)
i,R(iter)
N,i=1,···,K
1. Compute Rd. Reformulate problem (13a) by (14).
Set the iteration threshold ε> 0. Initialize c(0),c(1),/vextendsingle/vextendsinglec(1)−c(0)/vextendsingle/vextendsingle>ε.
while iter≤itermax and/vextendsingle/vextendsingleciter+1−citer/vextendsingle/vextendsingle≥εdo
2. Obtain W(iter)
i,R(iter)
N,i=1,···,Kby solving the
SDP problem (16).3. Update cby (17).
4.iter=iter+1.
end while
C. Complexity Analysis
In this subsection, the computational complexity of
Algorithm 1 is analyzed as follows. Note that SDP problems
are commonly solved by the interior point method (IPM) [46],
which obtains an /epsilon1-optimal solution after a sequence of
iterations with the given /epsilon1. In problem (14), it is noted
that the constraints are linear matrix inequality (LMI) except
for (14b), which is a second-order cone (SOC) [47] constraint.Thus, we demonstrate the complexity in Table I, where N
iter
represents iteration times. For simplicity, the computational
complexity can be given as O/parenleftbig√
2Niterln (1//epsilon1)K3.5N6.5/parenrightbig
by reserving the highest order term.
IV . E VE’SSNR M INIMIZATION WITHUNCERTAINTY IN
THETARGET DIRECTION AND PERFECT CSI
In practice, the precise location of the target is difﬁcult to
be known at the transmitter. In this section, we consider the
scenario where a rough estimate of the target’s angle, instead
of its precise counterpart, is available at Alice. Accordingly,the following beampattern design aims at achieving both a
desired main-beam width covering the possible angle uncer-
tainty interval of the target as well as a minimized sidelobepower in a prescribed region. First of all, the optimization
problem is formulated to form a wide main-beam beampat-
tern, following which an efﬁcient solver is proposed. Finally,
the complexity analysis is given at the end of this section.
5Letkandmdenote the number of n×nsquare matrix variables and linear
constraints, separately. For a compl ex separable QCQP, the SDR is tight if
m≤k+2, with the assumption that none of the solution
/A8X∗
i
/A9k
i=1to SDR
satisﬁesX∗
i=0 for some i[45].
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 6 页 ---
88 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
A. Problem Formulation
In this subsection, we consider the case that the angle
uncertainty interval of the target is roughly known within the
angular interval [θ0−Δθ,θ0+Δθ]. To this end, the target
from every possible direction should be taken in to considera-
tion when formulating the optimization problem. Accordingly,
the objective is given as the sum of Eve’s SNR at all the
possible locations as follows. Due to the uncertainty of target
location, wider beampattern needs to be formulated towardsthe uncertain angular interval to avoid missing the target.
Inspired by the 3dB main-beam width beampattern design for
MIMO radar [48], we propose a scheme aiming at keeping aconstant power in the uncertain angular interval, which can be
formulated as the following optimization problem
min
Wi,RN/summationdisplay
θm∈Φ|α|2aH(θm)/summationtextK
i=1Wia(θm)
|α|2aH(θm)RNa(θm)+σ2e(18a)
s.t.aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (18b)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (18c)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (18d)
SINR i≥γb,∀i, (18e)
tr(RX)=P0, (18f)
Wi∈P,∀i, (18g)
rank(Wi)=1,∀i, (18h)
RN∈P, (18i)
where θ0is the main-beam location, Ωdenotes the sidelobe
region of interest, Φdenotes the wide main-beam region, γs
is the bound of the sidelobe power.
Likewise, recall the probl em (14), SDR technique is
adopted. To solve the above su m-of-ratio problem, according
to [34], we equivalently recast the minimization problem by
neglecting rank-1 constraint in (18h) as
max
Wi,RN/summationdisplay
θm∈Φ|α|2aH(θm)RNa(θm)+σ2
e
|α|2aH(θm)/summationtextK
i=1Wia(θm)(19a)
s.t.aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (19b)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (19c)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (19d)
SINR i≥γb,∀i, (19e)
tr(RX)=P0, (19f)
Wi∈P,∀i, (19g)
RN∈P, (19h)
It is noted that problem (19) is still non-convex. The approach
to solve this sum-of-ratio FP problem is described in the
following.B. Efﬁcient Solver
To present the solution to problem (19), we ﬁrstly refer
to [34] and denote
A(θm)=|α|2aH(θm)RNa(θm)+σ2
e
B(θm)=|α|2aH(θm)/summationdisplayK
i=1Wia(θm)
One step further, the sum-of-ratio problem is equivalent to the
following optimization problem, which can be rewritten in theform
max
Wi,RN,y/summationdisplay
θm∈Φ/parenleftBig
2ym/radicalbig
A(θm)−y2
mB(θm)/parenrightBig
, (20a)
s.t.aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (20b)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (20c)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (20d)
SINR i≥γb,∀i, (20e)
tr(RX)=P0, (20f)
Wi∈P,∀i, (20g)
RN∈P, (20h)
whereydenotes a collection of variables {y1,···,yM}.T h e
optimal ymcan be obtained in the following closed form when
θmis ﬁxed
y∗
m=/radicalbig
A(θm)
B(θm). (21)
To this end, the reformulated optimization problem (20) can
be solved. Then, eigenvalue decomposition or Gaussian ran-
domization is required to get the approximated solution. For
clarity, the above procedure is summarized in Algorithm 2.
Algorithm 2 Algorithm for Solving Sum-of-Ratio
Problem (19)
Input: H,a(θ0),ε>0,σ2
e,σ2
z,α ,γ b,γs,P0,it er max≥2,
Δθ.
Output: W(iter)
i,R(iter)
N,i=1,···,K.
1. Reformulate problem (19) by (20).while iter≤iter
max and/vextenddouble/vextenddoubleyiter+1−yiter/vextenddouble/vextenddouble≥εdo
2. Obtain W(iter)
i,R(iter)
N,i=1,···,Kby solving the
new convex optimization problem (20).3. Update yby (21).
4.iter=iter+1.
end while
C. Complexity Analysis
We end this section by computing the complexity of solving
problem (18). It is noted that all the constraints can beconsidered as LMIs in optimization problem (18). We denote
Φ
0=card(Φ) andΩ0=card(Ω) as the cardinality of
ΦandΩ, respectively. Thus, referring to [46], we give the
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 7 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 89
computational complexity in Table I, which can be simpliﬁed
asO/parenleftbig
3√
2Niterln (1//epsilon1)K3.5N6.5/parenrightbig
by reserving the highest
order.
V. R OBUST BEAMFORMING WITHIMPERFECT CSI
ANDTARGET DIRECTION UNCERTAINTY
In this section, based on the models presented in the
previous sections, we consider the case that the perfect channelinformation is not available at the base station. By relying
on the method of robust optimization, we formulate an opti-
mization problem aiming for designing the dual-functionalbeamformer that is robust to the channel uncertainty, which
is bounded in a spherical region. Meanwhile, to guarantee
the generality, we minimize the worst-case SNR received
at the target in the angular interval of possible location of
potential eavesdropper. Then, an efﬁcient solver tailored forthe considered fractional optimization problem is developed,
following by a detailed complexity analysis.
A. Problem Formulation
Recalling the channel model demonstrated in Section II,
we formulate the optimization problem when the CSI isimperfectly known to the transmitter as follows. According to
the well-known S-procedure [49], ∀e
H
iei≤μ2
i, the constraint
that guarantees the worst-case SINR of legitimates users can
be reformulated as [38]
/parenleftBig
˜hi+ei/parenrightBigH⎛
⎝Wi−γbK/summationdisplay
k=1,k/negationslash=iWk−γbRN⎞
⎠/parenleftBig
˜hi+ei/parenrightBig
−γbσ2≥0,∀i.(22)
Then, we minimize the possible maximum Eve’s SNR in
the main-beam region of interest, which yields the followingrobust optimization problem
min
Wi,RN,timax
θm∈Φ|α|2aH(θm)/summationtextK
i=1Wia(θm)
|α|2aH(θm)RNa(θm)+σ2e(23a)
s.t./parenleftbigg˜hT
iYi˜h∗
i−γbσ2−tiμ2
i˜hT
iYi
Yi˜h∗
i Yi+tiIN/parenrightbigg
/followsequal0,∀i,
(23b)
Yi:=Wi−γb⎛
⎝/summationdisplay
k/negationslash=iWk⎞
⎠−γbRN
aH(θ0)RXa(θ0)−aH(θm)RXa(θm)/greaterorequalslantγs,
∀θm∈Ω (23c)
aH(θk)RXa(θk)/lessorequalslant(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (23d)
(1−α)aH(θ0)RXa(θ0)/lessorequalslantaH(θk)RXa(θk),
∀θk∈Φ (23e)
tr(RX)=P0, (23f)
ti/greaterorequalslant0,∀i, (23g)
Wi∈P,∀i, (23h)
rank(Wi)=1,∀i, (23i)
RN∈P, (23j)where Φ=[ θ0−Δθ,θ0+Δθ]is the main-beam region of
interest, m=1,···,M.Mrepresents the number of detecting
angles in the interval Φ, and ﬁnally t=[t1,···,tK]is an
auxiliary vector relying on the S-procedure.
B. Efﬁcient Solver
To solve problem (23), the SDR approach is adopted
again by dropping the rank-1 constraint in (23i). Moreover,
the objective function (23a) can be transformed to a max-min
problem initially which is given as
max
Wi,RN,timin
θm∈Φ|α|2aH(θm)RNa(θm)+σ2
e
|α|2aH(θm)/summationtextK
i=1Wia(θm). (24)
To verify this, we introduce a variable zand deﬁne
A(θm)= a(θm)aH(θm). The objective function (24)
can be rewritten as max
Wi,RN,ti,zz, which subjects to
z≤/parenleftBig
tr(A(θm)RN)+σ2/slashBig
|α|2/parenrightBig/slashBig
tr/parenleftBig
A(θm)/summationtextK
i=1Wi/parenrightBig
and any other constraints in (23). Likewise, we denote
C(θm)=tr(A(θm)RN)+σ2/slashBig
|α|2
D(θm)=tr/parenleftbigg
A(θm)/summationdisplayK
i=1Wi/parenrightbigg
The aforementioned constraint is equivalent to
z≤max
ym/parenleftBig
2ym/radicalbig
C(θm)−y2
mD(θm)/parenrightBig
,
which is a less-than-max inequality, so max
ymcan be integrated
into the objective. Consequently, problem (23) is reformu-
lated as
max
Wi,RN,y,ti,zz, (25a)
s.t.2ym/radicalbig
C(θm)−y2
mD(θm)≥z,θm∈Φ,∀m,
(25b)/parenleftbigg˜hT
iYi˜h∗
i−γbσ2−tiμ2
i˜hT
iYi
Yi˜h∗
i Yi+tiIN/parenrightbigg
/followsequal0,∀i,
(25c)
Yi:=Wi−γb⎛
⎝/summationdisplay
k/negationslash=iWk⎞
⎠−γbRN
aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (25d)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (25e)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (25f)
tr(RX)=P0, (25g)
ti≥0,∀i, (25h)
Wi∈P,∀i, (25i)
RN∈P, (25j)
where ymis an auxiliary variable, each ymcorresponds to
the radar detecting angles θmin the main-beam region of
interest Φ. We refer the rest variables to the deﬁnitions which
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 8 页 ---
90 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
we presented in the previous sections. Note that problem
(25) is convex and can be readily tackled. Here, we deﬁne
a collection of variables y={y1,···,yM}. To solve this
problem, we apply the quadratic transform and optimize theprimal variables W
i,RN,tiand the auxiliary variable collec-
tionyin an alternating manner. When the primal variables are
obtained by initializing the collection y, the optimal ymcan
be updated by
y∗
m=/radicalbig
C(θm)
D(θm). (26)
To this end, eigenvalue decomposition or Gaussian randomiza-
tion is required to obtain approximated solutions. For clarity,solution to problem (25) can be summarized as Algorithm 3.
Algorithm 3 Method for Solving Multiple-Ratio FP
Problem (24)
Input: a(θ0),˜hi,σ2
e,σ2
z,α ,γ b,γs,P0, CSI estimation error
threshold μi>0,Δθ, iteration threshold ε> 0,
itermax/greaterorequalslant2.
Initialization : Set initial values for y(0),y(1),w h i c h/vextenddouble/vextenddoubley(1)−y(0)/vextenddouble/vextenddouble>ε.
while iter/lessorequalslantitermax and/vextenddouble/vextenddoubley(iter)−y(iter−1)/vextenddouble/vextenddouble/greaterorequalslantεdo
1. Reformulate problem (24) by replacing the fractional
objective function with the form in (25b).
2. Reconstruct the problem with variable z.
3. Obtain W(iter)
i,R(iter)
N,∀iby solving the optimization
problem, and then update yby (26).
4. Update the primal variables by (25), over RN,Wi,∀i
for ﬁxed y.
end while
Output: RN,Wi,ti,z,∀i.
C. Complexity Analysis
The complexity of Algorithm 3 is analyzed in this sub-
section. Similarly, ΦandΩcan be regarded as discrete
domains. We denote Φ0=card(Φ) andΩ0=card(Ω)as
the cardinality of ΦandΩ, respectively. All the constraints
in problem (23) are LMIs. Speciﬁcally, we notice that the
problem is composed by 3Φ0+Ω0+K+1LMI constraints
of size 1, 2K+2 LMI constraints of size N,a n d KLMI
constraints of size N+1. For simplicity, we reserve the highest
order of computational complexity, which can be given asO/parenleftbig
4√
3Niterln (1//epsilon1)K3.5N6.5/parenrightbig
.
VI. R OBUST OPTIMAL BEAMFORMING WITH
STATISTICAL CSI A NDTARGET
DIRECTION UNCERTAINTY
In this section, we consider the extension of the scenario in
section V , where the channels from Alice to Bobs vary rapidly.
As a result, the instantaneous CSI is difﬁcult to be estimated.
Note that the second-order channel statistics, which vary muchmore slowly, can be obtained by the BS through long-term
feedback. Nevertheless, even in the event that the statistical
CSI is known at Alice, the uncertainty is always inevitable.We therefore take the uncertainty matrix into consideration by
employing additive errors to the channel covariance matrix.
Likewise, the complexity analysis is given at the end of this
section.
A. Problem Formulation
We ﬁrstly recall the reformulation of the SINR of the
i-th user and the channel model with statistical CSI presented
in Section II. To this end, based on Lagrange dual function[35], [50], the constraint corresponding to QoS of i-th user
can be formulated as
−δ
i/bardblAi+Zi/bardbl−tr(Rhi(Zi+Ai))−γbtr(RhiRN)
−γbσ2
z≥0Zi∈P,∀i
whereAi=γb/summationtextK
k=1,k/negationslash=iWk−Wi,∀i. Recalling the opti-
mization problem in Section V-A, likewise, the robust beam-
forming problem with erroneous statistical CSI is given as
min
Wi,RN,Zimax
θm∈Φ|α|2aH(θm)/summationtextK
i=1Wia(θm)
|α|2aH(θm)RNa(θm)+σ2e(27a)
s.t.−δi/bardblAi+Zi/bardbl−tr(Rhi(Zi+Ai))
−γbtr(RhiRN)
−γbσ2
z≥0,∀i, (27b)
aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (27c)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (27d)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (27e)
tr(RX)=P0, (27f)
Zi∈P,∀i, (27g)
Wi∈P,∀i, (27h)
rank(Wi)=1,∀i, (27i)
RN∈P. (27j)
We note that the problem (27) can be solved with SDR
approach by dropping the rank-one constraint in (27i). One
step further, similar to (23), problem (27) can be reformulated
in a similar way, given by
max
Wi,RN,Zi,zz (28a)
s.t.2ym/radicalbig
C(θm)−y2
mD(θm)≥z,θm∈Φ,∀m,
(28b)
−δi/bardblAi+Zi/bardbl−tr(Rhi(Zi+Ai))
−γbtr(RhiRN)
−γbσ2
z≥0,∀i, (28c)
aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (28d)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (28e)
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 9 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 91
TABLE I
COMPLEXITY ANALYSIS
Fig. 2. Beampatterns with various target direction uncertainty interval when (a) CSI is known, (b) CSI is imperfectly known and (c) statistical CSI is
imperfectly known.
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (28f)
tr(RX)=P0, (28g)
Zi∈P,∀i, (28h)
Wi∈P,∀i, (28i)
RN∈P. (28j)
Note that problem (28) is SDP feasibility and can be solved
in polynomial time using interior-point algorithms6[35].
B. Complexity Analysis
The complexity of problem (27) is given as follows. As is
noted in problem (28), almost all the constrains are LMI exceptfor the SOC constraint (28c). Likewise, we denote Φ
0=
card(Φ)andΩ0=card(Ω)as the cardinality of ΦandΩ.
Note that the problem is composed by KSOC constraints of
size 1, Ω0+3 Φ 0+1LMI constraints of size 1, and 4K+2
LMIs of size N. Accordingly, we compute the complexity
as is shown in Table I. When the CSI is statistically known,
the computational complexity can be simply demonstrated as
O/parenleftbig
5√
2Niterln (1//epsilon1)K3.5N6.5/parenrightbig
. which is the complexity of
each iteration. Then, The calculated complexities of all the
proposed optimizations are summarised in Table I.
VII. N UMERICAL RESULTS
To evaluate the proposed methods, numerical results based
on Monte Carlo simulations are shown in this section to
6Since solutions Wi,i=1,···,Kobtained from solving the convex
relaxation problems (15), (19), (24) and (28) are all rank-one, the rank-
one approximation procedures, e.g., eigenvalue decomposition or Gaussianrandomization, can be omitted in general for our study.validate the effectiveness of the proposed beamforming
method. Without loss of generality, each entry of channel
matrix His assumed to obey standard Complex Gaussian dis-
tribution, i.e. hi,j∼C N (0,1). We assume that the DFRC base
station employs a ULA with half-wavelength spacing between
adjacent antennas. In the follo wing simulations, the number of
antennas is set as N=1 8 and the number of legitimate users
isK=4. Moreover, for convenience, the noise level of the
eavesdropper is assumed to be the same as that of the intendedreceivers. The constrained beamforming design problems in
Section II-Section V are solved by the classic SDR technique
using the CVX toolbox [51].
A. Beam Gain And Secrecy Rate Analysis
We ﬁrst show the resultant radar beampattern in Fig. 2 with
different angular interval of target location uncertainty, i.e.
[−5
◦,5◦]and[−10◦,10◦]. The SINR threshold of each legiti-
mate user is set as γb=1 0 dB. The narrow beampattern when
the target location is precisely known at the BS is set as a
benchmark. It is found that the desired beampattern with widemain-beam is obtained by solving the proposed algorithms,
which maintain the same power in the region of possible target
location. Additionally, it is noted that with the expansion oflocation uncertainty angular interval, the power gain of main-
beam reduces.
The worst-case secrecy rate in terms of increasing SINR
threshold of each user is demonstrated in Fig. 3, where
the power budget is set as P
0=2 0 dBm and P0=
30dBm respectively. In this case, we set the sidelobe power
threshold γs=4 0 dB. Basically, in the SNR Eminimization
problem, the secrecy rate increases with the growth of γb.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 10 页 ---
92 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
Fig. 3. Worst-case secrecy rate versus the threshold of SINR at legitimate
users, with various transmission powe r budget, where solid and dashed lines
represent power budget P0=3 0 dBm and P0=2 0 dBm respectively,
N=1 8 ,K=4,Δθ=5◦.
Fig. 4. Convergence of (a) SNR of Eve and (b) secrecy rate for the target
SNR minimization algorithm, N=1 8,K=4,P0=3 0 dBm,γb=1 0 dB.
It is noteworthy that the system achieves higher secrecy rate
when both the target location and CSI are precisely known.When we increase the power budget, the secrecy rate grows
to some extent. In addition, the erroneous instantaneous and
statistical CSI affects the performance of secrecy rate slightlycomparing to the perfect CSI scenario.
In Fig. 4, we evaluate the convergence of target SNR and
secrecy rate. In these cases, the same system parameters are set
as previous simulations. In Fig. 4(a), the SINR of the target is
conﬁrmed to convergent to a minimum. In robust beamformingdesign problems, the SNR of target decreases slightly with the
increasing iteration number, which results in the slight growth
of secrecy rate as is shown in Fig. 4(b).
B. Trade-off Between the Performance of Radar And
Communication System
In this subsection, we evaluate the performance trade-off
between radar and communication system. Fig. 5 shows theFig. 5. Secrecy rate with different angular intervals, N=1 8 ,K=4,
P0=3 0 dBm, with γb=1 0 dB and γb=1 5 dB, respectively.
Fig. 6. Worst-case secrecy rate versus the sidelobe power with various
SINR threshold of legitimate users for the Algorithm 2, N=1 8 ,K=4,
P0=3 0 dBm,Δθ=5◦.
secrecy rate performance with various angular intervals for
γb=1 0 dB and γb=1 5 dB. The main-beam power decreases
when the target uncertainty incr eases, then the leaking infor-
mation would get less, which improve the secrecy rate. As is
demonstrated in Fig. 5, the secrecy rate increases with the
growth of target uncertainty interval. Besides, with 5dB growth
of legitimate user SINR threshold, the secrecy rate increases
0.5bit/s/Hz approximately.
Fig. 6 demonstrates the secrecy rate performance versus the
threshold of sidelobe with P0=3 0 dBm,Δθ=5◦,w h i c h
reveals the trade-off between the performance of radar and
communication systems. In Algorithm 2, the power difference
between main beam and sidelobe increases with the growth of
γs, which results in the increasing possibility of information
leaking. As the numerical result shown in Fig. 6, it is notable
that the secrecy rate decreases with the growth of γs, especially
the tendency gets obvious when γsis greater than 30dB.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 11 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 93
Fig. 7. Achieved secrecy rate with different error bounds in the scenario of
known imperfect CSI, N=1 8,K=4,P0=3 0 dBm.
Fig. 8. Worst-case secrecy rate versus different error bounds when statistical
CSI is imperfectly known, N=1 8,K=4,P0=3 0 dBm,γb=1 0 dB.
C. Robust Beamforming Performance
As the norm of CSI error is bounded by a constant,
the secrecy rate performance versus error bound is illustrated
in Fig. 7, with different location uncertainty. With the growth
of error bound, the achievable SINR at each legitimate userkeeps being above the given threshold but not a constant
according to constraints (25c) and (27b). We note that the
worst-case secrecy rate reduces after a certain value with the
increasing error bound, because of the different changing rate
between target SNR and user SINR corresponding to various
error bounds in Fig. 7. Whereas, as is shown in Fig. 8,
the secrecy rate keeps increas ing with the growth of error
bound. In addition, the robust beamforming designs achievehigher secrecy rate when the location uncertainty is limited in
a larger interval.VIII. C
ONCLUSION
In this article, optimization based beamforming designs
have been addressed for MIMO DFRC system, which aimed
at ensuring the security of information transmission in case of
leaking to targets by adding AN at the transmitter to confuse
the potential eavesdropper. Speciﬁcally, we have minimizedthe SNR of the target which is regarded as the potential
eavesdropper while keeping the each legitimate user’s SINR
above a certain constant to ensure the secrecy rate of the DFRCsystem. Throughout this article, the optimization beamforming
problem has been designed with perfect CSI and imperfect
CSI, as well as with the accurate and inaccurate target location
information.
First of all, both precise location of target and perfect CSI
have been assumed to be known at BS, which gained the
highest secrecy rate according to the numerical results. When
the target location was uncer tain, the main-beam power has
decreased with the growth of the uncertainty angular interval.
Moreover, the secrecy rate versus different thresholds of
sidelobe has been demonstrated, which revealed the trade-off
between radar and communication system performance. Then,
we have formulated target SNR minimization problem withimperfect instantaneous CSI and statistical CSI known to the
base station respectively. As shown in the numerical results,
the beamforming design has b een feasible in both robust
scenarios. Finally, simulation r esults have been presented to
show the secrecy rate tendency effected by error bound with
various target location uncertainty.
R
EFERENCES
[1] D. Oyediran, Spectrum Sharing: Overview and Challenges of Small
Cells Innovation in the Proposed 3.5 GHz Band .S a nD i e g o ,C A ,U S A :
International Foundation for Teleme tering, 2015. [Online]. Available:
https://repository.arizona.edu/handle/10150/596402
[2] B. Li, A. P. Petropulu, and W. Trappe, “Optimum co-design for spectrum
sharing between matrix completion based MIMO radars and a MIMO
communication system,” IEEE Trans. Signal Process. , vol. 64, no. 17,
pp. 4562–4575, Sep. 2016.
[3] A. Ghasemi and E. S. Sousa, “Collaborative spectrum sensing for
opportunistic access in fading environments,” in Proc. 1st IEEE Int.
Symp. New Frontiers Dyn. Spectr. Access Netw. (DySPAN) , Nov. 2005,
pp. 131–136.
[4] C. W. Kim, J. Ryoo, and M. M. Buddhikot, “Design and implementation
of an end-to-end architecture for 3.5 GHz shared spectrum,” in Proc.
IEEE Int. Symp. Dyn. Spectr. Access Netw. (DySPAN) , Sep. 2015,
pp. 23–34.
[5] G. Staple and K. Werbach, “The end of spectrum scarcity [spectrum
allocation and utilization],” IEEE Spectr. , vol. 41, no. 3, pp. 48–52,
Mar. 2004.
[6] S. Sodagari, A. Khawar, T. C. Clan cy, and R. McGwier, “A projection
based approach for radar and telecommunication systems coexistence,”
inProc. IEEE Global Commun. Conf. (GLOBECOM) , Dec. 2012,
pp. 5010–5014.
[7] A. Turlapaty and Y . Jin, “A joint design of transmit waveforms for
radar and communications systems in coexistence,” in Proc. IEEE Radar
Conf. , May 2014, pp. 315–319.
[8] B. Li and A. P. Petropulu, “Joint transmit designs for coexistence of
MIMO wireless communications and spa rse sensing radars in clutter,”
IEEE Trans. Aerosp. Electron. Syst. , vol. 53, no. 6, pp. 2846–2864,
Dec. 2017.
[ 9 ]F .L i u ,C .M a s o u r o s ,A .L i ,H .S u n ,a n dL .H a n z o ,“ M U - M I M O
communications with MIMO radar: From co-existence to joint transmis-
sion,” IEEE Trans. Wireless Commun. , vol. 17, no. 4, pp. 2755–2770,
Apr. 2018.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 12 页 ---
94 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
[10] F. Liu, C. Masouros, A. Li, and T. Ratnarajah, “Robust MIMO beam-
forming for cellular and radar coexistence,” IEEE Wireless Commun.
Lett., vol. 6, no. 3, pp. 374–377, Jun. 2017.
[11] F. Liu, C. Masouros, A. Li, T. Ratnarajah, and J. Zhou, “MIMO
radar and cellular coexistence: A power-efﬁcient approach enabled by
interference exploitation,” IEEE Trans. Signal Process. , vol. 66, no. 14,
pp. 3681–3695, Jul. 2018.
[12] F. Liu, L. Zhou, C. Masouros, A. Li, W. Luo, and A. Petrop-
ulu, “Toward dual-functional radar-communication systems: Optimal
waveform design,” IEEE Trans. Signal Process. , vol. 66, no. 16,
pp. 4264–4279, Aug. 2018.
[13] F. Liu, L. Zhou, C. Masouros, A. Lit, W. Luo, and A. Petropulu, “Dual-
functional cellular and radar tra nsmission: Beyond coexistence,” in
Proc. IEEE 19th Int. Workshop Signal Process. Adv. Wireless Commun.
(SPAWC) , Jun. 2018, pp. 1–5.
[14] L. Zhou et al. , “Optimal waveform design for dual-functional MIMO
radar-communication systems,” in Proc. IEEE/CIC Int. Conf. Commun.
China (ICCC) , Aug. 2018, pp. 661–665.
[15] A. Hassanien, M. G. Amin, Y . D. Zhang, and F. Ahmad, “Dual-function
radar-communications: Information e mbedding using sidelobe control
and waveform diversity,” IEEE Trans. Signal Process. , vol. 64, no. 8,
pp. 2168–2181, Apr. 2016.
[16] V. Va, T. Shimizu, G. Bansal, and R. W. Heath, Jr., “Millimeter wave
vehicular communications: A survey,” Found. Trends Netw. , vol. 10,
no. 1, pp. 1–113, 2016.
[17] C.-H. Lim, Y . Wan, B.-P. Ng, and C.-M. See, “A real-time indoor
WiFi localization system utilizing smart antennas,” IEEE Trans. Consum.
Electron. , vol. 53, no. 2, pp. 618–622, May 2007.
[18] G. M. Dillard, M. Reuter, J. Zeiddler, and B. Zeidler, “Cyclic code shift
keying: A low probability of intercept communication technique,” IEEE
Trans. Aerosp. Electron. Syst. , vol. 39, no. 3, pp. 786–798, Jul. 2003.
[19] P. M. McCormick, B. Ravenscroft, S. D. Blunt, A. J. Duly, and
J. G. Metcalf, “Simultaneous rada r and communication emissions from
a common aperture, part II: Experimentation,” in Proc. IEEE Radar
Conf. (RadarConf) , May 2017, pp. 1697–1702.
[20] W.-C. Liao, T.-H. Chang, W.-K. Ma, and C.-Y. Chi, “QoS-based transmit
beamforming in the presence of eave sdroppers: An optimized artiﬁcial-
noise-aided approach,” IEEE Trans. Signal Process. , vol. 59, no. 3,
pp. 1202–1216, Mar. 2011.
[21] S. Shaﬁee, N. Liu, and S. Ulukus, “Towards the secrecy capacity
of the Gaussian MIMO wire-tap channel: The 2-2-1 channel,” 2007,
arXiv:0709.3541 . [Online]. Available: http://arxiv.org/abs/0709.3541
[22] F. Oggier and B. Hassibi, “The secrecy capacity of the MIMO
wiretap channel,” 2007, arXiv:0710.1920 . [Online]. Available:
http://arxiv.org/abs/0710.1920
[23] E. Ekrem and S. Ulukus, “The secrecy capacity region of the Gaussian
MIMO multi-receiver wiretap channel,” IEEE Trans. Inf. Theory , vol. 57,
no. 4, pp. 2083–2114, Apr. 2011.
[24] S. Goel and R. Negi, “Guaranteeing secrecy using artiﬁcial noise,” IEEE
Trans. Wireless Commun. , vol. 7, no. 6, pp. 2180–2189, Jun. 2008.
[25] R. Negi and S. Goel, “Secret communication using artiﬁcial noise,” in
Proc. IEEE Veh. Technol. Conf. , 2005, vol. 62, no. 3, p. 1906.
[26] H. Ma, J. Cheng, X. Wang, and P. Ma, “Robust MISO beamforming with
cooperative jamming for secure transmission from perspectives of QoSand secrecy rate,” IEEE Trans. Commun. , vol. 66, no. 2, pp. 767–780,
Feb. 2018.
[27] Z. Lin, M. Lin, J. Ouyang, W.-P. Zhu, A. D. Panagopoulos, and
M.-S. Alouini, “Robust secure beamforming for multibeam satellite
communication systems,” IEEE Trans. Veh. Technol. , vol. 68, no. 6,
pp. 6202–6206, Jun. 2019.
[28] J. P. Vilela, M. Bloch, J. Barros, and S. W. McLaughlin, “Wireless
secrecy regions with friendly jamming,” IEEE Trans. Inf. Forensics
Security , vol. 6, no. 2, pp. 256–266, Jun. 2011.
[29] Z. Chu, K. Cumanan, Z. Ding, M. Johnston, and S. Y . Le Goff, “Secrecy
rate optimizations for a MIMO secrecy channel with a cooperativejammer,” IEEE Trans. Veh. Technol. , vol. 64, no. 5, pp. 1833–1847,
May 2015.
[30] P. R. Vaka, S. Bhattarai, and J.-M. Park, “Location privacy of non-
stationary incumbent systems in spectrum sharing,” in Proc. IEEE
Global Commun. Conf. (GLOBECOM) , Dec. 2016, pp. 1–6.
[31] A. Dimas, B. Li, M. Clark, K. Psounis, and A. Petropulu, “Spectrum
sharing between radar and communication systems: Can the privacy of
the radar be preserved?” in Proc. 51st Asilomar Conf. Signals, Syst.,
Comput. , Oct. 2017, pp. 1285–1289.[32] A. Deligiannis, A. Da niyan, S. Lambotharan, and J. A. Chambers,
“Secrecy rate optimizations for MIMO communication radar,” IEEE
Trans. Aerosp. Electron. Syst. , vol. 54, no. 5, pp. 2481–2492, Oct. 2018.
[33] B. K. Chalise and M. G. Amin, “Performance tradeoff in a uniﬁed system
of communications and passive radar: A secrecy capacity approach,”Digit. Signal Process. , vol. 82, pp. 282–293, Nov. 2018.
[34] K. Shen and W. Yu, “Fractional programming for communication
systems—Part I: Power control and beamforming,” IEEE Trans. Signal
Process. , vol. 66, no. 10, pp. 2616–2630, May 2018.
[35] I. Wajid, Y. C. Eldar, and A. Gershman, “Robust downlink beamforming
using covariance channel state information,” in Proc. IEEE Int. Conf.
Acoust., Speech Signal Process. , Apr. 2009, pp. 2285–2288.
[36] E. Telatar, “Capacity of multi-antenna Gaussian channels,” Eur. Trans.
Telecommun. , vol. 10, no. 6, pp. 585–595, Nov. 1999.
[37] M. F. Hanif, L.-N. Tran, M. Juntti, and S. Glisic, “On linear precod-
ing strategies for secrecy rate maximization in multiuser multiantennawireless networks,” IEEE Trans. Signal Process. , vol. 62, no. 14,
pp. 3536–3551, Jul. 2014.
[38] F. Wang, X. Wang, and Y . Zhu, “Transmit beamforming for multiuser
downlink with per-antenna power constraints,” in Proc. IEEE Int. Conf.
Commun. (ICC) , Jun. 2014, pp. 4692–4697.
[39] A. Aggarwal and T. H. Meng, “Min imizing the peak-to-average power
ratio of OFDM signals via convex optimization,” in Proc. IEEE Global
Telecommun. Conf. (GLOBECOM) , vol. 4, Jun. 2003, pp. 2385–2389.
[40] P. V . Amadori and C. Masouros, “C onstant envelope precoding by inter-
ference exploitation in phase shift k eying-modulated multiuser trans-
mission,” IEEE Trans. Wireless Commun. , vol. 16, no. 1, pp. 538–550,
Jan. 2017.
[41] F. Liu, C. Masouros, P. V . Amadori, and H. Sun, “An efﬁcient man-
ifold algorithm for constructive in terference based constant envelope
precoding,” IEEE Signal Process. Lett. , vol. 24, no. 10, pp. 1542–1546,
Oct. 2017.
[42] F. Liu, C. Masouros, A. Petr opulu, H. Grifﬁths, and L. Hanzo,
“Joint radar and communication design: Applications, State-of-the-art,
and the road ahead,” 2019, arXiv:1906.00789 . [Online]. Available:
http://arxiv.org/abs/1906.00789
[43] D. R. Fuhrmann and G. San Antonio, “Transmit beamforming for MIMO
radar systems using si gnal cross-correlation,” IEEE Trans. Aerosp.
Electron. Syst. , vol. 44, no. 1, pp. 171–186, Jan. 2008.
[44] W. Dinkelbach, “On nonlinear fractional programming,” Manage. Sci. ,
vol. 13, no. 7, pp. 492–498, Mar. 1967.
[45] Z.-Q. Luo, W.-K. Ma, A. So, Y. Ye, and S. Zhang, “Semideﬁnite
relaxation of quadratic optimization problems,” IEEE Signal Process.
Mag. , vol. 27, no. 3, pp. 20–34, May 2010.
[46] K.-Y . Wang, A. M.-C. So, T.-H. Chang, W.-K. Ma, and C.-Y . Chi,
“Outage constrained robust transmit optimization for multiuser MISO
downlinks: Tractable approxima tions by conic optimization,”
IEEE
Trans. Signal Process. , vol. 62, no. 21, pp. 5690–5705, Nov. 2014.
[47] M. S. Lobo, L. Vandenberghe, S. B oyd, and H. Lebret, “Applications
of second-order cone programming,” Linear Algebra Appl. , vol. 284,
nos. 1–3, pp. 193–228, 1998.
[48] J. Li and P. Stoica, “MIMO radar with colocated antennas,” IEEE Signal
Process. Mag. , vol. 24, no. 5, pp. 106–114, Sep. 2007.
[49] S. Boyd and L. Vandenberghe, Convex Optimization . Cambridge, U.K.:
Cambridge Univ. Press, 2004.
[50] K. L. Law, I. Wajid, and M. Pesavento, “Optimal downlink beamforming
for statistical CSI with robustness to estimation errors,” Signal Process. ,
vol. 131, pp. 472–482, Feb. 2017.
[51] M. Grant, S. Boyd, and Y . Ye. (2008). CVX: MATLAB Software for Dis-
ciplined Convex Programming . [Online]. Available: http://cvxr.com/cvx/
Nanchi Su (Graduate Student Member, IEEE)
received the B.E. and M.E. degrees from the
Harbin Institute of Technology, Heilongjiang, China,in 2015 and 2018, respectively. She is currentlypursuing the Ph.D. degree with the Information
and Communications Engineering Research Group,
Department of Electronics and Electrical Engineer-ing, University College London, London, U.K. Her
research interests include constructive interference
design, physical-layer security, radar signal process-ing, and convex optimization.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 13 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 95
Fan Liu (Member, IEEE) r eceived the B.Eng. and
Ph.D. degrees from the Beijing Institute of Technol-ogy, Beijing, China, in 2013 and 2018, respectively.
He has been a Visiting Ph.D. Student with the
Department of Electronics and Electrical Engineer-ing, University College London, from 2016 to 2018,where he is currently a Marie Curie Research Fellow.
His research interests include vehicular networks,
massive MIMO and mmWave communications, andradar signal processing. He is also the Founding
Member of the IEEE Wireless Communications
Technical Committee (WTC) Special Interest Group (SIG) on Integrated Sens-ing and Communication (ISAC). He was a recipient of the Best Ph.D. ThesisAward of the Chinese Institute of Electronics in 2019 and the Marie Curie
Individual Fellowship in 2018. He has b een named as an Exemplary Reviewer
of the IEEE T
RANSACTIONS ON WIRELESS COMMUNICATIONS , the IEEE
TRANSACTIONS ON COMMUNICATIONS , and the IEEE C OMMUNICATIONS
LETTERS . He has served as the Co-Chair for the IEEE ICC 2020 Workshop
on Communication and Radar Spectrum Sharing.
Christos Masouros (Senior Member, IEEE)
received the Diploma degree in electrical andcomputer engineering from the University of Patras,
Greece, in 2004, and the M.Sc. and Ph.D. degrees
in electrical and electronic engineering from TheUniversity of Manchester, U.K., in 2006 and 2009,respectively.
In 2008, he was a Research Intern at the Philips
Research Laboratories, U.K. From 2009 to 2010,he was a Research Associate with The University
of Manchester and a Research Fellow with Queen’s
University Belfast from 2010 to 2012. In 2012, he joined University CollegeLondon as a Lecturer. He has held a R oyal Academy of Engineering
Research Fellowship from 2011 to 2016. He is currently a Full Professor
with the Information and Communi cation Engineering Research Group,
Department of Electrical and Electroni c Engineering, and afﬁliated with the
Institute for Communications and Connected Systems, University College
London. His research interests include wireless communications and signal
processing with a particular focus on green communications, large scaleantenna systems, communications and radar co-existence, interferencemitigation techniques for MIMO, and multicarrier communications. He is
also an Elected Member of the EURASIP SAT Committee on Signal
Processing for Communications and N etworking. He was a recipient of the
best paper awards at the IEEE GLOBECOM 2015 and the IEEE WCNC
2019 conferences. He has been recogni zed as an Exemplary Editor of the
IEEE C
OMMUNICATIONS LETTERS and an Exemplary Reviewer of the
IEEE T RANSACTIONS ON COMMUNICATIONS .H ei sa nE d i t o ro ft h eI E E E
TRANSACTIONS ON COMMUNICATIONS , the IEEE T RANSACTIONS ON
WIRELESS COMMUNICATIONS , and the IEEE O PENJOURNAL OF SIGNAL
PROCESSING , and an Editor-at-Large of the IEEE O PEN JOURNAL OF
THE COMMUNICATIONS SOCIETY . He has been an Associate Editor of
the IEEE C OMMUNICATIONS LETTERS and a Guest Editor of the IEEE
JOURNAL ON SELECTED TOPICS IN SIGNAL PROCESSING Issues Exploiting
Interference towards Energy Efﬁcient and Secure Wireless Communications,
Hybrid Analog/Digital Signal Processi ng for Hardware-Efﬁcient Large Scale
Antenna Arrays, and Joint Communication and Radar Sensing for Emerging
Applications.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

================================================================================


================================================================================
文件: 报告正文—广东省自然科学基金面上项目.pdf
页数: 54
================================================================================


--- 第 1 页 ---
广东省自然科学基金面上项目  
报告正文  
参照以下提纲撰写，要求内容翔实、清晰，层次分明，标题突
出。  
一、立论依据  
1、研究意义 （对基础研究，着重结合国际科学发展趋势，论述
项目的科学意义；对应用基础研究，着重结合学科前沿、围绕国民
经济和社会发展中的重要科技问题，论述其应用前景）。  
当前，全球信息通信技术（ ICT）正处于从 5G向6G演进的关
键历史节点。国际电信联盟（ ITU-R）发布的《 IMT-2030及未来发
展的框架和总体目标建议书》明确将 “通感融合  (Integrated Sensing 
and Communication, ISAC)” 列为 6G的六大核心应用场景之一[1]，标
志着无线网络的设计范式正从单一的信息传输管道，向赋能物理世
界与数字世界深度交互的智能信息基础设施演进。这一转变驱动了
国际学术界和工业界（如 Qualcomm 、Ericsson、Nokia等）的研究
焦点，从传统的追求更高通信速率（ eMBB）、更低时延（ URLLC）
和更广连接（ mMTC）的 “通信三角 ”，扩展至一个包含通信、感知、
计算、控制等多维度能力的新范式[2,3]。 
在此背景下，通感一体化被认为是实现自动驾驶、元宇宙、工
业物联网、智慧城市等变革性应用的核心使能技术[2]。它不仅通过
共享频谱和硬件资源来提升系统效率，更重要的是，它催生了“感
知辅助通信”和“通信辅助感知”的全新可能性，为突破传统无线

--- 第 2 页 ---
技术的性能瓶颈提供了内生性路径[3]。然而，如图 1所示，这种深
度的功能融合也带来了一个根本性的、亟待解决的科学挑战：安全
与隐私的边界正在被重塑[4,5]。 
 
图1 面向 6G的通感一体 系统安全隐私风险  
传统的网络安全体系建立在功能分离的假设之上，而通感一体
化的深度融合，正从根本上瓦解这一基础，催生出交织在一起的、
源于物理层的全新安全与隐私挑战[4-6]。这些挑战正是本项目研究的
直接动因，集中体现为三个亟待突破的科学难题：  
- 身份隐私泄露的 “不可见性 ”与“不可度量性 ”： 传统物理层安全聚
焦于保护通信 “内容 ”的机密性。但在 ISAC中，一个更严峻的威
胁浮出水面：感知信号本身成为了泄露用户身份信息的 “后门 ”。
高分辨率的感知能力能够捕获目标的步态、微多普勒等独特的
“物理层指纹 ”，使得即便通信内容被加密，个体的物理身份、行


--- 第 3 页 ---
为模式乃至健康状态也可能被恶意方 “去匿名化 ”。这种隐私泄露
是“不可见的 ”（因为它不发生在比特内容层面）且 “不可度量的 ”
（因为它缺乏公认的量化模型）。如何科学地表征并量化这种源
于物理层信号的身份信息泄露，是构建一切隐私防护机制的理论
基石，也是当前研究的重大空白。  
- 安全与隐私目标的 “冲突性”与“相容性”： 在ISAC系统中，多重
目标之间存在着复杂的内在矛盾。例如，为增强通信安全而采取
的波束精准对准措施，可能会无意中为窃听者提供高质量的感知
信号，反而加剧隐私泄露风险；反之，为保护隐私而主动注入的
混淆信号，又可能干扰合法的通信与感知。这种目标间的 “冲突
性”是系统设计的核心掣肘。因此，如何发掘并利用信号维度资
源，在看似矛盾的目标中找到 “相容性 ”设计空间，构建能够协同
增强安全与隐私的物理层联合传输机制，是化挑战为机遇，实现
整体性能跃升的关键。  
- 多维性能适配的 “理论边界 ”与“实现路径 ”： 随着隐私保护成为与
通信、感知、安全并列的核心需求， ISAC系统形成了一个 “通信 -
感知 -安全 -隐私 ”四维性能空间。这四个维度此消彼长，存在一个
根本性的性能权衡边界（ Pareto Front ）。缺乏对这一理论边界的
认知，任何优化都将是盲目的。更重要的是，面向 6G多样化的
应用场景（如重性能的车联网  vs. 重隐私的智慧医疗），系统必
须具备动态适应能力。因此，揭示四维性能的理论边界在何处，
并在此基础上探索出一条能够根据业务优先级动态调整系统工作

--- 第 4 页 ---
点的有效实现路径，是连接基础理论与未来应用的核心科学问题。  
为应对上述三大科学挑战，本项目旨在从基础理论层面取得原
创性突破，其科学意义不仅在于解决具体技术难题，更在于为下一
代无线网络构建内生安全与隐私理论的基石。  
（1） 开辟物理层安全新维度：为 “隐私去标识性 ”建立科学度量体
系。  针对身份隐私泄露 “不可见、不可度量 ”的难题，本项目
将率先系统性地研究其泄露机理，并从信息论和估计理论的
源头出发， 建立一套包含身份模糊度、隐私泄露率和特征估
计精度下界的科学量化度量体系 。这项工作将 实现从“0到1”
的理论突破 ，使物理层隐私首次变得可定义、可量化、可优
化，为整个 ISAC安全领域开辟出一个全新的、至关重要的
研究维度  
（2） 奠定联合安全设计新范式：提出协同增强安全与隐私的优化
方法。  针对安全与隐私目标的 “冲突与相容 ”问题，本项目将
跳出独立优化的传统框架， 提出一种联合波束赋形与人工噪
声协同的物理层传输新范式 。通过构建多目标优化模型，探
索在保障通信与感知 QoS的同时，协同提升通信保密容量与
感知隐私水平的可行域。这项研究旨在 提供一套全新的联合
安全与隐私解决方案 ，将 “矛”与“盾”的内在矛盾转化为协同
增益。 
（3） 揭示多维性能权衡新规律：明晰理论边界并设计动态适配机
制。  针对多维性能适配的 “理论边界与实现路径 ”问题，本项

--- 第 5 页 ---
目将首次 构建“通信 -感知 -安全 -隐私”四维性能框架，并致力
于揭示其根本性的性能权衡规律与帕累托最优边界 。更进一
步，将设计一种能够响应业务需求的 动态适配机制 ，使系统
能效最优化。这项工作将为 ISAC系统的设计提供宏观的理
论指导和实用的技术蓝图，确保系统在复杂多变的应用场景
下保持高效、可靠与安全 。 
综上所述，本项目聚焦于通感一体化这 一6G核心技术所衍生
的、更深层次的安全与隐私挑战。 通过对“隐私去标识性 ”的建模、
联合安全优化和动态适配机制的研究，项目旨在推动物理层安全理
论的范式演进，其成果不仅为解决未来万物智联时代的数字身份隐
私问题提供颠覆性技术思路，更将在基础理论层面丰富和完善下一
代通信系统的安全内涵，具有鲜明的前沿性、探索性和原创性，意
义重大。  
2、国内外研究现状。  
本项目旨在研究面向物理层联合安全的通感一体化系统中的隐
私去标识性问题，其核心是建立全新的 “通信 -感知 -安全 -隐私 ”一体
化理论与方法。本节将从三个紧密相关的方面，回顾国内外研究现
状及发展动态 （研究时间线 如图2），以论证本研究的紧迫性与前
沿性。  
2.1 通感系统中安全和隐私的风险研究现状  
通信感知一体化在极大提升频谱与硬件效率的同时，其信号的
开放性和功能的融合性也引入了前所未有的安全与隐私风险 ，这已

--- 第 6 页 ---
成为国际学术界关注的焦点。  
 
图2 相关研究时间线  
首先，针对 通信安全风险的加剧 ，传统的物理层安全主要针对
窃听者与合法用户信道条件差异来设计[7]。然而，在 ISAC场景中，
感知目标本身可能就是潜在的恶意窃听者。为了实现高精度的感知，
系统需要将高功率的信号波束聚焦于目标方向，这恰恰为作为窃听
者的目标创造了绝佳的接收条件，使其能以极高的信噪比（ SNR）
截获嵌入在感知信号中的通信数据[8]。此外，随着无人机 等高移动
性节点被用于 ISAC中继或基站，其广播特性使得通信信号更容易
被非授权用户捕获[9]。这些研究表明， ISAC的“探测即通信 ”特性从
根本上改变了物理层窃听模型，传统基于信号强度差异的安全机制
面临严峻挑战 。 
其次，相比于通信安全，由感知功能引发的隐私泄露是 通感融


--- 第 7 页 ---
合带来的全新、更深层次的威胁。感知信号不再仅仅探测目标的存
在、距离和速度  ，而是能够解析出更为精细的敏感信息。前沿研究
表明，利用无线信号（尤其是毫米波 /太赫兹频段）可以实现对人体
步态、呼吸、心跳等生物特征的提取[10,11 ]，以及对室内人员活动的
成像与重构[12]。这意味着，即便个人身处封闭的私密空间（如墙体
之后），也难以逃脱电磁波的“监视”。更有甚者，恶意设备可捕
获通感设备的回波信号，通过强大的处理能力反演出目标的身份与
状态，直接构成对个人隐私的严重侵犯[13]。 
现有研究已充分揭示了 ISAC在通信安全和感知隐私两方面所
面临的风险[14]。然而，多数工作仍将二者割裂看待，缺乏对它们内
在关联性的深入分析。特别是对于“感知隐私”，目前的研究多停
留在风险定性描述和场景展示层面，严重缺乏一套能够从物理层信
号本质出发，对身份信息泄露进行量化表征的理论模型和度量[15]。
这正是本项目“去标识性建模”拟解决的核心空白。  
2.2 面向隐私保护和物理层安全的通感系统研究现状  
为应对上述风险，国内外研究者已开始探索 通感融合 系统中的
安全与隐私增强技术，主要集中在信号处理和资源分配层面。  
物理层安全技术研究 方面，当前的研究大部分 继承传统物理层
安全思路，研究者们尝试将人工噪声（ AN）加入[16]、方向性调制
（DM）[17]和鲁棒波束赋形[18]等技术应用于 ISAC系统。例如，通过
在合法通信方向的正交空间注入人工噪声，可以在不影响合法用户
的前提下，恶化窃听者的接收信道[19]。方向性调制技术则通过在符

--- 第 8 页 ---
号级进行预编码，使得信号仅在期望的方向上能够被正确解调，而
在其他方向则呈现为无意义的噪声星座图，该方法不依赖窃听信道
信息，具有较好的内生性[20]。此外，考虑信道不确定性，设计鲁棒
的联合波束赋形方案以最大化保密速率，也是当前的研究热点[21]。 
相比于成熟的通信安全研究， ISAC的隐私保护研究尚处于起步
阶段。目前，一些探索性的工作开始关注如何主动防止感知信息的
泄露。例如，罗格斯大学的团队分析了在频谱共享场景下，通信系
统对雷达系统位置隐私的潜在威胁，并提出了一些初步的隐私保护
机制[22]。近期，有学者提出利用最大化合法感知接收机与非法接收
机之间的“互信息差”来作为优化目标，旨在降低非法接收端获取
目标位置信息的能力[23]。此外，利用干扰或欺骗信号来混淆恶意感
知节点，也被认为是保护目标隐私的一种潜在途径[24]。 
当前的研究在 ISAC安全技术上已取得一定进展，但存在明显
不足： 1）安全与隐私目标未协同优化：现有的安全方案主要为最大
化保密速率而设计，几乎不考虑其对感知隐私的潜在影响（可能是
增强也可能是削弱），反之亦然。如何构建一个能够协同优化通信
安全和感知隐私的统一框架，是当前研究的难点[25]。2）隐私保护手
段单一且不成熟：现有的隐私保护研究大多针对“位置”这一单一
维度的隐私，对于更复杂的生物特征、身份标识等隐私的保护研究
几近空白。所提出的“互信息差”等指标虽有启发性，但尚未形成
体系化的“去标识性”优化理论和方法[26]。 
2.3 通感系统中多维性能权衡与资源优化研究现状  

--- 第 9 页 ---
通感融合系统 的本质是一个多目标系统，其性能权衡与资源优
化是实现系统设计的关键。  
通信与感知性能权衡是 ISAC研究中最核心的议题。大量文献
从不同角度分析了通信速率（ Capacity/Rate ）与感知精度（如 CRLB、
估计误差）之间的权衡关系。研究者通过设计时分、频分、空分或
码分等复用方案，以及一体化波形，来探索二者性能的帕累托边界
[27,28 ]。例如， Xiong等人从信息论角度推导了高斯信道下通信感知的
基础折衷关系[29]；另一些工作则通过具体的波束赋形优化问题，来
获得在特定约束下的性能边界[30]。 
近期，部分工作开始将物理层安全引入 ISAC性能权衡分析中，
形成了“通信 -感知 -安全”的三维性能空间。研究通常以最大化保
密速率为目标，同时将感知性能（如探测概率、 CRLB）和通信性能
（如用户速率）作为约束条件，进行资源分配和波束设计[31]。这些
工作初步揭示了安全需求的引入对原有通感性能边界的影响，即为
了保障安全，通信或感知的性能必然会遭受一定损失[32,33 ]。 
现有性能权衡研究为 ISAC设计提供了重要的理论指导。然而，
随着隐私问题的凸显，这些研究框架存在明显短板： 1）性能维度缺
失：当前主流的性能权衡框架普遍忽略了“隐私”这一新兴的关键
维度。一个完整的 ISAC系统，其性能边界应由“通信、感知、安
全、隐私”四者共同定义，缺少任何一个维度都将导致对系统能力
的片面理解和次优设计[34]。2）静态优化为主，缺乏动态适配：多数
研究集中于在给定条件下求解静态的优化问题，以获得理论上的性

--- 第 10 页 ---
能点。然而，面向 6G复杂多变的业务需求，系统需要能够根据不
同场景的优先级（如有的重性能，有的重隐私）动态调整其工作模
式。目前严重缺乏对这种多维性能动态适配机制的研究[35]。 
综合上述分析，本项目正是立足于以上研究空白，旨在 将“隐
私”作为内生维度，深度融入 ISAC物理层安全设计，建立“通信 -
感知 -安全 -隐私”四维一体的建模、优化与适配理论框架 ，具有重
要的理论创新价值和前瞻性。  
3、主要参考文献及出处 （格式：论文 --作者．题目．刊名．年
份．卷 (期)．页码／专著 --作者．书名．出版者．年份）。  
[1] N. R. Fachrurrozi, K. Ramli and K. Anwar, "Challenges on Security and Privacy in IMT -2030 
(6G) Networks," 2024 IEEE International Conference on Communication, Networks and Satellite 
(COMNETSAT), Mataram, Indonesia, 2024, pp. 691 -698. 
[2] N. Su, F. Liu and C. Masouros, "Sensing -Assisted Eavesdropper Estimation: An ISAC 
Breakthrough in Physical Layer Security," in  IEEE Transactions on Wireless Communications , vol. 
23, no. 4, pp. 3162 -3174, April 2024 . 
[3] N. Gonzá lez -Prelcic  et al., "The Integrated Sensing and Communication Revolution for 6G: 
Vision, Techniques, and Applications," in  Proceedings of the IEEE , vol. 112, no. 7, pp. 676 -723, 
July 2024  
[4] J. A. Zhang  et al., "Enabling Joint Communication and Radar Sensing in Mobile Networks —A 
Survey," in  IEEE Communications Surveys & Tutorials , vol. 24, no. 1, pp. 306 -345, First  quarter 
2022 . 
[5] K. Qu, J. Ye, X. Li and S. Guo, "Privacy and Security in Ubiquitous Integrated Sensing and 
Communication: Threats, Challenges and Future Directions," in  IEEE Internet of Things 
Magazine , vol. 7, no. 4, pp. 52 -58, July 2024  
[6] J. Chu, R. Liu, M. Li, Y . Liu and Q. Liu, "Joint Secure Transmit Beamforming Designs for 
Integrated Sensing and Communication Systems," in  IEEE Transactions on V ehicular Technology , 
vol. 72, no. 4, pp. 4778 -4791, April 2023  
[7] Y . -S. Shiu, S. Y . Chang, H. -C. Wu, S. C. . -H. Huang and H. -H. Chen, "Physical layer 
security in wireless networks: a tutorial," in  IEEE Wireless Communications , vol. 18, no. 2, pp. 
66-74, April 2011 . 
[8] J. A. Zhang  et al., "An Overview of Signal Processing Techniques for Joint Communication 
and Radar Sensing," in  IEEE Journal of Selected Topics in Signal Processing , vol. 15, no. 6, pp. 
1295 -1315, Nov. 2021 . 
[9] 陈新颖 , 盛敏 , 李博 , 等. 面向  6G 的无人机通信综述 [J]. 电子与信息学报 , 2022, 44(3): 781 -
789. 

--- 第 11 页 ---
[10] F. Adib and D. Katabi, “See through walls with WiFi,” SIGCOMM Computer 
Communication Review , vol. 43, no. 4, pp. 75 –86, Oct. 2013.  
[11] J. Liu, Y . Chen, Y . Wang, X. Chen, J. Cheng and J. Yang, "Monitoring Vital Signs and 
Postures During Sleep Using WiFi Signals," in  IEEE Internet of Things Journal , vol. 5, no. 3, pp. 
2071 -2084, June 2018,  
[12] D. S. Nunes, P. Zhang and J. Sá  Silva, "A Survey on Human -in-the-Loop Applications 
Towards an Internet of All," in  IEEE Communications Surveys & Tutorials , vol. 17, no. 2, pp. 
944-965, Secondquarter 2015  
[13] F. Marcello, G. Pettorru, M. Martalò  and V . Pilloni, "Preserving Privacy in CSI -based Human 
Activity Recognition: a Data Obfuscation Case Study,"  GLOBECOM 2024 - 2024 IEEE Global 
Communications Conference , Cape Town, South Africa, 2024, pp. 2822 -2827  
[14] F. Liu  et al ., "Integrated Sensing and Communications: Toward Dual -Functional Wireless 
Networks for 6G and Beyond," in  IEEE Journal on Selected Areas in Communications , vol. 40, no. 
6, pp. 1728 -1767, June 2022,  
[15] Z. Ren, J. Xu, L. Qiu and D. Wing Kwan Ng, "Secure Cell -Free Integrated Sensing and 
Communication in the Presence of Information and Sensing Eavesdroppers," in  IEEE Journal on 
Selected Areas in Communications , vol. 42, no. 11, pp. 3217 -3231, Nov. 2024  
[16] S. Goel and R. Negi, "Guaranteeing Secrecy using Artificial Noise," in  IEEE Transactions on 
Wireless Communications , vol. 7, no. 6, pp. 2180 -2189, June 2008 . 
[17] T. Hong, M. -Z. Song and Y . Liu, "Dual -Beam Directional Modulation Technique for 
Physical -Layer Secure Communication," in  IEEE Antennas and Wireless Propagation Letters , vol. 
10, pp. 1417 -1420, 2011 . 
[18] Z. Ren, L. Qiu, J. Xu and D. W. K. Ng, "Robust Transmit Beamforming for Secure Integrated 
Sensing and Communication," in  IEEE Transactions on Communications , vol. 71, no. 9, pp. 5549 -
5564, Sept. 2023 . 
[19] O. Gü nlü , M. R. Bloch, R. F. Schaefer and A. Yener, "Secure Integrated Sensing and 
Communication," in  IEEE Journal on Selected Areas in Information Theory , vol. 4, pp. 40 -53, 
2023 . 
[20] 徐勇军 , 曹娜 , 陈前斌 . 通信感知一体化波形设计方法综述 [J]. Journal of Chongqing 
University of Posts & Telecommunications (Natural Science Edition), 2023, 35(6).  
[21] E. Grossi, M. Lops and L. Venturino, "Joint Design of Surveillance Radar and MIMO 
Communication in Cluttered Environments," in  IEEE  Transactions on Signal Processing , vol. 68, 
pp. 1544 -1557, 2020  
[22] M. Grissa, A. A. Yavuz and B. Hamdaoui, "Preserving the Location Privacy of Secondary 
Users in Cooperative Spectrum Sensing," in  IEEE Transactions on Information Forensics and 
Security , vol. 12, no. 2, pp. 418 -431, Feb. 2017  
[23] L. Chen  et al., "Robustness, Security and Privacy in Location -Based Services for Future IoT: 
A Survey," in  IEEE Access , vol. 5, pp. 8956 -8977, 2017  
[24] D. Wen, Y . Zhou, X. Li, Y . Shi, K. Huang and K. B. Letaief, "A Survey on Integrated Sensing, 
Communication, and Computation," in  IEEE Communications Surveys & Tutorials , doi: 
10.1109/COMST.2024.  
[25] K. Qu, J. Ye, X. Li and S. Guo, "Privacy and Security in Ubiquitous Integrated Sensing and 
Communication: Threats, Challenges and Future Directions," in  IEEE Internet of Things 
Magazine , vol. 7, no. 4, pp. 52 -58, July 2024  

--- 第 12 页 ---
[26] X. Yan, G. Zhou, D. E. Quevedo, C. Murguia, B. Chen and H. Huang, "Privacy -Preserving 
State Estimation in the Presence of Eavesdroppers: A Survey," in  IEEE Transactions on 
Automation Science and Engineering , vol. 22, pp. 6190 -6207, 2025  
[27] F. Liu, C. Masouros, A. P. Petropulu, H. Griffiths and L. Hanzo, "Joint Radar and 
Communication Design: Applications, State -of-the-Art, and the Road Ahead," in  IEEE 
Transactions on Communications , vol. 68, no. 6, pp. 3834 -3862, June 2020  
[28] 马丁友 , 刘祥 , 黄天耀 , 等. 雷达通信一体化 : 共用波形设计和性能边界 [J]. 雷达学报 , 2022, 
11(2): 198 -212. 
[29] Y . Xiong, F. Liu, Y . Cui, W. Yuan, T. X. Han and G. Caire, "On the Fundamental Tradeoff of 
Integrated Sensing and Communications Under Gaussian Channels," in  IEEE Transactions on 
Information Theory , vol. 69, no. 9, pp. 5723 -5751, Sept. 2023 . 
[30] 徐金雷 , 赵俊湦 , 卢华兵 , 等. 面向  6G 的多维扩展通感一体化研究综述 [J]. 电子与信息学
报, 2024, 46(5): 1 -12. 
[31] 梁彦 , 杨晓宇 , 李飞 . 一种基于扩展卡尔曼滤波的智能反射面辅助通感一体化系统安全传
输方案 [J]. 电子与信息学报 , 2025, 47: 1 -14. 
[32] N. Su, F. Liu , C. Masouros , et al. Secure ISAC MIMO systems: exploiting interference with 
Bayesian Cramé r –Rao bound optimization[J]. EURASIP Journal on Wireless Communications 
and Networking, 2025, 2025(1): 10.  
[33] 林粤伟 , 王溢 , 张奇勋 , 等. 面向  6G 的通信感知一体化车联网研究综述 [J]. 信号处理 , 
2023, 39(6): 963 -974. 
[34] D. Wen, Y . Zhou, X. Li, Y . Shi, K. Huang and K. B. Letaief, "A Survey on Integrated Sensing, 
Communication, and Computation," in  IEEE Communications Surveys & Tutorials , doi: 
10.1109/COMST.2024.  
[35] A. Liu  et al., "A Survey on Fundamental Limits of Integrated Sensing and Communication," 
in IEEE Communications Surveys & Tutorials , vol. 24, no. 2, pp. 994 -1034, Secondquarter 2022 . 
二、研究方案  
1、研究目标、研究内容和拟解决的关键问题。  
1.1 研究目标  
本项目的总体目标是面向 6G通感一体化系统内生的安全与隐
私挑战，提出一套集“建模 -优化 -适配”于一体的物理层联合安全
与隐私去标识性传输机制。该机制旨在实现通信信息安全与感知身
份隐私的协同保障，并能够自适应匹配通信、感知、安全、隐私四
维度的动态性能需求，为构建未来高可靠、高私密性的智能网络信
息服务提供关键理论与技术支撑 。 

--- 第 13 页 ---
 
图3 总体研究内容、目标和关键科学问题之间的关系  
为实现上述总体目标，拟达成以下三个具体研究目标：  
（1） 建立一套面向通感一体化系统的物理层隐私量化表征模
型，明晰用户身份信息的泄露机理与设计约束。 本目标旨在解决
物理层隐私“不可度量”的根本难题，通过构建科学的度量衡
（如身份模糊度、特征估计下界），为后续的隐私保护技术优化
提供坚实的理论依据和清晰的设计边界 。 
（2） 探索一种面向隐私增强的物理层联合优化方法，提供兼
顾通信安全与身份隐私的协同增益。 本目标旨在解决安全与隐私
可能存在的内在冲突，通过设计新颖的联合波束赋形与人工噪声
方案，在保证通信服务质量（ QoS）和感知精度的前提下，最大
化通信保密速率与用户身份去标识性，实现“ 1+1>2”的协同防
护效果。  
（3） 提出一种支持多维性能权衡的动态适配传输机制，实现


--- 第 14 页 ---
对动态业务需求和复杂系统性能的灵活调控。 本目标旨在解决理
论方案的实际应用问题，通过揭示“通信 -感知 -安全 -隐私”四维
性能的根本权衡关系，设计一种能够根据不同业务场景的优先级，
智能优选系统工作模式的动态资源分配策略，实现系统整体效能
的最优化  
1.2 研究内容  
本项目针对 6G通感一体化网络因其开放融合特性所引发的内
生性、多维度安全与隐私威胁，研究物理层联合安全与隐私去标识
性的核心问题。从基础理论建模出发，探索协同优化方法，并最终
落脚于系统性能的动态适配。拟从  
➢ 物理层隐私泄露机理建模与量化表征  
➢ 隐私增强的物理层联合安全优化方法  
➢ “通感 -安全 -隐私”多维性能权衡与动态适配机制  
三个层面展开研究。以上三个研究内容紧密衔接、层层递进。
研究内容一中建立的隐私量化模型是研究内容二中优化方法设计的
前提和目标；研究内容二提出的联合优化方案是实现研究内容三中
性能适配的核心技术手段；研究内容三的性能权衡分析则为整个系
统的设计和优化提供了宏观的理论指导。下面针对具体的研究内容
进行详细阐述。  
研究内容  1：通感系统物理层隐私去标识性建模与量化表征  
通感一体化技术通过共享无线信号和硬件资源，在提升网络效
率的同时，也使得感知信道不可避免地成为隐私泄露的“后门”。

--- 第 15 页 ---
本部分旨在从理论层面揭示并量化这一风险，为后续的隐私保护设
计提供坚实的数学基础。具体包括： 1）物理层隐私泄露机理分析。  
深入研究通感信号与目标的交互过程，重点分析由感知回波信号
（如微多普勒效应、信道状态信息（ CSI）的精细化特征）导致用户
生物特征（步态、心率）、行为模式、设备类型等身份标识信息泄
露的关键物理机理，并建立相应的信号模型； 2）隐私去标识性量化
体系构建。  传统的保密容量等指标无法衡量身份信息的安全性。本
研究将从信息论和估计理论出发，构建一套全新的隐私量化指标体
系，核心包括：基于信息熵的身份模糊度（ Identity Ambiguity ），
用于刻画窃听者对目标身份的不确定性；基于克拉美 -罗下界
（CRLB）的特征估计误差界，用于量化窃听者对用户敏感物理特征
的理论最佳估计精度； 3）联合安全与隐私性能基准。  在上述指标
基础上，结合物理层安全的经典指标（如保密速率），建立一个能
够统一评估通信安全和感知隐私的综合性能表征框架，作为后续优
化设计的基准。  
研究内容  2：面向隐私增强的物理层联合安全优化方法  
在隐私可被科学度量的基础上，本部分旨在设计能够协同增强
通信安全与感知隐私的主动式物理层传输方案，解决安全与隐私目
标间的潜在冲突。具体包括： 1）联合安全与隐私波束赋形设计。  
研究多天线 ISAC系统的空域资源优化，设计联合预编码矩阵，目
标是协同地在空间上塑造信号分布：在保证合法用户通信质量和感
知精度的前提下，对通信窃听者形成“功率零陷”以保障信息安全，

--- 第 16 页 ---
同时对感知隐私窃听者形成“特征模糊区”，即有意恶化其对敏感
特征的信道观测质量，达成隐私去标识性； 2）隐私增强的人工噪声
（AN）辅助方案。  探索 AN在隐私保护中的新作用，研究与通感信
号共享同一协方差矩阵的 AN设计。通过优化 AN的协方差结构，
使其在合法用户处可被消除，而在窃听者处不仅能作为干扰降低其
通信窃听能力，更能作为“污染源”破坏感知回波的特征完整性，
从根本上提升隐私保护水平； 3）多目标协同优化与求解。将联合波
束赋形与 AN辅助方案相结合，构建以最大化“通信保密速率”和
“隐私去标识性度量”加权和为目标的、受限于总功率和基本服务
质量（ QoS）的优化问题。由于该问题是典型的多变量耦合非凸问
题，将探索分式规划（ Fractional Programming ）、半正定松弛
（Semidefinite Relaxation ）等高效求解算法。  
研究内容  3：“通感 -安全 -隐私”多维性能权衡与动态适配机制  
ISAC系统本质上是一个多目标系统，其“开放性”（通信与感
知性能）和“安全性”（信息安全与隐私）之间存在根本性的矛盾。
本部分旨在揭示其内在规律，并设计智能适配机制。具体包括： 1) 
四维性能根本权衡关系分析。  基于研究内容二的优化框架，建立包
含通信速率、感知精度、安全速率和隐私度量的统一性能空间。通
过调整不同目标的优化权重，求解一系列多目标优化问题，描绘出
系统的帕累托（ Pareto）最优边界，从而从理论上揭示“通信 -感知 -
安全 -隐私”四者之间此消彼长的根本制约关系； 2) 面向业务的动态
适配策略。  针对 6G多样化的业务场景（如重性能的车联网、重隐

--- 第 17 页 ---
私的智慧医疗），研究一种动态的工作模式选择策略。该策略能够
根据上层业务赋予的性能优先级（即不同的性能权重组合），在已
知的帕累托边界上寻找使当前业务场景“效用”最大化的最优工作
点； 3) 低复杂度在线资源分配算法。  将动态适配策略转化为可实时
运行的低复杂度在线算法。该算法能够根据环境和业务需求的变化，
快速调整波束赋形、功率分配和人工噪声等物理层参数，实现系统
在不同性能目标间的平滑切换与高效运行，最终达成多维性能的动
态适配。  
 
图4 拟解决的关键问题  
1.3 拟解决的关键问题  
本项目针对 6G通感一体化网络因其开放融合特性所引发的内
生性、多维度安全与隐私威胁，研究物理层联合安全与隐私去标识
性的核心问题。从通信感知融合系统架构切入，探索如何通过物理
层技术实现通感系统内生安全，并深入分析内生安全赋能的通感融
合系统的网络弹性。  


--- 第 18 页 ---
为突破上述瓶颈，本项目凝练并拟解决的关键科学问题：  
通信 -感知 -安全集成网络系统的多维性能动态适配。  
这一核心问题，从“表征”和“逼近”两个层面构成了研究的
重心。有效容量的精准表征是解决问题的前提和设计边界，而编码
增益的提升与对时变信道的持续逼近则是解决问题的途径与核心。
根据这一逻辑，关键科学问题可具体分解为以下三个相互关联、层
层递进的子问题：  
➢ 物理层隐私泄露的“不可见”与“不可度量”问题。  传统物理
层安全聚焦于比特流的机密性，而 ISAC的感知功能可能导致用
户生物特征、行为模式等“物理层指纹”的泄露，这是一种更隐
蔽的风险。如何科学地建模并度量这一新维度的风险，是实现隐
私保护从“定性”走向“定量”分析的首要科学难题。  
➢ 安全与隐私目标的“冲突性”与“相容性”问题。  增强通信安
全的措施与增强感知隐私的措施可能在物理层相互掣肘。如何发
掘其内在的协同性，设计出能够“化矛盾为机遇”的联合优化机
制，是一个极具挑战性的科学问题。  
➢ 多维性能动态适配的“理论边界”与“实现路径”问题。  “通
信-感知 -安全 -隐私”四维性能空间相互制约，其理论最优边界尚
不明确。如何揭示这一边界，并设计出能根据外部需求沿着边界
动态调整的实用适配策略，是连接基础理论与未来应用必须解决
的关键科学问题。  
以上三个子问题并非孤立存在，而是构成了一个从“理论基础

--- 第 19 页 ---
→技术核心→系统应用”的完整研究链条。其中，问题 1的解决是
整个研究的逻辑起点，它为安全与隐私的协同优化提供了可量化的
目标和边界；在此基础上，问题 2的解决构成了本项目的技术核心，
旨在提出能够兼顾多重目标的创新性传输方案；最后，问题 3的解
决是项目的最终落脚点，它基于前两者的研究成果，探索系统性能
的理论极限与实用化路径。通过对这三个关键科学问题的逐层深入
和系统性回答，本项目将为构建下一代高安全、高隐私的通感一体
化网络提供完备的理论与方法支撑。  
2、拟采取的研究方法、技术路线、实验方案及可行性分析。  
2.1 研究方法  
为系统性地解决本项目的关键科学问题并达成研究目标， 本项
目采用理论推导与数值仿真紧密结合的研究方法，确保研究成果的
理论深度与实践有效性。具体而言，本项目的研究方法可分解为以
下三个有机结合的层面：  
理论建模与分析方法：  针对物理层隐私泄露 “不可度量 ”的难题，
我们将首先采用 信息论和估计理论 作为核心分析工具。通过建立严
谨的系统信号模型，运用熵、互信息、克拉美 -罗下界（ CRLB）等
理论工具，对隐私泄露进行数学建模和量化表征。这一方法旨在从
问题的本源出发，为后续的优化设计提供清晰、可量化的目标和边
界，奠定整个项目的理论基石。  
优化理论与算法设计方法：  针对通信安全与感知隐私的协同增
强问题，我们将运用 最优化理论 作为核心设计工具。通过构建多目

--- 第 20 页 ---
标的联合优化问题，将复杂的系统需求转化为精确的数学规划模型。
对于模型中普遍存在的非凸、多变量耦合等难点，将综合运用分式
规划、半正定松弛（ SDR）、序列凸近似（ SCA）等优化算法设计
技巧，在理论上探索其最优解或高质量次优解的求解方法，以设计
出创新、高效的物理层传输方案。  
蒙特卡洛仿真与性能评估方法：  针对理论模型与优化算法的有
效性验证，我们将采用 蒙特卡洛仿真 作为主要实验手段。通过搭建
端到端的系统级仿真平台，在典型的通感一体化场景（如车联网、
无人机通信）和信道模型下，对所提出的建模理论和优化算法进行
大量的随机实验和性能评估。通过分析仿真数据，我们将验证理论
模型的准确性，评估所提方案相较于现有技术的性能增益，并直观
地展示 “通信 -感知 -安全 -隐私 ”四维性能的权衡关系，为理论研究提
供有力的实证支持。  
综上所述，本项目将理论分析的深度与仿真验证的广度相结合，
形成一个从理论构建到方案设计，再到性能验证的完整闭环研究范
式，以保障研究内容的顺利开展和研究目标的最终实现。  
2.2 技术路线  
本项目根据三个研究内容的特点，按总体技术路线图所示，详
细阐述如下：  

--- 第 21 页 ---
 
图5 项目总体技术路线  
2.2.1 通感系统物理层隐私去标识性建模与量化表征   
本研究内容 首先将从物理根源入手，深入分析隐私泄露的内在
机理，并建立精确的系统信号模型；在此基础上，引入信息论与估
计理论，构建一套能够科学度量隐私保护水平的全新指标体系；最
后，结合传统安全指标，建立一个统一的性能基准框架，为后续的
优化方案设计提供明确的参照。具体技术方案如下：  
 
图6 研究内容（ 1）技术路线  


--- 第 22 页 ---
（1）物理层隐私泄露机理分析  
为分析物理层隐私泄露机理，首先构建通感一体化系统和信号
模型。考虑一个通用的多输入多输出（ MIMO）通感一体化下行链
路场景。该场景包含一个配置了
TN 根天线的通感基站（ BS），一
个单天线合法通信用户（ Bob），一个待感知的目标，以及一个配
置
EN根天线的无源窃听者（ Eve）。 
基站发送的通感一体化信号向量
1TNx
 由通信部分和感知部
分叠加而成：  
 
c c r r=+x W s W s  (1) 
其中，
1cN
cs
 是发送给合法用户的通信符号向量，
1rN
rs
 是
专用的雷达感知导频符号，
cW 和
rW分别是对应的预编码矩阵。  
窃听者 Eve接收到的、由目标反射的信号
1EN
Ey
 可以写作：  
 
()()()()H
E TE BT Et t t t=+ y h h x n  (2) 
其中
1EN
TEh
 表示目标到窃听者的信道，
1TN
BTh
 表示基站到
目标的信道，
()t 是目标的 时变标量雷 达散射系数，其时变特性是
隐私信息泄露的物理根源。 令
H
cas TE BT= H h h 上式可以写为  
 
()()()()E cas Et t t t=+ y H x n  (3) 
接下来建立“物理层指纹”的数学模型，即 由目标的周期性微
小运动（如 人的呼吸、步态中肢体的摆动）所引发的微多普勒效应 ，
这种信息变化包含在时变散射系数
()t 中，其模型可表示为 ： 
 
() ()()2exp 2 t j d t=  (4) 

--- 第 23 页 ---
其中
是目标的平均雷达散射截面 ，
是信号波长，
()dt 是由微动
引起的视距位移。对于一个典型的简谐振动模型（可用于模拟呼吸
或单一部件的摆动），该位移可建模为：  
 
()()0 sin 2mm d t A f t =+  (5) 
其中隐私参数包括
,priv m m Af=θ 分别代表了微动的幅度和频率。
这些参数可直接关联到个体 特征，比如微振动频率， 从而构成可被
利用的 “物理层指纹 ”。 
  将微变模型（ 4）带入到接收信号模型（ 3），可以得到窃听段观测
信号和隐私参数之间的函数。由此， 可以写出在给定隐私参数
privθ
条件下， 窃听者观测到信号
()Ety 的条件概率密度函数：  
 
()() |E privptyθ  (6) 
该似然函数精确地描述了隐私信息是如何调制在窃听者的观测
数据之上的。它不仅是窃听者进行参数估计和身份识别的数学基础，
更是我们后续进行隐私量化分析和设计隐私保护方案的出发点。通
过对该函数的深入分析，将彻底阐明物理层隐私泄露的内在机理。  
（2）隐私去标识性量化体系构建  
为完成对物理层隐私的科学度量，解决传统安全指标无法衡量
身份信息安全性的问题，本部分将基于前述建立的信号模型，从信
息论和估计理论两个互补的维度，构建一套全新的、能够定量评估
隐私保护水平的指标体系。  
首先，构建基于信息论的 “身份模糊度 ”度量。该指标旨在从 “分
类”的角度量化窃听者对目标真实身份的不确定性。假设窃听者试图

--- 第 24 页 ---
从一个包含
K 个可能身份的集合
 1,2, , K=
 中识别出当前目标。
身份模糊度可定义为窃听者在获得观测信号
Ey 后，对目标身份
k 的
后验概率分布
()|E pk y 的香农熵：  
 
()()() 2
1logK
E E E
kp k p k
==− y y y
  (7) 
该指标直接量化了窃听者的不确定性，
 越大表示隐私保护水平越
高。表达式（ 7）中的后验概率
()E pky 如何求解是计算该指标的关
键问题。本研究 将采用贝叶斯定理进行推导：  
 
()()()
()()1E
E K
E ip k p kpk
p i p i==yy
y  (8) 
其中，
()pk 是目标为身份 为
k的先验概率（通常可假设为均匀分布
1K
）。而核心的似然 函数
() Epky 表示当目标为身份
k 时观测到信
号
Ey的概率，将直接基于上一节 “物理层隐私泄露机理分析 ”中建立
的信号模型
(), E priv kpyθ 进行计算，其中
,priv kθ 是与身份
k 关联的唯
一物理层指纹参数。  
接下来将 构建基于估计理论的 “特征估计误差界 ”度量。该指标
旨在从 “参数估计 ”的角度量化窃听者精确获取用户敏感物理特征的
难度。本研究将采用克拉美 -罗下界作为核心度量，它为任何无偏估
计量的方差提供了理论下限。对于上一节中定义的隐私参数矢量
 ,,priv m m Af=θ
，其估计协方差矩阵满足：  
 
()()()1T
priv privpriv priv priv− −−θθθθ Jθ
  (9) 
其中，
()privJθ 是关于参数
privθ 的费舍尔信息矩阵 （Fisher 

--- 第 25 页 ---
Information Matrix, FIM ）。
()1
priv−Jθ 表示 CRLB，CRLB越大，意
味着窃听者能达到的最佳估计精度越差，隐私保护就越好。 对于加
性复高斯噪声信道， 费舍尔信息矩阵 的第
(),ij 个元素
(),privijJθ
可由下式计算  
 
()()()
2,2ReH
priv priv
privijE i j
         =        θ θ
Jθ  (10) 
其中，
()()priv E t =θ y
 是窃听端接收信号的均值向量，其具体
形式由上一节的信号模型确定。
i 和
i是
privθ中的任意两个参数。
本研究将通过对信号模型求偏导来计算 FIM，并求其逆矩阵以得到
各项隐私参数的 CRLB，从而建立起发射信号设计与隐私保护水平
之间的直接数学关联。  
 
图7 不同信噪比 的条件下特征参数 值对隐私保护的影响。FEEB表示
估计的方均根误差， 其值越大 表示隐私保护越好。  


--- 第 26 页 ---
（3）联合安全与隐私性能基准  
在构建了全新的隐私度量体系之后，为形成一个能够完整评估
通感系统安全性的综合框架，并为后续优化设计提供明确的性能参
照，本部分将结合物理层安全的经典指标，建立联合性能基准。  
首先，引入通信安全性能度量。为实现通信安全与感知隐私的
统一评估，首先引入经典的物理层安全性能指标 ——可达保密率。
该指标定义为合法通信信道容量与窃听信道容量之差。假设合法用
户（ Bob）的接收信干噪比为
SINRB ，通信窃听者（ Eve）的接收信
干噪比为
SINRE ，则可达保密率
sR 可表示为：  
 
22 [ ] [log (1 SINR ) log (1 SINR )]s B E B ER R R++= − = + − +  (11) 
其中，各接收端的信干噪比是发射波束赋形向量
cw 和对应信道向量
（如
Bh和
Eh）的函数。例如，
2
2||SINRBc
B
B=hw 。该指标将作为
衡量系统抵抗通信内容窃听能力的核心度量。  
接下来构建联合安全与隐私性能评估框架。本研究将定义一个
多维度的联合性能向量
η ，用以综合表征系统的整体性能。该向量
至少包含以下四个核心维度：  
 
[ , , ( | ),CRLB ( )]B s E E privR R K=η Y θ
  (12) 
其中
BR表示合法用户的可达速率 ，用于衡量 通信服务质量 ；
sR表
示系统的可达保密率 ，用于衡量 通信信息安全；
 表示窃听者的身
份模糊度 ，用于评估 身份信息隐私 程度；
CRLBE 表示窃听者对敏
感特征的估计误差下界 ，用于衡量感知估计性能。  

--- 第 27 页 ---
 
图8 综合隐私度量 3D可视化 
为获得一个可供比较的性能基准点，将建立一个基础场景下的
资源分配优化问题。该问题以最大化合法用户的通信速率为目标，
同时将安全和隐私性能作为基本约束。 令
,cr=W w W 代表总的发
射预编码矩阵，该基准问题可形式化为：  
 
,th
priv ,th
sensing ,th
maxmax ( )
s.t. 1: ( )
       2: CRLB ( , )
       3:SINR
       4: Tr( )B
ss
Ep
s
HR
C R R
C
C
CP



W W
W
W
WW  (13) 
其中，约束 C1和C2分别保证了最低的通信安全和隐私保护水平，
C3保证了基本的感知任务性能（如探测信噪比）， C4为总发射功


--- 第 28 页 ---
率约束。通过求解该问题所得到的性能点
η ，将作为衡量后续 “研究
内容 2”中所提出的、更先进的联合优化方案性能增益的定量基准。  
2.2.2 面向隐私增强的物理层联合安全优化方法  
研究内容 1建立了科学的量化模型，本部分旨在将理论模型转
化为主动的、可实施的物理层传输方案，以达成协同增强通信安全
与感知隐私的目标。核心思路是充分利用通感基站所具备的信号处
理和多天线自由度，通过对发射信号的精细化设计，在空间、功率
等维度上对信息流和特征流进行主动调控。本部分将从联合波束赋
形这一核心术入手，并辅以人工噪声等增强手段，最终构建一个统
一的优化框架。  
 
图9 研究内容（ 2）技术路线  
（1）联合安全与隐私波束赋形设计  
为实现对通信安全和感知隐私的协同增强，本研究点旨在通过
优化发射波束赋形，精确地在空间上调控信号能量与特征的分布。
技术方案的核心是构建并求解一个能够兼顾多重目标和约束的联合
预编码矩阵优化问题。  


--- 第 29 页 ---
本研究的核心在于设计总的发射预编码矩阵
,cr=W w W ，其
中
cw  是通信波束赋形向量，
rW 是感知预编码矩阵。设计的核心目
标是双重的：  
a． 保障通信安全：通过在通信窃听者
Evec 方向上形成 “功率零
陷”，即最小化泄露给其的信号功率。该目标可表示为：  
 
2min
cH
EF WhW  (14) 
其中，
cEh 表示基站 到通信窃听者的信道向量。  
b． 增强感知隐私 ：通过在感知窃听者
Evep 方向上形成 “特征模糊
区”，即最大化其对目标敏感参数
privθ 的估计误差下界。该目
标可表示为：  
 
() max   CRLB ,
pE privWθ W  (15) 
其中
CRLB
pE 是基于上一节模型计算出的感知窃听者的克拉美 -罗下
界。 
接下来， 构建多维度性能约束条件。上述优化必须在保证系统
基本服务质量的前提下进行 。因此，需要引入以下关键约束：  
➢ 合法通信用户 QoS约束：保证合法用户 Bob的接收信干噪比不
低于某一门限
c ，以确保通信质量  。 
 
2
22||SINR||H
Bc
Bc H
B r B= +hw
hW  (16) 
➢ 合法感知任务性能约束：保证对目标的感知精度不低于某一门限
r
。这同样通过 CRLB来约束，但针对的是合法的感知接收机
（如 BS自身作为雷达接收机）对目标位置、速度等公共参数

--- 第 30 页 ---
pubθ。 
 
legit pub CRLB ( , )sθW  (17) 
➢ 总发射功率约束：基站的总发射功率不能超过其最大限制
maxP 。 
 
() max trHP WW  (18) 
综合上述目标与约束，可建立如下的多目标优化问题。一种常
见的处理方式是将其转化为加权单目标优化问题：  
 
2
priv
legit pub
maxmin CRLB ( , )
s.t. 1:SINR ( )
       2 : CRLB ( , )
       3: Tr( )cpH
E F E
Bc
s
HC
C
CP−


W hW θ W
W
θW
WW‖‖  (19) 
其中，
0 是权重因子，用于权衡通信安全与感知隐私的重要性。
该问题通常是一个复杂的非凸优化问题。本研究将探索采用半正定
松弛（ SDR）技术将其转化为凸的半正定规划问题，或采用序列凸
近似（ SCA）等迭代算法来寻求其高质量的次优解。通过求解该问
题，即可得到能够在空间上实现安全与隐私协同防护的最优波束赋
形策略
W 。 
（2）隐私增强的人工噪声 辅助方案  
在联合波束赋形的基础上，为进一步增强系统的安全与隐私性
能，并提供额外的优化自由度，本研究点将引入并设计一种专用于
通感一体化系统的隐私增强型人工噪声（ Privacy -Enhancing Artificial 
Noise, PE -AN）。技术方案的核心是优化人工噪声的协方差矩阵，
使其在不影响合法服务的同时，最大化对窃听者的通信干扰与隐私
特征混淆效果。  

--- 第 31 页 ---
在原有的发射信号模型基础上，引入一个与通信及感知信号统
计独立的人工噪声向量
1TNz
 。该噪声向量服从零均值的复高斯
分布，即
() 0,z zQ
 ，其中
TTNN
zQ
 是待优化的人工噪声协
方差矩阵。此时，基站的总发射信号向量
totalx 更新为：  
 
total c c r r= + = + +x s z w W s Ws z  (20) 
其中，
;cr ss= s 是联合符号向量。总发射功率约束相应更新为
() max trH
zP+ WW Q
 
为确保人工噪声不会对合法用户和合法的感知任务造成干扰，
其协方差矩阵
zQ 的设计必须满足以下约束：  
⚫ 对合法通信用户的正交约束 ：要求人工噪声落在合法用户信道
Bh
零空间内，即不产生任何干扰。数学上，该约束表示为：  
 
0H
B z B= h Q h  (21) 
⚫ 对合法感知任务的性能影响约束 ：要求人工噪声对合法感知的干
扰在可接受范围内。这可以通过限制其在合法感知接收机处的干
扰功率，或将其对合法感知 CRLB的影响限制在某一阈 值
th内
来实现：  
 
()() CRLB , , CRLB , ,legit pub z legit pub th  + θ WQ θ W0  (22) 
对于窃听者，人工噪声的设计从以下两个方面对窃听端进行抑
制：  
⚫ 恶化通信窃听信道 ：人工噪声将作为强干扰叠加在通信窃听者
Evec
的接收信号中，其接收信干噪比
SINR
cE 可以表示为 ： 

--- 第 32 页 ---
 
2
22SINRc
c
c c c cH
Ec
EHH
E r E z E E =
++hw
h W h Q h  (23) 
 通过最大化干扰项
ccH
E z Eh Q h  ，可以直接降低其窃听能力。  
⚫ 混淆感知隐私特征 ：同样，人工噪声也会干扰感知窃听者
Evep
的接收信号。这种干扰会直接影响其对隐私参数
privθ 的估计精度。
具体而言，在计算感知窃听者的 费舍尔信息矩阵时，其总的噪声
加干扰协方差矩阵将变为
( )2
p p pH
noise E z E ER  =+h Q h I ，这将直
接导致 FIM的数值减小，从而使其 CRLB增大，即隐私得到增
强。  
通过以上技术步骤，本研究将明确人工噪声在通感系统中的双
重作用机理，并将其效果纳入到统一的数学模型中，为下一阶段的
联合优化设计提供完备的变量和目标函数。  
 
图10 性能权衡 分析  
（3）多目标协同优化与求解  
为实现通信安全与感知隐私的最终协同，本研究点旨在将前述
的联合波束赋形与人工噪声辅助方案进行统一建模，构建一个能够


--- 第 33 页 ---
反映系统综合性能的、可求解的优化问题，并探索其高效算法。  
首先，建立联合安全与隐私的统一优化模型。 本研究将构建一
个以最大化 “通信保密速率 ”和“隐私去标识性度量 ”的加权和为目标
的总优化问题。优化变量为联合预编码矩阵
W 和人工噪声协方差矩
阵
zQ。为便于求解，对数形式的性能指标通常更具优势，因此目标
函数可设计为：  
 
, priv max , log CRLB , , ( ) ( ( ))
zp s s z p E zR   +WQ W Q W Q  (24) 
其中，
s 和
p是预设的权重因子，满足
1sp+= ，用于权衡安
全与隐私的相对重要性。 该优化问题的完整形式如下 : 
 
()
()
()1
2
3
4m,
ax
5m
0log CRLB
  s.t.   :SINR ,
         : CRLB , ,
         : 0
         : tr
        x
 :a ()
zp s s p E
B z c
legit pub z s
H
B z B
H
z
zR
C
C
C
CP
C+


=
+WQ
WQ
θ WQ
h Q h
WW Q
Q
  (25) 
其中， C1-C5 分别代表了合法用户的通信 QoS约束、合法感知任务
的性能约束、人工噪声对合法用户的正交性约束、总发射功率约束
以及协方差矩阵的半正定约束。  
优化问题（ 25）中，由于目标函数中的保密率项
sR 是两个对数
项之差（非凹），且变量
W 和
zQ在多个约束和目标中相互耦合，
因此问题（ 25）是一个典型的非凸优化问题，无法直接使用标准的
凸优化工具求解。为解决此难题，本研究拟采用交替优化的算法框

--- 第 34 页 ---
架。其核心思想是将原问题分解为两个交替进行的子问题：  
- 固定
zQ，优化
W  
- 固定
W，优化
zQ  
由此，通过反复迭代求解这两个相对简单的子问题，直至算法收敛，
从而获得原问题 （25）的一个高质量的次优解。  
针对交替优化中的每一个子问题，进一步探索具体的求解方法：  
- 子问题 1-优化
W：当
zQ固定时，该子问题仍然是非凸的（主要
由于
sR）。拟采用 连续凸近似（ SCA）方法，在每次迭代中，
将非凸的目标函数或约束项，通过一阶泰勒展开等方式，近似为
一个凸函数，从而将原问题转化为一个易于求解的凸优化问题。  
- 子问题 2-优化
zQ：当
W固定时， 该子问题通常可以被转化为一
个标准的半正定规划（ SDP）问题。 SDP是一类凸优化问题，可
以利用成熟的内点法求解器（如 CVX）进行高效、精确的求解。  
 
图11 多目标优化 算法收敛性分析  


--- 第 35 页 ---
综上，通过这一套 “交替优化  + 序列凸近似  + 半正定规划 ”的组
合算法，本研究将为所提出的复杂联合优化问题提供一个行之有效
的解决方案，最终得到能够协同实现通信安全与感知隐私的、可实
际部署的物理层传输策略。  
2.2.3 “通感 -安全 -隐私”多维性能权衡与动态适配机制  
 
图12 研究内容（ 3）技术路线  
（1）定义四维性能可达域与帕累托边界。  
为完成对系统多维性能根本制约关系的揭示，本研究点旨在通
过严格的数学优化方法，描绘出系统性能的理论边界。技术方案的
核心是采用多目标优化理论中的加权和方法，将多维性能的分析转
化为一系列可求解的单目标优化问题，最终勾勒出完整的帕累托最
优边界。  
首先，本研究将形式化地定义一个包含四个关键性能指标的性
能可达域
 。该区域是所有可能性能向量
, , ,B s s pR P R P=η 的集


--- 第 36 页 ---
合，其中各分量分别代表通信速率、感知精度、安全速率和隐私水
平。为便于统一优化（均为最大化目标），感知精度
sP 定义为负的
合法感知 CRLB，即
CRLBs legitP=− ；隐私水平
pP 定义为隐私窃听
者CRLB的对数，即
() log CRLB
p pEP= 。 
该可达域的边界即为帕累托最优边界。此边界上的任何一个性
能点都具有帕累托最优性，即无法在不牺牲至少一个其他性能指标
的前提下，单独提升任意一个性能指标。  
为描绘上述 帕累托边界，本研究将采用加权和方法，构建如下
的统一优化问题 该问题旨在最大化四维性能指标的加权和，优化变
量为联合预编码矩阵
W 和人工噪声协方差矩阵
zQ ： 
 
(),
1
2 max
3max    
s.t.      : 0
          : tr
          : 0zc B s s sec s p p
H
B z B
H
z
zR P R P
C
CP
C   + + +
=
+WQ
h Q h
WW Q
Q
  (26) 
其中
, , ,c s sec p   =ω 是非负的权重向量，且满足
1ii= 该
向量代表 对不同性能维度的偏好程度。约束 C1-C3分别是人工噪声
正交性、总功率和协方差矩阵半正定性的基本物理约束。  
求解不同权重向量
ω 下的优化问题 （27），即可得到帕累托边界
上的一个点。因此，描绘整个边界的技术路线如下：  
a. 在权重向量的可行域内 选取一组具有代表性的权重向量
1,,Nωω
。 
b. 对于每一个权重向量
iω ，采用研究内容二中开发的优化算法（如

--- 第 37 页 ---
交替优化框架）求解对应的优化问题 （27），得到最优的资源分配
策略
(),,i z iWQ 。 
c.  将最优解代入性能指标函数，计算得到该策略下的最优性能向量
,,,B s s pR P R P    =η
。 
d. 所有性能点
 1,,Nηη
 的集合，即可在四维空间中勾勒出帕累托
最优边界的形状。  
通过对该边界的分析，本研究将能够从理论上定量地揭示出 根
本性的性能制约关系，为后续的动态适配机制设计提供完备的理论
依据。  
 
图13 4D-帕累托前沿  
（2）面向业务的动态适配策略  
为连接理论上的帕累托性能边界与实际应用中的多样化需求，
本研究点旨在建立一种能够将上层业务优先级映射为底层物理资源
配置的动态工作模式选择策略。技术方案的核心是引入并建模 “系统


--- 第 38 页 ---
效用函数 ”，并将其作为在性能边界上选择最优工作点的决策依据。  
首先，为对系统的综合性能进行统一评价，本研究将定义一个
系统总效用（ Overall System Utility ）函数
U 。该函数旨在将 “通信 -
感知 -安全 -隐私 ”四个不同量纲的性能指标，通过归一化和加权，转
化为一个单一的标量评价值。其具体形式可建模为：  
 
sec max max max max( , )Ss BP
c s p
B S s PPR RPUR P R P   = + + +ηω  (27) 
其中
η表示上一节分析得到的帕累托边界上的一个性能点 ；每一项
的分母分别是各性能维度的理论最大值（即帕累托边界上各坐标轴
的 最 大 值 ） ， 用 于 性 能 归 一 化 ， 以 消 除 量 纲 影 响 ；
, , ,c s sec p   =ω
是代表业务优先级的 权重向量 ，满足
1i ，
1ii=
。 
接下来建立典型 6G业务场景与权重向量的映射关系。 不同的
业务场景对四维性能的侧重不同，这种差异可以通过设置不同的权
重向量
ω 来体现。本研究将针对典型的 6G业务场景进行权重映射
建模：  
- 重性能的车联网（ V2X）场景：该场景对高精度感知（以避免碰
撞）和高可靠 /低时延通信 要求极高。因此，其权重向量可设置
为
, , ,high high low low
c s sec pV2X   =ω ，例如
  0.4,0.4,0.1,0.1 。 
- 重隐私的智慧医疗 /家居场景 ：该场景涉及用户的健康和行为等
高度敏感信息，对隐私保护的要求最高。其权重向量可设置为

--- 第 39 页 ---
, , ,high mid mid low
c s sec phealth   =ω，例如
  0.2,0.2,0.1,0.5 。 
- 重安全的 通信场景 ：该场景对通信内容的机密性要求至高无上。
其 权 重 向 量 可 设 置 为
, , ,high mid low mid
c s sec pcomm   =ω ，例如
  0.3,0.1,0.4,0.2
。 
通过建立这样的映射关系，即可将上层的、定性的业务需求，
转化为底层的、定量的优化目标。  
基于上述定义，动态工作模式的选择过程可以形式化为一个 最
优化决策问题 。对于一个给定的业务场景 ，系统的最优工作模式对
应于帕累托边界上能够使系统总效用
U 最大化的 性能点。令
 代表
上一节中得到的帕累托最优性能点的集合，则最优性能点
η 的选择
问题可表示为：  
 
() argmax , U
=
ηη ηω
  (28) 
由于帕累托边界
 是一个预先通过离线计算或在线探测得到的已知
集合，上述问题本质上是一个在有限或离散化集合上的搜索问题，
而非复杂的非凸资源分配问题。求解该问题即可得到当前业务场景
下的最优性能目标
η ，并反向映射到实现该性能点的最优资源分配
策略
(),zWQ ，从而完成工作模式的自适应选择。  

--- 第 40 页 ---
 
图14 优化算法的复杂度分析  
（3）低复杂度在线资源分配算法  
为将前述的动态适配策略转化为可实际部署的方案，本研究点
旨在设计一种能够响应环境 和业务实时变化的 低复杂度在线资源分
配算法。技术挑战在于，直接求解研究内容 二中的非凸优化问题计
算复杂度极高，不适用于实时调控。因此，本研究将采用一种 “离线
训练 /构建 -在线匹配 ”的两阶段框架来解决此问题。  
第一步：离线阶段 ——构建参数化资源分配策略库。  
该阶段的目标是预先计算并存储在各种典型系统状态下的最优
资源分配策略，形成一个可供在线快速查询的 “解数据库 ”。 
- 系统状态空间离散化 ：首先，对影响系统性能的关键环境变量进
行离散化。例如，将目标的可能角度范围、合法用户的信道质量
等划分为有限的、具有代表性的状态点。  


--- 第 41 页 ---
- 生成参数化的帕累托边界集合 ：对于每一个离散化的系统状态点，
都重复执行 “研究内容 3”第一步中的帕累托边界分析。这将生成
一个参数化的帕累托边界库，其中每一个边界都对应一个特定的
信道和环境状态。  
- 构建“状态 -策略”映射数据库 ：将上一步得到的帕累托边界库与
第二步中的业务权重向量相结合，为每一个可能的状态 -业务组
合（即  “环境状态  + 业务权重向量
ω ”）计算出对应的最优资源
分配解
(),zWQ 。最终，将这些 “状态 -策略 ”的映射关系存储在
一个高效的数据库或查找表 中。  
第二步：在线阶段 ——实现快速策略匹配与平滑切换。  
该阶段的目标是在系统实际运行时，根据实时变化的环境和需
求，以极低的复杂度完成资源分配的调整。  
- 实时状态获取 ：通感基站通过周期性的信道估计和感知，获取当
前的环境状态信息（如用户信道、目标角度等），同时从核心网
或上层应用接收代表当前业务需求的权重向量
ω 。 
- 低复杂度策略查询 ：基站将获取的实时状态作为索引，在离线构
建的 “状态 -策略 ”数据库中进行快速查询（或通过插值找到最接
近的匹配项），直接检索出预先计算好的最优资源分配矩阵
(),zWQ
。若采用 DNN方案，则将状态向量输入已训练好的
网络，通过一次前向传播快速得到结果。这两种方式的在线计算
开销远低于实时求解非凸优化问题。  
平滑切换机制设计 ：为避免因资源分配策略的突变而导致的系统性

--- 第 42 页 ---
能抖动，将设计一种 策略平滑切换机制 。例如，在从旧策略
() ,old old
z WQ
切换到新策略
() ,new new
z WQ 时，在若干个符号周期内
采用加权组合
() 1old new−+WW 的方式进行过渡，其中过渡因
子
从0平滑增加到 1，从而保障系统在不同性能目标间的高效、
稳定运行。  
通过上述两阶段技术路线，本研究旨在将复杂的理论优化问题，
转化为一个实用、高效的在线算法，最终达成对系统多维性能的实
时、动态适配。  
2.3 可行性分析  
2.3.1 理论可行性分析  
本项目的理论基础建立在多个成熟的数学理论之上 ，其中包括： 
信息论基础 ： 
- 基于 Shannon信息论的保密容量理论为物理层安全提供了坚实的理
论基础  
- 互信息和条件熵理论为隐私度量指标的构建提供了数学工具  
- 信道容量理论为通感一体化系统的性能分析提供了理论框架  
估计理论支撑 ： 
- 克拉美罗下界理论为特征估计误差界 提供了理论依据  
- 费舍尔信息矩阵理论为感知精度量化提供了数学基础  
- 贝叶斯估计理论为身份模糊度建模提供了概率论基础  
优化理论保障 ： 
- 凸优化理论为多目标优化问题的求解提供了理论保证  

--- 第 43 页 ---
- 帕累托最优理论为性能权衡分析提供了数学框架  
- 拉格朗日对偶理论为约束优化问题提供了求解方法  
2.3.2 技术可行性分析  
隐私量化技术 ：身份模糊度 基于成熟的信息熵理论，计算方法
明确；特征估计误差界 基于 CRLB理论，具有理论最优性 ；综合隐
私度量通过加权融合实现，参数可调可控 。 
联合优化技术 ：波束赋形技术在 MIMO系统中已广泛应用，技
术成熟；人工噪声技术在物理层安全中已有成功应用案例 ；交替优
化和 SCA方法在无线通信优化中已被验证有效 。 
动态适配技术 ：帕累托边界分析方法在多目标优化中已有成熟
理论；权重自适应调整可基于梯度下降等经典方法实现 ；在线算法
设计可借鉴机器学习中的在线优化理论 。 
仿真验证方案完备 ：本项目基于 MATLAB/Python 构建完整的
MIMO -ISAC系统仿真平台 ，包含信道建模、信号处理、性能评估等
完整链路 ，支持不同场景和参数配置的灵活仿真验证 。 
算法方面，本项目针对每个关键算法设计专门的仿真验证方案
通过蒙特卡洛仿真验证理论分析的正确性 与现有基准算法进行性能
对比验证优越性 。 
性能评估 方面，本项目建立标准化的性能评估指标体系 ，设计
多种典型应用场景进行综合测试 ，通过统计分析验证算法的稳定性
和鲁棒性 。 
2.3.3 前期基础  

--- 第 44 页 ---
申请人近年来一直从事感知通信融合网络相关技术 研究，尤其
在信息传输安全方向具有较深的成果积累。具体而言，申请 人在通
信感知融合网络信息安全设计、波束赋形等方向 以第一作者 发表期
刊和会议 论文 10余篇，截止目前 引用量近1000次， 以第一作者身
份在 IEEE TWC 发表论文 3 篇，  其中 2019 年发表的《 Secure  radar -
communication systems with malicious targets: Integrating radar,  
communications and jamming functionalities 》文章首次提出了通信感
知融合网络中的通信信息安全保障方案，文章引用量 近300次。除
此之外， 申请人为专著“ Integrated Sensing and Communications ”撰
写章节综述通 信感知融合系统的安全和隐私。本项目的研究内容是
申请人前期研究成 果的进一步拓展和深化，申请人有能力克服研究
过程中的技术困难，保 证研究方案的顺利实施并达到预期目标。  
2.3.4 平台与团队  
申请人依托 哈尔滨工业大学 以及“广东省空天通信与网络技术
重点实验室 ”。哈尔滨工业大学信息 学部深圳研究院承担多项大型
科研项目。实验室具备较为完善的通 信与微波工程试验设备。申请
人所在团队已经在无线通信领域深耕多年， 能够为本项目提供有效
的理论支撑。此外，申请人所在团队与鹏城实验 室宽带通信部建立
了良好的人员设备资源共享机制，本项目可共享鹏城 实验室宽带通
信部的部分实验资源，对项目的实施过程提供补充支撑。  
3、本项目的创新之处。  
本项目瞄准 6G通感一体化技术所衍生的、传统安全框架无法

--- 第 45 页 ---
覆盖的内生性安全与隐私挑战  ，致力于构建一个集 “建模 -优化 -适配 ”
于一体的物理层联合防护新体系。通过将 “身份隐私 ”作为与 “信息安
全”并重的核心目标，并建立多维度性能协同的动态适配机制，力求
在理论和方法上取得源头性创新。本项目的特色与创新之处具体如
下：  
（1）拓展物理层安全之内涵：率先建立 “隐私去标识性 ”的量化
理论与模型。  
当前通感系统的物理层安全研究，仍主要沿用传统通信的思路，
聚焦于保障通信 内容的机密性（如最大化保密速率） 。然而，对于
通感融合带来的、由感知功能所引发的身份信息泄露这一全新威胁，
现有研究多停留在定性描述层面 ，严重缺乏科学的量化手段，形成
了“风险可见，但危害不可度量 ”的研究瓶颈。  
本项目的 首要创新 在于，率先将“隐私去标识性 ”作为物理层安
全的一个独立、可量化的新维度进行系统性研究 。创新性体现在：  
• 理论的源头创新 ：跳出 “内容安全 ”的范畴，首次从信息论和估
计理论的本质出发，为 “身份隐私 ”这一抽象概念建立坚实的数
学模型。  
• 度量的范式建立 ：提出一套全新的、可量化的隐私度量体系
（如身份模糊度、特征估计 CRLB），使物理层隐私保护从一
个模糊的概念，转变为一个可分析、可优化的工程科学问题，
实现了从 “0到1”的理论突破 。 
（2）革新通感系统之优化范式：构建 “通感 -安全 -隐私”四维协同动

--- 第 46 页 ---
态适配机制。  
现有通感系统的性能优化，主要围绕 “通信 -感知 ”的二维性能权
衡展开。近期虽有部分工作引入 “安全 ”维度，但也仅限于静态的三
维性能分析 。这种维度的缺失导致对系统能力的片面理解，且静态
优化无法适应 6G动态多变的业务需求。  
本项目的 核心创新 在于，构建了一个更完备的 “通信 -感知 -安全 -
隐私”四维性能空间，并提出了一套能够在此空间内动态寻优的全新
适配机制  。创新性体现在：  
• 框架的系统性革新 ：从根本上将隐私视为系统的内生性能维
度，建立了一个更完整、更符合 6G应用现实的四维性能权衡
框架，为全面理解系统能力边界提供了理论基础。  
• 方法的智能化升级 ：超越了寻求单一最优解的静态优化模式，
创新性地引入 效用函数 驱动的动态适配策略。该机制能将上层
业务的差异化 “偏好 ”转化为底层的资源分配方案，使系统能够
智能地在性能、安全与隐私之间做出最优权衡， 将“性能折衷 ”
问题转化为 “智能适配 ”问题，为理论走向实际应用铺平了道路。  
4、年度研究计划及预期研究成果。  
4.1 年度研究计划  
本项目研究周期为三年，拟按照“理论建模  → 优化设计  → 性
能适配”的总体技术路线，层层递进地开展研究工作。具体的年度
研究计划如下：  
（1）第一年度（ 2026.01 – 2026.12）：完成物理层隐私建模与量化

--- 第 47 页 ---
表征研究。  
研究任务：重点完成“研究内容 1”。深入调研通感一体化安
全与隐私领域的国内外最新文献。建立包含多天线基站、用户、目
标和窃听者的系统信号模型，并对微多普勒等物理层指纹泄露机理
进行数学建模。推导并建立基于信息熵的“身份模糊度”和基于估
计理论的“特征估计 CRLB”两大隐私度量指标。建立包含通信、
感知、安全、隐私的初步性能基准。  
预期进展：完成理论建模，通过初步仿真验证模型的有效性。
撰写学术论文 1-2篇，参加本领域国际学术会议 1次，分享初步研
究思路。  
（2）第二年度（ 2027.01 – 2027.12）：完成联合安全与隐私优化方
法研究。  
研究任务：重点完成“研究内容 2”。基于第一年建立的量化
模型，设计联合波束赋形与隐私增强人工噪声（ PE-AN）协同的物
理层传输方案。构建以协同增强通信安全和感知隐私为目标的多目
标优化问题，并研究其高效求解算法（如交替优化、序列凸近似
等）。  
预期进展：完成核心优化算法的设计与实现。通过大量蒙特卡
洛仿真，验证所提方案相比于基准方案在安全与隐私性能上的显著
优势。撰写高水平期刊论文 1-2篇，申请发明专利 1-2项。  
（3）第三年度（ 2028.01 – 2028.12）：完成多维性能权衡与动态适
配机制研究。  

--- 第 48 页 ---
研究任务：重点完成“研究内容 3”。利用加权和等方法，描
绘出系统的“通信 -感知 -安全 -隐私”四维性能帕累托边界，揭示其
根本性权衡关系。研究面向不同业务场景的效用函数，并设计低复
杂度的在线资源分配与切换算法。进行系统级仿真，验证动态适配
机制的有效性和高效性。  
预期进展：完成动态适配机制的完整设计与验证。整理全部研
究成果，撰写项目结题报告。再发表学术论文 1-2篇，完成所有专
利申请的提交工作。  
4.2 预期研究成果  
本项目预期在理论、技术和人才培养等方面取得一系列研究成
果：  
• 理论与技术成果： 一套物理层隐私量化理论与模型：建立包含
“身份模糊度”和“特征估计 CRLB”等指标的、能够科学度
量通感系统中身份隐私泄露风险的理论框架。  
• 一种联合安全与隐私的物理层优化方案：提出基于波束赋形和
人工噪声协同的创新传输方法，能够在保障通信和感知服务质
量的同时，协同增强信息保密性与身份隐私性。  
• 一种多维性能动态适配机制：揭示“通信 -感知 -安全 -隐私”四
维性能的根本权衡关系，并设计出一套能够根据业务需求智能
选择最优工作模式的低复杂度在线适配算法。  
学术成果：  
• 计划发表高水平 SCI期刊论文 3-4篇，其中至少 1-2篇发表于

--- 第 49 页 ---
本 领 域 顶 级 期 刊 （ 如  IEEE Transactions on Wireless 
Communications, IEEE Transactions on Information Forensics and 
Security 等）。  
• 计划申请国家发明专利 2-3项，对核心技术方案进行知识产权
保护。  
• 计划参加本领域国际旗舰学术会议（如  IEEE ICC, Globecom 
等） 2-3次，并宣读论文，与国内外同行进行深入交流。  
人才培养：  
在项目执行期间，培养或协助培养硕士 /博士研究生 1-2名，使
其系统性地掌握通感一体化安全与隐私领域的前沿理论与关键研究
方法，成长为本领域的优秀后备人才。  
三、研究条件与基础  
1、已取得的研究工作成绩及与本项目有关的研究工作积累 （请
着重填写申请人近期的主要工作业绩以及与本项目相关的研究工作
积累）。  
申请人长期从事通信感知系统的波束赋形设计、物理层安全研
究，并取得了 一定的创新性成果，在通信和感知系统设计方面累积
了丰富的研究基础和研究 经验。具体而言，申请人已经在本领域发
表期刊和会议论文 近20篇，截止目前引用量近1000次，获得国家
级人才引进项目，主持国自然青年科学基金项目（ C类），并获得博
士后科学基金 面上资助。此外，申请人参加 IEEE GLOBECOM 、
ICC 等国际通信旗舰会议，受邀参加 SPAWC、Asilomar、EUSIPCO 

--- 第 50 页 ---
等国际会议，开展学术 交流并作口头报告。担任 《通信学报 》青年
编委，同时担任 IEEE Trans. Wireless Commun., IEEE Trans. Commun.,
和IEEE Trans. Signal Processing 等十余个期刊 /期间会议审稿人，与
国内外学者保持 着良好的学术交流和合作。 下面将从三个方面介绍
研究基础。  
➢ 物理层信道与安全建模方面的研究积累（支撑研究内容 1）  
申请人在无线信道建模、信号特征分析以及信息论安全性能分
析方面具有扎实的研究基础。前期的研究工作深入探讨了复杂衰
落信道下的信号传播特性，并运用信息论工具对物理层安全性能
进行了理论界定。这些建模与分析经验，为本项目开展全新的
“物理层隐私泄露机理建模与量化表征 ”研究提供了直接的理论和
方法支撑。 其中代表 性工作有：  
[1] Nanchi Su , Fan Liu, and Christos Masouros. "Secure radar -communication  
systems with malicious targets: Integrating radar, communications and jamming  
functionalities." IEEE Transactions on Wireless Communications 20.1 (2020):  83-
95. 
[2] Nanchi Su , Fan Liu, and Christos Masouros. "Enhancing the physical layer  
security of dual -functional radar communication systems." 2019 IEEE Global  
Communications Conference (GLOBECOM). IEEE, 2019.  
[3] Zhongxiang Wei, Fan Liu, Christos Masouros, Nanchi Su , Athina Petropulu.  
"Toward multi -functional 6G wireless networks: Integrating sensing,  
communication, and security." IEEE Communications Magazine 60.4 (2022):  65-
71. 
其中代表性工作 [1]首次提出通信感知融合系统中通信信息安全
设计方案， 截至 2025 年6 月，文章引用量 近300次。  
➢ 安全波束赋形与资源优化算法方面的研究积累（支撑研究内
容2） 
申请人熟练掌握无线通信系统中资源分配问题的建模与求解，

--- 第 51 页 ---
在安全波束赋形、人工噪声设计以及非凸优化问题求解等方面积
累了丰富的算法设计经验。前期的工作已成功将序列凸近似
（SCA） 、半正定松弛（ SDR）等先进优化方法应用于多用户系
统的安全资源分配问题中。这些经验使申请人完全有能力解决本
项目中 “联合安全与隐私优化 ”这一核心技术挑战。 其中代表 性工
作有：  
[4] Nanchi Su , Fan Liu, and Christos Masouros. "Sensing -assisted eavesdropper  
estimation: An ISAC breakthrough in physical layer security." IEEE Transactions  
on Wireless Communications (2023).  
[5] Nanchi Su , Fan Liu, and Christos Masouros. "Sensing -assisted physical layer  
security." WSA & SCC 2023; 26th International ITG Workshop on Smart  
Antennas and 13th Conference on Systems, Communications, and Coding. VDE,  
2023.  
[6] Nanchi Su , Fan Liu, and Christos Masouros. "Secure Integrated Sensing and  
Communication Systems with the Assistance of Sensing Functionality." 2023  31st 
European Signal Processing Conference (EUSIPCO). IEEE, 2023  
[7] Nanchi Su , Fan Liu, Christos Masouros, George C. Alexandropoulos, Yifeng 
Xiong, and Qinyu Zhang. “Secure ISAC MIMO systems: exploiting interference 
with Bayesian Cramé r –Rao bound optimization.” EURASIP Journal on Wireless 
Communications and Networking  (2025).  
➢ 通感一体化性能权衡与系统设计方面的研究积累（支撑研究
内容 3）  
申请人紧跟 6G技术发展前沿，已在通感一体化这一新兴领域
开展了探索性研究，对通信与感知功能的内在性能制约关系有深
刻理解。前期的工作已涉及通感一体化波形设计和 “通信 -感知 ”二
维性能权衡分析。这些前沿探索为本项目将研究框架从二维拓展
至“通信 -感知 -安全 -隐私 ”四维，并设计动态适配机制，提供了宝
贵的、承上启下的研究经验和前期洞见。 其中代表 性工作有 ： 
[8] Nanchi Su , Fan Liu, Christos Masouros, Ahmed Al Hilli. "Security and  
Privacy in ISAC Systems." Integrated Sensing and Communications. Singapore:  

--- 第 52 页 ---
Springer Nature Singapore, 2023. 477 -506. 
[9] Nanchi Su , Fan Liu, Zhongxiang Wei, Ya -Feng Liu, Christos Masouros.  
"Secure dual -functional radar -communication transmission: Exploiting  
interference for resilience against target eavesdropping." IEEE Transactions on  
Wireless Communications 21.9 (2022): 7238 -7252.  
[10] Nanchi Su , Zhongxiang Wei, Christos Masouros. "Secure dual -functional  
radar -communication system via exploiting known interference in the presence of  
clutter." 2021 IEEE 22nd International Workshop on Signal Processing Advances  
in Wireless Communications (SPAWC). IEEE, 2021.  
[11] Nanchi Su , Fan Liu, Christos Masouros, Tharmalingam Ratnarajah, Athina  
Petropulu. "Secure Dual -functional Radar -Communication Transmission:  
ardware -Efficient Design." 2021 55th Asilomar Conference on Signals, Systems,  
and Computers. IEEE, 2021.  
综上所述，申请人已有的研究积累与本项目拟开展的研究内容
高度契合、一脉相承，前期工作中掌握的理论分析工具和算法设计
技巧，将为本项目的顺利实施和高质量完成提供充分的保障。  
2、已具备的实验条件 ，尚缺少的实验条件和拟解决的途径 （包
括利用国家重点实验室和部门开放实验室的计划与落实情况 ）。 
本项目依托单位为哈尔滨工业大学。申请人所在深圳校区通信
工程研究中 心长期从事无线通信、空间通信、网络优化、人工智能、
信号与信息处理技术、 芯片设计与安全等研究。申请人所在团队为
广东省空天通信与网络技术重点实 验室，广东省空天通信与网络技
术重点实验室面向国家网络强国与航天强国的 重大战略需求，及大
湾区推动卫星应用装备和空天信息技术发展的战略需求， 聚焦深空
探测通信、大规模卫星网络及无人自主通信系统等电子信息与航空
航天领域的重大科技问题。该实验室面向空间信息网络领域的基础
设施较为完善， 现有专用实验室面积 1000 平方米，拥有各类通用、
专用测试测试仪器超过 100台（套） 。其中包括中星 10 号Ku 频段
收发天线 2 套，实验用无人机 5 套， USRP软件无线电平台（ USRP -

--- 第 53 页 ---
2930、USRP -2974）35 套， LabVIEW 软件套装 1 套， R&S 的信号
分析系统（ FSQ26） ，泰克的高性能示波器（ DPO070804 ） 、频谱分  
析仪（ RSA3308A ） 、逻辑分析仪（ TLA5204B ） ，以及安捷伦的信号
发生器（E8267D）等高端设备，总价值超过 3000 万元， 2022 年底
计划建成天基物联 网分布式数据中心（具有≥ 3000T的存储能力，
≥5000 颗计算核心，可支持≥ 300 物理节点的 10Gbps 高速数据交换
机组） ，具备良好的科研平台基础保障。  
重点实验室主任为张钦宇教授，团队成员在职共计 36人，近三
年科研团队 产出了面向深空探测 -测控 -通信任务的高可靠信息传输
技术、面向空 -天-海广域覆盖网络的高时效组网与传输技术、基于
集群运动的无人机可重构编队通信与 网络技术等重要研究成果，解
决了深空超远距离可靠通信、卫星广域覆盖网络 实时互联、无自主
系统高效组网传输等技术难题。基于以上研究成果，获得国 家发明
专利授权 20余项，国家行业标准 1项，发表高水平学术论文 60余
篇，获得 3项领域内优秀论文奖励，实现 5项关键技术的成果转化。
科研团队承担省部级 以上纵向科研项目 25项，包含国家自然科学基
金重点项目、重大仪器研制项目、 国家重点研发计划等国家重大科
研项目，在研经费超过 8000万。实验室具有高 水平科技人才 8人，
包含国家杰青及国家海内外高层次人才计划入选者。实验室 广泛开
展科研合作，设立开放基金 10项，服务 10家企业单位，获得 3项
技术应用 证明。  
此外，申请人与伦敦大学学院的 Christos Masouros 教授、英国

--- 第 54 页 ---
爱丁堡大学的 Tharm Ratnarajah 教授、美国罗格斯大学的 Athina 
Petropulu 教授、雅典国立和卡 波迪斯特里安大学 George C. 
Alexandropoulos 教授和南方科技大学刘凡教授等知 名专家学者建立
了良好的合作关系，共同发表了多篇学术论文，为本项目开展 国内
外学术交流与合作提供了良好的条件。  
综上，本项目团队拥有良好的实验条件和科学仪器、强大的科
研创新载体 和丰富的科研项目经验，可为本项目的科研活动开展、
总体思路设计、技术方 案实施和项目成果验证提供充分的平台支撑。  

================================================================================


================================================================================
文件: 贝叶斯通感安全.pdf
页数: 14
================================================================================


--- 第 1 页 ---
Open Access
© The Author(s) 2025. Open Access This article is licensed under a Creative Commons Attribution 4.0 International License, which permits 
use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original 
author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third 
party material in this article are included in the article’s Creative Commons licence, unless indicated otherwise in a credit line to the material. 
If material is not included in the article’s Creative Commons licence and your intended use is not permitted by statutory regulation or 
exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit http://
creativecommons.org/licenses/by/4.0/.RESEARCHSu et al. J Wireless Com Network         (2025) 2025:10  
https://doi.org/10.1186/s13638-025-02428-1EURASIP Journal on Wireless
Communications and Networking
Secure ISAC MIMO systems: exploiting 
interference with Bayesian Cramér–Rao bound 
optimization
Nanchi Su1,2,3*  , Fan Liu2, Christos Masouros3, George C. Alexandropoulos4, Yifeng Xiong5 and Qinyu Zhang1,6 
Abstract 
In this paper, we present a signaling design for secure integrated sensing 
and communication (ISAC) systems comprising a dual-functional multi-input 
multi-output base station that simultaneously communicates with multiple users 
while detecting targets present in their vicinity, which are regarded as potential 
eavesdroppers. In particular, assuming that the distribution of each parameter to be 
estimated is known a priori, we focus on optimizing the targets’ sensing performance. 
To this end, we derive and minimize the Bayesian Cramér–Rao bound, while ensuring 
certain communication quality of service by exploiting constructive interference. 
The latter scheme enforces that the received signals at the eavesdropping targets fall 
into the destructive region of the signal constellation, to deteriorate their decoding 
probability, thus enhancing the ISAC’s system physical layer security capability. To 
tackle the nonconvexity of the formulated problem, a tailored successive convex 
approximation method is proposed for its efficient solution. Our extensive numerical 
results verify the effectiveness of the proposed secure ISAC design showing 
that the proposed algorithm outperforms block-level precoding techniques.
Keywords: Integrated sensing and communication, Physical layer security, Successive 
convex approximation, Bayesian Cramér–Rao bound, Constructive interference
1 Introduction
Future radar and communication (R&C) systems will operate at higher frequencies 
with larger bandwidth, while possibly exploiting massive antenna arrays and multi-
functional reconfigurable intelligent surfaces (RIS), resulting in striking similarities 
between R&C systems, including the hardware architecture, channel characteristics, 
and signal processing methods [1 , 2]. This provides unique opportunities to develop 
co-design techniques aiming at improving the mutual performance gain of both 
systems. Meanwhile, with the emergence of smart cities, Internet of Things (IoT) 
networks, and other advanced applications, the integration of sensing and communi -
cation (S&C) systems is being seen as a transformative technology, enabling autono -
mous vehicle networks, activity recognition, and unmanned aerial vehicle (UAV) [3 ]. *Correspondence:   
<EMAIL>
1 Guangdong Provincial Key 
Laboratory of Aerospace 
Communication and Networking 
Technology, Harbin Institute 
of Technology (Shenzhen), 
Shenzhen 518055, China
2 School of System Design 
and Intelligent Manufacturing, 
Southern University 
of Science and Technology, 
Shenzhen 518055, China
3 Department of Electronic 
and Electrical Engineering, 
University College London, 
London WC1E 7JE, UK
4 Department of Informatics 
and Telecommunications, 
National and Kapodistrian 
University of Athens, 
15784 Athens, Greece
5 School of Information 
and Electronic Engineering, 
Beijing University of Posts 
and Telecommunications, 
Beijing 100876, China
6 Peng Cheng Laboratory, 
Shenzhen 518055, China

--- 第 2 页 ---
Page 2 of 14 Su et al. J Wireless Com Network         (2025) 2025:10 
In light of the above, the need for seamless cooperation between S&C promotes the 
technical development of integrated sensing and communication (ISAC) systems.
The utilization of a communal spectrum frequency band, coupled with the intrin -
sic broadcasting characteristics of wireless transmission, introduces substantial secu -
rity vulnerabilities in ISAC systems [4 –6]. In conventional wireless communication 
systems, security designs are predominantly concerned at the physical layer and the 
network layer. Compared with network layer security (NLS), physical layer security 
(PLS) does not require complex cryptographic techniques or key distribution, reduc -
ing overhead and complexity. Moreover, PLS may provide a base level of security 
guarantee even when other layers are compromised, because it leverages the physical 
characteristics of wireless channels, which are independent of security at other layers 
of the communication stack.
The PLS in ISAC systems has been widely studied in recent years. Initially, the artifi -
cial noise (AN) is deployed to interfere with eavesdroppers by maximizing the secrecy 
rate; thus, the target/eavesdropper is unable to decode the received signal. To this 
end, the confidential information is prevented from being intercepted by the target/
eavesdropper [5 , 7–9]. Besides, the authors in [10] expand the AN-aided technique to 
full-duplex ISAC security, where the AN is utilized to enhance both downlink (DL) 
and uplink (UL) secrecy rates in the presence of multiple eavesdroppers. The work 
presents a power-efficient optimization model that maximizes UL/DL secrecy while 
targeting radar beams at eavesdroppers to extract their physical parameters, reveal -
ing key trade-offs between sensing performance and communication security. Moreo -
ver, the directional modulation (DM) technique, which is based on the principle of 
constructive interference (CI), has been deployed to design the transmit signal at a 
symbol level [11– 13]. In DM, as a step further from AN design, the signals received at 
multiple eavesdropping targets (Eves) are enforced to fall into the destructive constel -
lation region for further PLS improvements, which leverages destructive interference 
(DI) as a PLS measure. In particular, the CI-DI technique enables direct alteration of 
the amplitude and phase of signals at both intended users and potential Eves. Con -
sequently, this paradigm promotes an enhanced symbol error rate (SER) for com -
munication users (CUs), while deteriorating the decoding probability at potential 
eavesdroppers.
In this work, we consider the estimation task of random parameters of multiple tar -
gets, where the prior distribution of parameters is assumed to be known a priori. This 
is common in a number of practical scenarios, such as vehicle tracking and environ -
mental monitoring. Toward that aim, we then evaluate the sensing performance uti -
lizing the lower bound of the unbiased estimation, i.e., Bayesian Cramér–Rao bound 
(BCRB). Specifically, we formulate a novel signaling design problem that aims to 
minimize the BCRB, while guaranteeing a predefined quality of service (QoS) at the 
multiple CUs, by deploying the CI technique and improving the PLS by constrain -
ing the received signals at targets/Eves in the destructive constellation region. Moreo -
ver, we explore the impact of the a priori distribution of the parameters on the radar 

--- 第 3 页 ---
Page 3 of 14
 Su et al. J Wireless Com Network         (2025) 2025:10 
 
beampattern as well as the performance trade-off between the sensing and commu -
nication operations. For further clarity, the insights of this work are summarized as 
follows:
• This work not only derives the BCRB for the estimation of random target parameters 
within an ISAC system, but also proposes an optimization strategy specifically tai -
lored to minimize this bound while meeting stringent QoS requirements for multiple 
users. This focus on minimizing BCRB for ISAC applications is, to our knowledge, 
a novel contribution that directly addresses the sensing performance, particularly 
under security constraints.
• Although the concept of using CI-DI for security is not new, our approach adapts 
this for ISAC by introducing a three-zone division within the destructive region, 
providing a structured solution to manage the nonconvexity of the constraints. This 
adaptation is particularly tailored to the needs of ISAC systems, where communica -
tion and sensing objectives are tightly integrated.
• Unlike previous studies [14] that focus on single-target detection, our work extends 
the application to a multi-target scenario. By considering multiple targets as poten -
tial eavesdroppers, we develop an optimization strategy that ensures robust commu -
nication security while enhancing the overall sensing capabilities of ISAC systems.
Notations: Unless otherwise specified, matrices are denoted by bold uppercase letters 
(i.e., X ), vectors are represented by bold lowercase letters (i.e., x ), and scalars are denoted 
by normal font (i.e., α ). Subscripts indicate the location of the entry in the matrices or 
vectors (i.e., si,j and ln are the (i,  j)-th and the n-th element in S and l , respectively). ⊗ 
denotes the Kronecker product. tr(·) and vec (·) denote the trace and the vectorization 
operations. (·)T , (·)H, and (·)∗ stand for transpose, Hermitian transpose, and the com -
plex conjugate of the matrices, respectively. �·� , �·�∞ and �·�F denote the l2 norm, infinite 
norm, and the Frobenius norm, respectively. E{·} denotes the statistical expectation.
2  Methods and results
2.1  Signal model
As shown in Fig.  1, we consider a downlink multi-user multi-input single-output 
(MU-MISO) wireless system, where the dual-functional multi-input multi-output 
(MIMO) base station (BS) is capable of detecting multi-targets simultaneously with 
Fig. 1 The considered system model comprising multiple communication users (CUs) and multiple targets 
in the vicinity of an ISAC access point (AP)

--- 第 4 页 ---
Page 4 of 14 Su et al. J Wireless Com Network         (2025) 2025:10 
data transmission. The targets are treated as potential Eves of the communication 
information. The BS is equipped with Nt transmit antennas and Nr receive antennas, 
enabling communication with Kcu single-antenna users and detection of Ktar targets of 
interest1. Below we elaborate on the signal models of both radar and communication 
systems, respectively.
Let X∈CNt×L denote the dual-functional signal matrix, where 
X=[x[1],x[2],...,x[L]] , each element of which denotes the transmit signal vector 
at the l-th time slot with l=1, 2, ...,L . Then, the received signal at each k-th single-
antenna CU, with k=1, 2, ...,Kcu , at the l-th time slot is given as
where hH
CU,k∈CNt×1 denotes the MISO channel vector between the BS and the k-th CU, 
and the complex-valued zCU,k[l] denotes the zero-mean additive white Gaussian noise 
(AWGN) with the variance of each entry being σ2
CU,k . According to the paradigm of the 
CI technique [14, 17], the SNR per frame of the k-th CU is given as
On the other hand, the sensing signal model can be mathematically expressed as follows:
where YS∈CNr×L , ZS represents the AWGN with zeros-mean complex-value elements 
each with the variance of σ2
S , and HS∈CNr×N t denotes the target response matrix, 
which is a function of the physical parameters η∈RM to be estimated, including range, 
angle, and Doppler, with M denoting the number of parameters to be estimated. In this 
paper, we consider a particular case of channel matrix HS , which is expressed as
where αn denotes the channel coefficient of each target, consisting of both the radar 
cross section (RCS) and path loss, which obeys the complex Gaussian distribution, and 
a(θ) , b(θ) represent the transmit and receive steering vector, respectively. The received 
signal at the n-th target/Eve is accordingly written as(1) yCU,k[l]=hH
CU,kx[l]+zCU,k[l],
(2)SNR CU,k=E/bracketleftbigg/vextendsingle/vextendsingle/vextendsinglehH
CU,kx[l]/vextendsingle/vextendsingle/vextendsingle2/bracketrightbigg
σ2
CU,k.
(3) YS=HS(η)X+ZS,
(4) HS=Ktar/summationdisplay
n=1αnb(θn)aH(θn),
(5) yE,n=βnaH(θn)X+en,
1 From the sensing side, we assume that one sub-array (consisting of Nt antennas) is deployed to transmit signals and 
another sub-array (comprising Nr antennas) is deployed to receive signals. These sub-arrays are co-located at the BS and 
operated simultaneously to transmit the dual-function signal and receive its echoes for monostatic sensing. In principle, 
the transmitted signal will interfere with the reflected echoes (arriving with a round-trip propagation delay) at the 
receive sub-array, creating a self-interference signal at the BS. This is a typical problem in full-duplex (FD) BSs used for 
simultaneous communications and sensing [15]. Fortunately, there exist various approaches for efficiently suppressing 
self-interference below the noise floor in multi-antenna FD systems, ranging from isolation between the transmit and 
receive arrays to joint digital and analog beamforming and interference cancellation techniques. Capitalizing on this, in 
the paper, we neglect the impact of the self-interference assuming that it can be efficiently handled via the state-of-the-
art approaches [15, 16].

--- 第 5 页 ---
Page 5 of 14
 Su et al. J Wireless Com Network         (2025) 2025:10 
 
where βn,∀n denotes the path loss of the n-th target/Eve, and en denotes the zero-mean 
AWGN vector, with the variance of each entry being σ2
E,n.
Given the channel model (4), we define the vector with the unknown targets’ parame -
ters η=[Re{α}, Im{α},θ]∈CN×3 , with α=[α1,... ,αN]T,θ=[θ1,... ,θN]T . The steer -
ing vector and its derivative are specified as (assuming an even number of antennas):
where an , with n=1,...,Nt denotes the n-th element of the steering vector a(θ) . Here, 
we choose the center of the ULA as a phase reference, such that
Accordingly, the covariance matrix of the dual-functional transmitted signal is given as
For the sensing performance metric, we employ the estimation mean-squared error 
(MSE) of η , which is bounded by the CRB. By denoting the estimation of η as ˆη , we have 
that:
where J is the Bayesian Fisher Information Matrix (BFIM) of η which is defined as 
follows:
where pη(η) denotes the prior distribution of the parameters’ vector η , and pYS|η(YS|η) 
is the probability of observing the data YS given the parameter η . To derive the BFIM, we 
firstly let yS= vec/parenleftbig
YT
S/parenrightbig
 , and thus, the sensing signal model can be rewritten as
Then, let hS=/bracketleftBig
vec/parenleftbig
HT
S/parenrightbigT, vec/parenleftbig
HT
S/parenrightbigH/bracketrightBig
 and F=∂h∗
S
∂η∈CK×2N tNr . We further partition F 
as
where Fi∈CK×N t , with i=1,...,2Nr . Accordingly, the BFIM can be rewritten as [18](6)a(θ)=/bracketleftBig
e−jπNt−1
2sin(θ),e−jπNt−3
2sin(θ),...,ejπNt−1
2sin(θ)/bracketrightBigT
,
Pa(θ)=/bracketleftbigg
−jπNt−1
2cos(θ)a1,...,jπNt−1
2cos(θ)aNt/bracketrightbiggT
,
(7) aHPa=0,bHPb=0.
(8) Rx=1
LXXH=1
LL/summationdisplay
l=1x[l]xH[l].
(9) MSE η/parenleftbig
ˆη/parenrightbig
≥tr/parenleftBig
J−1/parenrightBig
,
(10)J=Eη/braceleftbigg∂lnpYS|η(YS|η)
∂η∂lnpYS|η(YS|η)
∂ηT/bracerightbigg
+Eη/braceleftbigg∂lnpη(η)
∂η∂lnpη(η)
∂ηT/bracerightbigg
,
(11) yS=/parenleftBig
INr⊗XT/parenrightBig
vec/parenleftBig
HT
S/parenrightBig
+vec/parenleftBig
ZT
S/parenrightBig
.
(12) F=/bracketleftbigF1,... ,F2Nr/bracketrightbig
,

--- 第 6 页 ---
Page 6 of 14 Su et al. J Wireless Com Network         (2025) 2025:10 
where JP depends on the a priori distribution pη(η).
To deal with the expectation operation in (14), we define the following matrices: 
 To derive the expectation of the later matrices, we start with (14a) and define the auxil -
iary matrices: 
 where the latter’s eigenvalue decomposition is defined as:
where ui denotes the corresponding eigenvector of /afii9838i , with i=1.... ,r1 . We assume that 
/afii98381≥/afii98382,...,/afii9838MNt and let r1 denote the number of nonzero elements in /Lambda11 . It can be eas -
ily shown that
where ˜Fi=√/afii9838imat (ui).
Likewise, we have E{A2(/Xi1)}=r2/summationtext
i=1˜Gi/Xi1˜GH
i , where ˜Gi=/radicalbig¯/afii9838imat (¯ui) , as derived from 
(14b). To this end, the BFIM is consequently reformulated as follows:
Therefore, the BCRB with respect to η is accordingly given as(13)J=L
σ2s/braceleftbigg
Eη/braceleftbigg
F/bracketleftbigg
INr⊗RT
x 0
0I Nr⊗Rx/bracketrightbigg
FH/bracerightbigg
+JP/bracerightbigg
=L
σ2s/braceleftBigg
Eη/braceleftBiggNr/summationdisplay
i=1/parenleftBig
FiRT
xFH
i+FNr+iRxFH
Nr+i/parenrightBig/bracerightBigg
+JP/bracerightBigg
(14a) A1(/Xi1)=Nr/summationdisplay
i=1Fi/Xi1FH
i,
(14b) A2(/Xi1)=Nr/summationdisplay
i=1FNr+i/Xi1FH
Nr+i.
(15a) B1=Nr/summationdisplay
i=1vec(Fi)vec(Fi)H,
(15b)NB1=E{B1}=Nr/summationdisplay
i=1E/braceleftbig
vec(Fi)vec(Fi)H/bracerightbig
,
(16)NB1=U1/Lambda11UH
1=r1/summationdisplay
i=1/parenleftBig/radicalbig
/afii9838iui/parenrightBig/parenleftBig/radicalbig
/afii9838iui/parenrightBigH
,
(17) E{A1(/Xi1)}=r1/summationdisplay
i=1QFi/Xi1QFH
i,
(18) J=L
σ2s
r1�
i=1˜FiRT
x˜FH
i+r2�
j=1˜GjRx˜GH
j
+JP.

--- 第 7 页 ---
Page 7 of 14
 Su et al. J Wireless Com Network         (2025) 2025:10 
 
2.2  Problem formulation
Given the simplified expression of the BFIM, we are now ready to formulate the opti -
mization problem to minimize the BCRB, while conveying the received signals at CUs 
into the constructive region and constraining the transmit power by designing the signal 
matrix X . Moreover, the received signals at targets/Eves are limited in the destructive 
region for the communication data security concern. Inspired by the CI-DI technique 
proposed in [14, 17], the BCRB minimization problem is formulation as follows 
where ˜hH
CU,k=hH
CU,ks∗
k , and ˜aH(θn)=aH(θn)s∗
1 by taking the symbol s1 as a reference. 
PT denotes the transmit power budget, ŴCU,k,∀k is the given SNR thresholds for CUs, 
and τE,n is the given scalar for limiting the targets’ received symbols in the DI region. 
Note that τE,n is generally set much smaller than the CUs’ SNR threshold ŴCU,k,∀k . We 
assume that the intended signals are M-Phase-shift keying (PSK) modulated, and thus, 
φ=±π/M . The constraint (20c ) limits the signals received by CUs within the construc -
tive region, while (20d) limits the received signals being distributed out of the construc -
tive region. This makes correct detection more challenging for the targets by designing 
the received signals’ constellation, meanwhile reducing the eavesdropping SINR [14, 19, 
20].2
Note that the nonconvexity of problem (20) lies in the objective function and the con -
straint (20d). Following the method presented in [14], we divide the destructive region 
into three zones; that is, the inequality (20d) holds when any one of the following con -
straints is fulfilled.(19) BCRB/definestr/parenleftBig
J−1/parenrightBig
.
(20a) min
Xtr/parenleftBig
J−1/parenrightBig
(20b) s.t.1
L�X�2
F≤PT,
(20c)/vextendsingle/vextendsingle/vextendsingleIm/parenleftBig
QhH
CU,kX/parenrightBig/vextendsingle/vextendsingle/vextendsingle≤/parenleftbigg
Re/parenleftBig
QhH
CU,kX/parenrightBig
−/radicalBig
σ2
CU,k/Gamma1CU,k/parenrightbigg
tanφ,∀k,
(20d)/vextendsingle/vextendsingle/vextendsingleIm/parenleftBig
βnQaH(θn)X/parenrightBig/vextendsingle/vextendsingle/vextendsingle≥/parenleftBig
Re/parenleftBig
βnQaH(θn)X/parenrightBig
−τE,n/parenrightBig
tanφ,∀n,
2 The eavesdropping SINR at the n-th target/Eve regarding the k-th CU is expressed as SINRE
n,k=E/bracketleftBig/vextendsingle/vextendsingleβnaH(θn)x[l]/vextendsingle/vextendsingle2/bracketrightBig
E/bracketleftBig
|βnaH(θn)x[l]−sk,l|2/bracketrightBig
+σ2
E,n , 
where sk,l denotes the desired constellation symbol for the k-th CU at the l-th time slot. It is easy to note that the SINRE
n,k 
is constrained once the inequality (20d) is satisfied.

--- 第 8 页 ---
Page 8 of 14 Su et al. J Wireless Com Network         (2025) 2025:10 
case 1:
case 2:
and Re/parenleftBig
βnQaH(θn)X/parenrightBig
>τ E,n,
case 3:
and Re/parenleftBig
βnQaH(θn)X/parenrightBig
>τ E,n.
Till now, (20d) is rewritten as three linear constraints; that is, problem (20) is converted 
to three subproblems. We solve each subproblem, and the one that results in the minimum 
value of the BCRB is the final solution to problem (20). However, the objective function is 
still nonconvex. In the following section, we present an efficient solver following the succes -
sive convex approximation (SCA) approach.
2.3  Proposed secure ISAC signaling design
Algorithm 1 SCA Algorithm for Solving (20)
We note that the constraints in problem (20) are all convex, while the objective function is 
nonconvex. To this end, we define Q as the feasible region of problem (20), which is convex. 
To tackle the problem, let us denote the objective function as f(X)/definestr/parenleftbig
J−1/parenrightbig
 . Then, we 
approximate the objective function by its first-order Taylor expansion near a given point 
f/parenleftbig
X′/parenrightbig
 , yielding
where ∇f(·) denotes the gradient of f(·) . Note that the first term in (21) is a constant, 
hence, we can equivalently solve the following optimization problem at the n-th itera -
tion of the SCA solver:Re/parenleftBig
βnQaH(θn)X/parenrightBig
≤τE,n,
Im/parenleftBig
βnQaH(θn)X/parenrightBig
≥/parenleftBig
Re/parenleftBig
βnQaH(θn)X/parenrightBig
−τE,n/parenrightBig
tanφ
−Im/parenleftBig
βnQaH(θn)X/parenrightBig
≥/parenleftBig
Re/parenleftBig
βnQaH(θn)X/parenrightBig
−τE,n/parenrightBig
tanφ
(21) f(X)≈f/parenleftbig
X′/parenrightbig
+Re/parenleftbig
tr/parenleftbig
∇fH/parenleftbig
X′/parenrightbig/parenleftbig
X−X′/parenrightbig/parenrightbig/parenrightbig
,

--- 第 9 页 ---
Page 9 of 14
 Su et al. J Wireless Com Network         (2025) 2025:10 
 
where Xn−1∈Q is the optimal signal at the (n−1)-th algorithmic iteration. By solving 
problem (22), we obtain the optimal solution, which is denoted as X∗∈Q . Here, the 
term X∗−X(n−1 ) yields a descent direction for each iteration. By letting the variable 
move along the descent direction with a stepsize /afii9838 , we have
where the stepsize /afii9838 may be obtained by adopting the Armijo search or the exact line 
search [21], and Xi∈Q . It is notable that the performance of the SCA technique is inev -
itably impacted by the initial point X0 . In this problem, the initial point can be found by 
solving the following optimization problem
which will provide a solution close to the minimizer of the original problem (20) since 
it falls into the same feasibility region. For clarity, the SCA method applied to solving 
problem (20) is summarized in Algorithm 1.
2.4  Numerical results and discussion
In this section, numerical results are presented based on Monte Carlo simulations of 
the proposed optimization technique, i.e., CI-based BCRB optimization. Without loss 
of generality, we set Nt=12 , Nr=10 , and L=100 . The communication channel is 
assumed to be Rayleigh fading, where each entry of the channel gain vector hH
CU,k,∀k 
is subject to the standard complex Gaussian distribution. Regarding the prior distribu -
tion of the parameters to be estimated, we assume that the propagation loss αn,∀n in (4) 
obeys the complex Gaussian distribution with the variance of σ2
0 . The prior distribution 
of each n-th target’s angle is assumed to be the von Mises distribution with a mean of µk 
and a standard deviation of σθk , which is expressed as follows:
where x is the circular variable (e.g., angle), µ is the mean direction (a.k.a. the location 
parameter), and κ=1
σ2
θn is the concentration parameter, which is analogous to the 
inverse of the variance in a normal distribution. Id ( κ ) is the modified Bessel function of 
order d. Note that the FIM for Gaussian distributions is the inverse of the covariance 
matrix when the variables are independent. Accordingly, we have the Bayesian a priori 
FIM as follows [22](22)min
Xg(X)/definesRe/parenleftBig
tr/parenleftBig
∇fH/parenleftBig
Xn−1/parenrightBig/parenleftBig
X−Xn−1/parenrightBig/parenrightBig/parenrightBig
s.t. (20b) to(20d),
(23) Xi=Xi−1+/afii9838/parenleftBig
X∗−Xi−1/parenrightBig
,
(24)max
XRe(tr(X))
s.t. (20b) to(20d),
(25) f(x|µ,κ)=1
2πI0(κ)exp{κcos(x−µ)},
(26) JP=
1
2σ2
000
01
2σ2
00
00 κ
.

--- 第 10 页 ---
Page 10 of 14 Su et al. J Wireless Com Network         (2025) 2025:10 
The spatial distribution of the received signals at CUs (denoted by blue dots) and at 
targets/Eves (denoted by red dots) is shown in Fig.  2, where QPSK and 8PSK modulated 
signals are taken as examples. The principal constellation points of the received signals 
for both CUs and targets/Eves on a 2D complex plane are illustrated. For CUs, the 
optimization process aligns symbols within the constructive interference region to 
maintain high decoding accuracy, ensuring reliable communications. In contrast, the 
received signals at the targets/Eves are forced into the destructive region, intentionally 
increasing the SER to enhance PLS by minimizing the probability of accurate decoding 
at their end.
In Fig.  3, we demonstrate the generated beampatterns with different standard 
deviations of the a priori information of the target angles. We assume that there exists in 
the field of interest Ktar=2 targets located at θ1=−50◦ and θ2=−20◦ , and the angle 
standard deviation is given as 1◦ and 5◦ in Fig.  3a and b, respectively. Figure  3 illustrates 
that the main lobes pointing to targets of interest get narrow and with higher beam gain 
when σθn gets smaller, which implies a higher accuracy of the target angle estimation.
Fig. 2 The constellation of received signals at CUs, a QPSK, b 8PSK, 
Kcu=3,Ktar=2,P0=30 dBm, ŴCU,k=15 dB,∀k,τE,n=−5 dB  , and σθn=5◦,∀n
Fig. 3 The resultant beampatterns via the proposed secure ISAC signaling design with different a priori 
information of the angle, a standard deviation is 1◦ , b standard deviation is 5◦ , Kcu=3,Ktar=2,P0=30 dBm  , 
ŴCU,k=20 dB∀ k

--- 第 11 页 ---
Page 11 of 14
 Su et al. J Wireless Com Network         (2025) 2025:10 
 
Furthermore, Fig.  4 shows the trade-off between the communication and the sensing 
performances. It is obvious that with the improvement of the communication QoS, the 
CRB increases. In the block-level precoding, we leverage the conventional block-level 
SINR of the k-th CU as
where the signal matrix X in (3) can be written as X=WS , with W denoting the pre -
coding matrix and wk denoting the k-th entry of the precoding matrix W correspond -
ing to the k-th CU, and the transmit signal vector s is a set to include QPSK-modulated 
symbols. The constraint SINR k≥Ŵk is equivalently rewritten as a convex second-order 
cone (SOC) constraints, which is given as √1+ŴkhH
kwk≥√Ŵk/vextenddouble/vextenddouble/bracketleftbig
hH
kW,σCU,k/bracketrightbig/vextenddouble/vextenddouble [23]. 
Afterward, by substituting (20c ) and dropping (20d), the block-level precoding problem 
can be accordingly formulated as
The block-level precoding scheme in (27) is set as a benchmark in Fig.  4. It indicates that 
the proposed CI-DI-based design outperforms the block-level precoding technique, due 
to the reason that the block-level design consumes more power to reach the same SINR/
SNR threshold, i.e., Ŵk.
Figure  5 depicts the average SER at CUs and targets/Eves versus the SNR threshold ŴCU,k . 
The proposed technique in [14] is set as a benchmark, which deploys the CI-DI algorithm 
while minimizing the reflected SINR. By imposing the DI constraints, we note that the SER 
at targets/Eves is close to one, which indicates that the communication data are effectively 
protected from being decoded by the targets/Eves. Besides, it also illustrated that the SER 
at CUs gets lower with an increasing power budget. Moreover, the BCRB optimization 
outperforms the benchmark technique, which reaches lower SER at the CUs.(27) SINR k=/vextendsingle/vextendsinglehH
kwk/vextendsingle/vextendsingle2
/summationtextKcu
i=1,i�=k/vextendsingle/vextendsinglehH
kwi/vextendsingle/vextendsingle2+σ2
CU,k,
(28)min
Xtr/parenleftBig
J−1/parenrightBig
s.t.1
L�X�2
F≤PT,
/radicalbig
1+ŴkhH
kwk≥/radicalbig
Ŵk/vextenddouble/vextenddouble/bracketleftbig
hH
kW,σCU,k/bracketrightbig/vextenddouble/vextenddouble.Fig. 4 Performance trade-off between the communication and sensing systems with different power 
budgets, for Kcu = 3 and Ktar = 2 and

--- 第 12 页 ---
Page 12 of 14 Su et al. J Wireless Com Network         (2025) 2025:10 
3  Conclusion
In this paper, we presented a novel symbol-level signaling design algorithm for ISAC 
systems aiming at ensuring communication data security. The proposed design 
exploits the CI-DI technique, while sensing performance was measured by the BCRB 
and its PLS capability was quantified by the SER. Our optimization problem formula -
tion deals with the BCRB minimization, while conveying the received signals at CUs 
into the constructive region and making sure the received signals at targets/Eves fall 
into the destructive region. The presented numerical results verified that the CI-DI 
technique effectively protects communication data security. It was also showcased 
that the proposed symbol-level precoding technique outperforms the block-level 
precoding design. It was also demonstrated that the resultant beampattern with the 
proposed design yields improved sensing performance (i.e., narrower main beam 
with higher beam gain) when a priori statistical information of the unknown targets’ 
parameters is known accurately.
Abbreviations
AN  Artificial noise
AWGN  Additive white Gaussian noise
BCRB  Bayesian Cramér–Rao bound
BFIM  Bayesian Fisher Information Matrix
BS  Base station
CI  Constructive interference
CU  Communication user
DM  Directional modulation
ISAC  Integrated sensing and communication
MIMO  Multi-input multi-output
MSE  Mean-squared error
NLS  Network layer security
PLS  Physical layer security
PSK  Phase-shift keying
QoS  Quality of service
R &C  Radar and communication
RCS  Radar cross section
RIS  Reconfigurable intelligent surfaces
S &C  Sensing and communication
Author contributions
All authors made contributions to the discussions, analysis, and implementation of the proposed solution. All authors 
read and approved the final manuscript.Fig. 5 SER of CUs and average SER of targets/Eves versus the SNR threshold ŴCU,k with different power 
budgets for Kcu=3 and Ktar=2

--- 第 13 页 ---
Page 13 of 14
 Su et al. J Wireless Com Network         (2025) 2025:10 
 
Funding
This work was supported in part by the Engineering and Physical Sciences Research Council (EPSRC) under Grant EP/
S028455/1, in part by the National Natural Science Foundation of China under Grant 62101234, Grant 62401181, Grant 
U20B2039, Grant 61831008 and Grant 62027802, in part by the Young Elite Scientist Sponsorship Program by CAST under 
Grant No. YESS20210055, and in part by Smart Networks and Services Joint Undertaking (SNS JU) project 6 G-DISAC 
under the European Union’s Horizon Europe research and innovation program under Grant Agreement no. 101139130.
Data availability
Not applicable.
Declarations
Competing interests
Christos Masouros, George C. Alexandropoulos, and Fan Liu are Guest Editors for EURASIP Journal on Wireless Communi-
cations and Networking and were not involved in the editorial review or the decision to publish this article. The authors 
declare no other conflict of interest.  
Received: 11 July 2024   Accepted: 16 January 2025
References
 1. F. Liu, L. Zheng, Y. Cui, C. Masouros, A.P . Petropulu, H. Griffiths, Y.C. Eldar, Seventy years of radar and communications: 
the road from separation to integration. IEEE Signal Process. Mag. 40(5), 106–121 (2023)
 2. S.P . Chepuri, N. Shlezinger, F. Liu, G.C. Alexandropoulos, S. Buzzi, Y.C. Eldar, Integrated sensing and communications with 
reconfigurable intelligent surfaces: from signal modeling to processing. IEEE Signal Process. Mag. 40(6), 41–62 (2023)
 3. A. Liu, Z. Huang, M. Li, Y. Wan, W. Li, T.X. Han, C. Liu, R. Du, D.K.P . Tan, J. Lu et al., A survey on fundamental limits of 
integrated sensing and communication. IEEE Commun. Surv. Tutorials 24(2), 994–1034 (2022)
 4. N. Su, F. Liu, C. Masouros, Sensing-assisted eavesdropper estimation: An ISAC breakthrough in physical layer security. 
IEEE Transactions on Wireless Communications (2023)
 5. N. Su, F. Liu, C. Masouros, Secure radar-communication systems with malicious targets: Integrating radar, 
communications and jamming functionalities. IEEE Trans. Wireless Commun. 20(1), 83–95 (2020)
 6. G.C. Alexandropoulos, K.D. Katsanos, M. Wen, D.B. Da Costa, Counteracting eavesdropper attacks through 
reconfigurable intelligent surfaces: A new threat model and secrecy rate optimization. IEEE Open Journal of the 
Communications Society (2023)
 7. X. Zhu, J. Liu, L. Lu, T. Zhang, T. Qiu, C. Wang, Y.Liu, Enabling intelligent connectivity: A survey of secure ISAC in 6G 
networks. IEEE Communications Surveys & Tutorials (2024)
 8. Z. Ren, L. Qiu, J. Xu, Optimal transmit beamforming for secrecy integrated sensing and communication. In: ICC 2022-
IEEE International Conference on Communications, pp. 5555–5560 (2022). IEEE
 9. Y. Liu, L. Li, G.C. Alexandropoulos, M. Pesavento, Securing relay networks with artificial noise: an error performance-
based approach. Entropy 19(8), 384 (2017)
 10. A. Bazzi, M. Chafii, Secure full duplex integrated sensing and communications. IEEE Transactions on Information 
Forensics and Security (2023)
 11. R. Liu, M. Li, Q. Liu, A.L. Swindlehurst, Secure symbol-level precoding in MU-MISO wiretap systems. IEEE Trans. Inf. 
Forensics Secur. 15, 3359–3373 (2020)
 12. Z. Wei, C. Masouros, F. Liu, S. Chatzinotas, B. Ottersten, Energy-and cost-efficient physical layer security in the era of 
IoT: the role of interference. IEEE Commun. Mag. 58(4), 81–87 (2020)
 13. M. Alodeh, S. Chatzinotas, B. Ottersten, Constructive multiuser interference in symbol level precoding for the MISO 
downlink channel. IEEE Trans. Signal Process. 63(9), 2239–2252 (2015)
 14. N. Su, F. Liu, Z. Wei, Y.-F. Liu, C. Masouros, Secure dual-functional radar-communication transmission: Exploiting 
interference for resilience against target eavesdropping. IEEE Transactions on Wireless Communications (2022)
 15. B. Smida, A. Sabharwal, G. Fodor, G.C. Alexandropoulos, H.A. Suraweera, C.-B. Chae, Full-duplex wireless for 6G: 
Progress brings new opportunities and challenges. IEEE Journal on Selected Areas in Communications (2023)
 16. G.C. Alexandropoulos, M.A. Islam, B. Smida, Full-duplex massive multiple-input, multiple-output architectures: 
Recent advances, applications, and future directions. IEEE Vehicular Technology Magazine (2022)
 17. C. Masouros, G. Zheng, Exploiting known interference as green signal power for downlink beamforming 
optimization. IEEE Trans. Signal Process. 63(14), 3628–3640 (2015)
 18. Y. Xiong, F. Liu, Y. Cui, W. Yuan, T.X. Han, G. Caire, On the fundamental tradeoff of integrated sensing and 
communications under Gaussian channels. IEEE Transactions on Information Theory (2023)
 19. Q. Xu, P . Ren, A.L. Swindlehurst, Rethinking secure precoding via interference exploitation: a smart eavesdropper 
perspective. IEEE Trans. Inf. Forensics Secur. 16, 585–600 (2020)
 20. M.R. Khandaker, C. Masouros, K.-K. Wong, S. Timotheou, Secure SWIPT by exploiting constructive interference and 
artificial noise. IEEE Trans. Commun. 67(2), 1326–1340 (2018)
 21. S.P . Boyd, L. Vandenberghe, Convex optimization (2004)
 22. A. Vempaty, H. He, B. Chen, P .K. Varshney, On quantizer design for distributed Bayesian estimation in sensor 
networks. IEEE Trans. Signal Process. 62(20), 5359–5369 (2014)
 23. F. Liu, Y.-F. Liu, C. Masouros, A. Li, Y.C. Eldar, A joint radar-communication precoding design based on Cramér-Rao 
bound optimization. In: 2022 IEEE Radar Conference (RadarConf22), pp. 1–6 (2022). IEEE

--- 第 14 页 ---
Page 14 of 14 Su et al. J Wireless Com Network         (2025) 2025:10 
Publisher’s Note
Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.

================================================================================

