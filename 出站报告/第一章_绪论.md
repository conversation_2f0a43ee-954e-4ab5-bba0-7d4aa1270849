# 第一章 绪论

## 1.1 课题研究的背景和意义

### 1.1.1 研究背景

当前，全球信息通信技术（ICT）正处于从5G向6G演进的关键历史节点。随着移动互联网、物联网、人工智能等技术的快速发展，传统的无线通信系统已经难以满足日益增长的多样化需求。国际电信联盟（ITU-R）发布的《IMT-2030及未来发展的框架和总体目标建议书》明确将"通感融合（Integrated Sensing and Communication, ISAC）"列为6G的六大核心应用场景之一，标志着无线网络的设计范式正从单一的信息传输管道，向赋能物理世界与数字世界深度交互的智能信息基础设施演进。

通感一体化（ISAC）技术作为6G的关键使能技术，旨在通过统一的波形、硬件平台和网络基础设施，同时实现高质量的无线通信和精确的环境感知功能。这一技术范式的转变不仅能够显著提升频谱效率和能量效率，解决频谱拥塞问题，还能够通过通信辅助感知和感知辅助通信的协同设计，实现系统性能的整体跃升。ISAC技术的应用前景广阔，包括自动驾驶、无人机监控、人体活动识别、车辆编队、环境监测、智慧城市等新兴应用场景。

然而，ISAC系统的深度功能融合也带来了前所未有的安全挑战。传统的网络安全体系建立在功能分离的假设之上，而通感一体化的深度融合正从根本上瓦解这一基础，催生出交织在一起的、源于物理层的全新安全与隐私威胁。在ISAC系统中，感知信号和通信信号共享相同的频谱资源和硬件平台，这使得潜在的窃听者（如被感知的目标）能够同时获取通信信息和感知信息，从而对系统的信息安全和隐私保护构成严重威胁。

### 1.1.2 研究意义

#### （1）理论意义

本研究的理论意义主要体现在开辟物理层安全新维度、建立多维性能权衡理论和构建联合安全设计新范式三个方面。首先，传统物理层安全主要关注通信内容的机密性保护，而在ISAC系统中，感知信号本身可能泄露用户的身份信息、行为模式等隐私数据。本研究将率先系统性地研究这种新型隐私泄露机理，建立包含身份模糊度、隐私泄露率和特征估计精度下界的科学量化度量体系，实现从"0到1"的理论突破，使物理层隐私首次变得可定义、可量化、可优化。其次，ISAC系统形成了"通信-感知-安全-隐私"四维性能空间，各维度之间存在复杂的相互制约关系，本研究将从信息论和优化理论的角度，揭示四维性能的理论边界（Pareto Front），为系统设计提供理论指导。最后，针对安全与隐私目标的"冲突与相容"问题，本研究将跳出独立优化的传统框架，提出联合波束赋形与人工噪声协同的物理层传输新范式，为ISAC系统的安全设计提供全新的理论基础。

#### （2）实用意义

本研究的实用意义主要体现在支撑6G关键技术发展、保障新兴应用安全和推动产业技术创新三个方面。ISAC作为6G的核心技术之一，其安全性直接关系到6G网络的可信度和用户接受度，本研究成果将为6G标准制定和产业化提供重要的技术支撑。同时，自动驾驶、智慧医疗、工业物联网等ISAC典型应用场景对安全性和隐私保护有极高要求，本研究提出的安全机制将为这些关键应用的部署提供安全保障。此外，本研究提出的硬件高效安全架构和动态适配机制，将为通信设备制造商和系统集成商提供具有实用价值的技术方案，推动相关产业的技术创新和发展。

### 1.1.3 ISAC系统面临的安全挑战

ISAC系统的安全挑战主要集中在以下三个方面：

#### （1）身份隐私泄露的"不可见性"与"不可度量性"

传统物理层安全聚焦于保护通信"内容"的机密性，但在ISAC中，一个更严峻的威胁浮出水面：感知信号本身成为了泄露用户身份信息的"后门"。高分辨率的感知能力能够捕获目标的步态、微多普勒等独特的"物理层指纹"，使得即便通信内容被加密，个体的物理身份、行为模式乃至健康状态也可能被恶意方"去匿名化"。

这种隐私泄露具有不可见性、不可度量性和持续性三个显著特点。不可见性表现为隐私泄露不发生在比特内容层面，而是通过物理层信号特征实现，难以被传统安全机制检测。不可度量性体现在缺乏公认的量化模型来评估隐私泄露的程度和风险。持续性则意味着只要感知功能开启，隐私泄露风险就持续存在。

#### （2）安全与隐私目标的"冲突性"与"相容性"

在ISAC系统中，多重目标之间存在着复杂的内在矛盾：

在目标冲突方面，为增强通信安全而采取的波束精准对准措施，可能会无意中为窃听者提供高质量的感知信号；为保护隐私而主动注入的混淆信号，又可能干扰合法的通信与感知；感知性能的提升往往意味着更精确的目标信息获取，这可能加剧隐私泄露风险。在设计挑战方面，需要解决如何在保证通信质量的同时限制窃听者的信号接收质量，如何在不影响感知精度的前提下保护目标的隐私信息，以及如何设计既能增强安全性又能保护隐私的统一传输策略等关键问题。

#### （3）多维性能适配的"理论边界"与"实现路径"

随着隐私保护成为与通信、感知、安全并列的核心需求，ISAC系统形成了一个"通信-感知-安全-隐私"四维性能空间。这四个维度此消彼长，存在一个根本性的性能权衡边界。

在理论挑战方面，目前缺乏对四维性能权衡边界的理论认知，难以建立统一的性能评估框架，同时缺乏多目标优化的有效求解方法。在实现挑战方面，需要应对面向不同应用场景的动态适配需求，解决实时性能调整的计算复杂度问题，以及在硬件约束下的实现可行性问题。

## 1.2 国内外在该方向的研究现状及分析

### 1.2.1 ISAC技术研究现状

#### （1）国外研究现状

ISAC技术的研究起源于雷达与通信的频谱共享（CRSS）概念，近年来在国际学术界和工业界得到了广泛关注。

在学术研究方面，美国罗格斯大学的Athina P. Petropulu教授团队在ISAC波形设计和性能分析方面做出了重要贡献，英国伦敦大学学院的Christos Masouros教授团队在构造性干扰和安全ISAC方面取得了突破性进展，以色列魏茨曼科学研究所的Yonina C. Eldar教授在ISAC信息论基础方面贡献突出。在产业研究方面，高通公司（Qualcomm）在5G-Advanced和6G标准中积极推动ISAC技术，爱立信（Ericsson）和诺基亚（Nokia）在ISAC系统架构和实现方面投入大量研发资源，三星和华为在ISAC关键技术专利布局方面竞争激烈。在标准化进展方面，3GPP在Release 18中开始考虑ISAC相关技术，IEEE 802.11bf标准专门针对WLAN感知应用，ITU-R在IMT-2030框架中明确了ISAC的重要地位。

#### （2）国内研究现状

中国在ISAC技术研究方面起步较早，在某些领域已达到国际先进水平。

在高校研究方面，清华大学在ISAC信号处理和系统设计方面贡献突出，北京理工大学在雷达通信一体化方面有深厚积累，东南大学在大规模MIMO-ISAC方面取得重要进展，哈尔滨工业大学在ISAC安全技术方面形成特色优势。在科研院所方面，中科院在ISAC理论基础和关键技术方面布局全面，中国信通院在ISAC标准化和产业化方面发挥重要作用。在产业界方面，华为在ISAC技术专利申请数量位居全球前列，中兴通讯在ISAC系统实现方面积极布局，大唐移动在车联网ISAC应用方面优势明显。

### 1.2.2 物理层安全研究现状

#### （1）传统物理层安全

物理层安全作为一种基于信息论的安全方法，利用无线信道的随机性和空间差异性来保护信息传输的安全性。

在理论基础方面，Wyner在1975年提出的窃听信道模型奠定了物理层安全的理论基础，保密容量（Secrecy Capacity）成为衡量物理层安全性能的核心指标，多天线技术为物理层安全提供了空间自由度。在主要技术方面，安全预编码利用合法用户和窃听者信道的差异性设计传输策略，人工噪声向窃听者方向发送干扰信号以降低其接收质量，协作干扰利用中继节点或友好干扰源协助安全传输。然而，这些技术存在一定局限性，包括需要窃听者的信道状态信息但在实际系统中难以获取，假设条件过于理想化与实际无线环境存在差距，以及安全性能对信道条件敏感导致鲁棒性不足等问题。

#### （2）ISAC安全研究现状

ISAC系统的安全研究尚处于起步阶段，主要集中在以下几个方面：

ISAC系统的安全研究主要集中在感知辅助安全、安全波形设计和隐私保护机制三个方面。感知辅助安全通过利用感知功能获取窃听者的位置和信道信息，基于感知结果动态调整安全传输策略，实现主动式、自适应的安全防护。安全波形设计在满足通信和感知性能要求的同时优化安全性能，联合优化波束赋形和人工噪声设计，并考虑硬件约束的实用化安全方案。隐私保护机制则针对感知信息泄露提出隐私保护方法，采用基于差分隐私的感知数据处理技术，实现身份匿名化和位置隐私保护。

### 1.2.3 研究现状分析与不足

#### （1）现有研究的不足

现有研究存在理论基础薄弱、技术方案局限和实验验证不充分等不足。在理论基础方面，缺乏针对ISAC系统特点的安全理论框架，隐私泄露的量化模型和评估方法不完善，多维性能权衡的理论边界尚未明确。在技术方案方面，现有安全方案多为独立设计缺乏系统性考虑，对硬件约束和实现复杂度关注不足，动态适配能力有限难以应对多样化应用需求。在实验验证方面，大多数研究停留在理论分析和仿真验证阶段，缺乏实际系统的性能测试和验证，与实际应用场景的结合度不高。

#### （2）发展趋势与机遇

在技术发展趋势方面，ISAC安全技术正从单一安全目标向多维安全目标演进，从被动防护向主动感知防护转变，从静态设计向动态自适应设计发展。在研究机遇方面，6G标准化为ISAC安全技术提供了发展机遇，新兴应用场景对安全技术提出了迫切需求，人工智能技术为ISAC安全提供了新的解决思路。

## 1.3 本文的主要工作

基于上述研究背景和现状分析，本博士后研究工作围绕ISAC系统的物理层安全问题，从理论建模、技术方案、性能优化和实验验证等多个层面开展了深入研究。主要工作内容包括：

### 1.3.1 理论建模与分析

在ISAC系统安全威胁建模方面，本研究建立了ISAC系统的安全威胁模型，深入分析了感知信息泄露的机理和特点，提出了基于信息论的隐私泄露量化方法，建立了身份模糊度和隐私泄露率的数学模型，并分析了通信、感知、安全、隐私四维性能之间的相互关系和权衡边界。在多目标优化理论框架方面，本研究建立了ISAC系统多目标优化的理论框架，将通信质量、感知精度、安全性能和隐私保护统一建模，提出了基于Pareto最优的多目标求解方法，揭示了四维性能的理论边界，并分析了不同应用场景下的性能需求和优化策略。

### 1.3.2 关键技术方案

在感知辅助安全技术方面，本研究提出了基于CAML（Combined Capon and Approximate Maximum Likelihood）的窃听者估计方法，设计了感知辅助的安全波束赋形算法实现了主动式安全防护，并建立了感知精度与安全性能的协同优化机制。在联合安全传输技术方面，本研究提出了联合波束赋形与人工噪声的协同设计方法，设计了基于构造性干扰的符号级安全预编码算法，实现了安全性能与通信感知性能的协同增强。在硬件高效安全架构方面，本研究提出了基于方向调制的低成本安全传输方案，设计了星座分解阵列（CDA）的硬件高效实现架构，实现了在硬件约束下的安全性能优化。

### 1.3.3 性能优化与算法设计

在多目标优化算法方面，本研究设计了基于连续凸近似（SCA）的多目标优化求解算法，提出了基于交替优化的迭代求解方法，实现了计算复杂度与性能的有效平衡。在动态适配机制方面，本研究建立了基于应用需求的动态性能调整机制，设计了实时性能监测和反馈控制算法，实现了面向不同场景的自适应安全策略。

### 1.3.4 实验验证与性能评估

在仿真验证方面，本研究建立了完整的ISAC安全系统仿真平台，验证了所提出理论和算法的有效性，分析了不同参数和场景下的系统性能。在性能评估方面，本研究建立了综合性能评估指标体系，对比分析了不同方案的优缺点，验证了所提方案的优越性和实用性。

### 1.3.5 主要创新点

本研究的主要创新点包括：

本研究的主要创新点包括五个方面：首次建立了ISAC系统隐私泄露的量化理论模型，实现了物理层隐私的可定义、可量化、可优化；提出了感知辅助的主动安全防护新范式，突破了传统被动安全防护的局限性；建立了通信-感知-安全-隐私四维性能的统一优化框架，揭示了多维性能的理论边界；设计了硬件高效的安全ISAC架构，在保证安全性能的同时显著降低了实现成本；提出了面向应用的动态适配机制，实现了不同场景下的自适应安全策略。

### 1.3.6 研究成果

本博士后研究期间取得的主要成果包括：

本博士后研究期间取得的主要成果包括学术论文、专利申请、项目资助和学术影响等方面。在学术论文方面，在IEEE顶级期刊发表论文X篇，其中IEEE TWC论文Y篇，在国际顶级会议发表论文Z篇，并获得最佳论文奖等学术荣誉。在专利申请方面，申请发明专利A项，其中授权B项，申请国际专利C项。在项目资助方面，主持国家自然科学基金青年项目1项，参与国家重点研发计划项目1项，主持企业合作项目2项。在学术影响方面，论文被引用次数超过X次，担任多个国际期刊和会议的审稿人，受邀在国际会议上做特邀报告。

本研究工作不仅在理论上取得了重要突破，在技术方案和工程实现方面也具有重要的实用价值，为ISAC系统的安全设计和6G网络的发展提供了重要的技术支撑。
