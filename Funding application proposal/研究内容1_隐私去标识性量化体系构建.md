# 研究内容1：通感系统物理层隐私去标识性建模与量化表征

## （2）隐私去标识性量化体系构建

为完成对物理层隐私的科学度量，解决传统安全指标无法衡量身份信息安全性的问题，本部分将基于前述建立的信号模型，从信息论和估计理论两个互补的维度，构建一套全新的、能够定量评估隐私保护水平的指标体系。

### ⚫ 基于信息熵的身份模糊度（Identity Ambiguity）构建

该指标旨在从"分类"的角度量化窃听者对目标真实身份的不确定性。假设窃听者试图从一个包含$K$个可能身份的集合$\Psi = \{\psi_1, \psi_2, \ldots, \psi_K\}$中识别出当前目标。身份模糊度可定义为窃听者在获得观测信号$y^E$后，对目标身份的后验熵：

$$IA(\Psi|y^E) = -\sum_{i=1}^{K} P(\psi_i|y^E) \log_2 P(\psi_i|y^E) \tag{6}$$

其中$P(\psi_i|y^E)$是窃听者基于观测信号$y^E$对身份$\psi_i$的后验概率，可通过贝叶斯定理计算：

$$P(\psi_i|y^E) = \frac{P(y^E|\psi_i)P(\psi_i)}{\sum_{j=1}^{K} P(y^E|\psi_j)P(\psi_j)} \tag{7}$$

其中$P(y^E|\psi_i)$是在身份为$\psi_i$条件下的似然函数，$P(\psi_i)$是身份的先验概率分布。身份模糊度$IA$的取值范围为$[0, \log_2 K]$，其中：

- $IA = 0$表示窃听者能够完全确定目标身份，隐私完全泄露；
- $IA = \log_2 K$表示窃听者对所有身份的后验概率相等，隐私得到最佳保护。

为进一步刻画隐私保护的相对水平，定义归一化身份模糊度：

$$IA_{norm} = \frac{IA(\Psi|y^E)}{\log_2 K} \tag{8}$$

该指标的物理意义在于：它直接反映了窃听者进行身份识别的困难程度，数值越大表示隐私保护效果越好。

### ⚫ 基于克拉美-罗下界（CRLB）的特征估计误差界构建

该指标从"估计"的角度量化窃听者对用户敏感物理特征的理论最佳估计精度。考虑窃听者试图估计目标的敏感特征参数向量$\boldsymbol{\theta} = [A, f]^T$（包括微动幅度$A$和频率$f$），其中$\boldsymbol{\theta}$包含了可用于身份识别的关键生物特征信息。

根据估计理论，任何无偏估计器$\hat{\boldsymbol{\theta}}$的协方差矩阵都满足克拉美-罗下界：

$$\text{Cov}(\hat{\boldsymbol{\theta}}) \succeq \mathbf{J}^{-1}(\boldsymbol{\theta}) \tag{9}$$

其中$\mathbf{J}(\boldsymbol{\theta})$是Fisher信息矩阵，其第$(i,j)$个元素定义为：

$$[\mathbf{J}(\boldsymbol{\theta})]_{ij} = \mathbb{E}\left[\frac{\partial^2\ln p(y^E|\boldsymbol{\theta})}{\partial\theta_i\partial\theta_j}\right] \tag{10}$$

基于前述建立的似然函数$p(y^E|\boldsymbol{\theta})$，可以推导出Fisher信息矩阵的具体表达式。对于简谐振动模型，Fisher信息矩阵可表示为：

$$\mathbf{J}(\boldsymbol{\theta}) = 2\text{Re}\left\{\sum_{t=1}^{T} \left[\frac{\partial\boldsymbol{\mu}_t}{\partial\boldsymbol{\theta}}\right]^H \mathbf{R}^{-1} \left[\frac{\partial\boldsymbol{\mu}_t}{\partial\boldsymbol{\theta}}\right]\right\} \tag{11}$$

其中$\boldsymbol{\mu}_t = \mathbb{E}[y^E(t)]$是信号的期望值，$\mathbf{R}$是噪声协方差矩阵，$T$是观测时长。

定义特征估计误差界（Feature Estimation Error Bound, FEEB）为：

$$FEEB = \sqrt{\text{tr}(\mathbf{J}^{-1}(\boldsymbol{\theta}))} \tag{12}$$

该指标的物理意义为：窃听者对敏感特征参数估计的理论最小均方根误差。$FEEB$值越大，表示窃听者的估计精度越差，隐私保护效果越好。

为便于不同场景下的比较，进一步定义相对特征估计误差界：

$$FEEB_{rel} = \frac{FEEB}{\|\boldsymbol{\theta}\|_2} \tag{13}$$

### ⚫ 综合隐私度量指标构建

为全面评估系统的隐私保护水平，结合上述两个互补的度量维度，构建综合隐私度量指标：

$$PM = \alpha \cdot IA_{norm} + (1-\alpha) \cdot \frac{FEEB_{rel}}{FEEB_{max}} \tag{14}$$

其中$\alpha \in [0,1]$是权重因子，用于平衡身份分类不确定性和特征估计困难度两个维度的重要性；$FEEB_{max}$是在给定系统参数下$FEEB$的理论最大值。综合隐私度量$PM$的取值范围为$[0,1]$，数值越大表示隐私保护水平越高。

### ⚫ 隐私泄露率（Privacy Leakage Rate）定义

为了与传统的保密容量等安全指标形成对应，进一步定义隐私泄露率，用于刻画单位时间内身份信息的泄露速度：

$$PLR = \frac{I(\Psi; Y^E)}{T} \tag{15}$$

其中$I(\Psi; Y^E)$是身份集合$\Psi$与窃听者观测信号$Y^E$之间的互信息，$T$是观测时长。互信息可表示为：

$$I(\Psi; Y^E) = H(\Psi) - H(\Psi|Y^E) \tag{16}$$

其中$H(\Psi) = \log_2 K$是身份的先验熵，$H(\Psi|Y^E)$是前述定义的身份模糊度$IA$。因此：

$$PLR = \frac{\log_2 K - IA}{T} \tag{17}$$

隐私泄露率$PLR$的物理意义为：窃听者每单位时间获得的关于目标身份的信息量（以比特为单位）。$PLR = 0$表示完全隐私保护，$PLR$越大表示隐私泄露越严重。

### ⚫ 多维隐私风险评估框架

考虑到实际应用中目标可能具有多种类型的敏感特征（如步态特征、呼吸模式、设备类型等），建立多维隐私风险评估框架。设目标具有$M$种不同类型的敏感特征，对应参数向量为$\boldsymbol{\theta}_1, \boldsymbol{\theta}_2, \ldots, \boldsymbol{\theta}_M$，则多维隐私度量可表示为：

$$PM_{multi} = \sum_{i=1}^{M} w_i \cdot PM_i \tag{18}$$

其中$w_i$是第$i$种特征的重要性权重（$\sum_{i=1}^{M} w_i = 1$），$PM_i$是针对第$i$种特征计算的隐私度量。权重$w_i$可根据不同特征的敏感程度和应用场景需求进行调整。

### ⚫ 动态隐私度量

考虑到通感系统的时变特性，进一步建立动态隐私度量模型。定义时刻$t$的瞬时隐私度量为：

$$PM(t) = \alpha \cdot IA_{norm}(t) + (1-\alpha) \cdot \frac{FEEB_{rel}(t)}{FEEB_{max}} \tag{19}$$

以及滑动窗口内的平均隐私度量：

$$PM_{avg}(t) = \frac{1}{W} \sum_{t'=t-W+1}^{t} PM(t') \tag{20}$$

其中$W$是滑动窗口长度。动态隐私度量能够实时反映系统隐私保护水平的变化，为自适应隐私保护策略提供依据。

## 总结

通过上述量化体系的建立，物理层隐私首次实现了从"定性描述"到"定量分析"的跨越，为后续的隐私保护方案设计和性能优化提供了科学、精确的目标函数和约束条件。该量化体系具有以下显著特点：

1. **理论严谨性**：基于信息论和估计理论的坚实数学基础，确保了度量指标的理论正确性；

2. **实用可操作性**：所有指标均可通过系统参数和信号模型进行计算，具备工程实现的可行性；

3. **全面性**：从身份分类和特征估计两个互补维度，全面刻画了隐私泄露风险；

4. **灵活性**：支持多维特征和动态场景，能够适应不同的应用需求和系统配置。

### ⚫ 联合安全与隐私性能基准

在上述指标基础上，结合物理层安全的经典指标（如保密速率），建立一个能够统一评估通信安全和感知隐私的综合性能表征框架，作为后续优化设计的基准。

#### 物理层安全经典指标回顾

首先回顾物理层安全的核心指标。在ISAC系统中，合法用户Bob和窃听者Eve分别接收到的信号可表示为：

$$y^B = \mathbf{h}^B \mathbf{x} + n^B \tag{21}$$

$$y^E = \mathbf{h}^E \mathbf{x} + n^E \tag{22}$$

其中$\mathbf{h}^B$和$\mathbf{h}^E$分别是合法用户和窃听者的信道向量，$\mathbf{x}$是发送信号，$n^B$和$n^E$是相应的噪声。

**保密速率（Secrecy Rate）**定义为合法用户信道容量与窃听者信道容量之差的正值部分：

$$R_s = [R^B - R^E]^+ \tag{23}$$

其中$R^B = \log_2(1 + \text{SNR}^B)$和$R^E = \log_2(1 + \text{SNR}^E)$分别是合法用户和窃听者的信道容量，$[x]^+ = \max(x, 0)$。

**保密中断概率（Secrecy Outage Probability）**定义为保密速率低于目标阈值的概率：

$$P_{out} = \Pr(R_s < R_{th}) \tag{24}$$

其中$R_{th}$是目标保密速率阈值。

#### 联合性能空间构建

基于前述建立的隐私度量指标和传统安全指标，构建四维联合性能空间：

$$\mathcal{S} = \{R_c, R_s, PM, SA\} \tag{25}$$

其中：
- $R_c$：通信速率（Communication Rate），表征系统的通信性能
- $R_s$：保密速率（Secrecy Rate），表征通信信息的安全性
- $PM$：隐私度量（Privacy Measure），表征感知身份的隐私保护水平
- $SA$：感知精度（Sensing Accuracy），表征系统的感知性能

#### 归一化性能指标

为便于不同维度性能的统一比较和优化，对各性能指标进行归一化处理：

$$\tilde{R}_c = \frac{R_c}{R_{c,max}}, \quad \tilde{R}_s = \frac{R_s}{R_{s,max}}, \quad \tilde{PM} = PM, \quad \tilde{SA} = \frac{SA}{SA_{max}} \tag{26}$$

其中$R_{c,max}$、$R_{s,max}$和$SA_{max}$分别是在给定系统配置下各指标的理论最大值。归一化后的性能向量为：

$$\tilde{\mathbf{s}} = [\tilde{R}_c, \tilde{R}_s, \tilde{PM}, \tilde{SA}]^T \tag{27}$$

#### 加权综合性能指标

针对不同应用场景的需求差异，构建加权综合性能指标：

$$\Phi = \mathbf{w}^T \tilde{\mathbf{s}} = w_c \tilde{R}_c + w_s \tilde{R}_s + w_p \tilde{PM} + w_a \tilde{SA} \tag{28}$$

其中$\mathbf{w} = [w_c, w_s, w_p, w_a]^T$是权重向量，满足$\sum_{i} w_i = 1$且$w_i \geq 0$。权重的选择反映了不同应用场景的优先级：

- **通信优先场景**：$w_c > w_s, w_p, w_a$，如高速数据传输应用
- **安全优先场景**：$w_s > w_c, w_p, w_a$，如军事通信应用
- **隐私优先场景**：$w_p > w_c, w_s, w_a$，如智慧医疗应用
- **感知优先场景**：$w_a > w_c, w_s, w_p$，如自动驾驶应用

#### 性能权衡边界分析

定义系统的可达性能区域为：

$$\mathcal{R} = \{\tilde{\mathbf{s}} : \tilde{\mathbf{s}} \text{ 在给定约束下可实现}\} \tag{29}$$

系统的帕累托最优边界（Pareto Front）定义为：

$$\mathcal{P} = \{\tilde{\mathbf{s}} \in \mathcal{R} : \nexists \tilde{\mathbf{s}}' \in \mathcal{R}, \tilde{\mathbf{s}}' \succeq \tilde{\mathbf{s}}, \tilde{\mathbf{s}}' \neq \tilde{\mathbf{s}}\} \tag{30}$$

其中$\tilde{\mathbf{s}}' \succeq \tilde{\mathbf{s}}$表示$\tilde{\mathbf{s}}'$在所有维度上都不劣于$\tilde{\mathbf{s}}$。

#### 性能效率指标

为量化系统性能相对于理论最优的接近程度，定义性能效率指标：

$$\eta = \frac{\Phi}{\Phi_{ideal}} \tag{31}$$

其中$\Phi_{ideal}$是理想情况下（无约束）的最大综合性能。

#### 安全-隐私协同增益

为量化联合优化相对于独立优化的增益，定义安全-隐私协同增益：

$$G_{sp} = \frac{\Phi_{joint}}{\Phi_{separate}} \tag{32}$$

其中：
- $\Phi_{joint}$：安全与隐私联合优化下的综合性能
- $\Phi_{separate}$：安全与隐私独立优化下的综合性能

当$G_{sp} > 1$时，表明联合优化能够产生正向协同效应。

#### 动态性能基准

考虑到ISAC系统的时变特性，建立动态性能基准。定义时刻$t$的瞬时性能向量为$\tilde{\mathbf{s}}(t)$，则滑动窗口内的平均性能为：

$$\bar{\tilde{\mathbf{s}}}(t) = \frac{1}{W} \sum_{\tau=t-W+1}^{t} \tilde{\mathbf{s}}(\tau) \tag{33}$$

定义性能稳定性指标：

$$\sigma_s(t) = \sqrt{\frac{1}{W} \sum_{\tau=t-W+1}^{t} \|\tilde{\mathbf{s}}(\tau) - \bar{\tilde{\mathbf{s}}}(t)\|^2} \tag{34}$$

该指标反映了系统性能的波动程度，数值越小表示性能越稳定。

#### 基准性能阈值

为指导系统设计，建立不同应用场景下的基准性能阈值：

**基础性能阈值**：
$$\tilde{R}_{c,min} \geq 0.6, \quad \tilde{R}_{s,min} \geq 0.4, \quad \tilde{PM}_{min} \geq 0.5, \quad \tilde{SA}_{min} \geq 0.7 \tag{35}$$

**高性能阈值**：
$$\tilde{R}_{c,high} \geq 0.8, \quad \tilde{R}_{s,high} \geq 0.7, \quad \tilde{PM}_{high} \geq 0.8, \quad \tilde{SA}_{high} \geq 0.9 \tag{36}$$

#### 性能评估流程

基于上述基准框架，建立标准化的性能评估流程：

1. **性能测量**：根据系统配置和信道条件，计算各维度性能指标
2. **归一化处理**：将性能指标映射到$[0,1]$区间
3. **权重配置**：根据应用场景确定权重向量$\mathbf{w}$
4. **综合评估**：计算加权综合性能指标$\Phi$
5. **基准对比**：与预设阈值和理想性能进行对比
6. **优化指导**：识别性能瓶颈，指导后续优化方向

该联合安全与隐私性能基准框架具有以下显著优势：

1. **统一性**：将传统分离的安全和隐私指标统一到同一评估框架中
2. **可比性**：通过归一化处理，实现不同维度性能的直接比较
3. **灵活性**：支持根据应用需求调整权重配置
4. **指导性**：为系统设计和优化提供明确的性能目标和评估标准

该量化体系的建立，不仅填补了ISAC系统隐私度量的理论空白，更为构建下一代具备内生隐私保护能力的通感一体化网络奠定了重要的理论基石。
