

=== 第 1 页 ===
申请代码
F0106
接收部门
收件日期
接收编号
6227010688
    
国家自然科学基金
申 请 书
（2 0 2 2 版）
资助类别：
面上项目
亚类说明：
附注说明：
项目名称：
面向在轨处理的巨星座网络协同编码容错机制研究
申 请 人：
顾术实
电 话：
0755-26406597
依托单位：
哈尔滨工业大学
通讯地址：
广东省深圳市南山区西丽大学城哈工大校区信息楼1128
邮政编码：
518055
单位电话：
0451-86414151
电子邮箱：
<EMAIL>
填写日期：
2022年03月05日
国家自然科学基金委员会
NSFC 2022


=== 第 2 页 ===
基本信息
申
请
人
信
息
姓        名
顾术实
性别
男
出生
年月
1986年01月
民族
汉族
学        位
博士
职称
讲师
是否在站博士后
否
电子邮箱
<EMAIL>
电        话
0755-26406597
国别或地区
中国
申请人类别
依托单位全职
工   作   单   位
哈尔滨工业大学/哈尔滨工业大学（深圳）
主 要 研 究 领 域
卫星通信网络、分布式编码
依
托
单
位
信
息
名        称哈尔滨工业大学
联   系   人王雪
电子邮箱
<EMAIL>
电        话0451-86414151
网站地址
http://kjc.hit.edu.cn/
合
作
研
究
单
位
信
息
单 位 名 称
项
目
基
本
信
息
项目名称
面向在轨处理的巨星座网络协同编码容错机制研究
英文名称
Research on Collaborative Coded Fault-Tolerance Mechanisms for
On-Board Processing in Mega-Constellation Networks
资助类别
面上项目
亚类说明
附注说明
申请代码
F0106.空天通信
研究期限
2023年01月01日 -- 2026年12月31日
研究方向：卫星通信
申请直接费用55.0000万元
中文关键词
低轨卫星通信；在轨处理；网络容错性；分布式编码；协同容错机制
英文关键词
LEO Satellite Communications; On-Board Processing; Network
Fault-Tolerance; Distributed Codes; Collaborative Fault-Tolerance
Mechanism
NSFC 2022
第 1 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 3 页 ===
中
文
摘
要
随着全球卫星通信业务量与对地观测数据量的不断激增，巨星座网络的在轨处理压力急剧
上升，大量传输、计算、存储任务需在天基平台通过多星协作处理完成。但由于巨星座网络存
在链路断续连接、节点算力迟滞、数据频繁丢失等系统差错问题，网络容错能力不足逐渐成为
制约其在轨高效处理的核心瓶颈。针对上述问题，本项目以提升巨星座网络容错能力为目标，
将分布式编码容错理论与卫星通信网络技术相融合，探索大规模在轨处理网络容错能力的多维
表征方法，明晰高动态资源约束下编码容错机制的协同优化机理。规划设计基于时变网络连通
度的多路径编码协作传输、基于拓扑感知的自适应无速率编码计算分配、基于集群划分的非对
称可再生编码数据修复等在轨协同编码容错机制。从信息可靠传输、任务稳定计算、数据鲁棒
存储三个维度上综合提升巨星座网络的容错能力，并优化相应编码容错机制的协同容错效率，
为我国大规模卫星在轨处理平台的长期稳定运行提供网络容错性保障支撑。
英
文
摘
要
With the continuous increasing of global satellite communication traffic and earth
observation data amount, the pressure of the on-board processing (OBP) in the
mega-constellation networks (MCNs) is rising dramatically. A large number of
transmission-computing-storage integrated tasks need to be completed on
space-based platform through multi-satellite cooperative OBP. However, due to the
system errors in MCNs, i.e., intermittent link connections, node straggling
computations, and frequent data losses, the poor fault-tolerance capability of
MCNs gradually becomes the core bottleneck restricting the OBP efficiency. Aim to
solve the above problem, with the purpose of improving the network
fault-tolerance, this project explores the multi-dimensional characterization
method of the fault-tolerance capability of large-scale OBP networks, and
clarifies the cooperative optimization theory of the coded fault tolerance
mechanisms under high dynamic resource constraints. Then, this project designs the
following cooperative coded fault-tolerance mechanisms: multi-path coded
cooperative transmission based on time-varying network connectivity,
topology-aware adaptive rateless coded computing allocation, and asymmetric
regenerating coded data repair based on dividing clusters. We attempt to
comprehensively improve the fault-tolerance capability of MCNs from the three
dimensions including reliable information transmission, stable task computation
and robust data storage, and to optimize the fault-tolerant efficiency of the
corresponding coded fault-tolerant mechanisms. The completion of this project can
provide network fault-tolerance support for the long-term and stable operations of
large-scale satellite OBP platform in China.
NSFC 2022
第 2 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 4 页 ===
科学问题属性
 
科学问题源于科研人员的灵感和新思想，且具有鲜明的首创性特征，旨在
“鼓励探索，突出原创”：
通过自由探索产出从无到有的原创性成果。
 
科学问题源于世界科技前沿的热点、难点和新兴领域，且具有鲜明的引领
“聚焦前沿，独辟蹊径”：
性或开创性特征，旨在通过独辟蹊径取得开拓性成果，引领或拓展科学前沿。
 
科学问题源于国家重大需求和经济主战场，且具有鲜明的需求导向、问题
“需求牵引，突破瓶颈”：
导向和目标导向特征，旨在通过解决技术瓶颈背后的核心科学问题，促使基础研究成果走向应用。
 
科学问题源于多学科领域交叉的共性难题，具有鲜明的学科交叉特征，旨
“共性导向，交叉融通”：
在通过交叉研究产出重大科学突破，促进分科知识融通发展为知识体系。
请阐明选择该科学问题属性的理由（800字以内）：
作为全球宽带卫星业务与对地观测遥感应用的重要支撑，巨星座网络在轨处理已成为面向我国大
规模卫星互联网“新基建”建设发展的核心技术储备，也是国家网络强国战略与全球互联网商业服务
共同关注的热点问题。
本项目从满足巨星座网络在轨处理“互联共享、按需服务、海量承载”的实际需求出发，针对巨
星座网络容错能力不足导致其在轨处理效率低下的核心瓶颈，从信息可靠传输、任务稳定计算、数据
鲁棒存储三个维度上，全面开展面向在轨处理的巨星座网络协同编码容错机制的研究。充分分析了巨
星座网络特性和在轨资源属性对网络容错能力和协同容错机制的相互约束关系，拟通过探索并明晰“
大规模在轨处理网络容错能力的多维表征”以及“高动态资源约束下编码容错机制的协同优化”两个
关键科学问题，揭示巨星座网络容错能力受限的内在原因，并给出能够实现多维网络容错能力提升的
协同编码容错机制设计原则。本项目凝练的科学问题来源于阻碍巨星座网络在轨处理顺利执行背后的
核心技术难点，具有鲜明的目标导向特征。
本项目将分布式编码容错技术与卫星通信网络技术相融合，详细阐述了协同编码容错机制的设计
方案和研究路线，在研究过程中逐步实现网络容错能力的多维表征和编码容错机制的协同优化，从而
大幅度提升巨星座网络能够保持长期稳定运行的系统级容错能力。本项目所提出的协同编码容错机制
，可为我国巨星座网络在轨处理提供相应的容错技术保障，从而推动大规模综合化卫星网络处理平台
走向实际应用。
综合上述理由，本项目的科学问题属性属于需求牵引类研究范畴。
NSFC 2022
第 3 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 5 页 ===
主要参与者（注: 主要参与者不包括项目申请人）
编号
姓名
出生年月
性别
职 称
学 位
工作单位
项目分工
电话
证件号码
1
史瑶
1993-01-11
女
讲师
博士
哈尔滨工业大学
协作传输机制
0755-22676371
4**************3
2
冯博文
1992-03-10
男
无
博士
哈尔滨工业大学
数据修复机制
18566688837
2**************X
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
总人数
高级
中级
初级
博士后
博士生
硕士生
11
0
2
0
1
2
6
NSFC 2022
第 4 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 6 页 ===
国家自然科学基金项目资金预算表
项目申请号：6227010688
项目负责人：顾术实
金额单位：万元  
序号
科目名称
金额
1
一、项目直接费用合计
55.0000
2
1、 设备费
10.0000
3
其中：设备购置费
10.0000
4
2、 业务费
29.0000
5
3、 劳务费
16.0000
6
二、 其他来源资金
0.0000
7
三、 合计
55.0000
注：请按照项目研究实际需要合理填写各科目预算金额。
NSFC 2022
第 5 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 7 页 ===
 
预算说明书 
（请按照《国家自然科学基金项目申请书预算表编制说明》等的有关要求，按照政策相符性、目标相关性和经济合
理性原则，实事求是编制项目预算。填报时，直接费用应按设备费、业务费、劳务费三个类别填报，每个类别结合
科研任务按支出用途进行说明。对单价≥50万元的设备详细说明,对单价＜50万元的设备费用分类说明，对合作研究
单位资质及资金外拨情况、自筹资金进行必要说明。） 
 
1. 设备费（总计10.00万元） 
（1）设备购置费（10.00万元）：主要用于购置 DELL PowerEdge R740 型号高性能服务器2台，用于巨星座在轨处理
网络拓扑模型分析及在轨处理协同编码容错机制仿真测试，单价50000元/台，合计10.00万元。 
（2）设备试制费：无 
（3）设备升级改造与租赁费：无 
 
2. 业务费（总计29.00万元） 
（1）材料费（6.00万元）：主要用于购买项目研究过程中仿真测试平台搭建所消耗的各类芯片、元器件、线缆等材
料。 
根据本项目研究需求，材料费预算明细如下： 
元器件名称 
单位 采购数量 
单价（元） 
总计（元） 
功能用途 
数据存储芯片 
块 
2 
5000 
10000 
仿真容错机制性能 
GPU加速显卡 
块 
2 
20000 
40000 
模拟卫星仿真场景 
外围条件 
套 
1 
10000 
10000 
配套材料 
合计 
6.00万元 
（2）差旅/会议/国际合作与交流费（总计8.00万元）：主要用于支付项目研究过程中开展学术调研交流和参加学术
会议等所发生的外埠差旅费。 
差旅费（6.00万元）：项目组成员计划国内现状调研和参加国内各类学术会议每年3人次，5000元/人/次，4年
共计6.00万元。 
国际合作与交流费（2.00万元）：项目组计划出访国外著名大学或参加国际学术会议4人次，5000元/人/次，共
计2.00万元。 
（3）出版/文献/信息传播/知识产权事务费（总计15.00万元）：主要用于支付项目研究过程中的出版费、资料费、
文献检索费、专利申请及其他知识产权事务等费用。 
论文版面费，EI会议文章注册费约6000元/篇，预计发表8篇，计4.80万元；SCI文章，中文期刊版面费约10000
元/篇，英文期刊版面费6000元/篇，预计发表2篇中文期刊，6篇英文期刊，计5.60万元。 
专利注册费，专利注册费约6000元/篇，预计申请6项专利，计3.60万元。 
其他文献检索、图书购买费用，计1.00万元。 
 
3. 劳务费：（总计16.00万元）：主要用于支付项目研究过程中研究生的劳务费用。 
博士研究生平均每年参与项目8个月，1000元/人/月，项目组每年投入2名博士生，4年劳务费计6.4万元。 
硕士研究生平均每年参加项目10个月，600元/人/月，项目组每年投入4名硕士生，4年劳务费计9.6万元。 
 
上述申请经费预算符合《国家自然科学基金项目资助经费管理办法》要求。 
 
 
NSFC 2022
第 6 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 8 页 ===
报告正文 
参照以下提纲撰写，要求内容翔实、清晰，层次分明，标题突出。
请勿删除或改动下述提纲标题及括号中的文字。 
（一）立项依据与研究内容（建议8000 字以下）： 
1．项目的立项依据（研究意义、国内外研究现状及发展动态分析，
需结合科学研究发展趋势来论述科学意义；或结合国民经济和社会发
展中迫切需要解决的关键科技问题来论述其应用前景。附主要参考文
献目录）； 
1.1 研究意义 
低轨宽带星座具有大容量、高速率、全覆盖等特点，是构建“网络无所不至，
服务无处不在”的新一代泛在通信与服务一体化网络的重要基础设施。近年来，
OneWeb、SpaceX、亚马逊等国外商业航天公司加紧密集部署巨型低轨通信星座，
旨在为全球提供无缝覆盖的互联网服务[1]。为在国际竞争中抢占频率与轨位资源，
我国在科技创新2030“天地一体化信息网络”重大项目的推动下，开展了“鸿雁
星座”、“虹云工程”、“天启星座”等低轨星座的部署规划工作。2020 年4 月，我
国将卫星互联网纳入通信网络基础设施“新基建”范畴[2]，并于2020 年9 月以“GW
公司”名义向国际电信联盟（ITU）递交了宽带星座频谱和轨位申请。随着轻量化
载荷、一箭多星、批量化生产等卫星应用技术的快速发展[3]，全球低轨星座的数量
将极速激增至数万颗之多，巨星座网络（Mega-constellation networks, MCNs）的部
署规划已成为国家网络强国战略与全球互联网商业的热点问题。 
表1 国内外典型巨星座网络规划分析 
星座计划 
轨道高度 
卫星数量 
星间链路 
OneWeb 
1200 km 
在轨394 颗 
计划720 颗 
无 
Starlink 
340 km、550 km、1150 km 
在轨1842 颗 
计划42000 颗 
有 
Kuiper 
590km、610km、630km 
计划3236 颗 
无 
GW 
590km、600km、1145km 
计划12992 颗 
有 
 
在轨处理（On-board processing，OBP）是利用高性能星载处理器与卫星组网
交换能力，实现天基多源数据融合与在轨智能计算的新兴技术[4,5]，能够有效缓解
星地通信链路负载压力，服务多样化宽带信息业务与海量化对地观测应用[6]。随着
星上处理、硬件设备和嵌入式系统的快速发展，以及高速星间激光/微波链路的广
泛使用，巨星座多星组网的在轨处理能力，逐渐成为国内外研究学者关注的热点。
空间计算、云原生卫星、6G 网络“多域融合、算网一体”[7]等理念的相继提出，
均促使巨星座网络的服务范畴从传统的广域通信覆盖演进为传输-计算-存储协同
NSFC 2022
第 7 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 9 页 ===
一体的在轨智能处理，形成“互联共享、按需服务、海量承载”的综合化天基信
息处理网络平台，在宽带传输、物联网应用、军事侦查、气象服务、海洋监测、
地形勘探等军民融合领域均可发挥巨大作用[8]。 
宽带业务
协同控制
中心
海洋监测
军事侦查
环境监控
在轨协同计算
在轨信息存储
在轨数据融合
物联网
大数据
 
图1 巨星座网络在轨处理的应用场景 
虽然巨星座在覆盖范围、通信速率、器件水平上已具备实现多星组网在轨处
理的基本条件，但由于巨星座是以天基卫星平台为主体的网络形式，具有节点数
量极多、链路间歇中断、资源分布不均、设备寿命有限等突出特点，存在频繁多
发的系统故障和节点失效等网络差错影响，对在轨处理中信息传输、任务计算和
数据存储等多个处理环节的高效运行均产生了阻碍作用，无法满足上述“互联共
享、按需服务、海量承载”的愿景需求。具体表现形式为： 
（1）动态的拓扑结构和时变的链路状态降低了巨星座网络信息传输的可靠性：
巨星座网络依靠大量星间链路实现多星互联，跨轨道面星间链路状态具有周期间
断的特点，使得网络拓扑的连通性显著下降。同时，星间激光链路易受到采集跟
踪精度、背景辐射噪声及卫星平台摄动等影响，导致信道突发中断和传输误码。
另外，大规模星座中易出现节点损毁或链路临时失效的情况，有限的链路带宽造
成局部网络拥塞和数据丢包，无法满足巨星座在轨信息可靠传递的互联共享需求。 
（2）多样的并发请求和异构的算力资源影响了巨星座网络任务计算的稳定性：
巨星座对全球区域进行均等化覆盖，但不同纬度的对地观测任务量呈现非均匀特
性，南北纬30-50 度区域人口密集且业务类型多样，并发的复杂业务请求导致局部
网络高负荷运转，易产生间歇性计算迟滞的现象。同时，在轨通用处理器包含DSP、
CPU、GPU、FPGA 等多种类型，异构的计算资源难以得到充分协同利用，计算效
能输出的稳定性受限，无法为多样化在轨处理任务提供按需服务的稳定算力支撑。 
（3）频繁的系统故障和有限的卫星寿命削弱了巨星座网络数据存储的鲁棒性：
巨星座在轨处理需要大量对地观测数据作为信息支撑，但由于空间碎片、太阳风
暴、宇宙射线等不可抗力因素造成设备永久性损坏，难以保障在轨重要存储数据
NSFC 2022
第 8 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 10 页 ===
的长期可用。低成本小型化卫星的使用寿命仅为5 年左右，需频繁进行“补网”
操作，星载分布式存储系统的鲁棒性维护，需要耗费大量的带宽资源成本，网络
自愈恢复能力薄弱，难以满足巨星座在轨处理数据存储的海量承载需求。 
综合上述难点分析，巨星座网络的信息传输可靠性、任务计算稳定性及数据
存储鲁棒性，均受到在轨空间环境、在轨网络资源、在轨系统故障等外在与内在
多重因素的联合制约，使得巨星座在轨处理的必要需求难以满足。归纳其本质原
因，在于巨星座的网络容错性受限，无法有效提供抵抗传输差错、保持计算稳定、
维护数据可用的网络容错能力。巨星座网络的在轨处理是基于多星协作的分布式
处理系统，在网络传输、计算、存储等功能上必须设计相应的多点协同容错机制，
使得网络系统在链路失效甚至节点损毁的情况下依然能够正常运行。因此，开展
巨星座网络容错性理论分析与网络协同容错机制设计的相关研究，是推动巨星座
在轨处理高效执行、满足任务需求的关键所在，具有重要的研究意义与研究价值。 
分布式编码（Distributed codes），亦称为网络编码[9]，是一种用于分布式网络
中抵抗数据传输差错的典型容错技术。近年来，随着大型分布式系统的广泛使用，
分布式编码在多播传输、协同计算、分布式存储等领域都取得了较为丰硕的理论
与应用研究成果[10,11]。巨星座网络拥有大量具备计算和存储能力的分散节点和高
速互联的星间通信链路，为分布式编码的在轨实现提供了可行契机。可采用分布
式编码设计相应的传输、计算、存储协同编码容错机制，提升巨星座在轨处理执
行中的多维网络容错能力。但是，由于巨星座网络结构的特殊性，传统分布式编
码在巨星座网络系统中难以获得较高的系统级容错效率，造成时间、带宽、功耗
等资源成本开销较大。链路间歇中断、节点算力异构、数据存储分散等应用限制
条件，也对编码算法和码字结构的网络匹配性设计提出了更高的要求。因此，如
何在大规模卫星网络中综合提升各类编码容错机制的协同容错效率，也是值得进
一步深入探索的关键难题。 
当前，我国在智能卫星应用和大规模星座部署方面已具备雄厚的国力水平与
丰富的技术储备。面向未来巨星座网络在轨信息处理的重大应用需求，应适时开
展巨星座网络在轨信息传输、在轨任务计算、在轨数据存储方面的基础理论研究
工作，率先抢占国际卫星网络科技领域前沿。针对大规模星座网络中节点损毁、
故障频发等系统差错问题，亟需突破巨星座网络容错性理论与协同编码容错机制
设计的核心瓶颈，在可靠信息传输、任务稳定计算、数据鲁棒存储方面提出创新
的研究思路和可行的技术方案，综合提升巨星座网络系统的多维容错能力，以保
障在轨处理网络平台的长期稳定运行。 
1.2 国内外研究现状及发展动态分析 
1.2.1 单星在轨处理系统容错机制研究现状 
宇宙空间环境中存在大量高能带电粒子，会在器件内部与半导体材料相互作
用，形成电离辐射效应，例如单粒子闩锁（Single event latchup，SEL）和单粒子
NSFC 2022
第 9 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 11 页 ===
翻转（Single event upsets，SEU）等，造成逻辑器件的状态改变或永久损伤，严重
影响星载存储器和处理器的使用寿命[12]。根据有关资料统计分析显示，空间辐射
效应造成的航天器故障占故障统计总数的71%，是航天器故障的主要来源[13]。因
此，包括处理器、存储器在内的星载计算机系统在设计时，都需要考虑设计相应
的容错机制，以保障计算和存储功能的正常运行。 
星载处理器早期普遍采用抗辐照设计，但工艺复杂、成本昂贵且处理性能有
限。近年来，小型化批量生产的多功能卫星对处理器性能的要求逐步提升，以NASA
为代表的航天机构开始着眼于使用商用器件（Commercial off the shelf, COTS）替
代专用抗辐照处理器。例如，表2 中美国RAD5545 星载处理器基于商用Power 架
构设计，具有更快的处理速度和更好的软件兼容性。星载商业器件必须采用硬件
冗余的容错结构以提高可靠性，目前较为常见的容错方法有双模冗余和三模冗余
[14]。哈尔滨工业大学自主研发的“紫丁香二号”卫星处理器采用了双模冗余设计[15]，
当ARM 主机发生故障时，仲裁单元将切换至备用工作机。中科院空间科学与引用
中心基于LEON3 处理器实现了三模冗余容错技术，对同一任务处理仲裁单元将进
行“三选二”的判决，以保障计算的可靠性[16]。 
表2 国内外宇航级在轨处理器性能指标 
 
型号 
时间 
架构 
性能 
功耗 
抗辐射 
国内 
龙芯1E 
2014 
32 位MIPS 架构 
200MIPS 
<3W 
100krad(Si) 
龙芯1E300 2017 
64 位MIPS 架构 
400MIPS 
<3W 
100krad(Si) 
国外 
GR740 
2016 
32 位8 核SAPRC 
1GIPS 
1.8W 
300krad(Si) 
RAD5545 
2017 
64 位4 核Power 架构 
5.6GOPS 17.7W 
1Mrad(Si) 
HPSC 
在研 64 位8 核ARM-CortexA53 
15GOPS 
<7W 
1Mrad(Si) 
 
星载存储器一般采用SDRAM，FLASH 等固态存储介质。在容错能力的保障
上，包含系统级容错和数据级容错[17]。系统级容错采用冗余备份的方式应对大批
存储单元的永久性损坏。数据级容错主要针对存储器中个别数据发生错误时，采
用错误检测与纠正（Error detection and crrection, EDAC）、纠错编码（Error correcting 
codes, ECCs）、循环冗余校验（Cyclic redundancy check, CRC）等的方法来进行解
决[18]。目前比较常用的是Hamming 码和RS 码。例如，我国研制应用于空间望远
镜的存储系统采用了Hamming 码，可检测出两位错误，纠正一位错误[19]；中国科
学院空间应用中心设计的星载固态存储器，以NAND Flash 作为存储介质，采用了
RS（256, 252）码[20]；美国2002 年发射的Alsate-1 卫星同样采用了RS (256, 252)
纠错码，并使用三个相同的存储模块执行相同操作以提高可靠性[21]。 
1.2.2 卫星网络容错机制研究现状 
当前，卫星网络中针对链路中断、数据丢失、节点失效等问题的容错机制大
部分集中于星座网络构型的连通性分析、星间动态拓扑的传输路径规划及星间可
靠链路的路由协议设计等方面。在卫星分布式计算和存储的容错机制领域，重点
包括天基边缘计算卸载、计算任务调度、大数据卫星存储管理和数据修复与传输
NSFC 2022
第 10 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 12 页 ===
等方面的研究工作。 
卫星网络容错机制
星座链路的
连通性分析
星座拓扑的
动态性分析
星间链路的
可靠性分析
星座构型设计
连接图路径规划
路由协议设计
天基边缘计算卸载
计算任务调度
卫星大数据管理
数据修复与传输
 
图2 卫星网络容错机制的研究方向 
在星座链路的连通性以及星座构型设计方面，文献[22]提出了一种估算巨星座
网络两点间最少连接跳数的路径决策算法，分析了Starlink星座中地面用户经纬度、
星座相位偏移参数对连接跳数的影响。文献[23]对Starlink 星座第一期1584 颗卫星
进行了星间激光链路可见性分析，对同轨道面、相邻轨道面和跨轨道面的链路特
性进行了详细描述。亦有研究针对LEO 星座链路中断时间和过顶时间的分布特性，
对星座构型进行设计[24][25]。另外，国内研究学者采用图论中k-连通度和k-边连通
度的分析方法[26][27]，将星间链路建模为邻接矩阵，针对不同低轨星座的可用性、
稳健性进行定量分析，给出了合理的评判标准。 
在星座拓扑的动态性以及路径规划方面，研究人员采用“快照”法处理可预测的
卫星网络动态拓扑问题，通过按一定规则将传输周期划分“时隙”，针对单个时隙
的准静态拓扑进行“连接规划设计”（Contact plan design，CPD）[28]。并结合电池
储备能量[29]、转发机制[30]、空时拓扑图[31]和链路连接容量[32]，在低轨星座、全球
导航卫星系统、多层卫星网络和天地一体化网络中，开展传输路径规划的研究，
以保障链路连通时间和通信效率。 
在星间链路可靠性及路由协议设计方面，文献[33]提出一种基于星间链路状态
的路由算法，根据卫星坐标和相对位置，建立了逻辑拓扑结构，避免碰撞失效节
点。文献[34]根据卫星网络状态及地面目的节点距离计算转移概率，以实现区域分
流，并以时延为约束进行多径搜索，获得最佳路径及备选路径，以缓解动态漏斗
型拥塞。文献[35]针对极轨道星座网络，给出单链路失效对可行路径上最少连接跳
数的影响，分析网络对链路失效的抵抗能力。 
天基计算是实现智能化星地协同、云边协同的关键技术[36]。多星协作计算大
多基于时空扩展图模型，对计算任务的调度处理进行统一规划[37]，相关研究包含
多跳网络[38]、分布式网络[39]等LEO 星座计算卸载场景，目标是缩小计算延迟和传
输开销。针对节点故障差错，文献[40]提出了一种基于实时任务调度的遥感卫星容
错处理机制，采用主备份策略，避免将对地观测任务部署在故障卫星上，并优化
了在轨数据调度的成功观测概率。 
在卫星数据存储性能提升方面，文献[41]利用LEO 星载大容量存储设备的高
速互联，建立低轨星座大数据存储中心，并设计了基于最小列队积压的数据流调
度策略，实现数据获取量最大化和能耗开销最小化。文献[42]研究了地面数据中心
NSFC 2022
第 11 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 13 页 ===
和卫星网络一体化的分布式存储系统架构，预测了卫星由于灾害造成的链路中断，
采用纠删编码和简单复制的方法，缩短了恢复数据所需的传输时间。文献[43]考虑
了不同卫星节点存储空间的占用率问题，结合信道条件、接入协议和排队策略，
给出了两层卫星网络中节点存储占用量的估计方法。 
1.2.3 分布式编码容错技术研究现状 
编码技术来源于无线信道的抗差错传输，随着网络信息流理论的快速发展，
分布式编码（网络编码）已成为多播网络传输、分布式存储和分布式计算领域的
典型容错技术。其核心理念是通过将原始数据拆分为信息块，利用编码交织的线
性组合，来抵抗网络中链路中断、节点故障、数据失效等差错影响。针对系统特
性与功能需求的不同，各类应用于分布式系统的编码容错技术相继应运而生。 
分布式编码
（网络编码）
多播网络传输
（2000年）
分布式存储
（2007年）
分布式计算
（2018年）
赋予节点计算能力
提升传输效率
修复失效节点
减少修复带宽
抵抗计算迟滞
减少数据传输量
 
图3 分布式编码容错技术的研究方向 
分布式编码的多播传输已在经典信息论、无线通信网络、移动自组织网络、
视频流传输等领域获得了理论突破和重大应用。近年来，将分布式编码用于卫星
网络的研究成果不断涌现，主要思路是利用随机线性网络编码方案，形成编码组
播或多路径编码传输效果，从而增强网络端到端吞吐量，减少反馈次数，有效降
低时延开销。相关技术的结合要点包含多跳卫星时分双工系统[44]、卫星网络路由
协议[45]、卫星物联网自动重传请求[46]、以及卫星-云一体化网络流传输[47]等多个方
面，均取得了相应的网络性能提升。但在卫星网络分布式计算、星上数据分布式
存储领域还缺乏相应分布式编码容错机制的研究成果，有待于进一步深入探索和
挖掘。以下简要分析分布式编码容错技术在分布式计算与分布式存储领域的最新
研究进展。 
（1）分布式编码计算容错 
为了提高机器学习和大数据分析等大规模矩阵-向量乘法计算的执行效率，通
常将任务拆分为多个子任务进行并行处理。但在实际系统工作中，由于计算资源
占用、数据包丢失和硬件故障等原因，导致部分节点的计算时延过高，被称为“慢
节点（Straggler）”，对系统整体计算性能造成了极大影响。针对该问题，2018 年 K. 
Lee, M. Lam 等学者提出了基于网络编码的编码计算（Coded computing）方案，使
用纠错码提供冗余的方式，抵抗Straggler 影响，降低任务完成时延[48]。 
编码计算的理论和应用研究开展的极为广泛，重点集中在实现计算负载均衡
[49]、码字优化[50]、慢节点检测[51]方面。在无线通信[52]、边缘计算[53]、联邦学习[54]
等领域，编码计算也成为很多学者关注的焦点。同时，点积码[55]、多项式码[56]、
NSFC 2022
第 12 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 14 页 ===
稀疏码[57]等新兴码字不断涌现，在矩阵-向量、矩阵-矩阵、梯度下降、非线性求解
等大规模乘法计算领域都取得了突破进展。在这其中，无速率码计算（Rateless 
coded computing）用于分布式计算系统[58]，可实现计算结果实时反馈，更好地解
决了异构算力带来的计算延迟问题，并具有复杂度低、容错性高、冗余比例小等
突出特点，对异构网络条件的兼容性做到更加完美的平衡。 
（2）分布式编码存储容错 
云存储系统在大量服务器上存储应用数据，由于硬盘损坏、软件故障以及升
级维护等原因，节点失效（Failure）的情况时有发生，造成数据丢失，系统瘫痪不
可用。传统应对措施包括三倍复制和RS 纠删码，但存在存储空间浪费和修复带宽
较高的问题。2007 年，Dimakis 等人借鉴网络编码的思想提出了再生码
（Regenerating codes）[59]，利用网络信息流理论开创性的给出了容量性能折衷公
式，描述了存储容量与修复带宽之间的权衡。随后，基于其概念的最小存储再生
码（MSR）和最小带宽再生码（MBR）等码字结构也相应得到了实现[60]。再生码
保留了RS 码的MDS 性质，减少了单个节点的修复带宽，但对帮助节点数量和编
译码复杂度要求较高。 
近年来，再生码的相关研究涉及复杂度优化[61]、码字参数选择[62]、码字结构
设计[63]、网络拓扑自适应[64]、子分组化水平[65]等众多方向，在移动自组织网络[66]、
无线通信网络[67]及信息安全[68]方面均有相应的突破进展。其中针对集群系统的广
义再生码（Generalized regenerating codes，GRC）通过双重编码架构[69]，适用于跨
集群的条带化存储系统，做到了存储负载和跨集群修复带宽之间更好的权衡，在
复杂大规模存储网络中具有重要的研究价值。 
1.2.4 现有技术存在的问题与不足 
通过对上述研究现状的综合分析，可以发现，当前卫星网络在抵抗系统差错
和节点失效影响的容错机制研究中，以及在分布式编码容错技术应用于卫星网络
的相关研究中，仍然存在以下问题亟待解决： 
（1）传统单星在轨处理系统和器件的容错性设计难以满足巨星座网络在轨处
理的容错需求：当前在卫星载荷的研发中，尤其在计算、存储等星载设备和处理
器架构的设计上，均有考虑系统突发性故障对卫星平台功能的影响，设立了相应
的容错性保障。但针对大规模卫星频繁突发的系统故障以及网络存储、算力异构
的特点，单点容错机制无法充分满足巨星座网络的实时互联互通、计算负载均衡
与数据长期可用等在轨处理需求，难以提供可靠、稳定、鲁棒的系统级网络容错
能力。因此，利用巨星座多点协作共享、任务分散处理的特点，开展分布式传输-
计算-存储协同容错机制的相关研究工作，是解决大规模星座网络容错能力不足的
重要途径。 
（2）现有卫星网络容错机制的研究目标相对孤立单一，缺乏对卫星网络整体
容错性能的表征分析方法：目前已有相关文章对卫星网络连通性、动态性和可靠
性进行了分析，其目标是对星座构型、路由规划及协议设计方面提供理论指导。
NSFC 2022
第 13 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 15 页 ===
而在卫星分布式计算和卫星网络存储方面，尚缺乏相关网络容错性的理论分析研
究成果。巨星座在轨处理是融合传输、计算、存储等多方面网络技术的综合应用，
为指导相关网络协同容错机制的设计，亟需一种能够全面表征和评估网络系统容
错能力的理论分析方法，将现有的卫星网络传输、计算、存储方面的容错机制进
行有机融合，为巨星座网络的在轨处理服务提供全面的技术支撑。 
（3）分布式编码是网络协同容错机制设计的重要手段，但其应用于卫星网络
的主要瓶颈是对在轨网络资源约束的匹配性设计尚不完善：巨星座网络具有链路
连接断续、算力部署异构和存储节点分散等在轨资源属性，使得传统分布式编码
容错机制的容错效率受到限制，造成传输延迟高、计算效率低、修复成本开销大
等问题。因此，若基于分布式编码设计巨星座网络的协同容错机制，必须突破在
轨网络资源的约束条件，在网络连通度、拓扑动态结构、资源使用成本等特殊性
要求方面进行具有匹配性的算法设计和协同优化，才能充分发挥分布式编码多点
协作、负载均衡、灵活可控的突出优势，多角度综合提升巨星座网络的容错能力。 
针对上述研究现状的不足之处，本项目面向国家大规模低轨星座在轨信息处
理的发展需求，以提升巨星座网络容错能力为目标，从信息可靠传输、任务稳定
计算和数据鲁棒存储三个维度，深入探索大规模在轨处理网络容错能力的多维表
征方法，开展巨星座网络容错性和协同编码容错机制的相关研究。拟采用无速率
码、广义再生码等分布式编码容错技术，设计并优化在轨高动态资源约束下的多
路径编码协作传输机制、自适应编码计算分配机制和非对称编码数据修复机制，
全面提升巨星座网络的容错能力和协同容错机制的容错效率，满足大规模星座在
轨处理的应用需求，为我国卫星互联网发展提供技术参考与理论支撑。 
 
参考文献 
[1]. 陈东, 裴胜伟, 黄华, 韩绍欢. 全球巨型低轨星座通信网络发展、特征与思考[J]. 国际太空, 
2020 (04): 42-47. 
[2]. 赛迪顾问物联网产业研究中心, 新浪5G.“新基建”之中国卫星互联网产业发展研究白皮书
[EB/OL]. https://n2.sinaimg.cn/tech/cbc3161f/20200528/SatelliteInternetWhitePaper.pdf  
[3]. 李峰, 禹航, 丁睿, 王宁远, 王雨琦, 周志成. 我国空间互联网星座系统发展战略研究[J]. 
中国工程科学, 2021, 23(04): 137-144. 
[4]. 何友, 姚力波, 李刚, 刘瑜, 杨冬, 李文峰. 多源卫星信息在轨融合处理分析与展望[J]. 宇
航学报, 2021, 42(01): 1-10. 
[5]. 李刚, 刘瑜, 张庆君. 多源卫星数据在轨智能融合技术[J].中国科学基金, 2021, 35(05): 
708-712. 
[6]. 岳兆娟, 秦智超, 李俊, 时月茹. 面向天地一体化信息网络的在轨信息处理机制设计[J]. 
中国电子科学研究院学报, 2020, 15(06): 580-585. 
[7]. IMT-2030(6G) 推进组，6G 网络架构愿景与关键技术展望白皮书[R]，2021. 
[8]. 王密, 杨芳. 智能遥感卫星与遥感影像实时服务[J]. 测绘学报, 2019, 48(12): 1586-1594. 
[9]. R. Alshwede, N. Cai, S.-Y. R. Li, R. W. Yeung, Network information flow[J], IEEE Trans. Inf. 
Theory, vol. 46, pp. 1204-1216, Feb. 2000. 
NSFC 2022
第 14 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 16 页 ===
[10]. S. B. Balaji, et al., Erasure coding for distributed storage: An overview[J], Sci. China Inf. Sci., 
vol. 61, no. 1421848, pp. 1-43, Oct. 2018. 
[11]. J. S. Ng, et al., A comprehensive survey on coded distributed computing: fundamentals, 
challenges, and networking applications[J], IEEE Commun. Surv. Tutor., vol. 23, no. 3, pp. 
1800-1837, 3rd-Quar, 2021. 
[12]. 赵兴, 栗伟珉, 程向丽, 王曦煜, 李昂阳. 航天空间环境单粒子效应研究[J]. 电子制作, 
2021(13): 87-89. 
[13]. 薛玉雄, 杨生胜, 把得东, 等. 空间辐射环境诱发航天器故障或异常分析[J]. 真空与低温, 
2012, 18(02): 63-70. 
[14]. Z. Gao, et al., Radiation tolerant viterbi decoders for on-board processing (OBP) in satellite 
communications[J], China Communications, vol. 17, no. 1, pp. 140-150, Jan. 2020. 
[15]. 新华网：国内首颗大学生自主研制管控纳卫星上天
[EB/OL]．http://big5.news.cn/gate/big5/education.news.cn/2015-09/21/c_128249957.htm 
[16]. L. Pei, Z. Jian, A high reliable SOC on-board computer based on Leon3[C], 2012 IEEE 
International Conference on Computer Science and Automation Engineering (CSAE), 2012, pp. 
360-363. 
[17]. MC Smayling, GG Marotta, G Santin, Flash memory block or sector clear operation[P]. US 
Patent, 2000. 
[18]. George A D, Wilson C M, Onboard processing with hybrid and reconfigurable computing on 
small satellites[J]. Proceedings of the IEEE, 2018, vol 106, no. 3, pp. 458-470, Mar. 2018. 
[19]. 王芳, 李恪, 苏林, 等. 空间太阳望远镜的星载固态存储器研制[J]. 电子学报, 2004, 32(3): 
472-475 
[20]. 宋琪, 邹业楠, 李姗, 安军社, 朱岩. 卫星固态存储器数据容错设计与机制[J]. 国防科技
大学学报, 2016, 38(1): 101-106. 
[21]. Y. Bentoutou, A real time EDAC system for applications onboard earth observation small 
satellites[J], IEEE Trans. Aerosp. Electron. Syst., vol. 48, no. 1, pp. 648-657, Jan. 2012. 
[22]. Q. Chen, G. Giambene, L. Yang, C. Fan, X. Chen, Analysis of inter-satellite link paths for LEO 
mega-constellation networks[J], IEEE Trans. Veh. Technol., vol. 70, no. 3, pp. 2743-2755, Mar. 
2021. 
[23]. A. U. Chaudhry, H. Yanikomeroglu, Laser Intersatellite links in a starlink constellation: a 
classification and analysis[J], IEEE Veh. Technol. Mag., vol. 16, no. 2, pp. 48-56, Jun. 2021. 
[24]. C. Chang, Y. Liu, and Y. Wang, Failure-based multi-controller placement in software defined 
satellite networking[C], 2021 IEEE International Conference on Communications Workshops 
(ICC Workshops), Jun. 2021, pp. 1-6. 
[25]. A. Al-Hourani, A tractable approach for predicting pass duration in dense satellite networks[J], 
IEEE Commun. Lett., vol. 25, no. 8, pp. 2698-2702, Aug. 2021. 
[26]. 韩松辉, 归庆明, 李建文, 杜院录. 混合星座星间链路的建立以及连通性和稳健性分析[J]. 
武汉大学学报（信息科学版）, 2012, 37(09): 1014-1019. 
[27]. 孙远辉, 韩潮. LEO 移动通信星座的网络综合连通度研究[J]. 航空学报, 2012, 33(02): 
327-334. 
[28]. J. A. Fraire, J. M. Finochietto, Design challenges in contact plans for disruption-tolerant 
satellite networks[J], IEEE Commun. Mag., vol. 53, no. 5, pp. 163-169, Mar. 2015. 
[29]. J. A. Fraire et al., Battery-aware contact plan design for LEO satellite constellations: the 
ulloriaq case study[J], IEEE Trans. Green Commun. Netw., vol. 4, no. 1, pp. 236-245, Mar. 
NSFC 2022
第 15 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 17 页 ===
2020. 
[30]. Z. Yan et al., Distributed contact plan design for GNSSs[J], IEEE Trans. Aerosp. Electron. Syst., 
vol. 56, no. 1, pp. 660-672, Feb. 2020. 
[31]. C. Q. Dai, L. Guo, S. Fu, Q. Chen, Contact plan design with directional space-time graph in 
two-layer space communication networks[J], IEEE Internet Things J., vol. 6, no. 6, pp. 
10862-10874, Dec. 2019. 
[32]. Y. Wang et al., Dynamic contact plan design in broadband satellite networks with varying 
contact capacity[J], IEEE Commun. Lett., vol. 20, no. 12, pp. 2410-2413, Dec. 2016 
[33]. 张路, 燕锋, 章跃跃, 等. 基于星间链路状态的低轨卫星网络路由算法[J]. 上海航天(中英
文), 2021, 38(04): 92-100. 
[34]. 周雅, 谢卓辰, 刘沛龙, 刘会杰. 基于区域分流的低轨卫星星座星间负载均衡路由算法[J]. 
中国科学院大学学报, 2021, 38(05): 687-695. 
[35]. 赵扬, 方海, 孙召, 陶孝锋. 极轨星座分布式数据报路由链路失效影响研究[J]. 空间电子
技术, 2021, 18(03): 36-42. 
[36]. 吴晓文, 焦侦丰,凌翔. 6G 中的卫星通信高效天基计算技术[J]. 移动通信, 2021, 45(04): 
50-53+78. 
[37]. 国晓博, 任智源, 程文驰, 纪哲. 低轨卫星网络中业务图驱动的星间协作计算方案[J]. 天
地一体化信息网络, 2021, 2(02): 35-44. 
[38]. 马步云, 任智源, 李赞. 基于多维梯度的卫星集群高可靠协同计算方法[J]. 中兴通讯技术, 
2021, 27(05): 36-42. 
[39]. C. Wang, Z. Ren, W. Cheng, S. Zheng, H. Zhang, Time-expanded graph-based dispersed 
computing policy for LEO space satellite computing[C], 2021 IEEE Wireless Communications 
and Networking Conference (WCNC), Mar. 2021, pp. 1-6. 
[40]. X. Zhu, et al., Fault-tolerant scheduling for real-time tasks on multiple earth-observation 
satellites[J], IEEE Trans. Parallel Distrib. Syst., vol. 26, no. 11, pp. 3012-3026, Nov 2015. 
[41]. H. Huang, S. Guo, W. Liang, K. Wang, and Y. Okabe, Coflow-like online data acquisition from 
low-earth-orbit datacenters[J], IEEE Trans. Mob. Comput., vol. 19, no. 12, pp. 2743-2760, Dec. 
2020. 
[42]. K. Suto, P. Avakul, H. Nishiyama, N. Kato, An efficient data transfer method for distributed 
storage system over satellite networks[C], 2013 IEEE 77th Vehicular Technology Conference 
(VTC Spring), 2013, pp. 1-5. 
[43]. E. Wang, Q. Dong, Y. Li, Y. Zhang, Estimation of node cache occupancy in satellite storage 
network[J], IEEE Access, vol. 9, pp. 122039-122050, Sept. 2021. 
[44]. M. Esmaeilzadeh, N. Aboutorab and P. Sadeghi, Joint optimization of throughput and packet 
drop rate for delay sensitive applications in TDD satellite network coded systems[J], IEEE 
Trans. Commun., vol. 62, no. 2, pp. 676-690, Feb. 2014. 
[45]. F. Tang, H. Zhang, L. T. Yang, Multipath cooperative routing with efficient acknowledgement 
for LEO satellite networks[J], IEEE Trans. Mob. Comput., vol. 18, no. 1, pp. 179-192, Jan. 
2019. 
[46]. J. Jiao, Z. Ni, S. Wu, Y. Wang, Q. Zhang, Energy efficient network coding HARQ transmission 
scheme for S-IoT[J], IEEE Trans. Green Commun. Netw., vol. 5, no. 1, pp. 308-321, Mar. 2021. 
[47]. T. Do-Duy, M. Á. Vázquez-Castro, Network coding function for converged satellite-cloud 
networks[J], IEEE Trans. Aerosp. Electron. Syst., vol. 56, no. 1, pp. 761-772, Feb. 2020. 
[48]. K. Lee, M. Lam, R. Pedarsani, D. Papailiopoulos, K. Ramchandran, Speeding up distributed 
NSFC 2022
第 16 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 18 页 ===
machine learning using codes[J], IEEE Trans. Inf. Theory, vol. 64, no. 3, pp. 1514-1529, Mar. 
2018. 
[49]. D. Kim, H. Park, J. K. Choi, Optimal load allocation for coded distributed computation in 
heterogeneous clusters[J], IEEE Trans. Commun., vol. 69, no. 1, pp. 44-58, Jan. 2021. 
[50]. J. Kosaian, K. V. Rashmi, S. Venkataraman, Learning a code: machine learning for 
approximate non-linear coded computation, 2018. [Online]. Available: arXiv:1806.01259. 
[51]. B. Tegin, E. E. Hernandez, S. Rini, T. M. Duman, Straggler mitigation through unequal error 
protection for distributed approximate matrix multiplication[J], IEEE J. Sel. Areas Commun., 
vol. 40, no. 2, pp. 468–483, Feb. 2022. 
[52]. K. Li, M. Tao, J. Zhang, O. Simeone, Coded computing and cooperative transmission for 
wireless distributed matrix multiplication[J], IEEE Trans. Commun., vol. 69, no. 4, pp. 
2224-2239, Apr. 2021. 
[53]. J. Wang, et al., Optimal task allocation and coding design for secure edge computing with 
heterogeneous edge devices[J], IEEE Trans. Cloud Comput., early access, doi: 
10.1109/TCC.2021.3050012. 
[54]. S. Prakash et al., Coded computing for low-latency federated learning over wireless edge 
networks[J], IEEE J. Sel. Areas Commun., vol. 39, no. 1, pp. 233-250, Jan. 2021. 
[55]. K. Lee, C. Suh, and K. Ramchandran, High-dimensional coded matrix multiplication[C], Proc. 
IEEE Int. Symp. Inf. Theory (ISIT), Aachen, Germany, 2017, pp. 2418-2422. 
[56]. Q. Yu, M. Maddah-Ali, S. Avestimehr, Polynomial codes: An optimal design for 
high-dimensional coded matrix multiplication[C], Proc. 30th Adv. Neural Inf. Process. Syst. 
(NIPS), Long Beach, CA, USA, 2017, pp. 4403-4413. 
[57]. S. Wang, J. Liu, N. Shroff, Coded sparse matrix multiplication, 2018. [Online]. Available: 
arXiv:1802.03430. 
[58]. A. Mallick, M. Chaudhari, U. Sheth, G. Palanikumar, G. Joshi, Rateless codes for near-perfect 
load balancing in distributed matrix-vector multiplication[C], ACM SIGMETRICS Perform. 
Eval. Rev., vol. 48, no. 1, pp. 95-96, Jun. 2020. 
[59]. A. G. Dimakis, P. B. Godfrey, M. J. Wainwright, K. Ramchandran, Network coding for 
distributed storage systems[C], IEEE INFOCOM 2007-26th IEEE International Conference on 
Computer Communications, 2007, pp. 2000-2008. 
[60]. K. V. Rashmi, N. B. Shah, P. V. Kumar, Optimal exact-regenerating codes for distributed 
storage at the MSR and MBR points via a product-matrix construction[J], IEEE Trans. Inf. 
Theory, vol. 57, no. 8, pp. 5227-5239, Aug. 2011. 
[61]. H. Hou, K. W. Shum, M. Chen, H. Li, BASIC codes: low-complexity regenerating codes for 
distributed storage systems[J], IEEE Trans. Inf. Theory, vol. 62, no. 6, pp. 3053-3069, Jun. 
2016. 
[62]. B. Zolfaghari, et al., Lower bounds on bandwidth requirements of regenerating code parameter 
scaling in distributed storage systems[J], IEEE Commun. Lett., vol. 25, no. 5, pp. 1477-1481, 
May 2021. 
[63]. K. Mahdaviani, A. Khisti, S. Mohajer, Bandwidth adaptive error resilient MBR exact repair 
regenerating codes[J], IEEE Trans. Inf. Theory, vol. 65, no. 5, pp. 2736-2759, May 2019. 
[64]. S. Qu, Q. Zhang, J. Zhang, Y. Sun, X. Wang, Performance analysis on distributed storage 
systems in ring networks[J], IEEE Trans. Veh. Technol., vol. 69, no. 7, pp. 7762-7777, Jul. 
2020. 
NSFC 2022
第 17 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 19 页 ===
[65]. K. V. Rashmi, N. B. Shah, K. Ramchandran, A piggybacking design framework for read-and 
download-efficient distributed storage codes[J], IEEE Trans. Inf. Theory, vol. 63, no. 9, pp. 
5802-5820, Sept. 2017. 
[66]. G. Calis, S. Shivaramaiah, O. Ozan Koyluoglu, and L. Lazos, Repair strategies for mobile 
storage systems[J], IEEE Trans. Cloud Comput., vol. 9, no. 4, pp. 1575-1591, Oct.-Dec.2021 
[67]. Y. Fu, Q. Yu, T. Q. S. Quek,W. Wen, Revenue maximization for content-oriented wireless 
caching networks (CWCNs) with repair and recommendation considerations[J], IEEE Trans. 
Wirel. Commun., vol. 20, no. 1, pp. 284-298, Jan. 2021. 
[68]. F. Ye, S. Liu, K. W. Shum, R. W. Yeung, On secure exact-repair regenerating codes with a 
single pareto optimal point[J], IEEE Trans. Inf. Theory, vol. 66, no. 1, pp. 176-201, Jan. 2020. 
[69]. N. Prakash, V. Abdrashitov, M. Medard, The storage versus repair-bandwidth trade-off for 
clustered storage systems[J], IEEE Trans. Inf. Theory, vol. 64, no. 8, pp. 5783-5805, Aug. 2018. 
 
2．项目的研究内容、研究目标，以及拟解决的关键科学问题（此
部分为重点阐述内容）； 
2.1 研究目标 
本项目以满足巨星座网络在轨处理“互联共享、按需分配、海量承载”的服
务需求为牵引，以全面提升巨星座网络容错能力为总体目标，探索大规模在轨处
理网络容错能力的多维表征方法，深入论证分布式编码容错技术在巨星座网络在
轨处理中应用的可行性，从可靠传输、稳定计算、鲁棒存储三个维度，优化设计
相应的协同编码容错机制，提升系统协同容错效率，从而解决由于巨星座网络容
错能力不足而导致其在轨处理低效的核心瓶颈问题。本项目设立的具体研究目标
可表述为： 
研究目标（1）：通过时变网络连通度的建模，及多路径编码协作传输机制的
设计，实现强抗毁性卫星网络的高效内容分发，解决巨星座在轨处理中信息传输
可靠性不足的问题。 
研究目标（2）：通过在轨计算资源状态的分析，及无速率编码计算分配机制
的设计，实现不同拓扑下异构算力节点的负载均衡，解决巨星座在轨处理中任务
计算稳定性不足的问题。 
研究目标（3）：通过对星座集群划分的方法，及非对称广义再生码数据修复
机制的设计，实现广域在轨失效节点的低成本修复，解决巨星座在轨处理中数据
存储鲁棒性不足的问题。 
2.2 主要研究内容 
研究内容（1）：基于时变网络连通度的多路径编码协作传输机制 
巨星座网络实质上是由分布在近地轨道上的卫星节点组成的互联互通网络，
具有明显的多路径特征，为在轨处理任务的高效分发提供了有利条件。但由于巨
星座网络规模庞大、节点故障概率较高、星间链路在极区易发生中断，信息传输
NSFC 2022
第 18 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 20 页 ===
的可靠性难以保障。本研究内容首先对巨星座网络中节点故障和链路中断的诱发
因素进行分析，建立节点故障概率模型以及网络时变图模型，确定故障因素对网
络拓扑连通性的影响。进而引入时变网络连通度的概念，以评估在有限时间内完
成在轨信息交互的可行网络集合的抗毁性。根据星座构型和链路参数特性，详细
设计星座网络的邻接矩阵和可达矩阵，缩小用于在轨处理分发的网络规模，确定
具有较强抗毁性并满足能耗约束条件的可行路径集合。基于多路径编码容错路由
的思想，在可行路径集合中设计多路径编码协作传输机制，重点考虑不同星间链
路质量和节点失效概率对关键任务端到端丢包率的影响，保障巨星座网络在轨处
理信息分布式传输的可靠性。研究要点包括： 
 节点故障概率与链路中断时间分析 
 巨星座时变网络连通度建模 
 多路径编码协作传输机制设计 
研究内容（2）：基于拓扑感知的自适应无速率编码计算分配机制 
巨星座网络中的计算节点承担着大量在轨处理任务，异构计算资源具有典型
的忙闲状态非均匀特性，极易形成节点“计算迟滞”的现象，无法保障持续稳定
的算力输出，造成在轨处理任务无法按时按需完成。本研究内容首先对巨星座网
络中在轨计算资源的忙闲状态和算力配置进行建模，获得节点发生计算迟滞的概
率分布函数，及算力配置参数矩阵。根据典型在轨处理任务的实际应用需求，将
可用于在轨分布式计算的网络拓扑分类为多跳网络和棋盘网络，将计算任务依据
矩阵-向量乘法规则进行拆分和编码交织，并设计具有灵活自适应性的无速率编码
计算容错机制。进而采用拓扑感知的手段，将计算任务在不同网络拓扑中的节点
上进行合理的在线部署，实现在轨分布式计算网络的负载均衡，缩短任务处理时
延，提高计算成功率。并根据异构的节点计算和传输资源属性，优化各节点批处
理计算结果的回传次序，避免链路碰撞，保障计算任务能够按时按需完成，提高
巨星座在轨处理任务计算的稳定性。研究要点包括： 
 在轨计算资源忙闲状态建模 
 自适应无速率编码计算理论分析 
 基于拓扑感知的计算负载均衡分配 
研究内容（3）：基于集群划分的非对称可再生编码数据修复机制 
巨星座网络在轨处理依赖于大量天基存储数据的信息支撑，而低轨卫星本身
使用寿命有限，且受到多发故障因素的影响，造成存储器失效、重要数据丢失，
且在轨数据维护难度大，修复成本高，削弱了巨星座存储网络的鲁棒性。本研究
内容首先根据业务需求、节点故障概率及网络连通度，对典型大规模星座存储网
络进行虚拟集群划分，形成具有分区自治特点的数据维护管理架构。进而采用广
义再生码对在轨数据进行集群编码存储，重点考虑集群内外修复带宽的差异，通
过信息流图最小割定理的分析，明确满足数据可用性条件下网络存储容量可达的
集群内外修复带宽约束条件，建立用于卫星集群存储网络的非对称广义再生码修
NSFC 2022
第 19 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 21 页 ===
复理论模型，构建在轨分布式存储网络容错机制。针对巨星座网络链路参数特性，
建立传输成本系数矩阵，以保障数据长期可用的最小化系统维护成本作为优化目
标函数，通过算法求解集群内外帮助节点数量与修复带宽分配等码字参数，提升
巨星座网络中在轨存储数据维护的鲁棒性。研究要点包括： 
 巨星座存储网络集群划分方法 
 非对称广义再生码修复理论模型 
 低成本系统维护的码字参数优化 
2.3 拟解决的关键科学问题 
本项目从信息可靠传输、任务稳定计算、数据鲁棒存储三个维度上，逐步探
索并明确巨星座网络容错能力的多维表征方法，及在轨协同编码容错机制的设计
原则，进而大幅度增强巨星座网络的容错性能，提升在轨处理效率。拟解决如下
两个关键科学问题，现分别进行详细描述： 
科学问题（1）：大规模在轨处理网络容错能力的多维表征 
所谓巨星座网络的容错能力，就是指在故障发生后，网络系统仍然能够保持
原有工作状态，持续支撑在轨处理任务高效执行的能力属性，也是网络系统能够
自动修复故障，完成网络自愈恢复的能力属性。巨星座网络拥有大量在不同轨面
运行的卫星节点，数量达上万颗之多，星间链路的连接规划具有复杂多样的特点，
造成网络全局的拓扑结构随时间推移和业务特性发生动态变化，大规模网络的连
通性和抗毁性准确表征成为了挑战性问题。与此同时，广域覆盖的在轨节点处理
能力和业务负载参差不齐，频繁多发的节点计算迟滞和存储数据失效等系统故障
都会直接影响在轨处理任务的正常运行，因此网络整体的计算和存储容错能力准
确表征也形成了难点。本项目为刻画不同来源因素的系统差错对大规模在轨处理
网络容错性的影响程度，在网络连通度、网络丢包率、计算延迟、计算成功率、
系统维护时长、修复成本开销等多个容错性指标上，建立与传输可靠性、计算稳
定性、存储鲁棒性的相互映射关系，从而精确表征巨星座多维网络容错能力的边
界条件，为设计协同编码容错机制提供相应的理论依据。 
科学问题（2）：高动态资源约束下编码容错机制的协同优化 
巨星座网络容错能力的提升，其手段来自于传输、计算、存储相应编码容错
机制的设计方法，而编码容错机制的容错效率又受限于在轨网络资源（可连同路
径数、节点计算能力、链路传输带宽、存储空间分布等）的利用条件。巨星座网
络的在轨资源受到空间环境和器件水平的影响，具有明显的时变动态特性，难以
保障传输、计算、存储资源的实时可用，即无法给予巨星座网络多维容错能力边
界逼近的资源支撑。本项目为应对上述问题，在设计相应编码容错机制时考虑不
同维度资源间的协同优化，形成弹性可塑、灵活可控的全局资源池。根据在轨处
理任务的具体需求，合理调度在轨网络资源，优化编码结构和码字参数。设计可
行路径连通集合下的编码协作、异构算力负载均衡的编码计算以及分区自治非对
NSFC 2022
第 20 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 22 页 ===
称传输成本的编码修复等协同容错机制，协调获得编码容错效率的全局最优化输
出。在链路连通与能量有限的约束下实现信息的可靠传输，在算力异构和时间有
限的约束下实现任务的稳定计算，在集群分区与成本有限的约束下实现数据的鲁
棒存储，进而通过协同优化逼近巨星座网络的多维容错能力。 
两个科学问题的内涵与相互关系如图4 所示。本项目凝练的两个科学问题，
聚焦于大规模星座的网络结构特性以及高动态在轨资源的利用约束条件，针对巨
星座网络容错能力表征与编码容错机制协同优化的基础理论问题，试图揭示制约
巨星座网络容错能力不足的本质原因，并设计与容错能力需求相匹配的协同编码
容错技术方案。科学问题（1）中的大规模在轨处理网络特性，是科学问题（2）
中的高动态在轨资源属性的约束来源，同时，科学问题（1）中的多维网络容错能
力与科学问题（2）中的编码容错机制之间是相互依存、相互支撑的关系，分别是
实现容错能力提升的理论依据和技术手段。在本项目实施的过程中，围绕上述两
个科学问题，将逐步明晰巨星座网络容错能力、容错机制、容错效率与在轨处理
网络结构、在轨资源属性之间的相互耦合机理，明确巨星座网络容错能力的边界
范围以及逼近方法，从而进一步提升巨星座网络传输-计算-存储融合的在轨任务处
理效率。 
大规模在轨处理
网络容错能力
高动态在轨资源
编码容错机制
可靠性
稳定性
鲁棒性
传输
机制
计算
机制
存储
机制
提供理论依据
设计逼近手段
多维化复杂化
支撑
利用
科学问题（1）
大规模在轨处理网络
容错能力的多维表征
科学问题（2）
高动态资源约束下
编码容错机制的协同优化
多
维
表
征
协
同
优
化
 
图4 两个科学问题的内涵与相互联系 
综合上述内容，本项目中研究目标-研究内容-科学问题的逻辑关系如图5 所示。
本项目从巨星座网络在轨处理的需求牵引出发，为满足“互联共享、按需服务、
海量承载”的服务愿景，以实现网络容错能力的提升为总体目标，从信息传输的
可靠性、任务计算的稳定性、数据存储的鲁棒性三个维度方面制定了具体的分目
标。相应设立了三个研究内容，基于分布式系统架构和分布式编码理论，针对巨
星座网络特性，设计相应的协同编码容错机制。在三个研究内容之间，是以探索
与解决两个关键科学问题为主线联系在一起的。研究内容中关于传输可靠性、计
算稳定性、存储鲁棒性的模型建立方法和量化分析手段，用以解决“大规模在轨
处理网络容错能力的多维表征”科学问题（1），力求获得网络容错能力边界的刻
画分析方法；而研究内容中协作传输机制、计算分配机制、数据修复机制的相关
研究，则重点针对“高动态资源约束下编码容错机制的协同优化”科学问题（2），
NSFC 2022
第 21 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 23 页 ===
试图通过在轨资源协同优化利用的方式，给出能够实现网络容错能力边界逐步逼
近的协同编码容错机制设计原则。通过科学问题的探索和解决，既能完善巨星座
网络容错性的理论体系，又能提高巨星座在轨处理中编码容错机制的容错效率。 
互联共享
按需服务
海量承载
研究内容（1）
基于时变网络连通度的
多路径编码协作传输机制
研究内容（2）
基于拓扑感知的自适应
无速率编码计算分配机制
研究内容（3）
基于集群划分的非对称
可再生编码数据修复机制
 信息可靠传输
 任务稳定计算
 数据鲁棒存储
科学问题（1）
大规模在轨处理网络
容错能力的多维表征
科学问题（2）
高动态资源约束下
编码容错机制的协同优化
明确
探索与解决
需求牵引
研究目标
研究内容
关键科学问题
设立
表征容错
能力边界
完成机制
协同优化
网
络
容
错
能
力
提
升
满
足
在
轨
处
理
需
求
 
图5 研究目标-研究内容-科学问题的逻辑关系 
3．拟采取的研究方案及可行性分析（包括研究方法、技术路线、
实验手段、关键技术等说明）； 
3.1 研究方法与研究思路 
本项目以满足巨星座网络在轨处理需求为牵引，以提升巨星座网络容错能力
为目标开展研究。设立的三个研究内容，分别从信息传输的可靠性、任务计算的
稳定性及数据存储的鲁棒性角度设计相应的编码容错机制。三个研究内容之间，
研究内容（1）是项目开展的基础，通过基于时变网络连通度的多路径编码协作传
输机制的研究，保障巨星座网络星间链路的可靠互联，为在轨计算、在轨存储提
供基本的链路、网络条件。研究内容（1）对巨星座时变网络连通度进行分析，通
过链路参数优化网络可达矩阵，给出的可行传输路径集合，为研究内容（2）提供
任务在轨计算的拓扑结构和具体链路参数，完成基于拓扑感知的自适应无速率编
码计算分配机制的研究。于此同时，研究内容（1）中对巨星座网络抗毁性邻接矩
阵的优化以及节点故障概率的研究，为研究内容（3）提供可连通的网络集合，作
为巨星座存储系统集群划分的依据，并将链路参数给到研究内容（3），作为链路
非对称传输成本系数设立的依据，从而完成基于集群划分的非对称可再生编码数
据修复机制的研究。 
三个研究内容中关于传输可靠性（网络连通度、网络丢包率）、计算稳定性（计
算时延、计算成功率）、存储鲁棒性（数据可用时间、数据修复成本）的各项分析
指标，最终用以多维表征与评估巨星座网络的容错能力。各研究内容中相应编码
容错机制的设计方法，将进行有机融合与协同优化，为巨星座在轨处理中协同容
错机制提供网络支撑、算力支撑和数据支撑。在课题开展过程中，重视各研究内
容之间的交互与协调，使其形成具有层进式的研究体系，综合考虑研究内容之间
的制约关系和融合方法，围绕拟解决的关键科学问题形成具有系统性、科学性、
NSFC 2022
第 22 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 24 页 ===
合理性的研究方案。 
研究内容（2）
基于拓扑感知的自适应
无速率编码计算分配机制
研究内容（1）
基于时变网络连通度的
多路径编码协作传输机制
研究内容（3）
基于集群划分的非对称
可再生编码数据修复机制
可传输路径
链路参数
可连通网络
链路参数
故障概率
提供算力支撑
提供网络支撑
提供数据支撑
协同编码容错机制
网络容错能力表征
指标：网络连通
度、网络丢包率
指标：计算时延、
计算成功率
指标：数据可用时间、
数据修复成本
协同优化
协同优化
 
图6 本项目的总体研究思路 
3.2 具体研究方案与技术路线 
根据项目设立的总体研究思路，下面分别对各研究内容的技术方案和研究要
点进行论述。 
3.2.1 研究内容（1）的技术方案 
在研究内容（1）中，目标是保障巨星座网络在轨信息传输的可靠性，设立编
码容错机制，并给出网络可靠性指标。总体上，分为系统模型与技术方案两部分。
研究内容（1）的具体技术路线如图7 所示。 
电源子系统故障
单粒子翻转
星座构型
网络连通度
链路可见性
时变网络连通度
链路参数
（带宽、功率、距离）
赋权网络集合
可行路径集合
多路径编码协作
码字参数
协作传输
容错机制
可靠性指标分析
给到研究内容（3）
给到研究内容（2）
部分解决科学问题（1）
部分解决科学问题（2）
技术方案
系统模型
节点故障概率模型
 
图7 研究内容（1）的技术路线 
在系统模型部分，首先基于卫星电源子系统动态故障树和单粒子翻转概率模
型，建立卫星节点故障概率模型，用以描述卫星节点失效引发的链路中断。进而，
根据已知的星座构型，以自然网络连通度最大化为目标，优化星座的邻接矩阵，
并对链路可见时间（STK 仿真）采样，建立时变网络连通度模型，以描述一段时
间内网络的平均抗毁性。同时，根据链路参数（带宽、功率、距离）构建权重邻
NSFC 2022
第 23 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 25 页 ===
接矩阵，获得赋权网络集合。通过约束路径开销，计算可达矩阵，获得支撑信息
传输的可行路径集合。在技术方案部分，采用分布式编码对所传输的信息进行编
码交织，在多路径上进行容错路由设计，根据节点失效概率和链路容量，设定码
字参数。基于时空扩展图方法，选取合理传输路径，从而构建协作传输容错机制。
对传输可靠性指标（时变网络连通度、网络丢包率）进行分析，在传输层面上表
征巨星座网络的容错能力。 
研究内容（1）研究要点概述： 
 节点故障概率与链路中断时间分析 
卫星的电源系统由太阳电池翼-蓄电池组组成，是由两条相同的供电母线并联
而成，每一条母线都包括蓄电池组、太阳电池阵和电源控制器等。建立电源子系
统的动态故障树模型如图8 所示。其中E 代表底层基本故障事件，G 代表模块综
合故障事件，T 代表子系统故障事件。 
11
T
11
G
12
G
15
G
14
G
13
G
1
20
E
1
19
E
1
13
G
HSP
1
21
E
1
22
E
19
G
1
10
G
1
11
G
1
12
G
1
10
E
1
18
E
16
G
17
G
18
G
18
E
19
E
1
14
G
13
E
14
E
15
E
11
E
12
E
 
图8 电源子系统动态故障树模型 
若电源子系统所有底层部件的故障都服从于指数分布，经分析得到卫星各子
系统可靠性随时间变化的曲线如图9 所示，进而描述卫星故障发生概率。 
 
图9 卫星各子系统可靠性与时间的关系 
单粒子翻转率指器件每天每位发生单粒子翻转的概率，一般计算公式如下： 
0
( ) ( )
p
p
E
R
E
E dE



= 
                       (1) 
其中，
0
E 为阈值能量，单位为MeV，
( )
p E

为质子单粒子翻转截面积，单位为
2 /
cm
bit , ( )
E

为质子微分流量，则卫星节点存储器上每n 比特内存中每天有m 比
特发生翻转的概率为： 
NSFC 2022
第 24 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 26 页 ===
(
)
( ,
)
(1
)
m
m
n m
p n m
n
p
p
R
C
R
R
−
=


−
                    (2) 
例如，当字段长n=8（一个字节）时，其发生单粒子翻转的概率为
(8,1)
p
R
。通过节
点故障树和单粒子翻转概率模型，构建节点故障概率模型，并不断综合多种节点
故障因素。 
对于链路中断时间的分析，主要基于STK 仿真软件进行获取。例如，针对
Starlink 第一期1584 颗卫星的星间链路进行仿真分析，可以获得某一卫星（Sat0101），
如图10 所示，在不同范围内与不同轨道卫星建立星间链路的可见时间。以此方法，
获得某一区域内所有卫星之间的链路可见时间，为时变网络连通度的建模提供依
据。 
 
图10 STK 仿真Starlink 第一期的星座网络 
 巨星座时变网络连通度建模 
巨星座网络的瞬时静态拓扑可认为是一个图
(
)
,
G V E ，通过计算图中闭环的数
量来衡量网络的抗毁性，即该图G 的自然连通度： 
1
1
=ln
i
N
i
e
N


=







                            (3) 
其中，N 表示网络中节点数，
i表示网络邻接矩阵
( ) (
)
ij
N N
A G
a

=
的每个特征
根，aij 表示Vi 和Vj 两点是否连通（0，1 取值）。如果每个卫星只有L 个天线，则
临近矩阵中( )
A G 每个节点的度最大为L。 
但由于星间链路的可见性是时变的，利用STK 仿真某一段时间的星座节点可
见性，进一步对邻接矩阵( )
A G 进行约束。并且对该段时间进行T 个时刻的采样，
得到每个取样时刻的自然连通度
t，获得每个采样时刻使得
t最大的邻接矩阵
(
)
t
A G
，计算得到每个时刻保障网络抗毁性最优的拓扑连接方式。再将
t加和平均，
得到时变网络连通度，即
1
1
T
t
t
S
T

=
= 
，用以衡量该段时间内星座网络的抗毁性。 
同时，根据链路参数条件，引入wij 表示网络中边Eij 的链路消耗，其与卫星
Vi 到卫星Vj 的距离平方成正比，则在T 时间内的平均路径消耗为： 
NSFC 2022
第 25 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 27 页 ===
( )


2
0
,
0,
T
ij
ij
r
t dt
w
t
T
T
=


                         (4) 
设定权重邻接矩阵
(
)
ij
N N
W
w

=
，形成赋权网络集合。则节点vi 到节点vj 经过
m 跳传输的端到端路径消耗为的最短路径和，用cij 来表示，cij 可利用W 进行测算： 


1
2
min
,
1
,
0
m
ik
ik
ik
ij
ij
w
w
w
m
c
w m

+
+
+


= 
=

                 (5) 
根据计算平均路径消耗
(
)
1
1,
1
n
n
ij
i
j
j i
c
c
n n
=
=

=
−

，设定阈值，获得端到端消耗约束下的
可达矩阵(
) ( )
t
ij
N N
B G
b

=
，bij 表示Vi 和Vj 两点是否在一定消耗范围内可达（0，1
取值），作为任务在星座网络中传输的连通路径集合。 
 多路径编码协作传输机制设计 
根据已有的可连通路径集合，进行如图11 所示的时空拓扑图建模描述，
|
i
j
t k
VV

= 表示第k 个时隙，从节点i 到节点j 的链路。
(
|
i
j t k
C VV

= 表示k时刻的链路
容量。对于传输文件大小为M 的任务流，可利用多路径传输的模式，当源节点或
中继节点存在连通机会时，将数据传输给其余中继节点，形成多路径协作，在目
的节点，累计多播流的数据量为M 时，传输完成。 
 
图11 可连通网络的时空拓扑图 
引入链路稳定性函数LSF 如式（6）所示。其中
D
E
,
,
w
w
w为加权系数，D、C、
λ 分别为当前链路的延迟、容量和丢包率，t0，M0，λ0 为源端到目的端的基准条件。
通过将可靠传目标与稳定性函数的三个权值进行映射，以保证每跳链路在满足传
输需求的同时，缩短传输时延和能量开销，并减少网络丢包。 
0
0
0
E
,
(
|
)
(
|
)
D
i j
t k
i
j
t k
i
j
t k
t
M
LSF
w
w
w
D VV
C VV







=
=
=
=
+
+

−


















 
 
 
(6) 
根据链路稳定性函数，建立以时延和能量约束为条件的链路稳定性函数最大
NSFC 2022
第 26 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 28 页 ===
化的动态规划问题，如式（7）所示。(
|
)
i
j
t k
VV


=
表示时隙k内的链路通断，t
为
生存时间，r 为距离，
d
p 为单位距离上链路功率损耗，ic 是节点
iV 可进行传输的能
量阈值，t
为时间约束。通过求解优化问题，在数据传输过程中选择转发链路。 
(
)
(
)
|
,
2
max
(
|
)
(
|
)
(1
s.t
(
|
)
)
.
i
j t k
i
j
t
k
i
j
t
k
VV
i j
t
k
i
j t
k
d
i
k
VV
L
D
SF VV
C VV
t
p
r
c








=
=
=
=
=





+
+
                (7) 
可采用系统Raptor 码作为协作编码传输方案，在设计过程中，节点首先通过
LSF 函数选择转发链路，再根据链路状态确定冗余量、码字度分布等编码参数配
置，利用中继节点编码融合多播信息流，在目的节点进行解码恢复，从而保障端
到端传输的可靠性。 
3.2.2 研究内容（2）的技术方案 
研究内容（2）的核心目标是提供在轨计算任务按时按需完成的稳定性保障，
设计编码计算容错机制，并给出任务计算稳定性指标。研究内容（2）的具体技术
路线如图12 所示。 
节点资源状态模型
计算能力因子
计算迟滞因子
计算任务需求
可行路径集合
任务时延约束
任务计算量
链路参数
分布式计算网络
多跳网络拓扑
棋盘网络拓扑
矩阵拆分
无速率编码计算
计算分配
容错机制
码字参数
稳定性指标分析
卫星载荷参数
来自研究内容（1）
来自研究内容（1）
部分解决科学问题（2）
部分解决科学问题（1）
系统模型
技术方案
负载均衡
拓扑
感知
批处理回传
 
图12 研究内容（2）的技术路线 
首先，在系统模型建立部分，根据星载处理器参数，综合地面任务流量密度，
获得计算能力因子和计算迟滞因子的概率分布函数，对在轨卫星的节点计算资源
状态进行建模。同时，结合研究内容（1）中的可行路径集合，根据在轨计算任务
特点，将网络拓扑分为多跳网络和棋盘网络，基于拓扑感知的方法，建立在轨分
布式计算网络模型，对异构的计算、传输参数进行表征。在技术方案设计部分，
首先明确任务计算量和任务时延约束，根据矩阵-向量乘法规则，将计算任务进行
拆分，采用无速率编码计算的方式，将子矩阵分配到各个卫星节点，设定不同的
码字参数。针对任务按时按需完成的要求，对各节点的计算量进行负载均衡优化，
NSFC 2022
第 27 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 29 页 ===
求取各节点最优的在轨计算时间，并对批处理计算结果的回传顺序进行规划，形
成完整的计算分配容错机制。对计算稳定性指标（计算时延、计算成功率）进行
分析，进而在计算层面上表征巨星座网络的容错能力。 
研究内容（2）研究要点概述： 
 在轨计算资源忙闲状态建模 
在巨星座网络节点的在轨计算能力十分有限，设定其计算资源为有限值
u
C （u
为任务采集节点），在计算任务发起时作为阈值辅助判断是否需要进行分布式计算。
对于辅助计算的卫星节点，假设任务采集节点可获知连接辅助计算卫星的可利用
计算资源。假设卫星的计算资源约束为
T
C ，则在轨辅助计算卫星的计算卸载资源
可表达为
=[
,
,
,
,
]
E
T
P
C
C





，其中为获知的计算资源利用率，P 为辅助计算
卫星的数量。 
由于卫星有各自的波束覆盖范围，卫星的计算资源占用实时动态变化，与各
自覆盖范围内服务请求流量息息相关。假定卫星的空闲计算资源为
s
m
C ，服从一维
指数分布如下， 
                   
(
)
Pr
)
s
m
m
m
C
c
s
m
c
C
e


−

=−
（
                     (8) 
其中，参数
m
与卫星承载高流量服务能力以及链路稳定性相关，
m
与卫星负
载自身损耗相关，即轨道上组网的各个卫星计算资源状态是随机的。建立在轨计
算资源忙闲状态模型如图13 所示，任务到达时刻按到达率为
m
的泊松过程分布，
t 时刻的计算任务At 大小由矩阵行数m 决定；蓝色节点表示可进行计算任务处理
的卫星设备，其中一个节点的计算资源存在被多个任务占用的情况。 
...
...
...
tA
tx
t
W
S
t
W
t
W
0
...
...
m
αm
Node P
Node p
Node 2
Node 1
t﹣r t﹣r＋1
t
t＋1
μm(t)
无速率编码码率α
 1
ε m
收集       
恢复计算结果
 
图13 时变资源状态下矩阵向量乘法任务分配模型 
对于一个密集型计算任务
{ ,
}
n
n
n
T
o
=
，
no 为计算任务量的大小，与通信资源
相关，
n 为完成任务所需的CPU 转数，即需要的计算资源。当
n
n


，
n
为任务
nT 的转发阈值，则执行时间表达为： 
       
( ,
)
( ,
,
)
max
max
(
)
( )
T
n
n
n
n
n
n
n
s
E
u
m
m
C
t
C m
C
P C







=
−
+ −





    (9) 
其中，
{0 1}
{0 1}
n
n




，；
，，
n
为1 时，任务进行本地计算，
n为1 时，任务进
行分布式卫星计算。、分别由接入卫星以及控制层分发策略决定，此时，卫
NSFC 2022
第 28 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 30 页 ===
星网络的最大任务计算率为 
( )
=
max
+
max
( ,
)
( ,
,
)
s
E
n
n
n
n
n
n
T
n
n
n
o
o
C m
o
C
CompR
t
m
m
C




=


         (10) 
为了实现高效率的任务计算，需要合理优化分层控制计算卸载策略，可采用
动态规划算法或深度学习算法，克服分层联合优化与各层计算资源的异构问题。 
 自适应无速率编码计算理论分析 
若分布式计算系统有1 个主节点和n 个工作节点，为计算矩阵向量乘法Ax，
将A 切分后编码为多个编码矩阵Wi 进行分布式计算，其中第i 个工作节点计算
iB
个向量内积的计算时间为
iY ，则 
i
i
i
Y
X
B
=
+
                            (11) 
其中Bi 是所需计算的向量内积数量，是计算1 个向量内积的时延。延迟涉及两
个部分：1）随机变量Xi 为工作节点实际开始执行计算之前的初始化时间；2）工
作节点计算任务处理时间Bi 呈线性变化。其中
( )
~exp
i
X
，其中是慢节点参数，
参数Xi 的变化是由于硬件损坏、丢包以及资源被占用等引起的，是无法预测且无
法消除的，造成计算时延拖尾严重，远远大于平均值。 
对于理想的负载平衡状态，是按照节点实时的计算状态对计算任务进行转移，
但需要对每个节点都具有中心控制能力和监控能力。对于理想的负载平衡状态的
计算时延期望


ideal
E T
无法获得闭合表达式，但其界限的表达式如下： 


1
1
ideal
m
m
E T
n
n
n





+


+
+
                  (12) 
常见的编码计算方案（如复制、MDS 码）都是通过纠错码提供计算冗余来降
低慢节点的影响，但编码冗余的产生也意味着计算负载的增大。无速率码很好地
解决了这个问题，在矩阵A 中按平均分布在m 行向量中随机选取d 行并相加得到
编码行： 


 
, ,...,
d
e
i
d
i S
S
m

=


，
W
W
                   (13) 
不仅额外的计算负载比较小，解码复杂度低，计算时延接近理想的负载平衡
状态。解码使用迭代剥离解码器（iterative peeling decoder），即在每次迭代中，解
码器找到一个编码符号，覆盖相应的原符号，然后从与该源符号连接的所有其他
编码符号中去除该符号，重复操作。无速率码的m 个原码经过编码后的码字数量
M
m

=
，其中m 是高维矩阵A 的行数，是冗余比例，以1 
−
的概率从任意的M
个编码后的集合中恢复。 
(
)
(
)
2
ln
M
m
O
m
m 
=
+
                  (14) 
显然，m
M
m

→
→
，
，无速率编码（LT 码）计算的解码复杂度和额外的计
算负载都很低。对于LT 码计算的计算时延和理想负载平衡的关系如下所示， 
NSFC 2022
第 29 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 31 页 ===
(
)
(
)
2
1
Pr
exp
LT
ideal
m
T
T
n
n

−





−




               (15) 
显然，随着和m 的增加，无速率码有很高的概率接近理想负载的计算时延。
选取10 个工作节点，针对100~50000 行的矩阵-向量乘法，时延模型中参数=1.0

，
τ=0.0002，仿真不同码字结构的计算平均时延性能如图14 所示，可以发现无速率
码的时延下界能够逼近理想的负载平衡状态，具有极大的潜在应用价值。 
 
图14 无速率编码计算的平均时延性能 
 基于拓扑感知的计算负载均衡分配 
针对计算任务的特点，可将在轨分布式计算拓扑结构分为多跳网络和棋盘网
络，如图15 所示。采用无速率编码计算，针对不同网络拓扑，进行基于拓扑感知
的计算负载均衡分配方案设计。 
M1
M2
 
图15 多跳网络和棋盘网络的分布式计算拓扑结构 
针对多跳网络拓扑，假设每个节点的计算能力因子为an，计算迟滞因子为
n
，
为计算Ax 的矩阵-向量乘法，
r d


A
，
1
d

x
，将r 行的矩阵A 划分为N 个子
矩阵A1, …, AN，进行无速率编码后生成
1, ...,
N
A
A ，分配给N 个多跳节点。每个节
点完成每一行向量-向量计算后，通过多跳网络将计算结果回传至目的节点。以最
小化计算任务完成时间
RC
T
为目标，求解最优的节点计算行数，即为负载均衡分配
方案，即求解以下问题： 
NSFC 2022
第 30 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 32 页 ===
main : minimize
[
]
RC
RC
T
                      (16) 
如果假设节点n 的计算时延为
CMP
n
T
，目的节点收到的计算行数可表达为
1
(
)
N
CMP
tot
n
n
n
X
X
T
=
=
，其中
(
)
CMP
n
n
X
T
为每个节点在
CMP
n
T
时长内完成的计算行数。 
通过分析可知，最大化目的节点收到的总行数[
]
tot
X
，即可最小化计算任务
完成时间。因此，求取最优的负载分配向量
*，即求解问题： 
(1)
*
:
argmax [
]
tot
X
=
                      (17) 
考虑到节点计算时间
CMP
n
T
服从指数分布，所以目的节点上聚合的总行数均值
[
]
tot
X
可表达为： 
(
)
1
1
(
)
1
[
]
[
]
CMP
n
n
n
n
n
N
N
T
a
CMP
tot
n
n
n
n
X
X T
e

−
−
=
=


=
=
−








         (18) 
由于每个节点计算是相互独立的，所以可将原优化问题（17）解耦为N 个子
优化问题，求解每个节点n 上的最优负载
*
n ，即： 
*
argmax
(
)
[
]
n
CMP
n
n
X T
=
                      (19) 
该问题可利用极值法得到最优解，即 
(
)
[
(
)]
1
(
1)
0
CMP
n
n
n n
n
CMP
T
a
CMP
n
n
n
n
n
T
X T
e


−
−

= −
+
=

          (20) 
可获得
*
CMP
n
n
n
T

=
，其中
n
是求解过程中等式
(
1)
n
n
n
n
a
n
n
e
e


=
+
的正解，则
最小时延
*
RC
T
，即可通过等式求解得到， 
*
*
*
*
*
*
(
)
(
)
*
*
1
1
[
]
(1
)
(1
)
n
PRO
n
n
cmp
n n
RC
n
n n
n
n
PRO
N
N
T
a
T
T
a
RC
n
n
n
n
n
T
T
X
e
e
r



−
−
−
−
−
=
=
−
=
−
=
−
=


  (21) 
其中
PRO
n
T
为节点n 到目的节点的路径传播时延。 
·
·
·
·
·
·
节点 1
节点 2
节点 3
节点 N-1
节点 N
·
·
·
T1
T2
T3
TN-1
TN
Ttot
计算时间
传输时间
t1
t2
t3
tN-1
tN
 
图 16 各节点计算结果回传次序优化方案 
NSFC 2022
第 31 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 33 页 ===
针对棋盘网络拓扑，由于每个节点不但计算能力因子、计算迟滞因子是异构
的，在结果回传时会发生相同中继节点的多路径碰撞，占用不同的带宽分配，相
当于传输速率也是异构的。根据计算和传输参数，优化每个节点完成本身计算任
务的次序，即分批处理回传，做到当一个节点完成计算结果传输时，下一个节点
终止计算，开始回传，从而获得最终计算任务完成时延的最小化，如图16 所示。
 
保障棋盘网络中节点通过链路协作，完成r 行内积计算，计算结果按次序回传。
通过求解下述优化问题，依次从而获得每个节点的计算时延Tn，最小化任务完成
时间： 
1
1
1
min
s.t.
tot
T
N
n
n
n
n
n
n
n
n
T
h T
r
T
T
t
k T
=
+
=
=
+
=

                        (22) 
其中
1
n
i
n
n
h
a


= +
，
(
)
1
1
j
j
j
j
j
k
R
a


= +
+
，为每个内积的单位计算时间，Rj 为
节点j 到目的节点的平均传输速率。 
3.2.3 研究内容（3）的技术方案 
研究内容（3）的目标是保障在轨数据存储网络的长期可用和低成本维护，设
计在轨编码数据修复机制，并给出数据存储鲁棒性指标。研究内容（3）的具体技
术路线如图17 所示。 
赋权网络集合
业务需求
广义再生码
码字参数
非对称修复模型
传输成本最优化问题
算法求解
最优修复成本
（修复时间、修复能耗）
数据修复
容错机制
鲁棒性指标分析
集群划分规则
修复带宽约束条件
存储容量上限
节点故障概率
来自研究内容（1）
来自研究内容（1）
链路参数
传输成本系数
（带宽、时延、能耗）
来自研究内容（1）
部分解决科学问题（2）
部分解决科学问题（1）
技术方案
系统模型
 
图17 研究内容（3）的技术路线 
在系统模型设计部分，首先根据地面用户请求流量分布的地域差异，按照不
同轨道或不同链路可见范围，确定单个存储集群的规模。再根据节点故障概率，
将可靠性较高的节点选定为跨集群通信节点，并结合赋权网络集合，将集群内节
点的邻接矩阵进行重构，使集群内部的带宽资源更为丰富，依据此划分规则将星
NSFC 2022
第 32 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 34 页 ===
座网络的划分为若干集群。同时，基于链路参数确定集群内外各通信链路的传输
成本系数矩阵。在技术方案设计部分，首先根据信息流图，获取广义再生码存储
容量上限可达的修复带宽约束条件，建立集群内外修复带宽差异的非对称数据修
复模型。进而，根据集群划分规则，选定广义再生码码字参数，结合成本系数矩
阵，建立传输成本最优化问题。通过算法求解，获得维持系统长期可用、且维护
成本最低的修复带宽分配比例，进而可拓展为最优的修复时间和修复能耗，描述
系统鲁棒性分析指标，再存储层面上表征巨星座网络的容错能力，并建立相应的
数据修复容错机制。 
研究内容（3）研究要点概述： 
 巨星座存储网络集群划分方法 
由于巨星座网络具有全球覆盖特性，而用户偏好与业务类型具有明显的地域
性，因此在轨数据存储系统的服务范围具有局部性特征，可根据实际需求将巨星
座划分为若干卫星存储集群。表3 中给出了几种常见的卫星集群划分方法并评估
了其优缺点。卫星存储网络集群间并没有明显的物理区分度，为了既能保证集群
内部拥有充足的带宽资源，又实现集群间的跨域互联，维护整体系统数据存储的
鲁棒性，需要对集群划分后的网络链路连通进行重构设计。 
表3 几种常见的卫星集群划分方法及优缺点 
在根据应用需求确定好集群范围后，设
(
)
,
i
i
i
G V E
为第i 个卫星集群，可以得
到位于集群i 与其他集群边界处的卫星节点集合
'
iV 和链路集合
'
iE 。若
(
)
,
i
i
i
G V E
与
其余N 个卫星集群相邻，且需保证跨集群连通度为k，则不妨设
'
,i n
V
为集群i 分别
与这N 个卫星集群相邻处的卫星节点集合。根据研究内容（1）得到的节点和链路
失效概率，可以对
'
,i n
V
中的节点可靠度进行排序。由于节点固有的损坏概率，跨集
群通信节点的数量选取必然大于k 才能保障网络连通度，因此取可靠度最高的
划
分
方
法 
按轨道划分 
按距离划分 
一个轨道一个集群 
一个轨道多个集群 
2500 km范围所有链
路作为一个集群 
2500km 范围永久链
路作为一个集群 
图
示 
 
 
 
 
 
优
缺
点 
优点：方法简单，集
群固定。 
缺点：集群内大量卫
星不可见，限制集群
内部数据修复过程。 
优点：集群固定且任
意两颗卫星可见。 
缺点：集群内卫星数
量太少，通常需要跨
集群修复。 
优点：集群内包含大
量卫星且任意两颗
卫星都可见。 
缺点：集群内链路随
卫星运动而改变，修
复过程复杂。 
优点：集群内卫星数
量较多且两两可见，
降低集群内部的修
复时延。 
缺点：集群内链路较
少。 
NSFC 2022
第 33 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 35 页 ===
k

+



个卫星，与相邻的集群建立跨集群链路。此时，设
''
iE 为
'
iE 中的还未使用的
剩余链路，则可对
''
iE 中的链路在集群内部进行重新分配。在重新分配时，要考虑
连通度
,i t
和和链路权重矩阵
i
W ，优化邻接矩阵
(
)
,
i
i t
A G
，以提高集群内部网络的
可靠性并减小传输损耗。虽然卫星星座拓扑具有时变性，但是对于同轨道高度的
低轨卫星星座的集群划分，各集群的相对位置关系不变，因此
'
iV 是非时变的，只
需按研究内容（1）的方法优化集群的邻接矩阵
(
)
,
i
i t
A G
即可。在集群划分的网络
重构中，使用高可靠节点作为跨集群通信节点可以使跨集群通信具有基本的连通
度保障，而更多的链路用于集群内部通信，使集群内部的连通度比集群划分前更
大，可靠性更高。 
 非对称广义再生码修复理论模型 
广义再生码的非对称修复由n 个集群构成，每个集群中有m 个节点，每个节点
存储的数据。将集群1 到集群n 表示为
1
2
,
,
,
n ，并且集群i 中的第j 个节点
表示为
,i j
N
。假设
,
[ ]
i i
n

中的一个节点发生损坏，那么在非对称修复下，对于参
数为( , , ,
, )
n k d m l 的广义再生码来说，如图18 所示，在失效节点所在集群内部，新
生节点会连接集群内其他l 个存活节点，并获取每个节点上带宽为的修复数据，
在跨集群修复方面，新生节点会连接其他d 个集群并分别获取带宽为
,i j

的修复数
据，其中，
,i j

表示在修复集群节点时，从集群j 传输到集群i 的跨集群数据量，
i
j
，
i 表示修复集群i 的节点时，外部集群的索引集。 
……
1,1
N
1,2
N
1,3
N
1,m
N
……
2,1
N
2,2
N
2,3
N
2,m
N
……
,1
n
N
,2
n
N
,3
n
N
,
n m
N
DC

存储节点
失效节点
1
C
2
C
n
C
k
\{1}

|
| d
=
,i j

 
图18 广义再生码非对称修复模型 
通过对广义再生码的有向无环图——信息流图
(
)
N 分析其非对称修复具体
过程。数据通过编码后存入到节点中时，被看作为阶段0，每经过一次节点修复，
所有节点的阶段数1
+ 。将集群i 中的第j 个节点表示为
,i j
N
，且每一个
,i j
N
都由入
节点
,
in
i j
N
和出节点
,
out
i j
N
组成，且
,
in
i j
N
和
,
out
i j
N
之间由一条有向边
in
out
,
,
(
)
i j
i j
e N
N
连接，
其容量为
。在信息流图(
)
N ，每一个集群中都由一个专门负责跨集群传输的特
殊节点，称为控制节点，用
ctrl
i
N
表示。 
NSFC 2022
第 34 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 36 页 ===
文件源经过编码后，被存储到nm 个节点中，每个节点中存储个符号的数据，
S 与每一个节点之间都有一个有向边
in
,
(S
)
[ ],
[ ]
i j
e
N
i
n
j
m
，
相连，其容量为无
穷，表示节点的存储上界不受限制，同样，在进行数据下载时，每一个集群的
ctrl
i
N
和数据收集器之间也通过有向边
ctrl
(
DC)
i
e N
连接。 
当
,
[ ]
i i
n

中的一个节点
,i j
N
损坏时，将利用
,i j
N
所在集群
i 中的一个新节点，
作为
,i j
N
的替代者，将这个新节点表示为
,i j
N
，此时，新节点
,i j
N
将连接d 个远程
帮助集群的控制节点来下载修复数据，其中d 个远程帮助集群的集合表示为
,
,
[ ]\{ }
ih
i
i
i
N
h
n
i ，
i 代表参与跨集群修复的集群索引集，且|
|
i
d ，由于
新节点从各集群分别下载
,i j 的数据量，因此从
ih
N 到新节点由一条容量为
, i
i h 的有
向边连接。另外，在集群内部的修复中，
,i j
N
还将下载本集群中l 个节点中的数据，
将这l 个本地帮助节点组成的集合表示为
，
out
'
'
,
{
,1
,
}
i
i j
N
j
m j
j 。在本地
修复带宽不受限的情况下，从
到
,i j
N
的有向边
'
,
,
(
)
in
out
i j
i j
e N
N
，其容量最大应为
。
图19 所示的信息流图中，
,i j
N
通过下载
ih
N 和
中的数据，完成节点修复。 




S






DC








3 (1)
ctrl
N
3,1(1)
in
N
3,1 (1)
out
N
3,2 (1)
out
N
3,2(1)
in
N
阶段0
阶段1
1,1(0)
in
N
1,1 (0)
out
N
1,2 (0)
out
N
1,2(0)
in
N
2,2(0)
in
N
2,1(0)
in
N
2,1 (0)
out
N
2,2 (0)
out
N
3,2 (0)
out
N
3,2(0)
in
N
3,1(0)
in
N
3,1 (0)
out
N
1 (0)
ctrl
N
2 (0)
ctrl
N
3 (0)
ctrl
N
3,1

3,2









 
图19 (
3,
2,
2)(
2,
1)
n
k
d
m
l
广义再生码非对称修复信息流图 
 低成本系统维护的码字参数优化 
首先，定义传输成本系数
,i j

表示集群i 和集群j 之间进行数据传输时，传输单
位比特数据所需要的成本。在卫星集群存储系统中，集群内部和集群之间的通信
链路不同，以及各链路之间的通信距离不同，因此各跨集群数据传输的成本不同。
在集群内部，由于通信的方便性，为了简化分析过程，将各个集群内部通信链路
的成本系数归一化，即
,
1,
[1, ]
i i
i
n

=

，另外，由于集群i 和集群j 之间相互进行
数据传输时，使用的是相同的通信链路，
,
,
i j
j i


=
，建立一个n n
的传输成本系
数矩阵Ψ来量化跨集群的传输成本。 
NSFC 2022
第 35 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 37 页 ===
1,2
1,
1,
2,1
2,
2,
,1
,2
,
,1
,2
,
1
1
1
1
i
n
i
n
i
i
i n
n
n
n i




















= 











Ψ
                (23) 
广义再生码通过集群内部和远程帮助集群共同作用完成修复，非对称修复过
程产生两种带宽，其成本也由两部分组成，表达式如下： 
int
int
ra cluster
er cluster
C
C
C
−
−
=
+
                   (24) 
其中，
int ra cluster
C
−
表示节点修复时，集群内部数据传输的成本消耗，
inter cluster
C
−
表示各远程帮助集群传输跨集群修复数据时的成本消耗。结合修复模型，可以得
到非对称修复下广义再生码的成本表达式： 
,
, ,
i
i
i
i
i h
i h
i
h
i
C
l
d
h
l






+


=
+ 
               (25) 
对于广义再生码本地集群带宽约束，当
'
'
,
l
m 

=
=
时，主集群本地修复带宽
满足
1
*
,
1
min{ ,
}
d
i j
j k





+
= +

−

，令
( )



β ，得到 
[
1,
1]
[
1,
1]
( )
min{ ,
}
k
d
k
d



+
+
+
+
=
−
β
e
β
                (26) 
式中
,1
,2
,
,
[
,
,
,
] ,
0
T
i
i
i n
i i




=
=
β
，是一个d 维列向量，代表跨集群修复带宽
的集合，
[
1,
1]
,
1
,
1
[
,
,
]T
k
d
i k
i d


+
+
+
+
=
β
表示β 中从第
1
k + 到第
1
d + 个元素的集合；
[
1,
1]
k
d
e
是和
[
1,
1]
k
d
β
相同维度的全1 行向量。此时，可以根据各跨集群传输成本系
数的不同，对各跨集群修复带宽进行优化，通过改变各
,i j

的值，将总修复成本作
为优化的对象，在主集群本地带宽受限时，探究最优的修复带宽分配。如： 
[ ]\[ ]
1
(
1)
min
( )
          
 
0
                        ( )
                
(
)
min{ ,
min
}
  
i
i
i
i
k
i
i
n
i
i
d
i
C
l
md
kl
m
l
M






β
β
ρ β
β
β
e β
          (27) 
式中 
( )
ic β ——修复集群
i 中节点的全局修复带宽成本；iρ 表示参与修复的远
程帮助集群与
i 间链路的成本系数集合，
,
={
}
i
i j
i
j
ρ
；
iβ 表示参与修复的远程
帮助集群与
i 间链路的跨集群修复带宽集合，
,
={
}
i
i j
i
j

β
。 
3.3 可行性分析 
在项目总体规划方面：本项目经过前期调研，制定了明确的研究目标、充实
的研究内容和具体的研究方案。 
NSFC 2022
第 36 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 38 页 ===
申请人对本项目研究中涉及的巨星座网络、在轨处理、卫星网络容错机制、
分布式编码等关键技术进行了大量调研。目前，巨星座网络已成为国内外空间信
息网络学术界和工业界关注的前沿领域之一，各国都在国家和商业层面进行战略
布局。然而，如何突破大规模网络结构对巨星座网络容错能力的约束，以及如何
设计相应的网络协同容错机制，保障巨星座在轨处理“互联共享、按需服务、海
量承载”的服务需求，仍然是充满挑战性的问题。申请人从巨星座网络在轨处理
的需求牵引出发，从信息传输的可靠性、任务计算的稳定性、数据存储的鲁棒性
三个维度，对巨星座网络容错能力的瓶颈进行了剖析，明确提出了本项目的研究
目标，即通过相应编码容错机制的设计，综合提升巨星座网络的容错能力。在研
究内容设立上，结合分布式编码理论，联合星座网络连通度与星间链路规划、在
轨资源状态与拓扑结构、星座集群划分与系统维护成本等卫星网络相关技术，创
新设计多路径编码协作传输、无速率编码计算分配和可再生编码数据修复的编码
容错机制，对研究目标形成具有针对性的技术突破。同时，本项目为探索大规模
在轨处理网络容错能力的多维表征方法，明晰高动态资源约束下编码容错机制的
协同优化机理，凝练了拟解决的两个关键科学问题，由问题导向推动研究内容实
施，使得各研究内容具有更为清晰明确的研究思路。在研究方案和技术路线设计
上，制定了详细的方案实施过程，明确各技术要素之间的输入输出关系，在模型
建立、理论分析、算法设计、参数优化等方面给出了细致的规划路线，分析了研
究中可能存在的技术难点，确保项目能够顺利完成。 
在团队研究基础方面：本项目申请人及所在团队具有从事卫星通信网络和在
轨信息处理领域的丰富研究经验，依托科研平台基础良好。 
申请人长期从事卫星通信网络、编码理论、分布式传输-存储-计算系统等方面
的研究工作。近五年来主持国家自然科学基金青年项目、中国博士后科学基金面
上项目以及广东省、深圳市自然科学基金项目等共计5 项，并作为骨干成员参与
了国家自然科学基金重点项目、国家自然科学基金重大仪器研制项目、广东省重
点实验室等多个空间通信领域的大型科研项目。申请人所在哈尔滨工业大学（深
圳）空天通信与网络技术科研团队与国内航天科研院所（航天五院、航天八院）
及网络通信科研机构（鹏城实验室）保持长期合作，丰富的项目经验和成熟的平
台载体，能够确保本项目中各研究内容和技术方案的顺利实施。近五年申请人致
力于研究分布式传输、存储、计算等相关技术在空间通信中的应用，在国内外重
要学术期刊（IEE WCM、IEEE TVT、IEEE TII、IEEE IOTJ、IEEE TCCN、IEEE TNSE、
中国通信、EURASIP JWCN、IET Comm 等）和重要学术会议（IEEE WCNC、IEEE 
VTC）上发表相关学术论文20 余篇，并申请获得了数项国家发明专利，在卫星网
络传输、天基网络存储与计算、卫星网络资源协同优化等方面做出了相应的探索，
从而为本项目的开展奠定了良好的研究基础，降低了技术风险。本项目研究团队
主要由讲师2 名、博士后1 名、博士/硕士研究生8 名组成，梯队结构合理，具有
较高的理论水平和创新的研究思路，能够保证在项目执行期内全力投入研究工作。 
NSFC 2022
第 37 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 39 页 ===
在技术方案论证方面：本项目经过充分论证和分析，已对部分技术取得阶段
性进展，理论分析和仿真结果表明研究方案切实可行。 
在前期研究过程中，申请人对编码存储和编码计算在空间通信网络应用场景
的延迟性能进行了仿真分析，如图20 所示。可以发现，利用RS 编码（MDS）和
再生码（RGC）技术能够在链路不稳定的卫星网络条件下获得数据获取延迟和计
算卸载延迟上的增益，论证了本项目研究内容的可行性。并且在码字结构设计上
存在进一步的优化空间，也是本项目采用无速率编码和广义再生码设计在轨任务
计算与在轨数据存储等编码容错机制中需要突破的重点内容。 
 
图20 编码存储与编码计算在空间通信网络中的时延性能仿真 
另外，申请人针对广义再生码在轨道数为50，每个轨道上70 颗卫星的天基大
数据存储网络中的应用场景，已初步完成非对称广义再生码（Asy-GRCs）修复理
论模型的算法验证。在简单划分网络集群的条件下，根据卫星链路参数设定异构
的成本系数矩阵（简单描述为传输跳数），进行仿真实验，如图21 所示。可以发
现，广义再生码的非对称修复相比再生码（MBR）和广义再生码对称修复
（MB-GRCs），能够获得更低的数据修复成本，论证了其在卫星大数据存储系统鲁
棒性维护中的应用可行性。 
    
 
(a) 按轨道划分集群                       (b) 按范围划分集群 
图21 不同文件大小下MBR、MB-GRCs 和Asy-GRCs 的修复成本对比 
4．本项目的特色与创新之处； 
本项目瞄准巨星座网络容错能力不足制约其在轨处理高效执行这一关键瓶颈
NSFC 2022
第 38 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 40 页 ===
问题，开展面向在轨处理的巨星座网络协同编码容错机制的研究。力求通过理论
分析全面表征巨星座网络的多维容错能力，并基于分布式编码理论，从信息传输
可靠性、任务计算稳定性、数据存储鲁棒性三个维度，联合在轨网络资源的协同
优化，设计相应的协同编码容错机制，综合提升巨星座网络的多维容错能力和协
同容错机制的容错效率，满足在轨处理服务需求。归纳本项目的创新点主要包括： 
创新点（1）：巨星座网络多维容错能力表征方法创新 
本项目拟解决的关键科学问题是国内首次聚焦巨星座网络容错性对在轨信息
传输、在轨任务计算和在轨数据存储等大规模星座在轨处理执行效率的内在影响
机理，将网络容错性作为衡量巨星座在轨分布式处理系统性能的重要指标。准确
针对巨星座网络中链路频繁中断、节点计算迟滞、数据频繁丢失等系统差错问题，
从传输可靠性、计算稳定性、存储鲁棒性三个维度上对巨星座网络容错能力进行
了细致剖析，利用网络连通度、网络丢包率、任务计算时延、计算成功率、数据
可用时间、数据修复成本等可量化指标对网络容错性进行了多维精准映射。在项
目执行过程中，将深入探讨巨星座网络传输、计算、存储各维度上容错性能的相
互制约关系，揭示大规模在轨处理网络容错能力边界的演变规律，为巨星座网络
在轨处理的容错性技术方案设计提供充足的理论分析依据。 
创新点（2）：巨星座网络协同容错机制设计方法创新 
本项目设立三个研究内容，将分布式编码理论与卫星网络技术相融合，设计
相应的分布式编码容错机制，提升信息传输的可靠性、任务计算的稳定性以及数
据存储的鲁棒性。在方法设计中，基于时变连通度的多路径协作编码传输机制，
是根据网络抗毁性的约束条件，规划局部网络连通性最佳的可行路径集合，并采
用编码多路径协作的方式，确保信息的可靠传递；基于拓扑感知的自适应无速率
编码计算分配机制，针对在轨分布式计算网络拓扑的特殊结构，采用无速率编码
抵抗节点算力异构特点，对计算负载量进行自适应均衡分配，提升任务计算的稳
定性和准确率；基于集群化分的非对称可再生编码数据修复机制，将构建天基集
群分布式数据存储网络，确立集群合理的划分规则，并采用广义再生码实现集群
内外非对称链路的低成本数据修复，维持在轨存储数据的长期可用，增强数据存
储的鲁棒性。上述容错机制对分布式编码在卫星网络场景中的应用进行了完善和
改进，突出对在轨高动态网络资源的协同利用，使得协同编码容错机制的容错效
率能够得到充分保障。在各编码容错机制完成的基础上，进一步通过全局资源协
同优化实现对巨星座网络容错能力边界的逐步逼近，具有开创性的研究思路。 
5．年度研究计划及预期研究结果（包括拟组织的重要学术交流活
动、国际合作与交流计划等）。 
5.1 年度研究计划 
第一年：2023 年1 月~2023 年12 月 
❖ 搜集整理巨星座网络在轨处理的相关资料，对国内外研究现状进行跟踪和
NSFC 2022
第 39 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 41 页 ===
分析，进一步梳理关键技术研究路线； 
❖ 开展巨星座网络时变连通度建模的研究，对巨星座网络抗毁性进行分析，
并根据链路条件建立赋权网络集合； 
❖ 开展在轨计算资源忙闲状态建模和分析，确立计算工作节点的资源属性； 
❖ 开展大规模星座存储网络集群划分，对现有集群划分方法进行改进，形成
具有数据长期维护能力的天基存储平台架构。 
第二年：2024 年1 月~2024 年12 月 
❖ 对节点故障概率进行分析建模，综合描述复杂空间环境对在轨设备的影响
因素； 
❖ 基于可行路径集合和时空扩展图，设计多路径编码协作传输机制，制定算
法流程，对码字参数进行优化； 
❖ 改进现有无速率编码计算的分配机制，针对典型网络拓扑结构，进行拓扑
感知的计算负载分配； 
❖ 对非对称广义再生码的数据修复理论进行研究，紧密结合卫星集群异构链
路特点，设计码字参数和非对称传输成本系数矩阵。 
第三年：2025 年1 月~2025 年12 月 
❖ 实现时变网络连通度与多路径编码协作传输的联合设计，减少编码路由算
法复杂度，并对信息传输可靠性进行指标分析； 
❖ 完成拓扑感知的无速率编码计算分配机制设计，对节点任务分配和计算结
果回传次序进行联合优化，对任务计算稳定性进行结果评估； 
❖ 完善非对称广义再生码数据修复机制，求解成本开销最小化的优化问题，
对在轨数据可用时长和数据维护成本等鲁棒性指标进行定量分析。 
第四年：2026 年1 月~2026 年12 月 
❖ 基于信息传输可靠性、任务计算稳定性、数据存储鲁棒性等指标分析结果，
表征巨星座网络多维容错能力边界，建立多维网络容错性理论分析模型； 
❖ 结合提出的各项编码容错机制，完善在轨资源的全局协同优化，实现多维
网络容错能力边界的联合逼近，进一步提升协同编码容错效率； 
❖ 总结上述研究成果，完善相应的文章撰写、专利申请和科技报告； 
❖ 对项目研究成果进行梳理，撰写结题报告。 
5.2 预期研究结果 
本项目拟完成以下研究成果： 
（1） 基于时变网络连通度的多路径编码协作传输机制 
（2） 基于拓扑感知的自适应无速率编码计算分配机制 
（3） 基于集群划分的非对称可再生编码数据修复机制 
（4） 大规模在轨处理网络容错性的多维表征模型 
（5） 协同编码容错机制的全局资源池联合优化方法 
NSFC 2022
第 40 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 42 页 ===
项目执行期内，完成上述研究内容，撰写发表16 篇以上国内外高水平学术论
文，其中SCI 检索期刊论文8 篇及以上（包含2 篇国内学术期刊），EI 检索国际会
议论文8 篇及以上；积极参与国际合作和交流计划，参加6 次以上重要国际学术
会议，并与国内外同领域技术专家进行访问交流；申请国家发明专利6 项及以上，
完成2 篇科技报告；培养青年科技人才1 名，硕士毕业研究生8 人及以上，博士
毕业究生2 人及以上；依托项目研究成果，完成国家重大卫星通信领域研究项目1
项以上。 
 
 
NSFC 2022
第 41 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 43 页 ===
（二）研究基础与工作条件 
1．研究基础（与本项目相关的研究工作积累和已取得的研究工作
成绩）； 
申请人及其所在团队（哈尔滨工业大学）长期致力于空间信息网络与卫星通
信的基础理论与关键技术研究，通过承担一批包括国家重点研发计划、国家自然
科学基金重点项目、国家自然科学基金重大仪器研制项目、中国工程院战略咨询
等国家级项目课题，系统地开展了天基通信新体制、空间信息传输新理论、卫星
信息处理新方法的研究工作。多年来，科研团队积累了大量创新性科研成果，在
国家多项重大工程应用中发挥了重要作用，相关科研成果获得了国家航天科研机
构（航天五院、中科院微小卫星创新研究院）的应用证明，丰富的项目研究经验
可为本项目后续的顺利实施提供充分的技术保障。 
申请人自2016 年参加工作以来，一直从事空间通信网络、分布式编码理论、
分布式存储与计算等领域的研究工作，获得了国家自然科学基金青年项目、中国
博士后科学基金面上项目、广东省自然科学基金面上项目、深圳市基础研究计划
高校稳定支持计划等基金项目的资助，共计发表SCI/EI 检索论文40 余篇，获得国
际会议Best paper Award 2 次，申请及授权国家发明专利20 余项。近年来，与本项
目相关的研究工作积累可以概况为以下三方面： 
（1）卫星网络可靠信息传输方面的相关研究：包括卫星机会式网络的协作编
码传输、星地时变信道的自适应编码、分布式无速率编码优化等。这部分研究基
础可以支撑本项目开展研究内容（1）——基于时变网络连通度的多路径编码协作
传输机制，提供网络时空扩展图建模、协作编码参数设计等方面的理论基础。代
表性研究成果包括： 
[1]. S. Gu, Y. Wang*, G. Chen, and S. Shi, Service-driven coded forward scheme based on 
streaming transmission model for lunar space communication networks[J], China 
Communications, vol.17, no.7, pp. 15-26, Jul. 2020.  
[2]. S. Gu, J. Jiao*, Z. Huang, S. Wu, and Q. Zhang, ARMA-based adaptive coding transmission 
over millimeter-wave channel for integrated satellite-terrestrial networks[J]. IEEE Access, vol. 
6, pp. 21635-21645, May 2018.  
[3]. S. Gu, J. Jiao*, Q. Zhang, and X. Gu. Rateless coding transmission over multi-state dying 
erasure channel for SATCOM[J]. EURASIP Journal on Wireless Communications and 
Networking, vol. 1, no.176, Oct. 2017.  
[4]. S. Gu, J. Jiao, Q. Zhang*, Z. Yang, W. Xiang, and B. Cao. Network-coded rateless coding 
scheme in erasure multiple-access relay enabled communications[J]. IET Communications, vol. 
8, no. 4, pp. 537-545, Mar. 2014.  
[5]. S. Gu, Y. Yang, J. Jiao, W. Xiang, and Q. Zhang. Distributed rateless coded collaboration for 
satellite relay networks[C]. in 2015 International Conference on Wireless Communications & 
Signal Processing (WCSP), Nanjing, China, Oct. 2015, pp. 1-6. (Best Paper Award) 
[6]. 张钦宇，顾术实，王洋，焦健，欧阳任耕. 一种适用于濒死信道的无速率编码传输方法, 发
明专利, 授权号: CN105141386B, 授权日期 : 2018.03.09. 
NSFC 2022
第 42 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 44 页 ===
[7]. 张钦宇，顾术实，焦健，杨志华. 一种基于LT 码的空间DTN 传输方法, 发明专利, 授权
号: CN103354479B, 授权日期 : 2017.02.08. 
[8]. 顾术实，陈国庆，王野，袁鹏，张钦宇. 机会式卫星网络的转发方法，发明专利，授权号: 
CN110149140B, 授权日期: 2021.09.14.   
[9]. 顾术实，徐运来，杨志华，王野，张钦宇. 基于网络编码和中继缓存辅助的DTN 数据传
输方法，发明专利，授权号: CN110138432B, 授权日期: 2021.06.29.  
 
（2）空间网络分布式编码计算方面的相关研究：包括空天地一体化网络编码
计算卸载、卫星-无人机网络编码容错机制优化、分布式系统一致性协议设计等。
这部分研究基础可以支撑本项目开展研究内容（2）——基于拓扑感知的自适应无
速率编码计算分配机制，提供无速率编码计算框架、在轨计算任务分配等方面的
理论基础。代表性研究成果包括： 
[1]. S. Gu, Q. Zhang* and W. Xiang, Coded storage-and-computation: a new paradigm to enhancing 
intelligent services in space-air-ground integrated networks[J], IEEE Wireless Communications, 
vol. 27, no. 6, pp. 44-51, Dec. 2020. 
[2]. S. Gu*, Y. Wang, N. Wang, and W. Wu, Intelligent optimization of availability and 
communication cost in satellite-UAV mobile edge caching system with fault-tolerant codes[J], 
IEEE Transactions on Cognitive Communications and Networking, vol. 6, no. 4, pp. 1230-1241, 
Dec. 2020.  
[3]. S. Gu, Y. Wang, Y. Wang*, Q. Zhang, and X. Qin, Grouping-based consistency protocol design 
for end-edge-cloud hierarchical storage system[J], IEEE Access, vol. 8, pp. 8959-8973, Jan. 
2020. 
[4]. Y. Guo, S. Gu, Q. Zhang, N. Zhang, and W. Xiang, A coded distributed computing framework 
for task offloading from multi-UAV to edge servers[C], in IEEE Wireless Communications and 
Networking Conference (WCNC), Nanjing, China, Mar. 2021 
[5]. B. Pang, S. Gu, Q. Zhang, N. Zhang, and W. Xiang, CCOS: A coded computation offloading 
strategy 
for 
satellite-terrestrial 
integrated 
networks[C], 
in 
International 
Wireless 
Communications and Mobile Computing Conference (IWCMC), Harbin, China, Jun. 2021. 
 
（3）分布式存储网络编码修复方面的相关研究：包括高能效缓存内容编码分
发、分层缓存网络的编码内容修复、D2D 移动存储系统数据修复、非对称广义再
生码修复带宽成本优化等。这部分研究基础可以支撑本项目开展研究内容（3）—
—基于集群划分的非对称可再生编码数据修复机制，提供数据修复延迟分析、广
义再生码修复模型构建等方面的理论基础。代表性研究成果包括： 
[1]. S. Gu*, W. Lu, W. Xiang, N. Zhang, Q. Zhang, Repair delay analysis of mobile storage systems 
using erasure codes and relay cooperation[J], IEEE Transactions on Vehicular Technology, vol. 
70, no.10, pp.10580-10593, Oct. 2021.  
[2]. S. Gu*, X. Sun, Z. Yang, T. Huang, W. Xiang and K. Yu, Energy-aware coded caching strategy 
design with resource optimization for satellite-UAV-vehicle integrated networks[J], IEEE 
Internet of Things Journal, early access, Mar. 2021. doi: 10.1109/JIOT.2021.3065664 
[3]. S. Gu, Y. Tan, N. Zhang, and Q. Zhang*, Energy-efficient content placement with coded 
transmission in cache-enabled hierarchical industrial IoT networks[J], IEEE Transactions on 
Industrial Informatics, vol. 17, no. 8, pp. 5699-5708, Aug. 2021. 
NSFC 2022
第 43 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 45 页 ===
[4]. S. Gu*, F. Wang, Q. Zhang, T. Huang, W. Xiang, Global repair bandwidth cost optimization of 
generalized 
regenerating 
codes 
in 
clustered 
distributed 
storage 
systems[J]. 
IET 
Communications, 1-13, Oct. 2021.  
[5]. Y. Wang, S. Gu*, L. Zhao, N. Zhang, W. Xiang, and Q. Zhang, Repairable fountain coded 
storage systems for multi-tier mobile edge caching networks[J]. IEEE Transactions on Network 
Science and Engineering, vol. 7, no. 4, pp. 2310-2322, Oct.-Dec. 2020.  
[6]. Z. Zhang, S. Gu*, Q. Zhang*, Scalable local reconstruction code design for hot data reads in 
cloud 
storage 
systems[J], 
Science 
China 
Information 
Sciences, 
accepted, 
doi: 
10.1007/s11432-021-3421-6. 
[7]. S. Gu, J. Li, Y. Wang*, N. Wang, and Q. Zhang, DR-MDS: an energy-efficient coding scheme in 
D2D distributed storage network for the Internet of Things[J]. IEEE Access, vol. 7, pp. 
24179-24191, Mar. 2019.  
[8]. 顾术实，王福刚，张智凯，张钦宇，孙新毅. 用于卫星集群存储网络的混合再生编码修复
方法及系统，发明专利，申请号: 202110856458.7，申请公布号: CN113553212A，申请公
布日: 2021.10.26 
[9]. 顾术实，孙新毅，郭云开，逄博，王福刚，张钦宇. 空天地一体化网络的编码缓存内容放
置与资源联合优化方法，发明专利，申请号: 202110251280.3，申请公布号: CN113015105A，
申请公布日: 2021.06.22. 
[10]. 顾术实，鲁万城，谭燕，孙先范，张钦宇. 一种面向移动存储系统的中继协作数据修复方
法及系统，发明专利，申请号: 202010730214.X，申请公布号: CN 112118604A，申请公布
日: 2020.12.22. 
[11]. 顾术实，孙先范，鲁万城，谭燕，张钦宇. 一种热数据存储系统的自适应局部重构码设计
方法及云储存系统，发明专利，申请号: 202010716814.0，申请公布号: CN 112000278A，
申请公布日: 2020.11.27. 
[12]. 顾术实，谭燕，孙先范，鲁万城，张钦宇. 异构网络中高能效编码缓存内容放置方案设计
方法，发明专利，申请号: 202010573250.X，申请日: 2020.06.22，申请公布号: CN 
111741495A，申请公开日: 2020.10.02. 
 
近年来，申请人作为项目负责人，主持省部级以上的科研项目包括： 
[1] 国家自然科学基金，青年项目，61701136，面向深空通信网络中数据流业
务的无码率传输协议研究，2018-01 至2020-12，24.5 万元，已结题 
[2] 中国博士后科学基金，面上项目，2018M630357，高低轨混合星座网络中
流业务驱动的分布式编码传输机理，2018-05 至2019-11，8 万元，已结题 
[3] 广东省自然科学基金，面上项目，2021A1515011572，基于编码计算的星
地融合网络协同任务分配方法研究，2021-01 至2023-12，10 万元，在研 
上述科研项目围绕着深空通信网络与卫星通信网络的分布式编码协作传输和
分布式编码计算分配等方向开展了前期研究，在分布式编码容错机制应用于空间
通信领域获得了丰富的研究基础。本项目融合分布式编码技术在传输、计算、存
储多个领域的综合优势，开展面向在轨处理的巨星座网络协同编码容错机制的研
究工作，其中所涉及的卫星网络建模与编码算法设计等关键技术方案，能够从上
述科研项目中获得重要的理论参考和技术支撑，是本项目成功实施的有力保障。 
 
NSFC 2022
第 44 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 46 页 ===
2．工作条件（包括已具备的实验条件，尚缺少的实验条件和拟解
决的途径，包括利用国家实验室、国家重点实验室和部门重点实验室
等研究基地的计划与落实情况）； 
本项目主要依托于哈尔滨工业大学（深圳）广东省空天通信与网络技术重点
实验室（2018 年成立），该实验室面向空间信息网络领域的基础设施较为完善，现
有专用实验室面积1000 平方米，拥有各类通用、专用测试测试仪器超过100 台（套）。
其中包括中星10 号Ku 频段收发天线2 套，实验用无人机5 套，USRP 软件无线
电平台（USRP-2930、USRP-2974）35 套，LabVIEW 软件套装1 套，R&S 的信号
分析系统（FSQ26），泰克的高性能示波器（DPO070804）、频谱分析仪（RSA3308A）、
逻辑分析仪（TLA5204B），以及安捷伦的信号发生器（E8267D）等高端设备，总
价值超过3000 万元，2022 年底计划建成天基物联网分布式数据中心（具有≥3000T
的存储能力，≥5000 颗计算核心，可支持≥300 物理节点的10Gbps 高速数据交换机
组），具备良好的科研平台基础保障。项目申请人是该重点实验室的核心骨干成员，
并长期从事空间信息网络领域的相关研究工作，参与过863 项目、国家自然科学
基金重点、国家自然科学基金重大研究计划、中国工程院战略咨询等国家级项目，
积累了扎实的理论研究基础和丰富的工程实践经验。另外，申请人所在团队自主
开发研制的空间通信仿真演示验证平台，如图22 所示，可实现卫星场景规划、计
算任务编排、链路质量预算、网络协议仿真等功能，可以为本项目提出的相关技
术提供仿真测试环境。本项目还可依托哈尔滨工业大学与航天通信专用通信技术
联合实验室，该研究所拥有美国CANDENCE 公司的卫星网络专用仿真软件
SATLAB，以及CADENCE 公司的网络级仿真软件BONES、信号级仿真软件SPW，
以及10 多台SUN 工作站和100 多台PC 终端。另外，哈工大通信技术研究所拥有
VSAT 卫星地面站、小卫星地面测控站、仪器设备有IFR 公司和Agilent 公司生产
的频率至26GHz 的矢量信号源、矢量频谱分析仪、扫频仪及规程分析仪等仪器可
供本项目使用。 
 
图22 卫星通信网络仿真演示验证平台 
综上，本项目团队拥有良好的实验条件和科学仪器、强大的科研创新载体和
丰富的科研项目经验，可为本项目的科研活动开展、总体思路设计、技术方案实
NSFC 2022
第 45 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 47 页 ===
施和项目成果验证提供充分的平台支撑。 
 
3．正在承担的与本项目相关的科研项目情况（申请人和主要参与
者正在承担的与本项目相关的科研项目情况，包括国家自然科学基金
的项目和国家其他科技计划项目，要注明项目的资助机构、项目类别、
批准号、项目名称、获资助金额、起止年月、与本项目的关系及负责
的内容等）； 
本项目申请人正在承担的与本项目相关的科研项目情况如下： 
[1] 国家自然科学基金，重点项目，61831008，面向载人登月的空间信息传输
理论与关键技术，296 万元，2019-01 至2023-12。申请人负责月表分布式数据存传
系统与修复方案设计工作，可为本项目巨星座存储系统的在轨数据修复机制设计
提供技术支撑。 
[2] 国家自然科学基金，重大科研仪器研制项目，62027802，深空通信信道模
拟仿真器的研制，691.36 万元，2021-01 至2025-12。申请人负责深空信道模拟器
的分布式计算平台模块设计工作，可为本项目在轨编码分布式计算分配机制的研
究提供仿真平台支撑。 
参与其他国家自然科学基金面上项目在研4 项，与本项目相关性不大。 
 
4．完成国家自然科学基金项目情况（对申请人负责的前一个已资
助期满的科学基金项目（项目名称及批准号）完成情况、后续研究进
展及与本申请项目的关系加以详细说明。另附该项目的研究工作总结
摘要（限500 字）和相关成果详细目录）。 
申请人前一个已结题科学基金项目： 
国家自然科学基金青年项目，面向深空通信网络中数据流业务的无码率传输
协议研究（项目编号61701136），2018-01 至2020-12，24.5 万元，已结题 
该项目聚焦国内外深空探测与空间通信的快速发展趋势，以大容量数据流业
务在深空网络化传输体制内的传输效率提升为根本解决问题，在链路频繁中断、
长延迟、拓扑变化的通信环境下，通过无码率编码与延迟中断容忍网络协议的联
合优化设计，实现数据流业务的高效可靠传输。 
该项目后续研究主要围绕着分布式编码技术在低轨星座网络中的应用拓展进
行深入探索，在星地融合网络计算任务分配、天基物联网缓存内容分发与数据修
复等方面承担了相应的省市级课题研究。本次申请项目是在青年项目的基础上，
聚焦于基于分布式编码的巨星座网络协同容错机制加以深入研究，进一步挖掘分
布式编码（无速率码、可再生码等）的负载均衡、灵活可控等优势在卫星分布式
计算与存储网络中的应用价值。在研究思路上，结合时变网络连通度、在轨资源
NSFC 2022
第 46 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 48 页 ===
状态建模、卫星网络集群划分等相关技术，创新设计适用于巨星座网络在轨处理
的编码协作传输、编码计算分配、编码数据修复等协同容错机制，实现巨星座网
络容错能力的提升。本项目的执行过程可借鉴青年项目中对多路径编码协作传输
方案的设计思路，以及对无速率编码码字的优化设计方法，为本项目的顺利完成
提供前期支撑。 
已结题项目研究工作总结： 
该项目执行期从2018 年1 月到2020 年12 月，执行过程中按照计划书的预定
目标和技术路线开展了相应的研究，完成了全部研究内容，具体包括以下几个方
面： 
（1）深空数据流传输信道模型与自适应无码率编码策略； 
（2）深空异步无码率协作网络传输方案与非等差错保护策略； 
（3）深空机会式网络模型与无码率中继转发方案； 
（4）深空DTN 网络协议与网络编码联合设计； 
（5）深空探测通信网络仿真测试平台设计与实现。 
通过本项目的研究，拓展了深空探测通信网络的信息传输理论与方法，为我
国未来构建火星探测网络和星际互联网等研究计划提供了一定的技术方案。研究
内容中的时变拓扑分析、轨道参数设计、自适应无速率编码策略、分布式编码协
作、协议聚合分块优化等关键技术，能够为我国空间信息网络的高质量信息服务
提供有力支撑。同时，完成了DTN 网络存算传一体化仿真演示平台，对DTN 网
络存算传一体化资源分配方法进行了仿真，并对能够对深空探测任务进行实景化
演示。 
项目执行期间，发表学术论文21 篇，其中期刊论文9 篇（8 篇为SCI 检索论
文），会议论文12 篇（均为EI 检索论文）；参与了9 次国际学术会议，获得1 次
会议优秀论文；申请及授权国家方面专利6 项（已授权4 项）；同时培养博士后人
员出站1 名，博士研究生毕业2 名，硕士研究生毕业6 名。 
相关成果详细目录： 
（1）发表学术论文： 
[1]. S. Gu, Q. Zhang* and W. Xiang, Coded storage-and-computation: a new paradigm to enhancing 
intelligent services in space-air-ground integrated networks[J], IEEE Wireless Communications, 
vol. 27, no. 6, pp. 44-51, Dec. 2020.  
[2]. S. Gu, Y. Wang*, G. Chen, and S. Shi, Service-driven coded forward scheme based on 
streaming transmission model for lunar space communication networks[J], China 
Communications, vol.17, no.7, pp. 15-26, Jul. 2020.  
[3]. S. Gu, J. Jiao*, Z. Huang, S. Wu, and Q. Zhang, ARMA-based adaptive coding transmission 
over millimeter-wave channel for integrated satellite-terrestrial networks[J]. IEEE Access, vol. 
6, pp. 21635-21645, May 2018.  
[4]. S. Gu, J. Jiao*, Q. Zhang, and X. Gu. Rateless coding transmission over multi-state dying 
erasure channel for SATCOM[J]. EURASIP Journal on Wireless Communications and 
Networking, vol. 1, no.176, Oct. 2017.  
NSFC 2022
第 47 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 49 页 ===
[5]. S. Gu*, Y. Wang, N. Wang, and W. Wu, Intelligent optimization of availability and 
communication cost in satellite-UAV mobile edge caching system with fault-tolerant codes[J], 
IEEE Transactions on Cognitive Communications and Networking, vol. 6, no. 4, pp. 1230-1241, 
Dec. 2020.  
[6]. Y. Wang, S. Gu*, L. Zhao, N. Zhang, W. Xiang, and Q. Zhang, Repairable fountain coded 
storage systems for multi-tier mobile edge caching networks[J]. IEEE Transactions on Network 
Science and Engineering, vol. 7, no. 4, pp. 2310-2322, Oct.-Dec. 2020. 
[7]. S. Gu, Y. Wang, Y. Wang*, Q. Zhang, and X. Qin, Grouping-based consistency protocol design 
for end-edge-cloud hierarchical storage system[J], IEEE Access, vol. 8, pp. 8959-8973, Jan. 
2020.  
[8]. S. Gu, J. Li, Y. Wang*, N. Wang, and Q. Zhang, DR-MDS: an energy-efficient coding scheme in 
D2D distributed storage network for the Internet of Things[J]. IEEE Access, vol. 7, pp. 
24179-24191, Mar. 2019.  
[9]. 张钦宇*, 顾术实, 王野, 薛佳音. 空间物联网的分布式数据存储与传输技术[J]. 物联网学
报. 2018, 2(4): 22-30.  
[10]. X. Sun, S. Gu, Y. Wang, K. Liu, N. Zhang, and Q. Zhang, Degraded read coding scheme in 
heterogeneous distributed cloud storage system for internet of things data[C], in 2020 IEEE 
91st Vehicular Technology Conference Workshop (VTC2020-Spring), Antwerp, Belgium, May 
2020.  
[11]. K. Li, S. Gu, Y. Wang, Q. Zhang, and W. Xiang. Repair bandwidth cost of generalized 
regenerating codes for clustered distributed storage[C]. in 2019 11th International Conference 
on Wireless Communications and Signal Processing (WCSP), Xi’an, China, Oct. 2019, pp. 1-6. 
[12]. Y. Xu, S. Gu, Z. Yang, and Q. Zhang. Relay buffer assisted DTN bundle protocol design via 
network coding[C]. in 2019 IEEE/CIC International Conference on Communications 
Workshops in China (ICCC WS), Changchun, China, Aug. 2019, pp. 7-12.  
[13]. G. Chen, S. Gu, Y. Wang, Q. Zhang, and N. Zhang. Streaming transfer model and forward 
scheme for satellite opportunistic network[C]. in 2019 IEEE/CIC International Conference on 
Communications in China (ICCC), Changchun, China, Aug. 2019, pp. 416-421.  
[14]. Y. Li, S. Gu, Y. Wang, W. Xiang, and Q. Zhang. Repairable fountain codes with unequal 
locality for heterogeneous D2D data storage networks[C]. in 10th EAI International Conference 
on Wireless and Satellite Systems (WiSATS), Harbin, China, Jan. 2019, pp. 514-528. (Best 
Paper Award) 
[15]. J. Li, S. Gu, Y. Wang, and Q. Zhang. Double replication MDS codes for wireless D2D 
distributed storage networks[C]. in 2018 10th International Conference on Wireless 
Communications and Signal Processing (WCSP), Hangzhou, China, Oct. 2018, pp. 1-6.  
[16]. Y. Li, S. Gu, Y. Wang, J. Li, and Q. Zhang. Repairable fountain codes with unequal repairing 
locality in D2D storage system[C]. in Proceedings of 10th EAI International Conference on Ad 
Hoc Networks (ADHOCNETS), Cairns, Australia, Sep. 2018, pp. 272-281.  
[17]. K. Li, S. Gu, Y. Wang, and Q. Zhang. Low-complexity shorten MSR code for limited 
bandwidth systems[C]. in 2018 IEEE/CIC International Conference on Communications in 
China (ICCC), Beijing, China, Aug. 2018, pp. 882-887.  
[18]. G. Chen, S. Gu, Y. Wang, J. Jiao, and Q. Zhang. Systematic raptor codes with UEP property for 
image media transmission[C]. in the 7th International Conference on Communications, Signal 
Processing, and Systems (CSPS), Dalian, China, July. 2018, pp. 403-410.  
NSFC 2022
第 48 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 50 页 ===
[19]. K. Li, S. Gu, Y. Wang, J. Jiao, and Q. Zhang. A low complexity shorten regenerating code with 
optimal repair bandwidth[C]. in the 7th International Conference on Communications, Signal 
Processing, and Systems (CSPS), Dalian, China, July. 2018, 361-369.  
[20]. J. Li, S. Gu, Y. Wang, Y. Li, and Q. Zhang. Multi-parameter analysis for distributed storage 
schemes in wireless D2D networks[C]. in the 7th International Conference on Communications, 
Signal Processing, and Systems (CSPS), Dalian, China, July. 2018, 376-384.  
[21]. S. Gu, J. Jiao and Q. Zhang. Intermediate performance of rateless codes over dying erasure 
channel[C]. in the 6th International Conference on Communications, Signal Processing, and 
Systems (CSPS), Harbin, China, July. 2017, pp. 45-52.  
 
（2）申请及授权发明专利： 
[1]. 顾术实，陈国庆，王野，袁鹏，张钦宇. 机会式卫星网络的转发方法，发明专利，授权号: 
CN110149140B, 授权日期: 2021.09.14.   
[2]. 顾术实，徐运来，杨志华，王野，张钦宇. 基于网络编码和中继缓存辅助的DTN 数据传
输方法，发明专利，授权号: CN110138432B, 授权日期: 2021.06.29.  
[3]. 张钦宇，顾术实，王洋，焦健，欧阳任耕. 一种适用于濒死信道的无速率编码传输方法, 发
明专利, 授权号: CN105141386B, 授权日期 : 2018.03.09. 
[4]. 张钦宇，顾术实，焦健，杨志华. 一种基于LT 码的空间DTN 传输方法, 发明专利, 授权
号: CN103354479B, 授权日期 : 2017.02.08. 
[5]. 顾术实，王念念，王野，李柯，李娟，张钦宇. 基于双倍复制的MDS 缓存方案，发明专
利，申请号: 201811267307.2，申请日: 2018.10.29，申请公布号: CN109445990A，申请公
开日: 2019.03.08.   
[6]. 顾术实，李柯，王野，王念念，李月，张钦宇. 非等局部域的可修复喷泉码设计方法，发
明专利，申请号: 201910090736.5，申请日: 2019.01.28， 申请公布号 : CN109756873A，
申请公开日: 2019.05.14.  
 
 
 
NSFC 2022
第 49 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 51 页 ===
（三）其他需要说明的情况 
1. 申请人同年申请不同类型的国家自然科学基金项目情况（列明
同年申请的其他项目的项目类型、项目名称信息，并说明与本项目之
间的区别与联系）。 
无 
2. 具有高级专业技术职务（职称）的申请人或者主要参与者是否
存在同年申请或者参与申请国家自然科学基金项目的单位不一致的情
况；如存在上述情况，列明所涉及人员的姓名，申请或参与申请的其
他项目的项目类型、项目名称、单位名称、上述人员在该项目中是申
请人还是参与者，并说明单位不一致原因。 
无 
3. 具有高级专业技术职务（职称）的申请人或者主要参与者是否
存在与正在承担的国家自然科学基金项目的单位不一致的情况；如存
在上述情况，列明所涉及人员的姓名，正在承担项目的批准号、项目
类型、项目名称、单位名称、起止年月，并说明单位不一致原因。 
无 
4. 其他。 
无 
NSFC 2022
第 50 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 52 页 ===
顾术实 简历
哈尔滨工业大学,  哈尔滨工业大学（深圳）,  讲师
教育经历：
(1) 2012-03 至 2016-01, 哈尔滨工业大学, 信息与通信工程, 博士
(2) 2009-09 至 2012-01, 哈尔滨工业大学, 信息与通信工程, 硕士
(3) 2005-09 至 2009-07, 哈尔滨工业大学, 测控技术与仪器, 学士
博士后工作经历：
(1) 2016-12 至 2019-11, 哈尔滨工业大学，哈尔滨工业大学（深圳）
(2) 2018-08 至 2019-08, 澳大利亚詹姆斯库克大学，NBIoT实验室
科研与学术工作经历（博士后工作经历除外）：
(1) 2019-12 至 今, 哈尔滨工业大学, 哈尔滨工业大学（深圳）, 讲师
曾使用其他证件信息：
无
近五年主持或参加的国家自然科学基金项目/课题：
(1) 国家自然科学基金委员会, 面上项目, 62171159, 面向星地融合网络的智能边缘缓存机制研究, 2022-
01-01 至 2025-12-31, 56万元, 在研, 参与
(2) 国家自然科学基金委员会, 国家重大科研仪器研制项目, 62027802, 深空通信信道模拟仿真器的研制,
2021-01-01 至 2025-12-31, 691.36万元, 在研, 参与
(3) 国家自然科学基金委员会, 面上项目, 62071141, 空间物联网的信息时效与大规模接入技术研究, 202
1-01-01 至 2024-12-31, 64万元, 在研, 参与
(4) 国家自然科学基金委员会, 重点项目, 61831008, 面向载人登月的空间信息传输理论与关键技术, 201
9-01-01 至 2023-12-31, 296万元, 在研, 参与
(5) 国家自然科学基金委员会, 面上项目, 61871147, 信息时效约束下的深空测控通信传输技术研究, 201
9-01-01 至 2022-12-31, 63万元, 在研, 参与
(6) 国家自然科学基金委员会, 面上项目, 61871426, 基于连通控制划分的飞行自组网络拓扑控制机理, 2
019-01-01 至 2022-12-31, 63万元, 在研, 参与
(7) 国家自然科学基金委员会, 面上项目, 61871151, 基于水声信道辨识的前导检测和信道跟踪机理研究,
2019-01-01 至 2019-12-31, 16万元, 结题, 参与
(8) 国家自然科学基金委员会, 青年科学基金项目, 61801145, 面向无线传感器网络的联合交替优化算法
与能量收集策略研究, 2019-01-01 至 2021-12-31, 24万元, 在研, 参与
(9) 国家自然科学基金委员会, 面上项目, 61771158, 高通量天基信息网络容量与多址技术研究, 2018-01
-01 至 2018-12-31, 16万元, 结题, 参与
(10) 国家自然科学基金委员会, 青年科学基金项目, 61701136, 面向深空通信网络中数据流业务的无码率
传输协议研究, 2018-01-01 至 2020-12-31, 24.5万元, 结题, 主持
(11) 国家自然科学基金委员会, 面上项目, 61771163, 面向空间信息网络的高能效非正交接入方法研究,
NSFC 2022
第 51 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 53 页 ===
2018-01-01 至 2021-12-31, 65万元, 在研, 参与
近五年主持或参加的其他科研项目/课题（国家自然科学基金项目除外）：
(1) 广东省基础与应用基础研究基金委员会, 自然科学基金面上项目, 2021A1515011572, 基于编码计算的
星地融合网络协同任务分配方法研究, 2021-01 至 2023-12, 10万元, 在研, 主持
(2) 深圳市科技创新委员会, 高等院校稳定支持计划（面上项目）, GXWD20201230155427003-20200824081
029001, 面向天基物联网的高能效内容缓存与数据修复技术研究, 2021-01 至 2022-12, 40万元, 在研, 主持
(3) 中国博士后科学基金会, 面上项目, 2018M630357, 高低轨混合星座网络中流业务驱动的分布式编码传
输机理, 2018-05 至 2019-11, 8万元, 结题, 主持
代表性研究成果和学术奖励情况：
一、代表性论著：
(1) Shushi Gu; Qinyu Zhang; Wei Xiang ; Coded Storage-and-Computation: A New Paradigm to Enha
ncing Intelligent Services in Space-Air-Ground Integrated Networks, IEEE Wireless Communications,
2020, 27(6): 44-51      (期刊论文)
(2) Shushi Gu; Wancheng Lu; Wei Xiang; Ning Zhang; Qinyu Zhang ; Repair Delay Analysis of Mob
ile Storage Systems Using Erasure Codes and Relay Cooperation, IEEE Transactions on Vehicular Tec
hnology, 2021, 70(10): 10580-10593      (期刊论文)
(3) Shushi Gu; Yan Tan; Ning Zhang; Qinyu Zhang ; Energy-Efficient Content Placement with Cod
ed Transmission in Cache-Enabled Hierarchical Industrial IoT Networks, IEEE Transactions on Indus
trial Informatics, 2021, 17(8): 5699-5708      (期刊论文)
(4) Shushi Gu; Ye Wang; Niannian Wang; Wen Wu ; Intelligent Optimization of Availability and 
Communication Cost in Satellite-UAV Mobile Edge Caching System With Fault-Tolerant Codes, IEEE Tr
ansactions on Cognitive Communications and Networking, 2020, 6(4): 1230-1241      (期刊论文)
(5) Gu, Shushi; Wang, Ye; Chen, Guoqing; Shi, Shuo ; Service-Driven Coded Forward Scheme Base
d on Streaming Transmission Model for Lunar Space Communication Networks, China Communications, 2
020, 17(7): 15-26      (期刊论文)
二、论著之外的代表性研究成果和学术奖励：
(1) 顾术实; 徐运来; 杨志华; 王野; 张钦宇 ; 基于网络编码和中继缓存辅助的DTN数据传输方法, 2019-
5-16, 中国, CN201910404890.5      (专利)
(2) 顾术实; 陈国庆; 王野; 袁鹏; 张钦宇 ; 卫星机会式网络的转发方法, 2019-5-17, 中国, CN2019104
14554.9      (专利)
(3) 张钦宇; 顾术实; 王洋; 焦健; 欧阳任耕 ; 一种适用于濒死信道的无速率编码传输方法, 2015-8-19,
中国, CN201510510795.5      (专利)
(4) 张钦宇; 顾术实; 焦健; 杨志华 ; 一种基于LT码的空间DTN传输方法, 2013-3-22, 其他国家, CN2013
10097489.4      (专利)
(5) 顾术实; 王福刚; 张智凯; 张钦宇; 孙新毅 ; 用于卫星集群存储网络的混合再生编码修复方法及系统
, 2021-7-28, 中国, 202110856458.7      (专利)
(6) 顾术实; 孙新毅; 郭云开; 逄博; 张钦宇 ; 空天地一体化网络的编码缓存内容放置与资源联合优化方
法, 2021-3-8, 中国, 202110251280.3      (专利)
(7) 顾术实; 鲁万城; 谭燕; 孙先范; 张钦宇 ; 一种面向移动存储系统的中继协作数据修复方法及系统, 
NSFC 2022
第 52 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 54 页 ===
2020-7-27, 中国, 202010730214.X      (专利)
(8) 顾术实; 谭燕; 孙先范; 鲁万城; 张钦宇 ; 异构网络中高能效编码缓存内容放置方案设计方法, 2020
-6-22, 中国, CN202010573250.X      (专利)
(9) 顾术实; 王念念; 王野; 李柯; 李娟; 张钦宇 ; 基于双倍复制的MDS缓存方案, 2018-10-29, 中国, C
N201811267307.2      (专利)
(10) 顾术实; 李柯; 王野; 王念念; 李月; 张钦宇 ; 非等局部域的可修复喷泉码设计方法, 2019-1-28, 
中国, CN201910090736.5      (专利)
NSFC 2022
第 53 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 55 页 ===
史瑶 简历
哈尔滨工业大学,  哈尔滨工业大学（深圳）,  讲师
教育经历：
(1) 2017-09 至 2021-02, 曼彻斯特大学, 电气与电子工程, 博士
(2) 2015-09 至 2017-07, 哈尔滨工业大学, 信息与通信工程, 硕士
(3) 2011-09 至 2015-06, 哈尔滨工程大学, 通信工程, 学士
博士后工作经历：
无
科研与学术工作经历（博士后工作经历除外）：
(1) 2021-09 至 今, 哈尔滨工业大学, 哈尔滨工业大学（深圳）, 讲师
曾使用其他证件信息：
无
近五年主持或参加的国家自然科学基金项目/课题：
(1) 国家自然科学基金委员会, 面上项目, 61671183, 基于认知的星地混合移动通信系统的动态接入方法,
2017-01-01 至 2020-12-31, 58万元, 结题, 参与
近五年主持或参加的其他科研项目/课题（国家自然科学基金项目除外）：
(1) 欧盟, Horizon 2020 Research and Innovation Programme through the Marie Sklodowska Curie,
812991, Energy-autonomous portable access points for infrastructure-less networks, 2018-10 至 202
2-10, 3000万元, 在研, 参与
代表性研究成果和学术奖励情况：
一、代表性论著：
(1) Yao Shi; Mutasem Q. Hamdan; Emad Alsusa School of Electrical and Electronic En; Khairi A.
Hamdi; Mohammed W. Baidas ; A Decoupled Access Scheme with Reinforcement Learning Power Control f
or Cellular-Enabled UAVs, IEEE Internet of Things Journal, 2021, 8(24): 17261-17274      (期刊论
文)
(2) Yao Shi; Emad Alsusa; Mohammed W. Baidas ; Joint DL/UL Decoupled Cell-Association and Res
ource Allocation in D2D-Underlay HetNets, IEEE Transactions on Vehicular Technology, 2021, 70(4):
3640-3651      (期刊论文)
(3) Yao Shi; Mohammed W. Baidas; Emad Alsusa ; Energy-Efficient Decoupled Access Scheme for C
ellular-Enabled UAV Communication Systems, IEEE Systems Journal, 2021, 0(0): 1-12      (期刊论文)
(4) Yao Shi; Mohammed Wael Baidas; Aysha Ebrahim; Emad Alsusa ; Uplink Performance Enhancemen
t Through Adaptive Multi-Association and Decoupling in UHF-mmWave Hybrid Networks, IEEE Transacti
ons on Vehicular Technology, 2019, 68(10): 9735-9746      (期刊论文)
(5) Yao Shi; Emad Alsusa; Aysha Ebrahim ; Single and Dual Connectivity for Decoupled Uplink a
nd Downlink Access in UHF-mmWave Hybrid Networks, 2018 IEEE Global Communications Conference (GLO
NSFC 2022
第 54 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 56 页 ===
BECOM), Abu Dhabi, United Arab Emirates, 2018-12-9至2018-12-13      (会议论文)
二、论著之外的代表性研究成果和学术奖励：
(1) Yao Shi(1/5); The Best Paper Award of The International Conference on Communications, Sig
nal Processing, and Systems (2016), CSPS'16会议组委会, 会议最佳论文, 其他, 2016(Yao Shi; Bo Yu; M
in Jia; Shilong Wang; Qing Guo )      (科研奖励)
(2) 贾敏; 史瑶; 杨健; 顾学迈; 郭庆; 刘晓锋 ; 多观测值向量稀疏度自适应压缩采样匹配追踪方法, 20
18-12-14, 中国, CN201510741861.X      (专利)
(3) 2021-10-30至2021-10-31, 举办EAI 6GN 2021 - 4th EAI International Conference on 6G for Fut
ure Wireless Networks, Huizhou, People's Republic of China, 6GN Community, Xuemai Gu      (举办或
参加学术会议)
NSFC 2022
第 55 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 57 页 ===
冯博文 简历
哈尔滨工业大学,  哈尔滨工业大学（深圳）,  无
教育经历：
(1) 2016-09 至 2021-07, 哈尔滨工业大学, 信息与通信工程, 博士
(2) 2014-09 至 2016-07, 哈尔滨工业大学, 信息与通信工程, 硕士
(3) 2010-09 至 2014-06, 哈尔滨工业大学, 电子信息工程, 学士
博士后工作经历：
(1) 2021-07 至 今, 在站, 哈尔滨工业大学, 哈尔滨工业大学（深圳）
科研与学术工作经历（博士后工作经历除外）：
无
曾使用其他证件信息：
无
近五年主持或参加的国家自然科学基金项目/课题：
(1) 国家自然科学基金委员会, 面上项目, 62071141, 空间物联网的信息时效与大规模接入技术研究, 202
1-01-01 至 2024-12-31, 64万元, 在研, 参与
(2) 国家自然科学基金委员会, 面上项目, 61972113, 多维边缘网络资源协同优化与运营, 2020-01-01 至
2023-12-31, 60万元, 在研, 参与
(3) 国家自然科学基金委员会, 青年科学基金项目, 61801145, 面向无线传感器网络的联合交替优化算法
与能量收集策略研究, 2019-01-01 至 2021-12-31, 24万元, 在研, 参与
(4) 国家自然科学基金委员会, 面上项目, 61871147, 信息时效约束下的深空测控通信传输技术研究, 201
9-01-01 至 2022-12-31, 63万元, 在研, 参与
(5) 国家自然科学基金委员会, 面上项目, 61771158, 高通量天基信息网络容量与多址技术研究, 2018-01
-01 至 2018-12-31, 16万元, 结题, 参与
(6) 国家自然科学基金委员会, 青年科学基金项目, 61701136, 面向深空通信网络中数据流业务的无码率
传输协议研究, 2018-01-01 至 2020-12-31, 24.5万元, 结题, 参与
(7) 国家自然科学基金委员会, 面上项目, 61771162, 多样化电磁频谱资源的复用机理及生态研究, 2018-
01-01 至 2018-12-31, 16万元, 结题, 参与
近五年主持或参加的其他科研项目/课题（国家自然科学基金项目除外）：
(1) 广东省基础与应用基础研究基金委员会, 区域联合基金-青年基金项目, 2021A1515110071, 面向低轨
卫星组网通信的高时效极化编码技术研究, 2021-10 至 2024-09, 10万元, 在研, 主持
代表性研究成果和学术奖励情况：
一、代表性论著：
(1) Feng Bowen; Jiao Jian; Wu Shaohua; Zhang Qinyu ; How to apply polar codes in high through
put space communications, Science China-Technological Sciences, 2020, 63(8): 1371-1382      (期刊
论文)
NSFC 2022
第 56 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 58 页 ===
(2) Feng Bowen; Jiao Jian; Wu Shaohua; Wang Ye; Zhang Qinyu ; Iterative and Adjustable Soft L
ist Decoding for Polar Codes, IEEE Transactions on Signal Processing, 2020, 68: 5559-5572      (
期刊论文)
(3) Bowen Feng; Shushi Gu; Jian Jiao; Shaohua Wu; Qinyu Zhang ; Novel polar-coded space-time 
transmit diversity scheme over Rician fading MIMO channels, EURASIP Journal on Wireless Communica
tions and Networking, 2018, 2018(1): 24      (期刊论文)
(4) Feng, Bowen; Zhang, Qinyu; Jiao, Jian ; An Efficient Rateless Scheme Based on the Extendi
bility of Systematic Polar Codes, IEEE ACCESS, 2017, 5(5): 23223-23232      (期刊论文)
(5) 冯博文; 焦健; 王莎; 吴绍华; 张钦宇 ; 基于树图剪枝的极化码译码简化算法, 系统工程与电子技术
, 2017, 39(2): 410-417      (期刊论文)
二、论著之外的代表性研究成果和学术奖励：
(1) 焦健; 冯博文; 顾术实; 吴绍华; 张钦宇; 一种极化码的简化译码方法, 2019-7-23, 中国, ZL201610
045755.2      (专利)
(2) 焦健; 冯博文; 王莎; 吴绍华; 张钦宇; 一种极化码级联空时码系统及其级联极化码编码方法, 2019-
7-23, 中国, ZL201610216554.4      (专利)
(3) 焦健; 王莎; 冯博文; 周刘; 吴绍华; 张钦宇; 基于打孔的码率兼容极化码编码方法及系统, 2017-6-
16, 中国, ZL201710458844.4      (专利)
(4) 焦健; 冯博文; 田园; 吴绍华; 张钦宇; 可调的串行抵消列表极化码译码方法和装置, 2019-10-23, 
中国, CN201911011582.2      (专利)
(5) 焦健; 冯博文; 田园; 吴绍华; 张钦宇; 极化码的迭代可调软串行抵消列表译码方法和装置, 2019-12
-27, 中国, CN201911380480.8      (专利)
NSFC 2022
第 57 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 59 页 ===
附件信息
序号
附件名称
备注
附件类型
1
代表性论著1
IEEE 著名杂志
代表性论著
2
代表性论著2
IEEE Trans 期刊
代表性论著
3
代表性论著3
IEEE Trans 期刊
代表性论著
4
代表性论著4
IEEE Trans 期刊
代表性论著
5
代表性论著5
国内通信期刊
代表性论著
6
专利1
已授权专利
其他
7
专利2
已授权专利
其他
8
专利3
已授权专利
其他
9
专利4
已授权专利
其他
10
专利5
已申请专利
其他
11
专利6
已申请专利
其他
12
专利7
已申请专利
其他
13
专利8
已申请专利
其他
14
专利9
已申请专利
其他
15
专利10
已申请专利
其他
NSFC 2022
第 58 页
国家自然科学基金申请书
2022版
版本：*****************


=== 第 60 页 ===
项目名称：面向在轨处理的巨星座网络协同编码容错机制研究
资助类型：面上项目
申请代码：F0106.空天通信
国家自然科学基金项目申请人和参与者承诺书
为了维护国家自然科学基金项目评审公平、公正，共同营造风清气正的科研生态，本人在此郑重
严格遵守《中华人民共和国科学技术进步法》《国家自然科学基金条例》《关于进一步加强科
承诺：
研诚信建设的若干意见》《关于进一步弘扬科学家精神加强作风和学风建设的意见》以及科技部、自
然科学基金委关于科研诚信建设有关规定和要求；申请材料信息真实准确，不含任何涉密信息或敏感
信息，不含任何违反法律法规或违反科研伦理规范的内容；在国家自然科学基金项目申请、评审和执
行全过程中，恪守职业规范和科学道德，遵守评审规则和工作纪律，杜绝以下行为：
（一）抄袭、剽窃他人申请书、论文等科研成果或者伪造、篡改研究数据、研究结论；
（二）购买、代写申请书；购买、代写、代投论文，虚构同行评议专家及评议意见；购买实验数
据；
（三）违反成果发表规范、署名规范、引用规范，擅自标注或虚假标注获得科技计划等资助；
（四）在项目申请书中以高指标通过评审，在项目计划书中故意篡改降低相应指标；
（五）以任何形式探听或散布尚未公布的评审专家名单及其他评审过程中的保密信息；
（六）本人或委托他人通过各种方式和途径联系有关专家进行请托、游说，违规到评审会议驻地
窥探、游说、询问等干扰评审或可能影响评审公正性的行为；
（七）向工作人员、评审专家等提供任何形式的礼品、礼金、有价证券、支付凭证、商业预付
卡、电子红包，或提供宴请、旅游、娱乐健身等任何可能影响评审公正性的活动；
（八）违反财经纪律和相关管理规定的行为；
（九）其他弄虚作假行为。
如违背上述承诺，本人愿接受国家自然科学基金委员会和相关部门做出的各项处理决定，包括但
不限于撤销科学基金资助项目，追回项目资助经费，向社会通报违规情况，取消一定期限国家自然科
学基金项目申请资格，记入科研诚信严重失信行为数据库以及接受相应的党纪政务处分等。
申请人签字：
编号
参与者姓名 / 工作单位名称（应与加盖公章一致）/ 证件号码
签字
1
  
  
史瑶/ 哈尔滨工业大学/ 4**************3
 
2
  
  
冯博文/ 哈尔滨工业大学/ 2**************X
 
3
 
 
4
 
 
5
 
 
6
 
 
7
 
 
8
 
 
9
 
 
国家自然科学基金申请书
2022版


=== 第 61 页 ===
项目名称：
面向在轨处理的巨星座网络协同编码容错机制研究
资助类型：
面上项目
申请代码：
F0106.空天通信
国家自然科学基金项目申请单位承诺书
为了维护国家自然科学基金项目评审公平、公正，共同营造风清气正的科研生态，本单位郑重承
申请材料中不存在违背《中华人民共和国科学技术进步法》《国家自然科学基金条例》《关于进
诺：
一步加强科研诚信建设的若干意见》《关于进一步弘扬科学家精神加强作风和学风建设的意见》以及
科技部、自然科学基金委关于科研诚信建设有关规定和要求的情况；申请材料符合《中华人民共和国
保守国家秘密法》和《科学技术保密规定》等有关法律法规和规章制度要求，不含任何涉密信息或敏
感信息；申请材料不含任何违反法律法规或违反科研伦理规范的内容；申请人符合相应项目的申请资
格；在项目申请和评审活动全过程中，遵守有关评审规则和工作纪律，杜绝以下行为：
（一）以任何形式探听或公布未公开的项目评审信息、评审专家信息及其他评审过程中的保密信
息，干扰评审专家的评审工作；
（二）组织或协助申请人/参与者向工作人员、评审专家等给予任何形式的礼品、礼金、有价证
券、支付凭证、商业预付卡、电子红包等；宴请工作人员、评审专家，或组织任何可能影响科学基金
评审公正性的活动；
（三）支持、放任或对申请人/参与者抄袭、剽窃、重复申报、提供虚假信息（含身份和学术信
息）等不当手段申报国家自然科学基金项目疏于管理；
（四）支持或协助申请人/参与者采取“打招呼”“围会”等方式影响科学基金项目评审；
（五）其他违反财经纪律和相关管理规定的行为。
如违背上述承诺，本单位愿接受自然科学基金委和相关部门做出的各项处理决定，包括但不限于
停拨或核减经费、追回项目已拨经费、取消本单位一定期限国家自然科学基金项目申请资格、记入科
研诚信严重失信行为数据库以及主要责任人接受相应党纪政务处分等。
依托单位公章:
日期：
年
月
日
合作研究单位公章:
日期：
年
月
日
 
合作研究单位公章:
日期：
年
月
日
国家自然科学基金申请书
2022版
