#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1.3 拟解决的关键问题可视化图
Key Scientific Problems Visualization
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import matplotlib.patches as patches

# 设置英文字体和图表样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10
sns.set_style("white")
plt.style.use('seaborn-v0_8-whitegrid')

def create_key_problems_diagram():
    """创建关键科学问题结构图"""
    fig = plt.figure(figsize=(18, 14), facecolor='white')
    ax = fig.add_subplot(111)
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 12)
    ax.axis('off')

    # 添加渐变背景
    gradient = np.linspace(0, 1, 256).reshape(256, -1)
    gradient = np.vstack((gradient, gradient))
    ax.imshow(gradient, extent=[0, 12, 0, 12], aspect='auto', cmap='Blues', alpha=0.1)

    # 定义现代化颜色方案
    colors = {
        'core': '#1E3A8A',      # 核心问题 - 深蓝
        'sub1': '#7C3AED',      # 子问题1 - 紫色
        'sub2': '#F59E0B',      # 子问题2 - 琥珀色
        'sub3': '#EF4444',      # 子问题3 - 红色
        'challenge': '#059669', # 挑战 - 翠绿
        'solution': '#8B5CF6'   # 解决方案 - 紫罗兰
    }
    
    # 核心科学问题 - 添加阴影效果
    shadow_box = FancyBboxPatch((3.05, 8.45), 6, 1.8,
                               boxstyle="round,pad=0.15",
                               facecolor='gray',
                               alpha=0.3)
    ax.add_patch(shadow_box)

    core_box = FancyBboxPatch((3, 8.5), 6, 1.8,
                             boxstyle="round,pad=0.15",
                             facecolor=colors['core'],
                             edgecolor='white',
                             linewidth=3,
                             alpha=0.95)
    ax.add_patch(core_box)

    # 添加装饰性边框
    inner_box = FancyBboxPatch((3.1, 8.6), 5.8, 1.6,
                              boxstyle="round,pad=0.1",
                              facecolor='none',
                              edgecolor='lightblue',
                              linewidth=1,
                              alpha=0.7)
    ax.add_patch(inner_box)

    ax.text(6, 9.6, 'CORE SCIENTIFIC PROBLEM',
            ha='center', va='center', fontsize=16, fontweight='bold', color='white',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='darkblue', alpha=0.8))
    ax.text(6, 9.15, 'Multi-dimensional Performance Dynamic Adaptation',
            ha='center', va='center', fontsize=14, fontweight='bold', color='white')
    ax.text(6, 8.85, 'of Communication-Sensing-Security Integrated Network System',
            ha='center', va='center', fontsize=11, color='lightcyan', style='italic')
    
    # 三个子问题 - 重新设计布局
    sub_problems = [
        {
            'pos': (1, 6),
            'size': (3.2, 1.5),
            'color': colors['sub1'],
            'title': 'SUB-PROBLEM 1',
            'content': 'Privacy Leakage\n"Invisibility" &\n"Immeasurability"',
            'detail': 'Physical layer privacy\ncharacterization and\nquantification challenge',
            'icon': '🔒'
        },
        {
            'pos': (4.4, 6),
            'size': (3.2, 1.5),
            'color': colors['sub2'],
            'title': 'SUB-PROBLEM 2',
            'content': 'Security-Privacy\n"Conflict" &\n"Compatibility"',
            'detail': 'Joint optimization of\nsecurity and privacy\nobjectives challenge',
            'icon': '⚖️'
        },
        {
            'pos': (7.8, 6),
            'size': (3.2, 1.5),
            'color': colors['sub3'],
            'title': 'SUB-PROBLEM 3',
            'content': 'Multi-dimensional\n"Theoretical Boundary"\n& "Implementation Path"',
            'detail': 'Dynamic adaptation\nmechanism design\nchallenge',
            'icon': '🎯'
        }
    ]
    
    for i, prob in enumerate(sub_problems):
        # 添加阴影
        shadow_box = FancyBboxPatch((prob['pos'][0] + 0.05, prob['pos'][1] - 0.05),
                                   prob['size'][0], prob['size'][1],
                                   boxstyle="round,pad=0.1",
                                   facecolor='gray',
                                   alpha=0.3)
        ax.add_patch(shadow_box)

        # 子问题主框
        sub_box = FancyBboxPatch(prob['pos'], prob['size'][0], prob['size'][1],
                                boxstyle="round,pad=0.1",
                                facecolor=prob['color'],
                                edgecolor='white',
                                linewidth=2,
                                alpha=0.9)
        ax.add_patch(sub_box)

        # 添加渐变效果的内框
        inner_box = FancyBboxPatch((prob['pos'][0] + 0.1, prob['pos'][1] + 0.1),
                                  prob['size'][0] - 0.2, prob['size'][1] - 0.2,
                                  boxstyle="round,pad=0.05",
                                  facecolor='none',
                                  edgecolor='lightblue',
                                  linewidth=1,
                                  alpha=0.5)
        ax.add_patch(inner_box)

        # 子问题标题
        ax.text(prob['pos'][0] + prob['size'][0]/2, prob['pos'][1] + prob['size'][1] - 0.25,
                prob['title'], ha='center', va='center',
                fontsize=12, fontweight='bold', color='white',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='darkblue', alpha=0.7))

        # 子问题内容
        ax.text(prob['pos'][0] + prob['size'][0]/2, prob['pos'][1] + prob['size'][1]/2 + 0.1,
                prob['content'], ha='center', va='center',
                fontsize=10, color='white', fontweight='bold')

        # 添加图标
        ax.text(prob['pos'][0] + 0.3, prob['pos'][1] + prob['size'][1] - 0.3,
                prob['icon'], ha='center', va='center', fontsize=20)

        # 美化的连接线到核心问题
        con = ConnectionPatch((prob['pos'][0] + prob['size'][0]/2, prob['pos'][1] + prob['size'][1]),
                             (6, 8.5), "data", "data",
                             arrowstyle="->", shrinkA=5, shrinkB=5,
                             mutation_scale=25, fc="darkblue", ec="darkblue",
                             alpha=0.7, linewidth=2)
        ax.add_artist(con)
    
    # 技术挑战 - 重新设计为圆形
    challenges = [
        {
            'pos': (1.5, 4),
            'radius': 0.6,
            'text': 'Identity Privacy\nLeakage Risk',
            'icon': '⚠️'
        },
        {
            'pos': (3.5, 4),
            'radius': 0.6,
            'text': 'Multi-objective\nOptimization Conflict',
            'icon': '⚡'
        },
        {
            'pos': (6.5, 4),
            'radius': 0.6,
            'text': 'Performance\nTrade-off Complexity',
            'icon': '🔄'
        },
        {
            'pos': (8.5, 4),
            'radius': 0.6,
            'text': 'Real-time Adaptation\nRequirement',
            'icon': '⏱️'
        }
    ]

    for challenge in challenges:
        # 阴影
        shadow_circle = plt.Circle((challenge['pos'][0] + 0.05, challenge['pos'][1] - 0.05),
                                  challenge['radius'], color='gray', alpha=0.3)
        ax.add_patch(shadow_circle)

        # 主圆圈
        challenge_circle = plt.Circle(challenge['pos'], challenge['radius'],
                                    color=colors['challenge'], alpha=0.8,
                                    linewidth=2, edgecolor='white')
        ax.add_patch(challenge_circle)

        # 内圆装饰
        inner_circle = plt.Circle(challenge['pos'], challenge['radius'] - 0.1,
                                color='none', linewidth=1,
                                edgecolor='lightgreen', alpha=0.6)
        ax.add_patch(inner_circle)

        # 图标
        ax.text(challenge['pos'][0], challenge['pos'][1] + 0.2,
                challenge['icon'], ha='center', va='center', fontsize=16)

        # 文字
        ax.text(challenge['pos'][0], challenge['pos'][1] - 0.15,
                challenge['text'], ha='center', va='center',
                fontsize=8, color='white', fontweight='bold')
    
    # 解决方案路径
    solutions = [
        {
            'pos': (0.5, 1.5),
            'size': (2.8, 1.0),
            'title': 'Solution Path 1',
            'content': 'Privacy Modeling &\nQuantitative\nCharacterization'
        },
        {
            'pos': (3.6, 1.5),
            'size': (2.8, 1.0),
            'title': 'Solution Path 2',
            'content': 'Joint Security\nOptimization\nMethods'
        },
        {
            'pos': (6.7, 1.5),
            'size': (2.8, 1.0),
            'title': 'Solution Path 3',
            'content': 'Dynamic Adaptation\nMechanism\nDesign'
        }
    ]
    
    for i, sol in enumerate(solutions):
        sol_box = FancyBboxPatch(sol['pos'], sol['size'][0], sol['size'][1],
                                boxstyle="round,pad=0.05",
                                facecolor=colors['solution'],
                                edgecolor='black',
                                linewidth=1.5,
                                alpha=0.8)
        ax.add_patch(sol_box)
        
        ax.text(sol['pos'][0] + sol['size'][0]/2, sol['pos'][1] + sol['size'][1] - 0.15,
                sol['title'], ha='center', va='center', 
                fontsize=10, fontweight='bold', color='white')
        
        ax.text(sol['pos'][0] + sol['size'][0]/2, sol['pos'][1] + sol['size'][1]/2 - 0.1,
                sol['content'], ha='center', va='center', 
                fontsize=9, color='white')
        
        # 连接线从子问题到解决方案
        con = ConnectionPatch((sub_problems[i]['pos'][0] + sub_problems[i]['size'][0]/2, 
                             sub_problems[i]['pos'][1]),
                             (sol['pos'][0] + sol['size'][0]/2, 
                             sol['pos'][1] + sol['size'][1]), 
                             "data", "data",
                             arrowstyle="->", shrinkA=5, shrinkB=5,
                             mutation_scale=15, fc="gray", alpha=0.6)
        ax.add_artist(con)
    
    # 添加美化的标题
    title_box = FancyBboxPatch((2, 11), 8, 0.8,
                              boxstyle="round,pad=0.1",
                              facecolor='navy',
                              edgecolor='gold',
                              linewidth=3,
                              alpha=0.9)
    ax.add_patch(title_box)
    ax.text(6, 11.4, '1.3 KEY SCIENTIFIC PROBLEMS TO BE SOLVED',
            ha='center', va='center', fontsize=18, fontweight='bold', color='white')

    # 添加美化的图例
    legend_elements = [
        mpatches.Patch(color=colors['core'], label='Core Scientific Problem'),
        mpatches.Patch(color=colors['sub1'], label='Sub-problems'),
        mpatches.Patch(color=colors['challenge'], label='Technical Challenges'),
        mpatches.Patch(color=colors['solution'], label='Solution Paths')
    ]
    legend = ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.95),
                      frameon=True, fancybox=True, shadow=True, fontsize=11)
    legend.get_frame().set_facecolor('lightblue')
    legend.get_frame().set_alpha(0.8)

    # 添加美化的说明文字
    ax.text(6, 0.8, 'Research Focus: Multi-dimensional Performance Dynamic Adaptation in ISAC Systems',
            ha='center', va='center', fontsize=13, style='italic', fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.4", facecolor='lightsteelblue',
                     edgecolor='darkblue', linewidth=2, alpha=0.8))
    
    plt.tight_layout()
    return fig

def create_problem_relationship_diagram():
    """创建问题关系图"""
    fig = plt.figure(figsize=(16, 12), facecolor='white')
    ax = fig.add_subplot(111)
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 10)
    ax.axis('off')

    # 添加渐变背景
    gradient = np.linspace(0, 1, 256).reshape(256, -1)
    gradient = np.vstack((gradient, gradient))
    ax.imshow(gradient, extent=[0, 12, 0, 10], aspect='auto', cmap='Purples', alpha=0.1)

    # 四维性能空间 - 重新设计
    performance_dims = [
        {'pos': (2, 7.5), 'name': 'Communication\nRate (Rc)', 'color': '#3B82F6', 'icon': '📡'},
        {'pos': (10, 7.5), 'name': 'Secrecy\nRate (Rs)', 'color': '#EF4444', 'icon': '🔐'},
        {'pos': (2, 2.5), 'name': 'Privacy\nMetric (PM)', 'color': '#10B981', 'icon': '🛡️'},
        {'pos': (10, 2.5), 'name': 'Sensing\nAccuracy (SA)', 'color': '#F59E0B', 'icon': '🎯'}
    ]
    
    # 绘制四维性能节点 - 添加阴影和装饰
    for dim in performance_dims:
        # 阴影
        shadow_circle = plt.Circle((dim['pos'][0] + 0.1, dim['pos'][1] - 0.1),
                                  1.0, color='gray', alpha=0.3)
        ax.add_patch(shadow_circle)

        # 主圆圈
        circle = plt.Circle(dim['pos'], 1.0, color=dim['color'], alpha=0.9,
                           linewidth=3, edgecolor='white')
        ax.add_patch(circle)

        # 内圆装饰
        inner_circle = plt.Circle(dim['pos'], 0.8, color='none',
                                 linewidth=2, edgecolor='lightblue', alpha=0.6)
        ax.add_patch(inner_circle)

        # 图标
        ax.text(dim['pos'][0], dim['pos'][1] + 0.3, dim['icon'],
                ha='center', va='center', fontsize=24)

        # 文字
        ax.text(dim['pos'][0], dim['pos'][1] - 0.2, dim['name'],
                ha='center', va='center', fontsize=11, fontweight='bold', color='white')

    # 中心问题 - 美化设计
    # 阴影
    center_shadow = plt.Circle((6.1, 4.9), 1.8, color='gray', alpha=0.3)
    ax.add_patch(center_shadow)

    # 主圆圈
    center_circle = plt.Circle((6, 5), 1.8, color='#8B5CF6', alpha=0.9,
                              linewidth=4, edgecolor='gold')
    ax.add_patch(center_circle)

    # 装饰圆环
    deco_circle1 = plt.Circle((6, 5), 1.5, color='none',
                             linewidth=2, edgecolor='lightcyan', alpha=0.7)
    ax.add_patch(deco_circle1)
    deco_circle2 = plt.Circle((6, 5), 1.2, color='none',
                             linewidth=1, edgecolor='white', alpha=0.5)
    ax.add_patch(deco_circle2)

    # 中心图标
    ax.text(6, 5.5, '🎯', ha='center', va='center', fontsize=30)

    # 中心文字
    ax.text(6, 4.7, 'Multi-dimensional', ha='center', va='center',
            fontsize=12, fontweight='bold', color='white')
    ax.text(6, 4.5, 'Performance', ha='center', va='center',
            fontsize=12, fontweight='bold', color='white')
    ax.text(6, 4.3, 'Dynamic Adaptation', ha='center', va='center',
            fontsize=12, fontweight='bold', color='white')
    
    # 美化的连接线表示相互关系
    connections = [
        ((2, 7.5), (6, 5)),   # Rc to center
        ((10, 7.5), (6, 5)),  # Rs to center
        ((2, 2.5), (6, 5)),   # PM to center
        ((10, 2.5), (6, 5)),  # SA to center
        ((2, 7.5), (10, 7.5)), # Rc to Rs (trade-off)
        ((2, 2.5), (10, 2.5)), # PM to SA (trade-off)
        ((2, 7.5), (2, 2.5)),  # Rc to PM (cross-domain)
        ((10, 7.5), (10, 2.5)) # Rs to SA (cross-domain)
    ]

    for start, end in connections:
        if start == (2, 7.5) and end == (10, 7.5):  # Rc-Rs trade-off
            # 弧形箭头
            ax.annotate('', xy=end, xytext=start,
                       arrowprops=dict(arrowstyle='<->', color='#DC2626', lw=4, alpha=0.8,
                                     connectionstyle="arc3,rad=0.3"))
            ax.text(6, 8.5, '⚖️ Communication-Security Trade-off',
                   ha='center', va='center', fontsize=12, color='#DC2626', fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        elif start == (2, 2.5) and end == (10, 2.5):  # PM-SA trade-off
            ax.annotate('', xy=end, xytext=start,
                       arrowprops=dict(arrowstyle='<->', color='#059669', lw=4, alpha=0.8,
                                     connectionstyle="arc3,rad=-0.3"))
            ax.text(6, 1.5, '🔄 Privacy-Sensing Trade-off',
                   ha='center', va='center', fontsize=12, color='#059669', fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        elif start[0] == end[0]:  # 垂直连接
            ax.annotate('', xy=end, xytext=start,
                       arrowprops=dict(arrowstyle='<->', color='#6366F1', lw=3, alpha=0.6,
                                     connectionstyle="arc3,rad=0.2"))
        else:  # 到中心的连接
            ax.annotate('', xy=end, xytext=start,
                       arrowprops=dict(arrowstyle='->', color='#8B5CF6', lw=2, alpha=0.7))

    # 添加美化的标题
    title_box = FancyBboxPatch((1, 9), 10, 0.8,
                              boxstyle="round,pad=0.1",
                              facecolor='#1E3A8A',
                              edgecolor='gold',
                              linewidth=3,
                              alpha=0.9)
    ax.add_patch(title_box)
    ax.text(6, 9.4, 'FOUR-DIMENSIONAL PERFORMANCE SPACE RELATIONSHIPS',
            ha='center', va='center', fontsize=16, fontweight='bold', color='white')
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("Generating Key Scientific Problems Visualization...")
    
    # 生成关键问题结构图
    fig1 = create_key_problems_diagram()
    fig1.savefig('key_scientific_problems.png', dpi=300, bbox_inches='tight')
    print("- Saved: key_scientific_problems.png")
    
    # 生成问题关系图
    fig2 = create_problem_relationship_diagram()
    fig2.savefig('problem_relationships.png', dpi=300, bbox_inches='tight')
    print("- Saved: problem_relationships.png")
    
    print("\nVisualization Complete!")
    print("Generated 2 figures for Section 1.3 Key Scientific Problems")

if __name__ == "__main__":
    main()
