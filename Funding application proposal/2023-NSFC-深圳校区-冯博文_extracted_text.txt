

=== 第 1 页 ===
申请代码
F0106
接收部门
收件日期
接收编号
6230010540
    
国家自然科学基金
申 请 书
（2 0 2 3 版）
资助类别：
青年科学基金项目
亚类说明：
附注说明：
项目名称：
面向低轨卫星通信的预变换极化编码传输机制研究
申 请 人：
冯博文
办公电话：
0755-26404115
依托单位：
哈尔滨工业大学
通讯地址：
广东省深圳市南山区深圳大学城哈工大校区信息楼L1015室
邮政编码：
518055
单位电话：
0451-86414151
电子邮箱：
<EMAIL>
国家自然科学基金委员会
NSFC 2023


=== 第 2 页 ===
基本信息
申
请
人
信
息
姓        名
冯博文
性别
男
出生
年月
1992年03月
民族
汉族
学        位
博士
职称
无
是否在站博士后
是
电子邮箱
<EMAIL>
办公电话
0755-26404115
国别或地区
中国
申请人类别
依托单位全职
工   作   单   位
哈尔滨工业大学/哈尔滨工业大学（深圳）
主 要 研 究 领 域
卫星通信、信道编码
依
托
单
位
信
息
名        称哈尔滨工业大学
联   系   人王雪
电子邮箱
<EMAIL>
电        话0451-86414151
网站地址
http://kjc.hit.edu.cn/
合
作
研
究
单
位
信
息
单 位 名 称
项
目
基
本
信
息
项目名称
面向低轨卫星通信的预变换极化编码传输机制研究
英文名称
Research on Pre-transformed Polar Coding and Transmission Mechanism
for LEO Satellite Communications
资助类别
青年科学基金项目
亚类说明
附注说明
申请代码
F0106.空天通信
研究期限
2024年01月01日 -- 2026年12月31日
研究方向：卫星通信
申请直接费用30.0000万元
中文关键词
低轨卫星通信；预变换极化码；信道有效容量；跨层编码
英文关键词
LEO Satellite Communication; Pre-transformed polar codes; Channel
effective capacity; Cross-layer coding
NSFC 2023
第 1 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 3 页 ===
中
文
摘
要
低轨卫星通信业务类型的不断丰富和业务数据量的飞速增长对信息传输速率的需求日益凸
显，同时业务包的差异化QoS需求需要编码传输机制具备灵活高效的承载能力。由于低轨星地
链路质量随机时变且难以预估，受限于载荷能力、传播时延等因素，现有编码传输机制对链路
的适配性不足，难以保障卫星过顶周期内业务信息的持续高速传输，且相对固定的机制调整模
式难以支撑多样业务的差异化需求。针对上述问题，本项目以提升业务编码传输速率为目标，
基于预变换极化编码技术，探究低轨星地信道有效容量表征与自适应逼近机制。通过明晰业务
需求对信道容量的约束机理，探索低开销高增益编译码方法，构建信道自适应的跨层编码传输
机制，实现编码传输对星地链路的良好匹配，在满足业务包的误块率和时延差异化需求的基础
上，支撑业务信息的持续近容量传输。通过本项目的研究，可为未来低轨卫星通信系统建设和
标准建立提供一条新的编码传输机制解决途径。
英
文
摘
要
The increasing type of services and the surge in business volume in
low-earth-orbit (LEO) satellite communications have increasing demand for
information transmission rate. At the same time, the differentiated QoS
requirements of the business packets need the coding and transmission mechanism to
have flexible service capacities. The existing coding and transmission mechanisms
cannot adequately accommodate the LEO satellite-terrestrial links, due to the
time-varying and unpredictable quality, limited load capacity, and large
propagation delay. It is difficult to guarantee the high-speed transmission of
business information during the satellite-terrestrial link-maintaining cycle, and
the relatively fixed mechanism adjustment model also makes it difficult to support
the differentiated needs of businesses. To address the above problems, this
project aims to improve the coded transmission rate and explore the effective
capacity characterization and adaptive approaching mechanism of the LEO
satellite-terrestrial channel based on the pre-transformed polar coding. By
studying the principle of service demand constraints on channel capacity,
exploring the low-overhead and high-gain encoding and decoding method,
constructing the cross-layer mechanism of channel coding and transmission, the
mechanism can be well matched to the satellite-terrestrial link. At the same time,
it supports the continuous capacity-approaching transmission of business
information, on the basis of meeting the differentiated service requirements of
block-error-rate and latency. The research of this project can provide a new
solution of coding transmission mechanism for the system construction and standard
establishment of future LEO satellite communications.
NSFC 2023
第 2 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 4 页 ===
科学问题属性
 
科学问题源于科研人员的灵感和新思想，且具有鲜明的首创性特征，旨在
“鼓励探索，突出原创”：
通过自由探索产出从无到有的原创性成果。
 
科学问题源于世界科技前沿的热点、难点和新兴领域，且具有鲜明的引领
“聚焦前沿，独辟蹊径”：
性或开创性特征，旨在通过独辟蹊径取得开拓性成果，引领或拓展科学前沿。
 
科学问题源于国家重大需求和经济主战场，且具有鲜明的需求导向、问题
“需求牵引，突破瓶颈”：
导向和目标导向特征，旨在通过解决技术瓶颈背后的核心科学问题，促使基础研究成果走向应用。
 
科学问题源于多学科领域交叉的共性难题，具有鲜明的学科交叉特征，旨
“共性导向，交叉融通”：
在通过交叉研究产出重大科学突破，促进分科知识融通发展为知识体系。
请阐明选择该科学问题属性的理由（800字以内）：
卫星通信是发展空间科学、空天技术和应用的重要手段，能够广泛地支撑多类个人和行业应用，
也是“新基建”信息基础设施的重要组成部分。随着低轨通信卫星的不断部署，网络容量不断提升，
业务形态随之发生变革。由于远低于同步轨道的星地传输时延、更广的网络服务范围等优势，部分原
依托于同步轨道卫星和地面网络的通信业务向低轨卫星通信转移。业务类型的丰富和业务数据量的激
增，对低轨卫星通信系统提出更高的传输速率需求。同时，低轨卫星通信多样业务的差异化QoS指标还
对通信系统提出了灵活的承载能力需求。映射到对编码传输机制的需求则体现在对业务信息近容量编
码传输的支撑，以及对业务数据包的误块率及时延差异化需求的满足。
然而，由于低轨星地信道随机时变且难以预估，受限于载荷能力、传播时延等因素，难以得到一
种理想的编码传输机制，在保障业务误块率和时延差异化需求的前提下，实现卫星过顶周期内业务信
息的近容量编码传输。而造成这一技术瓶颈的深层原因主要在于以下两方面，一是信道有效容量的表
征不清，也就是编码传输机制的设计边界不清，物理信道容量随机时变加之差异化业务需求约束，造
成编码传输机制设计缺乏依据；二是对容量的逼近不足，体现在瞬时是编码增益在资源开销受限情况
下仍不足，体现在过顶周期则是编码传输对信道的自适应适配能力不足。因此，本项目凝练了“基于
预变换极化编码技术的低轨星地信道有效容量表征与自适应逼近”这一关键科学问题。
本项目将按照“有效容量表征——编码增益近界——容量持续匹配”的总体思路开展研究工作，
以期提出灵活高效的预变换极化码编码传输机制，自适应匹配低轨星地动态链路，支撑业务信息在过
顶周期内持续近有效容量传输。本项目的研究可为低轨卫星通信系统设计提供一种新的技术方案借鉴
，有利于卫星通信网络与地面网络的技术协同，有助于抢占低轨卫星通信技术先发优势。
综上所述，本项目具有“需求牵引，突破瓶颈”科学属性特征。
NSFC 2023
第 3 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 5 页 ===
报告正文 
参照以下提纲撰写，要求内容翔实、清晰，层次分明，标题突出。
请勿删除或改动下述提纲标题及括号中的文字。 
（一）立项依据与研究内容（建议8000 字以内）： 
1. 项目的立项依据（研究意义、国内外研究现状及发展动态分
析，需结合科学研究发展趋势来论述科学意义；或结合国民经济和社
会发展中迫切需要解决的关键科技问题来论述其应用前景。附主要参
考文献目录）； 
1.1 研究意义 
卫星通信网络是发展空间科学、空天技术和应用的重要手段，能向上有效
支撑太阳系甚至更远的空间科学活动，也能够有效服务载人航天、高分辨率对
地观测系统、北斗导航系统等重大航天工程，同时向下为远洋航行、应急救援、
导航定位、航空运输等重大行业应用提供服务[1]。2020年，国家发改委将卫星互
联网连同5G、物联网、工业互联网等列为“新基建”中的信息基础设施。国家
“十四五”规划中提出强化发展空天科技等前沿领域。 
近年来，在低轨（Low Earth Orbit，LEO）上，Starlink、OneWeb、Telesat
等大规模宽带星座的出现和部署，推动了卫星通信的变革。其中，具有代表性
的SpaceX公司“星链”（Starlink）计划正在建立由4425颗卫星组成的宽带网络，
采用“天星天网”架构。随着大规模低轨卫星通信网络的不断部署和发展，卫星通
信技术由传统的“弯管”式透明转发逐渐向多跳传输、网络化传输发展。随着低轨
卫星网络容量的不断提升，其具备远低于同步轨道的星地传输时延、更广的网
络服务范围等优势，使部分原依托同步轨道卫星的通信业务向低轨卫星网络转
移，如实时流媒体、遥感数据等宽带回传业务，海洋、航空用户互联网宽带接
入业务等。此外，部分原主要依托地面互联网的业务，如密集跨境电子商务、
车联网等，也有向低轨卫星网络转移发展的趋势[2]。不断丰富的业务类型和飞速
增长的业务数据量，对低轨卫星通信系统提出更高的传输速率需求。低轨卫星
通信逐渐形成了复杂多样的业务形态，但各业务的QoS（Quality of Service）需
求往往并不相同。例如，大部分视频回传业务的实时性需求并不高，但跨境电
子商务、车联网等业务的时延要求极其严格且对可靠性需求也很高[3]。此外，不
同行业应用业务的QoS需求差别很大，往往需要服务定制化。因此，低轨卫星通
信系统还需具备更加灵活高效的承载能力，支撑多样业务的差异化QoS需求。 
低轨卫星相对地面的运行速度很快，对于地面用户或信关站，每颗低轨卫
星单次过顶时间大约只有十分钟，低轨星地链路质量随仰角快速变化。此外，
雨、大气、云雾等随机时变的环境条件对Ka、Ku等主流星地通信频段电磁波衰
NSFC 2023
第 4 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 6 页 ===
减严重。由于卫星高速运动且传输时延大，发送端难以获取准确、及时的链路
信息反馈，且受时变环境参数影响，难以准确预测链路质量。使发送端的通信
系统配置缺乏依据，难以良好匹配链路状态，造成传输能力下降。因此，星地
通信中动态时变且难以预估的链路特性，对业务信息的高速可靠传输造成困难。 
过顶周期
信道容量/
信息速率
信道容量/
信息速率
理想方式
ACM方式
CCM方式
信道容量
(a) 低轨星地通信场景与链路特性示意图
(b) 理想天气条件下的星地信道容量与现有调制编码方式
可达信息速率示意图
(c) 存在降雨、云雾等天气条件下的星地信道容量与现有
调制编码方式可达信息速率示意图
错误传输
理想方式
ACM方式
CCM方式
信道容量
错误传输
过顶周期
信关站/用户
LEO卫星：大多600~1200km
过顶周期：约10min，仰角快变
多普勒频移：约700kHz(Ka频段卫星)
雨衰：与降雨强度、雨层厚度、仰角等
时变因素相关
大气衰减：与气压、水汽密度、温度等
时变因素相关
 
图1  低轨星地链路特性与现有技术方式局限性示意图 
现行卫星通信系统大多采用针对同步轨道通信卫星设计的DVB-S2、S2X、
RCS等技术标准，聚焦物理层和链路层技术。其中，信息传输速率受调制编码方
式直接影响。目前低轨卫星通信大多数仍采用固定编码调制（Constant Coding and 
Modulation, CCM）方式，按照较差的链路情况并留有足够余量，选择固定的码
长和较低的码率以保障可靠译码，这种方式造成信息传输速率很低、有效传输
窗口较小，如图1中蓝色实线所示。虽然存在基于信道测量反馈进行编码调制方
式调整的自适应编码调制（Adaptive Coding and Modulation, ACM）方式，但这
种方式适用于相对稳定的同步轨道星地链路和“弯管”模式，而对于低轨星地链路
信道状态快时变和大传输时延特点存在局限性，一方面发送端难以获取准确、
实时的信道状态信息（Channel State Information，CSI）反馈，使做出的调整不
够及时、准确，易造成连续错误传输，容量逼近效果不佳，如图1中绿色线所示；
另一方面低轨星上受限于载荷处理能力等因素，难以开展精准的CSI估计。 
因此，希望通过合理设计实现一种理想的调制编码方式，使信息传输速率
能够在过顶通信周期内持续逼近信道容量，如图1中的红色曲线所示。对于信道
编码技术，支撑该方式的实现需要满足以下两个方面，一是对于每个过顶时刻，
需要编码可靠性可以足够逼近香农极限；二是对于全过顶周期，需要编码构造
参数自适应调整以适配信道的随机变化。然而，在发送端缺乏CSI的情况下，单
独依靠信道编码技术及其他物理层技术难以在随机时变信道下保障业务的高效
可靠传输。因此，需引入跨层机制，联合考虑编码技术与传输策略，通过连续
NSFC 2023
第 5 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 7 页 ===
传输策略的设计增强编码技术对信道的适配能力，以支撑上述愿景的实现。但
是，差异化业务需求、弱载荷处理能力和恶劣传播环境等因素对编码传输机制
的设计和容量逼近带来诸多制约和挑战。具体表现为： 
(1) 差异化业务需求约束编码传输容量边界 
传统信道容量依托对真实物理信道的理解得到，对编码技术的约束仅在于
将比特信道容量作为编码码率的理想上界，即
bin
R
C

。而对于实际通信系统，
传统物理信道容量并不能反映编码传输技术对通信业务QoS的支撑。因此，存在
信道有效容量（Effective Capacity）的概念，定义为单位时间能够成功服务的最
大业务比特数量，描述业务需求约束下的物理信道容量，可用于评估调制编码
技术对业务数据包的承载能力。 
通信业务QoS指标映射到编码传输机制，主要体现在对时延和误块率（Block 
Error Rate，BLER）性能的需求。对于时延需求，除了考虑传统的业务数据包排
队时延，卫星通信中巨大的传播时延将成为时延组成的主要因素。因此，卫星
通信业务对重传次数和队列长度的容忍程度将决定有效容量，进而约束编码块
构造方式和重传机制设计；对于误块率需求，其取决于编码最小汉明距离、编
码错误系数、编码码率等。因此，有效容量不仅要指导码率R ，还需对编码构
造、传输策略设计以及各参数的选用等产生约束和指导作用。由于低轨卫星通
信场景复杂、业务需求差异化明显，有效容量约束方式随之多变，传统仅针对
排队时延的有效容量表征方法难以适配。 
(2) 弱载荷能力制约编码增益近界 
由于卫星载荷体积、功率及空间环境限制，星上载荷的运算、存储等编译
码处理能力十分有限。虽然当前流行的信道编码技术，如LDPC码、极化码等已
能够逼近性能极限，但在实际中是以巨大的编译码资源开销获取足够的增益。
例如，DVB-S2、S2X标准中所采用的LDPC码的码长极长，最短也需要16200比
特，且需要通过采用大量迭代的译码获取良好的可靠性。这种方式可用于依托
地面处理的“弯管”体制，但面向未来低轨卫星星上处理模式的增多，这种高
运算、缓存和编译码时延开销的编码方式在星上将无法良好适用。 
 
图2 不同编译码方案的(128, 64)码的误块率对比 
>1dB 
NSFC 2023
第 6 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 8 页 ===
若因载荷能力约束，采用与地面移动通信相似码长（即
13
2
N 
）的信道编
码并使用较低复杂度译码器，则会造成增益不足。尤其在短码长时，距达到有
限长编码性能界仍有很长的距离。如图2所示，码长128的短码LDPC码、极化码
在分别采用迭代次数25的置信传播（BP）译码、长度为8的列表译码时，所获得
的误块率距有限长编码NA性能基准仍有1dB以上的差距。 
 (3) 大传输时延影响编码传输对信道持续匹配 
由于信道状态随机时变且难以准确估计和预测，往往需要采用反馈重传的
方式调整编码参数以适配信道状态。典型的方法如地面移动通信中常用的编码
联合链路层混合自动重传（Hybrid Automatic Repeat Request，HARQ）机制，通
过不断的反馈调整码率和重传内容，实现信息的可靠传输。然而，该模式在低
轨卫星通信中的应用存在争议。主要原因在于地面移动通信中重传的时延最大
只有几毫秒，而在低轨卫星通信中一次重传回路的时延可达几十毫秒以上，在
需要多次重传时，过大的重传回路时延使信息传输效率大大降低，且易对发送
端缓存造成长期占用，如图3所示。 
C1
发送端
可达百毫秒级甚至秒级
C2 C3 C4 C5 C6 C7 C8 C9 C10C11C12
C1 C2 C3 C4 C5 C6 C7 C8 C9 C10C11C12
C'1 C'2 C'3
C'6 C'7
C'9
C'11
C'1 C'2 C'3
C'6 C'7
C'9
C'11
C''2 C''3
C''2 C''3
接收端
 
图3 低轨卫星通信采用HARQ 机制造成的时延示意图 
因此，亟需探索适用于低轨卫星通信的新编码传输机制，从信道有效容量
的精确描述、编码增益的近极限挖掘、时变有效容量的持续适配三个方面，实
现业务编码传输速率对有效容量的持续逼近。3GPP组织从R15版本开始针对非
地面网络（Non-Terrestrial Networks，NTN）进行了专门立项，定义NTN包括高
空平台及卫星通信网络，并认为其是5G演进、6G网络的重要组成。在3GPP 
TR38.811、38.821及其他关于NTN的技术提案中，认为5G新空口中的部分技术
具有应用在卫星通信中的潜力。因此，除了对现有编码技术方案的改进外，还
需考虑未来卫星网络与地面网络的技术协同和融合，探索先进编码技术在低轨
卫星通信场景下的适用性，以便形成技术特色和优势。 
极化码（Polar Code）[4]作为5G编码技术，用作控制信道编码方案，具有良
好的可靠性、较低的编译码开销和改进潜力。预变换极化码（Pre-transformed 
Polar Code）是一类基于极化码的变种编码，采用前置预变换的方式重构编码生
成矩阵，以改善极化码在高码率时因最小汉明距离不足造成的可靠性劣势。其
中，具有代表性的Polarization-Adjusted Convolutional（PAC）码是极化码发明人
E. Arıkan在2019年给出的香农奖演说中提出的[5]，采用卷积码预变换，能够在短
码时取得比极化码更优的可靠性。预变换极化码可视作一种基于极化码的特殊
NSFC 2023
第 7 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 9 页 ===
级联编码，由于预变换过程的引入，相比极化码能够提供更灵活的构造方式，
并可提供可变的额外编码增益。因此，预变换极化码具有应用于低轨卫星通信
的潜力，但面临技术成熟度以及上述编码传输机制设计难题。可综合卫星、地
面通信系统现有编码技术方案的有益经验，基于预变换极化码探究差异化业务
需求约束下的有效容量表征和逼近机制，在明晰客观因素制约机理的基础上，
寻求更优的编码传输解决方案，在支撑业务误块率和时延需求的基础上，充分
提升信息传输速率。 
本项目研究工作的科学意义在于，可探究面向低轨卫星通信业务的信道有
效容量表征方法，探索低复杂度约束下预变换极化码的编码增益优化方法，以
及编码可靠性与随机时变信道的匹配机制；工程实践价值在于，可为低轨卫星
通信系统乃至6G 中的信道编码技术设计提供一种新的技术方案借鉴。该研究工
作有利于卫星通信网络与地面网络的技术协同，有助于抢占低轨卫星网络通信
技术先发优势。 
1.2 国内外研究现状及发展动态分析 
（1）卫星通信信道编码技术现状简述 
从上世纪90 年代后，由于Turbo 码和LDPC 码的性能接近香农极限，逐渐
在卫星系统中应用。Turbo 码被列入CCSDS、DVB-RCS 等技术标准中，常用作
反向链路编码方案，例如iPSTAR 卫星采用Turbo 乘积码和高阶调制，码率可在
0.879 到0.325 间调整。面向前向链路的DVB-S2 标准中采用了以LDPC 码为内
码，BCH 码为外码的级联码结构，其中LDPC 码长为长码64800、短码16200，
速率从1/4 到8/9 有11 种可选择。DVB-S2X 中，增加了中等码长30780，以及
更多的码率选择。 
从DVB-S2/S2X 的提出至今，大量卫星公司采用或设计了相近的技术标准，
包括Intelsat、Eutelsat、Telstar、Viasat 等公司的卫星大多都采用了DVB-S2/S2X
的级联编码方案。同步轨道的数字广播电视卫星可采用基于DVB-RCS 反向链路
信道估计反馈机制的ACM 方式对前向链路协议DVB-S2/S2X 中采用的码长和码
率进行调整。在具备信道测量和反馈条件下，接收端对信道进行估计，并将估
计结果不断通过反向链路反馈至发送端，发送端据此对码长、码率以及调制方
式在合适的时机进行调整。这种方法适用于相对稳定的同步轨道星地链路，且
在“弯管”体制下无需在星上处理。但对于低轨卫星通信，受限于大传输时延和快
时变特点，加之星上处理能力的有限，难以准确地获取每跳链路实时CSI，使做
出的调整不够准确，可能造成译码错误导致再次反馈调整，或造成编码冗余过
度引起传输速率低下，均严重影响信息传输效率，使ACM 方式的适用性不足。
但这种自适应的思想仍对解决链路适配问题具有借鉴意义，这需要编码传输机
制能够在适应大传输时延、弱载荷能力等天然障碍的基础上，灵活且快速地实
现对链路特征准确的自适应。因此，需借助上层技术提升物理层编码技术对信
NSFC 2023
第 8 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 10 页 ===
道的适应性，通过连续发送以及少量设置反馈，连续调整编码传输参数不断适
应信道变化。 
3GPP 在R17 阶段关于NTN 的会议和技术提案中，认为5G 新空口中的部
分物理层和链路层技术具有应用在卫星通信中的潜力。主要针对NTN 中HARQ
机制的使用进行了提案讨论，解决思路可归纳为以避免停等式的HARQ，采用
主动式或预先式的HARQ，禁用部分反馈等方式来减少等待时延。 
（2）有效容量研究现状 
有效容量是一种从物理层和链路层跨层的角度评价信息服务速率上限的方
式，Wu 等建立了链路层业务数据包的QoS 需求与物理层技术能力的耦合关系[6]。
传统的有效容量表征中，主要关注业务数据包的排队时延和时延违约概率，并
将满足时延违约概率时能够支撑的最大信息速率作为有效容量。此后，有效容
量多用于无线通信资源分配优化、底层技术性能评估等工作。[7]中提出了基于
有效容量的跨层设计模型，研究物理层技术对业务QoS 满足情况的影响，并对
编码传输方案进行评估。在HARQ 机制的有效容量研究方面，研究工作多关注
重传和编码参数对于排队时延的影响。[8]中对地面通信中HARQ方式和基于CSI
的ACM 方式进行对比，采用有效容量对两者的性能进行分析。[9]中对于增量冗
余HARQ（IR-HARQ）的有效容量进行分析，采用对累积码块拆分简化的方式
等效传输，对最大化有效容量及其对应的中断概率进行了研究。[10]中对时敏类
业务需求下的IR-HARQ 有效容量进行分析，聚焦物理层编码参数设置和重传对
排队时延的影响，框定参数设置边界。在卫星通信的有效容量研究方面，[11]
基于阴影莱斯信道模型，在完美CSI 估计假设下描述有效容量。此外，[12]中指
出由于卫星与用户之间的距离非常远，卫星通信传播时延长，有效容量适用于
这类长时延通信系统的底层技术性能评估。 
对于时延的精细描述方面，目前较流行的信息年龄（Age of Information, AoI）
[13]作为一种衡量信息时效性的指标，用于描述有时延系统对具有时间戳信息的
更新过程，一般认为AoI 越小的信息对于接收端越“新鲜”，其时效性价值越
高。相比较传统的以数据到达时间为开始时刻的排队时延分析方法，AoI 以信息
生成为起始点，且对信息的时延经历过程描述更加完备，适用于时延组成复杂
的场景。[14]利用多种随机排队理论模型，如M/M/1，M/D/1 等，对AoI 的更新
过程进行建模。最初的AoI 模型是线性模型，后续出现了指数形式、阶梯形式
等。AoI 常被用于分析并优化车联网场景中的信息时效性，后来又用于分析评估
无人机通信及其他复杂系统。这些研究工作多是在理想接收情况下进行的，主
要针对信息获取过程的时延进行优化。后续有研究关注了接收准确性对信息年
龄的影响，[15]中分析了接收有错情况下的AoI 模型，对比了M/M/1 模型下“先
到先服务”和“后到先服务”模式时效性，并初步探索了重传机制下的AoI 模型。
[16]中在删除信道模型下，对固定增量和冗余增量两种典型HARQ 机制的AoI
NSFC 2023
第 9 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 11 页 ===
更新模型进行分析和比较。[17]中进一步在删除信道模型下对HARQ 机制采用
的编码码长对AoI 的影响进行分析，并对其进行了优化，得到了可使AoI 最优
的码长设置策略。 
在基于AoI 的有效容量的表征方面，Zhang 等在2021 年ISIT 中提出了探索
性的工作[18]，分析了AoI 违约概率的上界，并提出了峰值AoI 违约概率最小化
和有效容量最大化方法，用于评估地面通信低时延高可靠场景下有限长码块长
度的选择。 
通过以上分析可见，现有的有效容量模型多是以排队时延为主要指标的跨
层编码方案评价方法，部分工作也对误码率开展分析并基于有效容量进行码块
设计优化。面向卫星通信场景，需要完善各时延组成的影响因素分析，AoI 作为
一种精细的时延模型，适于分析卫星通信场景复杂时延组成，并且可进一步用
于有效容量模型的构建。现有的AoI 模型中仍未考虑编码参数对信息时延的影
响因素。因此，需要完善AoI 模型，使之与低轨卫星通信场景和编码方案深度
匹配，进而提出一种新的有效容量表征模型，作为跨层编码传输机制设计和评
估的理论依据。 
（3）极化码、预变换极化码技术研究现状 
极化码（Polar Code）由E. Arıkan在2008年ISIT中提出，并在2009年的长文
中给出了详细的理论证明[4]。在其文中，证明了信道组合方式产生的容量极化特
性，并利用这一特性构造极化码。极化码在理论上可以被证明逼近香农极限，
被选为5G控制信道的短码方案。从极化码的提出起，出现了大量的研究和改进
工作，大致可以分为编译码方法研究和实用化联合设计。 
 极化码编译码的研究工作：针对编码中信息比特放置位置的选择问题已
有一些计算简单的实用方法，包括低复杂度的高斯近似算法[19]，信道的进化和
退化方法[20]，华为公司提出不借助CSI的权重方法[21]等。针对编码码长为2N的约
束，需要进行速率匹配（Rate Matching），即采用重复、打孔和截短的方式获取
任意码长、码率的码块，以适配资源块需求。由于重复较为简单，研究工作集
中于打孔和截短方法。典型的打孔方法包括采用随机打孔、停止树打孔[22]、准
均匀打孔[23]等，标准的原型方法是一种基于准均匀打孔并额外冻结的低复杂度
打孔[24]；典型的截短方法包括基于列重的截短[25]，以及截短位置与冻结位选择
的联合优化[26]等。针对译码的改进工作方面，最初始的译码是贪婪式算法的SC
（Successive Cancellation）译码，为了提升其差错控制性能，出现了列表译码（SC 
List, SCL）[27]、堆栈译码（SC Stack，SCS）、混合堆栈译码（SC Hybrid，SCH）
[28]等，这些改进方法可视作采用了多重的SC译码。还有一些软输入软输出迭代
译码方法。此外，已有大量针对译码算法的优化和译码器实现方法的研究工作。 
 实用化极化码的联合设计：一是内外码的级联设计，以取得更好的差错
控制性能。实际上CRC辅助的极化码译码也可认为是一种级联结构。此外，极
NSFC 2023
第 10 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 12 页 ===
化码已与卷积码、RS码、LDPC码进行了级联设计[29-30]，以满足不同应用的需求；
二是极化码的跨层、联合编码设计，目的是获取更好的实用性以适应不同的场
景，这里主要关注与本项目相关的编码与链路层重传控制的联合设计。针对地
面移动通信系统，[31]中提出了一种极化码与HARQ的联合设计，即每次重传相
同的极化码码块，在接收端进行合并后译码。[32]中考虑了HARQ与CRC-SCL译
码的联合设计。[33]中提出了一种无码率极化码编码方案中，构造了一组等长的
极化码块，各码块的码率呈下降趋势，各重传码块中包含有上次传输所含的部
分信息，使接收端码块码率逐渐下降。[34]中提出了一种类似的方案，不同之处
是其采用了打孔的方式构造了一组不同长度的码块，也可以实现接收端码块码
率的逐渐下降。以上极化码的无码率编码构造方法研究，可为本项目研究提供
借鉴。但其难以直接用于卫星通信，主要原因在于HARQ机制造成的时延问题，
但即使禁用大部分HARQ机制避免时延问题，仍然面临时变信道下的逆向译码
失效，重传时机不佳造成的低信息速率等问题。 
预变换极化码（Pre-transformed Polar Code）是一类极化码的变种编码，通
过前置预变换的方式进行编码生成。目前的研究工作大多集中在具有代表性的
PAC码。PAC码是E. Arıkan在2019年ISIT给出的香农奖演说中所提到的最新研究
内容，而后发布了一篇预印稿予以说明[5]。PAC码的编码生成顺序为，首先进行
码率配置（Rate-profiling），其次进行卷积码预变换编码，最后进行极化码编码。
因此，PAC码也可认为是一种卷积码与极化码的特殊级联结构，以卷积码为外码、
Polar码为内码。与一般级联编码方案的根本区别在于，编码过程中码率配置置
于最前，预变换和极化码编码均是全码率编码。因此，其中的预变换并非完整
的外码编码，而是对极化码生成矩阵的改变。如图4所示。 
码率配置
Rate-Profiling
卷积码预变换
Convolutional Pre-
transformation
 T
极化码编码
Polar Coding
G
d
0
c
v
v




d


T
u
v
G
x
u
 
图4 PAC 码的编码过程 
 码率配置：预变换极化码的码率配置与极化码的信息位选择过程是相似
的，同样选取一组编码位置集合放置信息比特，其他位置的比特置0。但是由于
编码结构的变化，按照极化码的码率配置方法构建的PAC码并不是最优的。[5]
中提出在构建PAC码时采用Reed-Muller（RM）码码率配置相比采用Polar码的码
率配置方式可以取得更高的截断码率（cutoff-rate），使其在短码时取得更好的
最大似然译码性能。此外，[35]中针对PAC码在删除信道下给出了一种优化的码
率配置方法。 
 预变换：预变换矩阵是预变换极化码的核心，决定最终生成的预变换极
化码码块的最小汉明距离和码重分布。预变换的目的在于改善极化码在高码率
时的最小汉明距离不足的问题，因此结合极化码生成矩阵特点，一般预变换矩
NSFC 2023
第 11 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 13 页 ===
阵设置为上三角矩阵。[36]中证明了上三角预变换矩阵带来的码重提升，并给出
了随机预变换矩阵下码重的概率分布情况。PAC码的预变换矩阵由单一的序列扩
展而来。[37]中对PAC码的错误指数（码重分布）进行了分析，给出了一种错误
指数的优化方法。 
 预变换极化码的译码：PAC码的原始论文中提出了针对PAC码的译码是
一种采用Fano算法的序列译码，采用该序列译码可使短码PAC码在不级联CRC
的情况下相比CRC-极化码取得更优的BLER性能，但是这种序列译码的实现难
度、计算复杂度和缓存开销较大，不适用于卫星载荷。极化码的列表译码也可
以用于PAC码译码，需将PAC码视作动态冻结的极化码，并在译码时增加解预变
换步骤[38-39]。但是，存在短列表长度时增益不明显的问题，原因在于列表译码
的回溯长度与卷积码预变换的约束长度往往不匹配，难以发挥预变换的额外增
益。 
此外，目前暂无PAC码的速率匹配等方法的研究。极化码的打孔和截短方案
可用于PAC码，但由于编码结构的改变，对极化码的打孔和截短方案并不是对
PAC码的最优方案。例如，在截短方案的使用中，由于预变换的存在，部分额外
的可靠信息位置被强制用作冻结位，造成误块率损失。 
综上，预变换极化码是一种处于研究起步阶段的新信道编码技术，有望取
得较极化码更优的性能，可变的预变换也提供了更多的优化可能性。其与极化
码存在一定的技术共通性，但在编译码优化、速率匹配及跨层联合编码方案等
方面仍需要开展大量探索性研发工作。 
（4）无码率编码技术研究现状 
无码率编码（Rateless Coding）是一种灵活的无固定码率编码方式。其可结
合无反馈或少反馈的传输方式实现任意码率可达，从而实现对信道状态的适配
和信息的可靠恢复，从而减少反馈重传次数，适合卫星等大时延通信场景。喷
泉码是最典型的无码率编码，Luby等人提出了一种“喷泉”形式的数据分发方法，
即像喷泉一样不断向外喷射编码符号，形成的编码包没有固定的码率。基于此，
Luby提出了LT码，是第一种实用的喷泉编码方案[40] 。基于设置编码度的分布，
随机选择原始比特组合进行编码并发送，当接收端获得多于原始比特数量的符
号时即可开始译码尝试，直至正确译码。Amin. Shokrollahi提出了一种级联结构
的无码率编码，即Raptor码[41]，由LDPC码作为外码，LT码作为内码构成。Raptor
码在继承了LT码良好适应性的同时，相比LT码的纠错性能更好。但Raptor码若要
达到很好的误码率性能，需要极大的译码开销，尤其是在长码的情况下。此外，
喷泉码后续还出现RaptorQ码、模拟喷泉码、Spinal码等形式，均具备良好的灵
活性。喷泉码的传输方式带来很好的启发，即在物理层和链路层进行跨层编码
设计，通过少量反馈的方式实现码率可达，实现未知CSI下对时变信道的良好匹
配。喷泉吗采用度分布的方式进行编译码，其误码率性能相比LDPC码、极化码
NSFC 2023
第 12 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 14 页 ===
等固定编码矩阵的分组码有一定的劣势。因此，针对LDPC码也有一些无码率编
码研究，最初提出的RC-LDPC码方案[42]采用打孔的方式实现码率下降，并采用
ARQ方式重传实现码率适配。之后出现了针对HARQ设计的RC-LDPC码方案[43]，
以打孔为主要构建方式。为了避免打孔造成的可靠性损失以及编译码器设计便
捷，陆续有无需打孔的无码率LDPC码编码方案提出，包括采用校验矩阵行合并
方式构建相同码长不同码率的码块[44]，校验矩阵行拆分方式[45]，以及对这些方
式的结合和改进工作[46]。以上针对LDPC码的无码率编码方案与上述极化码的无
码率编码方案存在相似的问题，其解决了降级码率的码块构建问题，但是未针
对传输策略开展匹配研究，难以保障传输效率。 
综上，针对现有卫星通信信道编码技术、有效容量与信息年龄、极化码与预
变换极化码、无码率编码等方面的研究现状进行了分析，可见虽然现有信道编
码传输方案仍难以适应低轨卫星通信的链路特性，但其中的部分理论和技术思
路具有借鉴意义。可借鉴基于信息年龄构建有效容量的思路，充分结合低轨卫
星通信场景特点和业务需求特点，建立编码传输机制设计准则。进而可针对预
变换极化码开展编译码优化和速率匹配设计，并开展适配星地信道的无码率编
码传输机制研究，实现业务信息的近容量编码传输。 
主要参考文献 
[1] 李德仁, 沈欣, 龚健雅, 张军, 陆建华. 论我国空间信息网络的构建[J]. 武汉大学学
报ꞏ信息科学版, 2015, 40(6): 711-715. 
[2] 陈山枝. 关于低轨卫星通信的分析及我国的发展建议[J]. 电信科学, 2020, 36(06): 5-17.  
[3] 孙晨华, 章劲松, 赵伟松, 肖永伟. 高低轨宽带卫星通信系统特点对比分析[J]. 无线电
通信技术, 2020, 46(5):6. 
[4] Arikan E. Channel polarization: A method for constructing capacity-achieving codes for 
symmetric binary-input memoryless channels[J]. IEEE Transactions on information Theory, 
2009, 55(7): 3051-3073. 
[5] Arıkan E. From sequential decoding to channel polarization and back again[J]. arXiv 
preprint arXiv:1908.09594, 2019. 
[6] Wu D, Negi R. Effective capacity: a wireless link model for support of quality of service[J]. 
IEEE Transactions on wireless communications, 2003, 2(4): 630-643. 
[7] Tang J, Zhang X. Cross-layer modeling for quality of service guarantees over wireless 
links[J]. IEEE Transactions on Wireless Communications, 2007, 6(12): 4504-4512. 
[8] Sassioui R, Szczecinski L, Le L, et al. AMC and HARQ: Effective capacity 
analysis[C]//2016 IEEE Wireless Communications and Networking Conference. IEEE, 2016: 
1-7. 
[9] Akin S, Fidler M. Backlog and delay reasoning in HARQ system[C]//2015 27th International 
Teletraffic Congress. IEEE, 2015: 185-193. 
[10] Gunaseelan N, Liu L, Chamberland J F, et al. Performance analysis of wireless hybrid-ARQ 
systems with delay-sensitive traffic[J]. IEEE Transactions on Communications, 2010, 58(4): 
1262-1272. 
[11] Vassaki S, Panagopoulos A D, Constantinou P. Effective capacity and optimal power 
NSFC 2023
第 13 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 15 页 ===
allocation for mobile satellite systems and services[J]. IEEE Communications Letters, 2011, 
16(1): 60-63. 
[12] Amjad M, Musavian L, Rehmani M H. Effective capacity in wireless networks: A 
comprehensive survey[J]. IEEE Communications Surveys & Tutorials, 2019, 21(4): 
3007-3038. 
[13] Kaul S, Gruteser M, Rai V, et al. Minimizing age of information in vehicular 
networks[C]//2011 8th Annual IEEE Communications Society Conference on Sensor, Mesh 
and Ad Hoc Communications and Networks. IEEE, 2011: 350-358. 
[14] Kaul S, Yates R, Gruteser M. Real-time status: How often should one update?[C]//2012 
Proceedings IEEE INFOCOM. IEEE, 2012: 2731-2735. 
[15] Chen K, Huang L. Age-of-information in the presence of error[C]//2016 IEEE International 
Symposium on Information Theory (ISIT). IEEE, 2016: 2579-2583. 
[16] Najm E, Yates R, Soljanin E. Status updates through M/G/1/1 queues with HARQ[C]//2017 
IEEE International Symposium on Information Theory (ISIT). IEEE, 2017: 131-135. 
[17] Sac H, Bacinoglu T, Uysal-Biyikoglu E, et al. Age-optimal channel coding blocklength for 
an M/G/1 queue with HARQ[C]//2018 IEEE 19th International Workshop on Signal 
Processing Advances in Wireless Communications (SPAWC). IEEE, 2018: 1-5. 
[18] Zhang X, Wang J, Poor H V. AoI-Driven Statistical Delay and Error-Rate Bounded QoS 
Provisioning for URLLC Over Wireless Networks in the Finite Blocklength 
Regime[C]//2021 IEEE International Symposium on Information Theory (ISIT). IEEE, 2021: 
3115-3120. 
[19] Wu D, Li Y, Sun Y. Construction and block error rate analysis of polar codes over AWGN 
channel based on Gaussian approximation[J]. IEEE Communications Letters, 2014, 18(7): 
1099-1102. 
[20] Tal I, Vardy A. How to construct polar codes[J]. IEEE Transactions on Information Theory, 
2013, 59(10): 6562-6582. 
[21] He G, Belfiore J C, Land I, et al. Beta-expansion: A theoretical framework for fast and 
recursive construction of polar codes[C]//GLOBECOM 2017-2017 IEEE Global 
Communications Conference. IEEE, 2017: 1-6. 
[22] Eslami A, Pishro-Nik H. A practical approach to polar codes[C]//2011 IEEE International 
Symposium on Information Theory Proceedings. IEEE, 2011: 16-20. 
[23] Niu K, Chen K, Lin J R. Beyond turbo codes: Rate-compatible punctured polar 
codes[C]//2013 IEEE International Conference on Communications (ICC). IEEE, 2013: 
3423-3427. 
[24] Bioglio V, Gabry F, Land I. Low-complexity puncturing and shortening of polar 
codes[C]//2017 IEEE Wireless Communications and Networking Conference Workshops 
(WCNCW). IEEE, 2017: 1-6. 
[25] Wang R, Liu R. A novel puncturing scheme for polar codes[J]. IEEE Communications 
Letters, 2014, 18(12): 2081-2084. 
[26] Miloslavskaya V. Shortened polar codes[J]. IEEE Transactions on Information Theory, 2015, 
61(9): 4852-4865. 
[27] Tal I, Vardy A. List decoding of polar codes[J]. IEEE Transactions on Information Theory, 
2015, 61(5): 2213-2226.  
[28] Chen K, Niu K, Lin J. Improved successive cancellation decoding of polar codes[J]. IEEE 
Transactions on Communications, 2013, 61(8): 3100-3107. 
NSFC 2023
第 14 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 16 页 ===
[29] Mahdavifar H, El-Khamy M, Lee J, et al. Performance limits and practical decoding of 
interleaved 
Reed-Solomon 
polar 
concatenated 
codes[J]. 
IEEE 
Transactions 
on 
Communications, 2014, 62(5): 1406-1417. 
[30] Wang Y, Zhang Z, Zhang S, et al. A unified deep learning based polar-LDPC decoder for 5G 
communication 
systems[C]//2018 
10th 
International 
Conference 
on 
Wireless 
Communications and Signal Processing (WCSP). IEEE, 2018: 1-6. 
[31] Chen K, Niu K, He Z, et al. Polar coded HARQ scheme with Chase combining[C]//2014 
IEEE Wireless Communications and Networking Conference (WCNC). IEEE, 2014: 
474-479. 
[32] Liang H, Liu A, Zhang Y, et al. Analysis and adaptive design of polar coded HARQ 
transmission under SC-List decoding[J]. IEEE Wireless Communications Letters, 2017, 6(6): 
798-801. 
[33] Li B, Tse D, Chen K, et al. Capacity-achieving rateless polar codes[C]//2016 IEEE 
International Symposium on Information Theory (ISIT). IEEE, 2016: 46-50. 
[34] Hong S N, Hui D, Marić I. Capacity-achieving rate-compatible polar codes[C]//2016 IEEE 
International Symposium on Information Theory (ISIT). IEEE, 2016: 41-45 
[35] Sun H, Viterbo E, Liu R. Analysis of Polarization-adjusted Convolutional Codes (PAC): A 
Source-Channel Coding Method[C]//2021 IEEE Globecom Workshops (GC Wkshps). IEEE, 
2021: 1-6. 
[36] Li B, Zhang H, Gu J. On pre-transformed polar codes[J]. arXiv preprint arXiv:1912.06359, 
2019. 
[37] Rowshan M, Hoang Dau S, Viterbo E. Error coefficient-reduced polar/PAC codes[J]. arXiv 
e-prints, 2021: arXiv: 2111.08843. 
[38] Yao H, Fazeli A, Vardy A. List decoding of Arıkan’s PAC codes[J]. Entropy, 2021, 23(7): 
841. 
[39] Rowshan M, Burg A, Viterbo E. Polarization-adjusted convolutional (PAC) codes: Sequential 
decoding vs list decoding[J]. IEEE Transactions on Vehicular Technology, 2021, 70(2): 
1434-1447. 
[40] Luby M. LT codes[C]//The 43rd Annual IEEE Symposium on Foundations of Computer 
Science, 2002. Proceedings. IEEE Computer Society, 2002: 271-271. 
[41] Shokrollahi A. Raptor codes[J]. IEEE transactions on information theory, 2006, 52(6): 
2551-2567. 
[42] Li J, Narayanan K. Rate-compatible low density parity check codes for capacity-approaching 
ARQ scheme in packet data communications[C]//Int. Conf. on Comm., Internet, and Info. 
Tech.(CIIT). 2002: 201-206. 
[43] El-Khamy M, Hou J, Bhushan N. Design of rate-compatible structured LDPC codes for 
hybrid ARQ applications[J]. IEEE Journal on Selected Areas in Communications, 2009, 
27(6): 965-973. 
[44] Casado A I V, Weng W Y, Valle S, et al. Multiple-rate low-density parity-check codes with 
constant blocklength[J]. IEEE Transactions on Communications, 2009, 57(1): 75-83. 
[45] Zhao P, Wang Z, Wang Q. Construction of multiple-Rate QC-LDPC codes using hierarchical 
row-splitting[J]. IEEE Communications Letters, 2016, 20(6): 1068-1071. 
[46] Zhang C, Mu X, Yuan J, et al. Construction of Multi-Rate Quasi-Cyclic LDPC Codes for 
Satellite Communications[J]. IEEE Transactions on Communications, 2021, 69(11): 
7154-7166. 
NSFC 2023
第 15 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 17 页 ===
2. 项目的研究内容、研究目标，以及拟解决的关键科学问题（此
部分为重点阐述内容）； 
2.1 拟解决的关键科学问题 
低轨卫星通信海量的业务信息和多样的业务形态，对通信系统的传输速率
和业务QoS指标承载能力提出更高更复杂的需求。由于低轨星地信道随机时变且
难以预估，同时受限于载荷能力、传播时延等因素，难以得到一种理想的编码
传输机制，在保障业务误块率和时延差异化需求的前提下，实现卫星过顶周期
内业务信息的近容量编码传输。其深层原因主要在于以下两方面，一是信道有
效容量的表征不清，也就是编码传输机制的设计边界不清，物理信道容量随机
时变加之差异化业务需求约束，造成编码传输机制设计缺乏依据；二是对容量
的逼近不足，体现在瞬时是编码增益在资源开销受限情况下仍不足，体现在过
顶周期则是编码传输对时变信道容量的自适应适配能力不足。因此，本项目拟
解决的关键科学问题是：基于预变换极化编码技术的低轨星地信道有效容量表
征与自适应逼近问题。 
根据上述科学问题的由来分析，有效容量表征、编码增益近界、容量持续
匹配，从“表征”和“逼近”两个层面构成科学问题的核心要素。其中，有效
容量的精准表征是科学问题的基础和实现容量逼近先决条件，所传输编码块的
增益提升是实现容量逼近的根本保证，编码传输机制对时变信道的连续适配是
持续容量逼近的必然途径。因此，关键科学问题可具体表现为以下三个子问题： 
1) 面向多样化通信业务的有效容量表征问题 
低轨卫星通信信道特点和业务需求是该子问题的要素。虽然物理信道容量
是未知时变量，但其影响可以通过跨层设计适配和弥补。而业务需求是可知的，
从物理层和链路层的跨层角度决定有效容量，且对编码传输机制设计形成明确
的边界约束。因此，该子问题的重点在于对业务数据包时延和误块率影响因素
的精确刻画。 
2) 低资源开销下预变换极化码的近极限编码问题 
该子问题的实质在于，在业务可靠性需求边界内，如何采用有限的码长和
低复杂度的编译码实现更高的编码增益。因此，该子问题的重点之一在于对预
变换极化码编码生成矩阵和实际译码方法性能的优化；重点之二在于预变换极
化码的速率匹配方法的设计，保障任意码长、码率的码块可靠性。 
3) 大传播时延场景下编码传输机制对信道的连续适配问题 
编码传输机制对时变信道的适配程度决定该周期内容量逼近程度，也体现
对业务需求的支撑效果。该子问题的实质在于，如何将编码传输机制中的物理
层编码技术和链路层重传控制合理联动，在未知CSI条件下，实现对时变有效容
量的自适应逼近。因此，该子问题的重点在于连续编码块的构造方式优化以及
反馈重传的内容和时机优选。 
NSFC 2023
第 16 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 18 页 ===
以上三个子问题的内在关联如图5所示，子问题1是从物理层和链路层跨层
角度关注如何划清编码传输方案的设计边界，为后两个子问题的解决提供理论
指导和技术约束；在子问题1提供的可靠性边界指导下，子问题2从物理层编码
技术角度，关注编译码方法实际性能优化和增益的提升；子问题3在子问题1的
跨层有效容量理论指引下，基于子问题2的编码增益提升机理，关注跨层编码传
输机制设计与有效容量自适应持续逼近。 
链路层
物理层
多样化通信业务的有
效容量表征问题
预变换极
化码近极
限问题
编码传输机制对信道
的连续适配问题
...
理论依据
逼近机理
性能边界
 
图5  项目关键科学问题各子问题间的内在关联 
2.2 研究内容 
根据以上对于科学问题的剖析和分解可见，三个子问题的要素间呈现层进
式关系。因此，可对子问题逐个解决，并注重子问题间的关联性，从而实现对
科学问题的解决。依次针对三个子问题提出如下解决思路： 
首先，针对有效容量表征问题，对卫星通信场景下的造成业务包时延和误
块率的因素进行精准刻画，探明二者与编码传输参数的关系。对于时延因素的
分析，拟采用信息年龄这一较为完整的分析模型。对于误块率的分析，由于其
与实际译码算法相关，不失一般性拟采用分组码最大似然软译码误块率分析模
型作为理论性能边界；其次，针对预变换极化码近容量编码问题，需在误块率
理论模型基础上结合实际译码方法，对任意维度的生成矩阵进行匹配设计和实
际性能优化。拟采用短列表译码这一可部分并行计算且资源开销较低的方法。
对未降维生成矩阵进行的优化，拟对列表译码中错误传播情况进行改善。而对
降维生成矩阵进行的优化，拟研究与之对应且适用列表译码的高可靠速率匹配
方法；最后，针对编码传输机制对信道的连续适配问题，需按照有效容量模型
指引，基于可靠编译码方法，联合设计预变换极化码连续编码和重传控制策略。
对于预变换极化码的连续编码传输，拟采用无码率编码这一适用于未知信道状
态的灵活编码方法。设计与之匹配的高效重传控制策略，拟将喷泉传输策略与
HARQ策略相结合，通过优化全周期业务时延优选机制参数。 
基于该解决思路，可提出以下三方面研究内容。 
研究内容1：基于编码信息年龄的低轨星地信道有效容量表征研究 
首先，构建面向低轨卫星通信的编码信息年龄（Age of Coded Information，
C-AoI）时延分析模型。分析低轨卫星通信编码传播时延、反馈重传时延、排队
时延等时延组成的特征和影响因素，探究码率和反馈重传次数对C-AoI的影响机
理；其次，研究预变换极化码的理论误块率。明晰预变换矩阵与编码错误系数
NSFC 2023
第 17 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 19 页 ===
和最小汉明距离的关联，基于对星地信道模型，探究理论误块率的影响机理，
并优化预变换构造；最后，基于C-AoI和误块率模型，结合对需求指标的量化，
建立面向低轨卫星业务的有效容量模型。 
研究内容2：低复杂度高增益预变换极化码编译码方法研究 
首先，基于研究内容1中预变换构造理论优化基础，结合短列表译码特性，
开展编码生成矩阵的优选改造方法及实际误块率降低机理研究。通过在预变换
中增加对不可靠比特位置的保护，减少列表译码的错误传播效应，充分降低实
际误块率；其次，研究预变换极化码的速率匹配方法。基于对极化码速率匹配
方法应用于预变换极化码的局限性分析，结合速率匹配对列表译码的影响机理
分析，提出打孔和截短新方法，保障任意码长、码率的码块可提供与之匹配的
可靠性。  
研究内容3：无码率预变换极化码自适应编码传输机制研究 
首先，基于研究内容2的高增益编译码方法，构建无码率预变换极化码编码
方案。即构建一组部分信息重复且码率降级的预变换极化码码块，当接收端不
断累积并进行译码尝试时，等效码率不断下降且可达任意预定的码率；其次，
在研究内容1的编码传输设计边界约束下，研究与无码率编码块相匹配的类喷泉
重传控制策略，形成无码率编码传输机制。基于业务需求约束和有效容量模型，
确定机制中的参数选用范围；最后，在满足业务需求的基础上，对全过顶通信
周期的业务编码块平均时延进行优化，实现编码传输机制对业务的高效支撑。 
科学问题
海量业务信息的持续高速率传输需求
多样业务的误块率和时延差异化需求
低轨卫星的链路特性、载荷能力、传播时延等
客观因素约束
科学问题：基于预变换极化编码技术的低轨星地信道有效容量表征与自适应逼近问题
面向多样化通信业务的有效容量
表征问题
低资源开销下预变换极化码的近
极限编码问题
大传播时延场景下编码传输技术
对信道的连续适配问题
研
究
问
题
时延和误块率影响因素的刻画
时延因素：编码信息年龄模型
误块率：最大似然误块理论
生成矩阵与译码方法匹配设计
完整码块：联合列表译码特性
降维生成矩阵：速率匹配方法
编码块与重传控制联合设计
码块：无码率预变换极化码
重传控制：喷泉结合HARQ
解
决
思
路
研究内容1：基于编码信息年龄的
低轨星地信道有效容量表征研究
研
究
内
容
研究内容2：低复杂度高增益预变
换极化码编译码方法研究
研究内容3：无码率预变换极化码
自适应编码传输机制研究
构造优化基础
可靠编译码
机制设计边界
自适应匹配低轨星地动态链路，支撑业务信息的近有效容量传输
需求与约束
目
标
 
图6  项目“研究问题-解决思路-研究内容-研究目标”关系剖析图 
NSFC 2023
第 18 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 20 页 ===
2.3 研究目标 
本项目的总体目标是：提出灵活高效的预变换极化码编码传输机制，实现
对低轨星地动态链路的自适应匹配，支撑业务信息的近有效容量传输。 
分为以下三个具体研究目标： 
1）建立一种面向低轨卫星通信场景的信道有效容量表征模型，探明业务需
求对编码传输设计的约束机理。 
2）探索一种预变换极化码的增益提升方法，利用低复杂度编译码方法提供
更优的可靠性。 
3）提出一种无码率预变换极化码编码传输机制，实现动态链路下的业务信
息的尽早接收和可靠译码恢复。 
3. 拟采取的研究方案及可行性分析（包括研究方法、技术路线、
实验手段、关键技术等说明）； 
3.1 研究方法与技术路线 
本项目在分析现有需求和约束基础上，通过剖析技术局限性背后的理论原
因凝练出科学问题。根据对科学问题的分析和拆解，提出解决思路，并给出了
三方面紧密关联的研究内容。面向研究目标，本项目将遵循“容量模型构建—
—编码技术优化——跨层联动机制”的总体技术路线，采用“数学模型构建与
应用场景模拟相结合、理论优化与技术实验相结合”的基本研究方法，层进式
开展三方面研究内容，具体技术路线如图7 所示。 
容
量
边
界
建
立
场景特性
业务需求
双重约束
码率降级可达
无码率
预变换极化码
码块实际增益优化
预变换与列表译码
联合优化
任意码长、码率
速率匹配方法
可靠性模型
最大似然译码
理论误块率
边界表征
有效容量模型
优
化
基
础
编
码
技
术
优
化
跨
层
联
动
机
制
可靠码块
跨层设计
机
制
边
界
参数优选
模
型
基
础
时效提升
过顶周期平均
C-AoI优化
反馈重传优化
类喷泉传输策略
时延模型
编码信息年龄
(C-AoI)
 
图7 项目总体研究思路与技术路线示意图 
NSFC 2023
第 19 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 21 页 ===
3.2 各研究内容实施方案及可行性分析 
（1）基于编码信息年龄的低轨星地信道有效容量表征研究 
针对研究内容1，研究重点在于通过对低轨星地通信场景下业务时延和误块
率的模型建立和理论分析，构建信道有效容量模型。其中，业务时延模型的构
建与星地通信场景和编码传输机制密切相关，星地通信场景引入的大传播时延
和编码传输机制中的反馈重传次数将重构时延模型的表达；误块率的理论模型
则与所采用的预变换极化码的构造及物理信道参数密切相关，由于预变换极化
码的构造可变，因此可通过优化构造的方式优化对应的误块率；信道有效容量
模型的构建需基于前两者模型的建立，结合业务指标，构建能够有效评价编码
传输机制的模型。研究内容1 的实施方案思路如图8 所示。主要研究点的实施
方案细节如下： 
编码信息
年龄模型
传播时延因素
反馈重传次数
低轨星地
信道有效
容量模型
理论误块
率分析
研究点1
研究点2
研究点3
物理信道状态
码块生成参数
通信场景因素
机制设计因素
业务时延
指数
业务误块
率需求
业务需求
优化
 
图8 研究内容1 的实施方案示意图 
 编码信息年龄（C-AoI）模型：AoI 是一种衡量信息时效性的指标，用于
描述有时延系统对具有时间戳信息的更新过程，一般认为AoI 越小的信息对于
接收端越“新鲜”，其时效性价值越高。传统的AoI 描述中最基础的线性AoI ( )t

表示为 
( )
( )
t
t
t


                          (1) 
其中
( )t

指信息生成时的时间戳。随着AoI 研究的开展，仅通过线性方式不足
以评价物理场景中的复杂更新过程，针对不同应用场景出现了AoI 的惩罚函数
表达，包括适用于评价实时数据的指数形式 
( ( ))
exp(
( )),
0
g
t
a
t
a




                    (2)  
以及评价周期性状态数据更新的阶梯形式 
( ( ))
( )
g
t
a
t





                        (3) 
此外，还有AoI 的效用函数表达等形式。因此，可以基于对场景时延特点，对
与不同类型业务数据需求的差异，选取适用的AoI 表达。对于编码传输场景，
需关注编码码率对信息从生成至成功接收的时延影响。因此，提出一种编码信
NSFC 2023
第 20 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 22 页 ===
息年龄（C-AoI）模型，表示为 
1
( )
(
( ))
t
t
t
R




                                            (4) 
其中，R 是指(
( ))
t
t

时间内的平均编码码率。该模型的合理性在于，对于业务
数据包，经编码后业务信息的平均服务时间与码率成反比。 
低轨卫星通信场景下，编码传输过程的时延组成主要包括链路单次单向传
播时延
td ，发送端排队时延
w
d ，反馈重传回路时延。因此，C-AoI 的峰值可表
达为 
peak
1
1
( )
(
( )
2
)
b
t
q
t
c
t
d
d
c
d
R












                  (5) 
峰值出现在编码信息包成功译码恢复时刻，R 可理解为从
( )t

时刻到此刻的平
均编码码率。时延组成中，单次传输时延
td 主要取决于通信距离，可根据通信
场景确定。反馈重传次数为b 。
( )
q
d c 是指第c 次的排队时延。发送端的信息生
成速率
( ) bit/s
m t
与传输速率( ) bit/s
r t
往往不匹配，引入排队时延，如果均是服
从“先到先服务”策略，排队时延
q
d 可通过下式得到 
( )
d
( ( ))
q
d
r
r t p
t
Q
N







                    (6) 
其中，N 为码长，( ( ))
Q 
为( )
时刻的队列长度，与传输过程中生成速率和服
务速率变化相关。 
 预变换极化码误块率分析：预变换极化码的编码过程包含码率配置、预
变换和极化码编码三部分。首先，进行码率配置过程，此过程与极化码的构造
类似，将信息d 映射到数据码块v ，其中信息比特v
d

，冻结比特
0
c
v


。其
次，对数据码块v 进行预变换得到中间码块
N

u
vT 。预变换矩阵一般是上三角
矩阵，例如PAC 码采用卷积码生成矩阵
( , )
{
, ,
{1,2,
,
}}
N
a b
t
a b
N



T
作为预变换，
其由冲激响应
1
2
( ,
,
,
)
c c
c


c
展开得到，其中 
( , )
1
( , )
, if
0, otherwise               
a b
b a
a b
t
c
a
b
a
t










 
                 (7) 
最后，对中间码块u 进行码率为1 的极化码编码得到PAC 码码块
N

x
uG 。综上，
PAC 码的生成矩阵为
N
N
N

J
T G 。 
假设在星地信道常采用的阴影莱斯信道模型下，分组码的最大似然软译码
误块率的近似公式为 
min
BLER
min
0
(2
( )
/
)
d
b
P
A
Q
d
R h
E
N





                (8) 
其中，( )
Q 是指Q 函数，
min
d
是指最小汉明距离，
min
d
A
是具有最小汉明重量（最
小码重）的码块数量分布，称为错误系数。( )
h 指时刻的信道系数，衰落信噪
NSFC 2023
第 21 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 23 页 ===
比
0
/
b
hE
N

的概率分布为 
1
1
1
2
( )
exp(
/ 2 )(
)
( ,1,
)
2
2
(2
)2
m
fm
L
p
f
F m
f
fm
L
fm
L
f







              (9) 
其中，m 是指Nakagami 系数，2 f 是指散射分量平均功率，L 指LOS 分量平均
功率。因此，对于预变换极化码误块率的优化思路是提升最小汉明距离
min
d
以及
降低错误系数
min
d
A
。 
对于极化码和预变换极化码，最小汉明距离
min
d
和错误系数
min
d
A
与码率和信
息位选择策略相关。极化码的最小汉明距离
min
d
和生成矩阵最小行重
min
)
w
(
)
(
N
G

具有一致性，即 
min
min
))
w
(
(
N
d

G

                          (10) 
这是由于生成矩阵
N
G 存在一性质，即其生成矩阵中任一行w(
)
ig
有 
w(
)
w(mod(
,2))
i
i
j
j H



g
g
g
                     (11) 
其中，
{
1,
2,...,
}
H
i
i
N



。因此，对于采用生成子矩阵
)
(
N H
G
得到的码字，
其汉明重量不可能低于w(
)
ig
。该性质也是预变换矩阵设置为上三角矩阵的原
因，可通过提升生成矩阵行重从而提升码重。但另一方面，预变换矩阵
N
T 的存
在，使预变换极化码的最小码字距离
min
d
和生成矩阵最小行重
min
)
w
(
)
(
N
J

失去
一致性，仅有 
min
min
))
w
(
(
N
d

J

                          (12) 
因此，需要对
min
d
A
进行计算。对于极化码生成矩阵的各行的行重有如下关系 
|
|
|
| 1
|
|
)
2
2
w
2
(
j
i
j
i
i
S
j
S
S
S






g
g
                (13) 
其中，
supp(bin(
1)),
{1,...,
}
iS
i
i
N



。根据此关系，可以通过遍历的方式得到
各码重预变换极化码码块的精确数量。但是遍历方式复杂度极高，往往超出计
算能力，因此需要进行简化估算。由于信息位选取服从部分顺序（Partial Order）
特性，即当位置索引
{1,...,
1}
i
i
N



，

时，必然有其他位置优于该位置，其
索引组成的集合
iS 。有以下特性成立， 
min
w(
w
)
i
i
i
s
c
s M
c C






g
g
g
                                  (14) 
其中集合
i
M 是集合
iS 的任一子集，
i
C 是存在于集合{
\
}
iS

的一子集。式(14)较
大程度降低了遍历得到
min
d
A
的复杂度。此外，基于式(14)还可得到
min
d
A
的下界 
min
2
i
m
d
i
S
A



                                                  (15) 
其中，
m
是信息位集合中对应最低行重的索引值子集。因此，对于预变换极
化码误块率的优化，可通过减少具有最低行重
min
w(
)
w
i 
g
的中间比特
iu 对信息
的承载。根据预变换过程 
NSFC 2023
第 22 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 24 页 ===
(: , )
( , )
supp(
)
i
i
j i
j
j
t
u
t
v



                      (16) 
其中，
(: , )
supp(
)
i
t
是指预变换矩阵
N
T 的第i 列的集合支撑函数。可以将预变换矩
阵
N
T 中对应的列(: , )i
t
置0 或降低列重，从而降低
min
d
A
进而实现误块率下降。 
 有效容量模型：传统的有效容量是以链路层接入技术的数据缓存队列为
切入点进行容量分析，认为传输速率( ) 
r t
时变。此时的时延违约概率为 
max
max
Pr{
}
exp(
)
l
D
D
D




                  (17) 
其中，是QoS 时延指数，反应业务时延需求，当较小时，时延容忍程度较高。
信道有效容量表示为 
eff
1
1
lim log(E[exp(
( ))])
t
C
S t
t





               (18) 
其中
0
( )
( )
t
S t
r
d



是0 到t 时刻传输的比特数。可见业务时延指数对物理信道
容量产生了约束，当
0

时，即业务容忍任意时延时，有效容量等于物理信道
容量。 
式(18)表示的有效容量中，仅关注业务包的时延指数要求，并未关注场景时
延组成和业务包的编译码可靠性对有效容量的约束。因此，将C-AoI 与传统时
延违约概率进行融合，可以得到 
peak
max
max
Pr{ ( )
}
exp(
)
t
D
D





                (19) 
此外，根据切尔诺夫限，可采用另一更严格的时延指数
peak
'
E[exp(
( )
)]
t






，
如下式所示 
peak
max
max
peak
max
Pr{ ( )
}
exp(
'
)
exp(
E[exp(
( )
)]
)
t
D
D
t
D









      (20) 
假设各时刻的传输速率独立，基于上文中分析的预变换极化码的理论误块率
BLER( )
P
R ，连续传输一组M 个码块时，码率为
iR 的码块传输时的有效容量为 


eff
BLER
BLER
0
1 log(E[exp(
' )])
'
1
     
log
( )
(
)
( )(1
(
))exp(
'
)d
'
i
i
i
i
i
C
S
p
P
R
p
P
R
S















   (21) 
其中，信噪比概率
( )
ip 根据式(9)计算得到。式(21)的合理性在于，当发生误块
时，该块包含的信息不计入成功服务的比特数量。可结合编码传输策略对式(21)
进行进一步的扩展。综上，根据不同业务的时延指数要求和对误块概率
BLER(
)
i
P
R 的分析，结合星地物理信道参数，得到二者约束下的有效容量表征模
型。 
研究内容1 的可行性分析：研究内容1 主要针对编码信息年龄构建、理论
误块率分析和有效容量模型构建三方面开展研究。在关于时延模型的构建工作
中，申请人已在前期工作针对时延模型开展研究分析，对低轨卫星通信的C-AoI
NSFC 2023
第 23 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 25 页 ===
时延模型进行了初步构建，并已在研究方案中给出主要时延构成因素的量化表
达。此外，申请人参与的一项国家自然科学基金面上项目（62071141），空间物
联网的信息时效与大规模接入技术研究，2021.01 至2024.12，申请人在项目中
承担信息时效的理论分析工作内容，该项目的部分研究成果可以为本项目研究
的低轨卫星编码时延模型研究提供一定的理论参考；关于预变换极化码的误块
率的分析工作，申请人在已投稿论文中证明预变换极化码的最小汉明距离
min
2n
d



，其中 
1
1
1
k
k
n
n
o
k
k















                        (22) 
此外，申请人在已发表论文《Iterative and Adjustable Soft List Decoding for Polar 
Codes, IEEE Transactions on Signal Processing, 2020, 68: 5559-5572》中对极化码块
的误块概率进行了理论分析，可作为本项目的理论基础。关于有效容量模型的
构建，研究方案中已给出了初步的时延和误块率双重约束的有效容量表征模型。
其中对于业务需求的研究工作，3GPP TR38.811 等技术文档给出了部分业务描
述。此外，由于卫星通信部分业务由地面通信转化而来。因此，可借鉴地面移
动通信的eMBB 和URLLC 场景的业务需求开展分析。误块率和有效容量研究中
关于星地物理信道模型，3GPP TR38.811 等技术文档已给出了多仰角下的星地信
道参数，ITU RP.618 等技术文档给出了各频段的雨衰、大气等衰减与仰角的关
系。申请人在已发表论文《Performance Analysis of Millimeter-Wave Hybrid 
Satellite-Terrestrial Relay Networks Over Rain Fading Channel, IEEE VTC 
2018-Fall》中对Ka 频段雨衰星地链路的中断容量进行了分析，可作为本研究的
理论借鉴。此外，申请人所在实验室具备卫星信道模拟器，可结合STK 软件对
星地信道进行模拟仿真。实验室还建有Ku 频段卫星接收地面站，具有专用带宽，
可对天气影响进行实际测量。 
（2）低复杂度高增益预变换极化码编译码方法研究 
针对研究内容2，研究重点在于结合实际采用的列表译码，对预变换矩阵进
行实用优化并给出速率匹配方法。其中，列表译码作为顺序译码，实际误块率
多来自靠前的不可靠位置引发的错误传播。因此，在研究内容1 提出的预变换
矩阵理论优化基础上，需增加对不可靠位置的保护。针对预变换极化码的速率
匹配方法研究，由于预变换极化码码块可视为特例极化码，可借鉴极化码的打
孔和截短方案。但由于预变换矩阵引起的生成矩阵变化，原面向极化码的打孔
和截短方案存在应用局限性，需对打孔和截短方案进行适应性改造。还需结合
列表译码的多重路径等特点，开展新打孔和截短方案设计。研究内容2 的实施
方案思路如图9 所示。主要研究点的实施方案细节如下： 
NSFC 2023
第 24 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 26 页 ===
研究点1
预变换矩阵
短列表译码
降低实际误
块率
研究内容1
理论误块率
不可靠位置保护
匹配设计
打孔、截短
方法研究
降
维
设
计
研究点2、3
短列表译码
极化码速率
匹配方法
优化基础
局限性
基于译码流程特
点的联合设计
 
图9 研究内容2 的实施方案示意图 
 预变换构造与短列表译码的联合优化：预变换极化码的列表译码过程是
基于极化码的SCL 译码而来，增加解预变换的步骤，本项目采用基于LLR 的列
表译码，并采用短长度的列表以保持低的计算复杂度和空间复杂度，研究内容1
中得到的最大似然译码的理论误块率可作为列表译码误块率的下界。对于列表
译码，译码开始时根据接收符号y 对初值LLR 进行计算。根据SC 译码的LLR
递推公式，顺序地对每个路径的每个中间码块比特ˆ [ ]
iu 对应的LLR 进行计算，
即计算得到 
1
1
ˆ
ˆ
Pr( ,
[ ]|
[ ]
0)
L [ ]
ln
,
ˆ
ˆ
Pr( ,
[ ]|
[ ]
1)
(
)
i
i
i
n
i
i
u
u
u
u






y
y








              (23) 
并对该路径此时的路径度量（PM，Path Metric）进行计算，即 
ˆ
(1 2
[ ])L [ ]
ˆ
ˆ
[ ]
[ ]
1
1
ln(1
)
n
j
j
i
j
i
i
u
v
v
j
j
PM
BM
e













            (24) 
其中，中间码块比特的估计值ˆ [ ]
iu 根据与之相关的数据码块比特的估计值计算
得到，与式(16)相似有 
(: , )
( , )
supp(
)
ˆ
ˆ
i
i
j i
j
j
t
u
t
v



                      (25) 
根据SCL 译码的LLR 值的递推公式可知，位置靠前的且具有较低行重的位置，
对应的LLR 值均值较低，其误码概率较高。SC 译码作为顺序译码，会发生错误
传播（Error Propagation）的现象，即靠前的位置发生的错误会影响后续的软译
码判决，造成更多的估计值错误产生。而列表译码作为多重的SC 译码，同样对
每个路径也有错误传播的现象。因此，在编译码设计中，需要对靠前的且对应
较低行重的信息位进行保护。当前PAC 码的列表译码，在短列表情况下的增益
不明显，原因就在于回溯长度有限，使靠前信息位与后续信息位在译码中未产
生足够的相关性，对靠前信息位的保护不足。因此，在编译码设计中，需要考
虑靠后的可靠信息位对靠前的较不可靠信息位的校验作用，从而保障列表译码
中正确路径的PM 最低。 
NSFC 2023
第 25 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 27 页 ===
需要在研究内容1 的基础上对预变换矩阵进行再次优选和改进，可用以下
两种方法：一是在下三角部分引入部分非零元素的方式，使可靠比特的译码结
果对靠前的较不可靠比特结果形成校验，再通过对译码方法的改造，实现部分
译码流程的回溯；二是通过扩大较不可靠比特v的关联范围，即扩大其对应的
集合支撑函数
(: , )
supp(
)
t

，使更多的中间比特
ju 的取值与其相关。第一种方法的
通过增加校验的方式剔除错误路径，是较为常规的改进方式。第二种方法的理
论依据在于，列表译码过程中的正确路径cl 和任一错误路径wl 在第位的译码估
计结果发生歧义后，后续各自的LLR 均值幅度有以下关系 
E[L [ ]]
E[L [ ]] ,
{ 1,
,
}
|
| |
|
n
w
n
i
i
c
l
l
i
N




               (26) 
根据式(24)，式(26)中LLR 均值幅度的差异会引起下式 
ˆ
ˆ
[
]
[
]
]
]
0
=E[
E[
i
w
i
c
i
v
v
PM
PM





                                    (27) 
其中，
ˆ [
]]
E[
i
c
v
PM

是正确路径cl 对应的PM 均值，
ˆ [
]]
E[
i
w
v
PM

是错误路径对应的
PM 均值。因此，减小误块率的方式之一是提升
i。扩大较不可靠比特v的关联
范围，使ˆ [ ]
wl
u
u


这一错误判决直接影响到后续比特估计值
(: , )
supp(
)
ˆ
[ ]
t
w
u
l

，将造
成式(26)中|E[L [ ]]|
n
i
wl
的进一步降低，从而引起
ˆ [
]]
E[
i
w
v
PM

的提升，提升了
i
。 
 打孔方法：打孔对母码码块的特定位置码字符号进行删去或不发送，在
译码端对打孔位置对应的LLR 值置0，并采用母码长度的译码器进行译码。分
组码的最优打孔方法是寻求一组打孔位置
1
2
{
,
,...,
}
M
p
p
p

p
，使打孔后码块的最
小汉明距离最大化，即 
                   
min
{1,2,...,
}
argmax  d
( (
))
,
N
c


p
p
p

                  (28) 
但是对于预变换极化码，这种理论最优方式往往无法达到实际最优。原因在于，
打孔所造成的0 容量子信道对列表译码中各比特可靠性造成的变化。其中，u 
q 对
应的LLR 值均值降为0，
q 是

p 经二进制值反转排序后映射得到的。因此，ˆv 
q
的错误概率约为0.5。若ˆv 
q 发生错误，与之相关的中间比特结果
(: , 
)
supp(
)
ˆ
t
u

q
将发生
错误，并直接影响
(: , 
)
supp(
)
ˆ
t
v

q
的正确性，造成错误弥散。为了避免这种现象，需要
在码率配置时将v 
q 冻结，即新的信息位集合
'

q
。因此优化目标变更为 
min
{1,2,...,
}
argmax  d
( (
))
',
N
c



p
p
p
                 (29) 
结合实际列表译码方法，可按照最小化译码误块率的方式对各打孔图样p 进行
优选。将式(29)转换为 
{ ,
'}
BLER
argmin
( (
), )
{
,
}
',
c
P
L





p
p
p
               (30) 
NSFC 2023
第 26 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 28 页 ===
其中，L 是列表尺寸。各路径的各中间比特
iu 对应的LLR结果值E[L [ ]]
n
i l
可由高
斯近似方法得到，其中x 
p 对应的初值需置0。对于正确路径的PM均值
ˆ [
]]
E[
i
c
v
PM

推导过程不引入错误估计值，而对于错误路径的PM均值
ˆ [
]]
E[
i
w
v
PM

推导过程引
入错误估计值，并保留较低的
1
L 个PM均值结果。此时的列表译码误块率表示
为 
ˆ
ˆ
[
]
[
BLER-LD
]
1
1
( (
), )
erfc(
(min(
])
]))
',
E[
E[
2
2
i
w
i
c
c
v
v
P
P
PM
L
M





p
     (31) 
此外，还可对低复杂度打孔方法开展研究。极化码的准均匀(QUP)打孔方法
选取集合
{1,2,...,
}
M

q
，将其中各元素iq 对应的二进制值，反转排序后映射
到p 。经反转排序得到的打孔位置p 在{1,...,
}
N 上的散布是较为均匀的。由于极
化码的部分顺序原则，生成矩阵
)
(
G 中一定含有一行
ig ，其前
min
)
w
(
(
)
G 
个元
素值为1，其余元素值为0。此列在准均匀打孔后的行重为 
min
w(
( ))
(
/
)w
(
(
1
))
i
E N


p
g
G 
                 (32) 
在部分条件下，w(
( ))
i p
g
是打孔生成矩阵的最小行重。但是QUP方法并未考虑
打孔所造成的0容量子信道对各编码位置可靠性造成的变化。这一变化会造成信
息位集合的选择在打孔后并不可靠。因此，标准的原型方案中采用与QUP方
法相同的打孔位置。区别在于其联合考虑了信息位集合的选择，将uq 强制为冻
结位。在选择信息位集合时，其选择范围为{
1,
2,...,
}
M
M
N


。由于预变换
极化码的最后生成步骤是极化码的编码，可采用极化码的低复杂度打孔方法。
除了对vq 进行冻结，还需考虑0容量子信道对其他编码位置可靠性造成的变化，
如图10所示。连续的0容量子信道会严重降低其后续临近位置的可靠性，易造成
误码。 
1
5
3
7
2
6
4
8



8
3
[L ]
E

8
2
[L ]
E

8
1
[L ]
E

8
0
[L ]
E

ix
iv
1
2
3
4
5
6
7
8


0
0
0
0
0
0
0
0
0



1
5
3
7
2
6
4
8

8
3
[L ]
E

8
2
[L ]
E

8
1
[L ]
E

8
0
[L ]
E

ix
iv
1
2
3
4
5
6
7
8





0
0
0




4
2
2
2
2
2
2




可靠性
降低
2

 
图10 打孔对各编码位置可靠性造成的变化示意图 
 截短方法：截短既对编码码块进行打孔，也对与之相关的全部信源比特
强制冻结，需按照生成矩阵开展。在译码端，对编码码块中的未发送位置对应
的LLR 值置为，采用母码长度的译码器进行译码。极化码基于生成矩阵权重
的截短方法的策略是，每次选择一个列重为1 的列，将其对应的索引列入打孔
位置集合p ，并在当前生成矩阵中删去所选择的1 所在的行和列。删除的行对应
的索引构成强制冻结集合，删除的列对应的索引构成截短图样。由于极化
NSFC 2023
第 27 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 29 页 ===
码的未反转生成矩阵
2
n

F
是下三角矩阵，因此存在一种简单的强制冻结集合
{
1,
2,
,
}
E
E
N





和截短图样
({
1,
2,
,
})
B
E
E
N





。该方法是标准方法
的原型。 
但对于预变换极化码，由于增加了预变换步骤，生成矩阵
N
N
N

J
T G 的最低
列重为
(: , )
min(| supp(
) |)
1
i
t
。因此，预变换极化码的生成矩阵中不存在列重为1
的列。对其进行截短有以下两种方法，一是扩展强制冻结集合；二是重构预
变换矩阵
N
T 。对于扩展强制冻结集合方法，每个中间比特的值取决于
(: , )
supp(
)
i
t
v
，
因此对于截短图样，其对应的强制冻结集合
ex
应满足 
(: , )
ex
{supp(
)}
,
i
i
i
t
f
f




                 (33) 
此时的信息位集合的选择区间被压缩为
ex
{
}
N

，其中
{1,2,...,
}
N
N


。可
见，强制冻结集合的扩展，将造成对于选择区间的挤压，需采用更多不可靠
的位置来承载信息比特，会造成部分误块性能的损失。对于重构预变换矩阵方
法，通过对预变换矩阵进行改造避免的扩展，即
ex 

。改造预变换矩阵的
原则是使截短图样与截短图样在新生成矩阵中一一对应。因此，改造的预
变换矩阵可表示为
( , )
{
, ,
}
N
a b
N
t
a b


T


，其中 
( , )
( , )
( , )
( , )
, if 
and
1, elseif 
                
0, otherwise 
a b
a b
a b
a b
t
t
a
b
a
b
t
a
b
b
t


















 
 
且
              


               (34) 
此时，中间码块比特u
v


，对应的截短比特x实际上未经历预变换，会造成
部分级联增益的损失。 
为了避免以上损失，可采用前置冻结位校验的方式。允许部分元素
ex
,
v


作为信息位，并在前置冻结位
{1,2,..., }
v
中放置与v相关的比特。表示
为 
{1,2,..., }
)
(
v
g v

                         (35) 
通过对列表译码中的初始化赋值部分的改动，对可能的ˆv取值的组合进行遍历，
对各路径前位比特的估值
{1,2,..., }[ ]
ˆv
l

按照式(35)直接赋予不同的取值组合。通过
结合列表译码对映射关系
)
(
g 进行良好设计，使列表译码运行到第min(
) 1


位
时，保障正确路径的PM 最低，即 
min(
) 1
min(
) 1
ˆ
ˆ
[
]
[
]
E[
E[
0
]
]
w
w
c
l
l
v
v
l
PM
PM








            (36) 
该PM 差值均值的最小值
)
min(E[
]
wl

需要尽可能的大，以减轻错误路径对译码
结果的影响，避免正确路径被剔出列表。该方法的理论依据在于，列表译码中
ˆ [ ]
x l

对应的无穷大LLR 初值的符号，对子码块译码LLR 符号的影响，如图11
所示。因此，映射
)
(
g 的设计需针对其中未受影响的部分，以提升
)
min(E[
]
wl

。 
NSFC 2023
第 28 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 30 页 ===
1
5
3
7
2
6
4
8









8
3
[L ]
E

8
2
[L ]
E

8
1
[L ]
E

8
0
[L ]
E

ix
iv
1
2
3
4
5
6
7
8








1
5
3
7
2
6
4
8









8
3
[L ]
E

8
2
[L ]
E

8
1
[L ]
E

8
0
[L ]
E

ix
iv
1
2
3
4
5
6
7
8








:
wl
:
cl
 
图11 截短对译码LLR 符号的影响示意图 
研究内容2 的可行性分析：研究内容2 主要针对预变换极化码的编译码联
合优化、打孔和截短方法进行研究。在编译码联合优化方面，方案细节中提出
在下三角部分引入部分非零元素的方式和扩大较不可靠比特的关联范围方式两
种，其中后者的可行性已在方案中论述。关于在下三角部分引入部分非零元素
的方式，申请人在已发表论文《On Tail-Biting Polarization-Adjusted Convolutional 
(TB-PAC) Codes and Small-Sizes List Decoding, IEEE Communications Letters, 
2023, 27(2): 433-437》中提出了一种咬尾卷积预变换结构，并设计了相应的短列
表译码方案以降低误块率，证明了引入下三角元素对预变换的优化可行性，所
取得的误块率优于现有标准短码极化码及其他构造；在打孔和截短方法研究方
面，方案中指出了极化码的速率匹配方法在预变换极化码中使用的局限性，给
出了解决方法及相关理论依据。申请人已对PAC 码的多种打孔和截短方法开展
研究，对PAC 码的低复杂度打孔方案进行了研究，并采用短码进行误块率验证，
如图12 所示，可见PAC 码的低复杂度打孔方法可取得较好的误块率性能。 
 
图12 采用低复杂度打孔图样的码长为96 的PAC 码误块率性能 
针对截短方法的研究，申请人已在投稿论文中针对短码提出一种采用前置冻结
位校验的截短方案，其中采用短列表译码。并与所述的扩展强制冻结集合及重
构预变换矩阵方法进行了对比，所提方法取得了较优的误块率，如图13 所示。
关于打孔和截短的现有研究成果可作为本研究的研究基础和方案借鉴。 
NSFC 2023
第 29 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 31 页 ===
 
图13 采用所提出截短方法的码长为56 的PAC 码误块率性能 
（3）无码率预变换极化码自适应编码传输机制研究 
针对研究内容3，研究重点在于构建适配星地时变链路的无码率预变换极化
码编码传输机制。其中，需基于研究内容2 中提出的任意码长、码率的高增益
预变换极化码编译码方法构建无码率码块，通过部分信息的重复编码、累积译
码的实际码率的不断下降，实现任意预定码率的降级可达。基于研究内容1 的
业务需求约束和有效容量模型，设计与无码率编码相匹配的反馈重传策略，根
据时延和误块率对重传次数、编码参数等的约束，结合容量模型，给出编码传
输机制参数的可选用范围。最后，根据研究内容1 的时延模型，构建编码传输
机制在过顶周期内的C-AoI 变化模型，对平均时延进行优化，从而得到优选的
编码传输机制参数。研究内容3 的实施方案思路如图14 所示。主要研究点的实
施方案细节如下： 
研究内容1
业务需求约束、
有效容量模型
研究内容2
任意码长、码率
编译码方法
无码率预变换
极化码构造
任意码率
降级可达
反馈重传策略
减少次数
提升码率
机制参数
选用范围
研究点1
方法基础
信息重复
累积译码
理论依据
研究点2
跨层机制
平均C-AoI优化
机制参数
优选
过顶周期C-AoI
变化模型
时延模型
研究点3
 
图14 研究内容3 实施方案示意图 
 无码率预变换极化码编码：预变换极化码的无码率设计可借鉴无码率极
化码方案，通过重复编码比特、编码块打孔、码块延长三种构造方式实现码率
的降级可达。重复编码比特方式是构建一组相同码长的预变换极化码码块
NSFC 2023
第 30 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 32 页 ===
1
1
2
2
{ ( ,
,
), ( ,
,
),..., ( ,
,
)}
k
k
C N R
C N R
C N R



，其中各码块的码率呈现降级关系
1
2
...
k
R
R
R



。在对码块
( ,
,
),
{1,..., }
i
i
C N R
i
k


编码时，其需要包含前
1
i 个
码块中的部分信息比特，并添加部分新信息比特。当接收端顺序接收到码块
( ,
,
)
i
i
C N R 后译码时，若译码成功则逆向对已接收的前
1
i 个码块逐个进行译
码。由于重复编码的信息比特可认为是已知，从而将全部已接收的码块的等效
码率降至
iR ；编码块打孔方式与重复编码比特方式的构造思路相似，同样对部
分编码比特进行重复编码，但是各码块不等长，不添加新的信息比特，而是采
用打孔的方式构建码块；码块延长的方式则是在接收端不断累积并补长码块，
实现任意设置的一组降级码率
1
2
{
,
,...,
}
k
R R
R
的可达，接收端每次累积并译码的码
块是完整码块。 
NR1/2
NR1/6
NR1/6
新信息位
编码器
编码器
编码器
新信息位
编码器
重复编码比特方式：
码块延长方式：
编码器
编码器
编码器
打孔/截短至
打孔/截短至
打孔/截短至
编码块打孔方式：
(1)
1
1
1
(
,
,
)
C N R 
(2)
2
2
2
(
,
,
)
C N
R 
(3)
3
3
3
(
,
,
)
C N
R 
1
1
(
,
,
)
C N R 
2
2
(
,
,
)
C N R 
3
3
(
,
,
)
C N R 
(1)
(1)
1
2



(1)
2

(2)
3

(1)
(1)
2
3



2
2
2
1
1
1
(
,
,
)
(
,
,
)
C N
R
C N R



1
1
1
(
,
,
)
C N R 
4
4
4
3
3
3
(
,
,
)
(
,
,
)
C N
R
C N
R



3
3
3
2
2
2
(
,
,
)
(
,
,
)
C N
R
C N
R



(1)
1
1
1
(
,
,
)
C N R 
(2)
(2)
2
3



(2)
2
2
2
(
,
,
)
C N
R 
(3)
3
3
3
(
,
,
)
C N
R 
 
图15  无码率预变换极化码构造方式示意图 
对于重复编码比特方式，由于其需逆向对先前收到的码块进行译码，在信
道时变情况下存在译码失败可能，即由于信道容量变化，当前的等效码率
iR 不
足以使之前信道条件下接收到的某一码块
(
,
,
)
j
j
C N R 
正确译码，且无法继续将
前
1
j 个码块的码率降至
iR 。因此拟对重复编码比特方案进行优化设计。可通
过优化设计重复比特的选择和放置方式，适当增加部分重复比特尤其是相隔较
远码块间的重复比特的方式解决，使
(
,
,
)
j
j
C N R 
译码时，其等效码率低于
iR ，
若其依然译码错误，其前一个码块
1
1
(
,
,
)
j
j
C N R 


仍可不受影响将等效码率降至
低于
iR ，从而使逆向译码能够适应时变信道； 
对于编码块打孔方式，需基于研究内容2 给出的任意码长、码率的预变换
NSFC 2023
第 31 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 33 页 ===
极化码码块，并采用与重复编码比特方式相似的逆向译码方法。因此，同样需
要解决逆向译码失效的问题，解决方法与重复编码比特方式相似。 
对于编码块延长方式，对于有规律的预变换矩阵适用，如PAC 码采用的卷
积码预变换矩阵。对于PAC 码有 
/2
/ 2 1:
,
/ 2 1:
1:
/ 2,
/ 2 1:
(
)
(
)
N
N
N
N
N N
N
N
N
N





J
J
J
        (37) 
因此，码长为N 的PAC 码仍可拆分出码长为N/2 的有效PAC 码块，从而使码
块延长方式可行。 
 反馈重传策略：综合HARQ 机制和喷泉码传输机制，基于所提出的无码
率预变换极化码编码方案，设计一种类喷泉传输机制。无反馈地连续发送每一
组无码率预变换极化码码块
1
1
2
2
{ (
,
),
(
,
),...,
(
,
)}
i
i
i
i
i
i
i
i
k
k
C N
R
C N
R
C N
R
，接收端当收到第
一个码块时即开始译码。当接收完毕第i 组无码率码块并译码后，以提升后续传
输码块译码正确的可能性，减少反馈重传次数为原则，确定译码成功或失败时
的反馈内容，并设计和发送第
1
i 组无码率码块。 
需基于研究内容1 的提出的时延约束，在满足误块率需求的基础上，通过
对各组码块的良好设计减少重传次数，降低C-AoI 峰值
peak
( )t

，从而降低违约
概率
peak
max
Pr{ ( )
}
t
D


，以满足业务时延需求。采用类喷泉传输机制，对于
peak
( )t

中的反馈重传为b 次的概率是 
1
BLER-LD
BLER-LD
1
Pr{ }
(1
( (
,
)))
( (
,
))
b
b
c
c
b
b
b
c
c
k
k
k
k
c
b
P
C N
R
P
C N
R





                (38) 
另一方面，根据C-AoI 表征模型，此时的峰值为
peak
( )
1
b
b
k
t
R


。此外，
peak
( )t

还
与各次重传的排队时延相关，根据式(6)，排队时延
( )
q
d
c 与服务速率
( )
cr t 相关，
而
( )
cr t 的可达上界是当前时刻的有效容量
eff
C
，可得到 


BLER-LD
BLER-LD
0
sup( ( ))
1 log
( )
( (
,
))
( )(1
( (
,
)))exp(
'
)d
'
c
c
c
c
c
c
c
c
c
i
k
k
i
k
k
i
r t
p
P
C N
R
p
P
C N
R
S












(39) 
综上对于各参数的分析，结合实际场景参数，根据式(20)的业务时延约束以及业
务可靠性约束，通过求数值解或采用计算机方法，可得到各组无码率码块参数
1
1
2
2
{ (
,
),
(
,
),...,
(
,
)}
i
i
i
i
i
i
i
i
k
k
C N
R
C N
R
C N
R
的选用范围Π 。 
此外，若某一组无码率码块在等效码率降至
i
j
R 就能译码正确时，可以将
i
j
R
反馈至发送端并以
i
j
R 为参考，设计下一组码块数量、可达码率等构造参数。为
了减少下一组码块的译码错误情况，下一组码块的码率初值
1
1
i
R 可设置为 
1
1
1
i
i
i
j
j
R
R
R



                          (40) 
这样设置的原因在于可以使下一组码块尽快适配信道状态，无论物理信道容量
NSFC 2023
第 32 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 34 页 ===
正在变大或变小。此外，还可以减少码率下降的步进，以实现高码率的传输。 
 通信周期内业务平均时延优化：对低轨卫星过顶通信周期内的无码率预
变换极化码编码传输方案在各时刻的C-AoI ( )t

进行计算，即建立一个通信周期
内的( )t

变化情况。图16 给出示意图来说明这一变化情况。其中，给出了5 个
轮次的无码率编码传输时的( )t

变化示意，每轮次均以达到峰值
peak
( )t

结束。
每轮次编码的( )t

初始高度是初传排队时延。 
对于无码率预变换极化码编码传输机制，当各组码块的数量、码长、码率
等参数
1
1
2
2
{ (
,
),
(
,
),...,
(
,
)}
i
i
i
i
i
i
i
i
k
k
C N
R
C N
R
C N
R
Π 确定后，就可得到( )t

的变化情
况。图16 中示意的阴影部分面积为该通信周期
0
nt
t

内( )t

的积分，表示为 
1
1
( )
i
i
n
t
t
i
t dt




                         (41) 
...
t
第1轮次无码率编码
传输C-AoI达峰过程
...
√ 
√ 
√ 
√ 
√ 
t1
t2
t3
t4
t5
...
平均C-AoI
t0
×  ×  ×  
×  
...
...
...
...
传播
时延
反馈重传与
排队时延
第2轮次
第3轮次第4轮次
第5轮次
初传
排队
×  ...
( )t

斜率为
1
1
1
k
R
斜率为
2
2
1
k
R
 
图16  无码率预变换极化码编码传输过程中C-AoI 变化示意图 
此时的优化目标为基于上述反馈重传策略中得到的参数选用范围Π ，寻求一种
参数集合Π ，使通信周期内的平均C-AoI 最小，即达到平均时延最优。目
标函数可表示为 
1
0
1
E[ ]
E[ ]
min
min
E[
]
E[
]
n
i
i
n
n
i
i
t
t
t











Π
Π
                   (42) 
其中，E[ ]指长期传输（多个通信周期）下的数学期望。由于选用范围Π 已根据
业务需求约束框定，因此无需列出约束函数。为了方便优化问题的求解，采用
确定性平稳策略，即每个周期更新选择参数的依据是固定的，这样可认为每次
更新中产生的面积{ }
i
和时间宽度{
}
it

是平稳分布，即{ }
i


、{
} ~
it
t

，可
NSFC 2023
第 33 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 35 页 ===
将目标函数简化为 
min E[ ]/ E[
]t



Π
                        (43) 
其中是( )t

的积分，是非负递增函数的积分，其显然是凸函数，而t
可视作变
量b 的仿射函数。因此，该目标函数是拟凸函数，具有优化可行性，使平均C-AoI
最低。 
研究内容3 的可行性分析：研究内容3 主要针对无码率预变换极化码编码
和传输方案进行设计，并进行时延优化。其中针对无码率预变换极化码的研究，
通过借鉴无码率极化码方案，已有三种可行途径，包括重复编码比特方式、打
孔方式和码块延长方式。其中的码块延长方式在申请人已发表论文《An Efficient 
Rateless Scheme Based on the Extendibility of Systematic Polar Codes, IEEE 
ACCESS, 2017, 5(5): 23223-23232》中提出。针对这三种无码率编码方式存在的
问题，研究方案中给出了解决思路，申请人在已发表论文《How to apply polar 
codes in high throughput space communications. Science China Technological 
Sciences, 2020, 63: 1371-1382.》中对重复编码比特方式给出了一种改进设计方
案，一定程度上避免了部分逆向译码错误问题。可作为本研究的技术参考；针
对类喷泉传输策略的研究工作，申请人基于对喷泉码等相似技术路径开展的预
研工作，初步构建了策略模型。申请人在已发表论文《Rate-compatible transmission 
schemes based on parallel concatenated punctured polar codes. ACM MSWiM’2017》
进行了极化码的连续传输策略设计，如图17 所示。可作为本研究的方案参考。 
C1
C2
Ck  
...
C1
C2
Ck
...
...
1
C
k
C 
...
C
...
1
C
2
C
k
C 
...
C
...
1
C
2
C
C
...
1
C
2
C
发送端
接收端
...
...
...
...
{C1}
{C1,C2}
...
{C1,C2,...,Ck}
译码结果状态
（红色为错误，
绿色为正确）
...
...
...
2
C
1
{
}
C
1
2
{
,
}
C C


1
2
{
,
,...,
}
C C
C



1
{
,...,
}
k
C
C




 
图17  无码率极化码编码反馈重传策略示意图 
此外，申请人所在团队积累了大量面向卫星通信、深空通信的喷泉码相关研究
成果，可为本研究的反馈重传设计提供理论技术借鉴；针对时延的优化工作，
方案中已初步论述了时延优化目标函数的拟凸性。而约束函数特性虽无需列出，
但对优化问题的分析有影响。该问题中生效的约束函数是误块率需求。根据对
信道编码误块率的认知，误块率服从Q 函数变化，在误块率为
min
0.5
d
A
以下时是
凸函数，因此在可行集范围内约束函数是凸的。综上，该优化模型可认为是拟
凸优化问题，针对这类问题一般的做法通过将拟凸目标函数的下水平集表达为
凸不等式，从而转化为凸优化求解，这一优化过程已有大量成熟的解决方法和
工具可作为借鉴。 
NSFC 2023
第 34 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 36 页 ===
4. 本项目的特色与创新之处； 
本项目面向我国卫星互联网新型基础设施建设和空天科技前沿领域发展趋
势，综合考虑低轨卫星通信链路特性和业务需求，基于预变换极化码技术构建
灵活、高效的编码传输机制，充分提升对低轨卫星通信业务需求的支撑能力，
实现全过顶周期对通信链路的精准匹配，持续保障业务数据高速可靠传输。通
过本项目的研究，可以为低轨卫星通信提供一种全新的编码传输解决方案，探
索预变换极化码技术在卫星通信中的可用性，并增强适用性。助推低轨卫星通
信网络与地面通信系统的编码技术研发协同，为我国构建自主可控的低轨卫星
通信技术链和抢占标准化先发优势提供符合实际特点的理论基础和技术方案。 
本项目的编码技术研究从“有效容量表征”、“编码增益提升”、“跨层联动
设计”多个维度开展，从面向业务的跨层设计视角研究编码技术的设计边界和
优化方式。不仅对低轨卫星通信编码技术研究具有借鉴意义，也可为其他领域
的编码设计提供一种新的研究范式和思路。 
项目的主要创新点包括： 
（1）面向低轨卫星通信，提出一种新的信道有效容量模型，明晰了低轨卫
星通信编码传输机制的设计边界。其中，提出了C-AoI 这一考虑了编码码率的
AoI 新形式。 
（2）基于预变换极化码这一新编码技术，开展低复杂度约束下的可靠性提
升研究，提出预变换极化码的生成矩阵与译码方法联合优化设计的新思路。此
外，提出预变换极化码的速率匹配新方法。 
（3）对预变换极化码与重传控制机制的联合设计，为低轨卫星通信提供了
一种新的跨层编码传输设计范式，实现对可靠编码与高效传输的兼顾。 
5. 年度研究计划及预期研究结果（包括拟组织的重要学术交流
活动、国际合作与交流计划等）。 
5.1 年度研究计划 
本项目预计研究年限从2024 年01 月至2026 年12 月，拟按照研究内容和
技术路线逐步开展。具体年度研究计划及预期研究进展下： 
（1）第一年度（2024.01-2024.12）：完成低轨星地信道有效容量表征方法研
究。构建适用于低轨卫星通信场景的C-AoI 时延模型，分析预变换极化码的理
论误块率，对有效容量进行分析。对卫星周期内的物理信道和业务发生进行仿
真实验。撰写学术论文1-2 篇，参加学术会议1 次。 
（2）第二年度（2025.01-2025.12）：完成高增益预变换极化编码技术研究。
结合列表译码提出一种预变换矩阵设计方法，实现增益的提升。提出预变换极
化码的打孔和截短方案，并对降维的预变换矩阵进行优化。对各典型码长、码
率的预变换极化码码块进行仿真实验，验证可靠性。撰写学术论文2-3 篇，申请
发明专利1-2 项，参加学术会议1-2 次。 
NSFC 2023
第 35 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 37 页 ===
（3）第三年度（2026.01-2026.12）：完成无码率预变换极化码编码传输机制
研究。提出无码率预变换极化码编码方案，对重传机制进行联合设计，完成对
低轨卫星通信全周期业务时延的优化，并进行模拟仿真实验验证。撰写学术论
文2 篇，申请发明专利1-2 项，参加学术会议1 次，撰写结题报告。 
5.2 预期研究结果 
（1）建立一种面向低轨卫星通信场景的信道有效容量表征模型，探明业务
需求对编码传输设计的约束机理。 
（2）提出一种高增益预变换极化码编译码新方案。提出预变换极化码的高
可靠打孔和截短方法。 
（3）提出一种无码率预变换极化码编码传输机制，实现动态星地链路下的
业务信息的尽早接收和可靠译码恢复。 
（4）撰写学术论文5-7 篇，申请发明专利2-4 项，参加学术会议3 次以上。 
（5）培养或协助培养研究生1-2 名。 
（二）研究基础与工作条件 
1. 研究基础（与本项目相关的研究工作积累和已取得的研究工
作成绩）； 
申请人长期从事卫星通信、信道编码技术研究，尤其针对极化码、预变换
极化码及相关技术开展了大量研究，发表相关的学术论文十余篇，申请及授权
国家发明专利5项。代表性工作成绩归纳为以下三个方面： 
（1）针对极化码的译码方法开展的优化工作，代表性成果包括： 
[1] Bowen Feng, Jian Jiao, Shaohua Wu, Ye Wang and Qinyu Zhang. Iterative and adjustable 
soft list decoding for polar codes. IEEE Transactions on Signal Processing, 2020, 68: 
5559-5572. 
[2] 冯博文, 焦健, 王莎, 吴绍华, 张钦宇. 基于树图剪枝的极化码译码简化算法. 系统工
程与电子技术, 2017, 39(2): 410-417. 
[3] Bowen Feng, Jian Jiao, Kexin Liang, Shaohua Wu, Ye Wang, Qinyu Zhang. Adjustable soft 
list decoding for polar codes. IEEE VTC 2019-Fall. 
[4] 焦健, 冯博文, 顾术实, 吴绍华, 张钦宇. 一种极化码的简化译码方法. 国家发明专利, 
专利号: ZL201610045755.2. 授权日期: 2019.07.23. 
[5] 焦健, 冯博文, 田园, 吴绍华, 张钦宇. 可调的串行抵消列表极化码译码方法和装置. 
国家发明专利, 专利号: ZL201911011582.2. 授权日期: 2022.05.06 
[6] 焦健, 冯博文, 田园, 吴绍华, 张钦宇. 极化码的迭代可调软串行抵消列表译码方法和
装置. 国家发明专利申请, 申请号: CN201911380480.8. 
其中，提出了一种极化码SC译码、BP译码的剪枝简化方案，通过对部分译
码过程的等效替换，实现了对译码复杂度的降低，对于SC译码和BP译码可降低
40%以上的译码计算复杂度；提出了一种极化码可调列表译码，以及一种可迭代
的可调列表译码，保障了高的译码可靠性，且极大程度地降低译码复杂度。该
NSFC 2023
第 36 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 38 页 ===
方法基于对各编码位置的误码概率分析，对正确译码列表概率进行描述，通过
合理设计阈值，对列表长度进行实时调整，达到译码可靠性与复杂度之间的较
优权衡，在取得逼近固定长列表译码BLER性能的同时，大幅度地降低了译码列
表长度，减少了大量复杂度，如图18所示。针对极化码译码方法的研究工作基
础，可为本项目中有效容量模型的构建，预变换极化码的编译码方法研究等内
容提供参考和借鉴。 
    
 
（a）可调列表译码BLER性能         （b）译码列表长度2累积概率分布 
图18 极化码可调列表译码性能示意图 
（2）针对极化码级联编码、预变换极化码的研究，代表性成果包括： 
[1] Bowen Feng, Jian Jiao, Shaohua Wu, and Qinyu Zhang. How to apply polar codes in high 
throughput space communications. Science China Technological Sciences, 2020, 63: 
1371-1382. 
[2] Bowen Feng, Yi Yang, Jian Jiao, Qinyu Zhang. On Tail-Biting Polarization-Adjusted 
Convolutional (TB-PAC) Codes and Small-Sizes List Decoding. IEEE Communications 
Letters, 2023, 27(2): 433-437 
[3] Bowen Feng, Jian Jiao, Liu Zhou, Shaohua Wu, Bin Cao, Qinyu Zhang. A novel high-rate 
polar-staircase coding scheme. IEEE VTC2018-Fall. 
[4] Liu Zhou, Bowen Feng, Jian Jiao, Kexin Liang, Shaohua Wu, Qinyu Zhang. Performance 
Analysis of Soft Decoding Algorithms for Polar-Staircase Coding Scheme. 2018 10th 
International Conference on Wireless Communications and Signal Processing (WCSP). 
其中，预变换极化码的研究基础集中在PAC码。申请人主持中兴通讯产学研
合作项目，与中兴通讯研究团队针对PAC码开展研发合作。已完成的短码PAC
码预变换改造、速率匹配方法适用性改造等研究成果可作为本项目研究基础，
为本项目中关于预变换极化码的构造优化和编译码方法设计提供技术支撑。 
（3）针对极化码的跨层及联合编码设计，代表性成果包括： 
[1] Bowen Feng, Qinyu Zhang, Jian Jiao. An efficient rateless scheme based on the extendibility 
of systematic polar codes. IEEE Access, 2017, 5: 23223-23232. 
[2] Bowen Feng, Jian Jiao, Sha Wang, Shaohua Wu, Shushi Gu, Qinyu Zhang. Rate-compatible 
transmission schemes based on parallel concatenated punctured polar codes. ACM 
MSWiM’2017, Miami, FL, USA, 2017. 
[3] Bowen Feng, Shushi Gu, Jian Jiao, Shaohua Wu, Qinyu Zhang. Novel polar coded 
NSFC 2023
第 37 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 39 页 ===
space-time transmit diversity scheme over Rician fading MIMO channels. EURASIP Journal 
on Wireless Communications and Networking. 2018, 24. 
[4] Jian Jiao, Kexin Liang, Bowen Feng, Shaohua Wu, Ye Wang, Qinyu Zhang. Joint channel 
estimation and decoding for polar coded SCMA system over fading channels. IEEE 
Transactions on Cognitive Communications and Networking, 2021, 7(1): 210-221. 
[5] Bowen Feng, Jian Jiao, Sha Wang, Shaohua Wu, Qinyu Zhang. Construction of polar codes 
concatenated to space-time block coding in MIMO system. IEEE VTC 2016-Fall. 
[6] 焦健, 冯博文, 王莎, 吴绍华, 张钦宇. 一种极化码级联空时码系统及其级联极化编码
方法. 国家发明专利, 专利号: ZL201610216554.4. 授权日期: 2019.07.23. 
[7] 焦健, 王莎, 冯博文, 周刘, 吴绍华, 张钦宇. 基于打孔的码率兼容极化码编码方法及
系统. 国家发明专利, 专利号: ZL201710458844.4. 授权日期: 2020.07.14 
其中，主要提出了基于码块延长的无码率极化码联合编码方案。通过对极
化码生成矩阵的分析，提出极化码码块的延长理论，基于该理论设计与HARQ联
合的无码率编码方案，在接收端不断累积并译码，译码器每次处理的都是完整
的码块且无需进行逆向译码，避免了打孔造成的可靠性损失。此外，还对极化
码的联合编码实用方案进行了研究，分别对极化码与空时编码、极化码与非正
交多址技术等进行了联合编码设计，提出了多种实用方案。针对极化码跨层及
联合编码研究的工作基础，可为本项目中面向低轨卫星通信的无码率预变换极
化码及反馈重传策略设计等内容提供技术借鉴。 
综上，以上研究工作积累和已取得的研究工作成绩可为本项目研究提供实
用的理论和技术支持。 
2. 工作条件（包括已具备的实验条件，尚缺少的实验条件和拟
解决的途径，包括利用国家实验室、国家重点实验室和部门重点实验
室等研究基地的计划与落实情况）； 
本项目依托单位是哈尔滨工业大学。申请人所在深圳校区通信工程研究中
心长期从事空天通信、深空通信、空天地一体化网络领域研究，承担多项国家、
省部重大科研任务，产出了大批高质量的文章、专利等科研成果，多项科研成
果获得应用，建有广东省空天通信与网络技术重点实验室、深圳市空间信息网
络自主协同技术重点实验室等科研平台。 
本项目将依托广东省空天通信与网络技术重点实验室科研平台开展，实验
室具备完善的研究条件和实验环境，建有自研低轨星座半实物仿真系统、深空
通信协作传输仿真系统等实验系统，可为本项目的技术验证提供环境支撑。 
本项目研究的主要科研仪器和实验条件支撑情况如下： 
（1）PXI 集成卫星通信仿真平台。包括一套集成的sub6G 矢量信号收发模
块VST，搭配定制上下变频器，可支持Ka/K/Ku 频段的射频信号模拟。主要算
法功能通过FPGA 板卡实现，申请人采用自研极化码编译码方案完成了前向链
路的调制编码的功能实现，可对本研究的方案提供链路级仿真环境和技术方案
借鉴。 
NSFC 2023
第 38 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 40 页 ===
 
图19 已具备的部分项目相关仪器和实验环境 
（2）矢量信号源、频谱仪等测试设备。实验室的矢量信号源选装了DVB-S2、
OneWeb 标准格式信号，频谱仪具备大分析带宽，并选装了OneWeb 链路测量功
能。可为本项目研究方案的链路级仿真性能提供标准测试参考。 
（3）服务器集群及相关软件。实验室提供本项目专用CPU 计算资源为
6230*4，包含80 核、160 线程。此外，还可按需共享实验室其他CPU 资源和
GPU 资源。可为本项目的软件仿真提供充足条件。 
（4）卫星地面站。实验室建有卫星地面站，位于实验室楼顶，采用Ku 频
段。目前可接收同步轨道高通量卫星数据，具备专用带宽，可进行收发和信道
测量等实验。 
 
图20 实验室建有的卫星地面站 
实验室还具有软件无线电平台、信道模拟器、程控衰减器、FPGA 开发板等
设备，可以用于本项目的研究工作。综上，重点实验室具备的科研仪器和实验
条件可为本项目的顺利实施提供充足保障。 
此外，申请人所在团队与鹏城实验室宽带通信部建立了良好的人员设备资源
共享机制，本项目可共享鹏城实验室宽带通信部的部分实验资源，对项目的实
施过程提供补充支撑。 
3. 正在承担的与本项目相关的科研项目情况（申请人正在承担
的与本项目相关的科研项目情况，包括国家自然科学基金的项目和国
NSFC 2023
第 39 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 41 页 ===
家其他科技计划项目，要注明项目的资助机构、项目类别、批准号、
项目名称、获资助金额、起止年月、与本项目的关系及负责的内容等）； 
申请人参与的一项国家自然科学基金面上项目（62071141），空间物联网的
信息时效与大规模接入技术研究，2021.01 至2024.12。该项目重点研究面向空
间物联网的短码AFC 编码和高时效的大规模接入技术。申请人在项目中承担信
息时效的理论分析工作内容，该项目的部分研究成果可以为本项目中编码信息
时效性表征研究提供一定的理论参考。 
4. 完成国家自然科学基金项目情况（对申请人负责的前一个已
资助期满的科学基金项目（项目名称及批准号）完成情况、后续研究
进展及与本申请项目的关系加以详细说明。另附该项目的研究工作总
结摘要（限500 字）和相关成果详细目录）。 
无。 
（三）其他需要说明的情况 
1. 申请人同年申请不同类型的国家自然科学基金项目情况（列
明同年申请的其他项目的项目类型、项目名称信息，并说明与本项目
之间的区别与联系）。 
无。 
2. 具有高级专业技术职务（职称）的申请人是否存在同年申请
或者参与申请国家自然科学基金项目的单位不一致的情况；如存在上
述情况，列明所涉及人员的姓名，申请或参与申请的其他项目的项目
类型、项目名称、单位名称、上述人员在该项目中是申请人还是参与
者，并说明单位不一致原因。 
无。 
3. 具有高级专业技术职务（职称）的申请人是否存在与正在承
担的国家自然科学基金项目的单位不一致的情况；如存在上述情况，
列明所涉及人员的姓名，正在承担项目的批准号、项目类型、项目名
称、单位名称、起止年月，并说明单位不一致原因。 
无。 
4. 其他。 
无。 
NSFC 2023
第 40 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 42 页 ===
2023版
冯博文 简历 
哈尔滨工业大学,  哈尔滨工业大学（深圳）,  无
教育经历：
(1) 2016-09 至 2021-07, 哈尔滨工业大学, 信息与通信工程, 博士
(2) 2014-09 至 2016-07, 哈尔滨工业大学, 信息与通信工程, 硕士
(3) 2010-09 至 2014-06, 哈尔滨工业大学, 电子信息工程, 学士
博士后工作经历：
(1) 2021-07 至 今, 在站, 哈尔滨工业大学, 哈尔滨工业大学（深圳）
科研与学术工作经历（博士后工作经历除外）：
无
曾使用其他证件信息：
无
近五年主持或参加的国家自然科学基金项目/课题：
(1) 国家自然科学基金委员会, 面上项目, 62271165, 面向在轨处理的巨星座网络协同编码容错机制研究,
2023-01-01 至 2026-12-31, 54万元, 在研, 参与
(2) 国家自然科学基金委员会, 面上项目, 62071141, 空间物联网的信息时效与大规模接入技术研究,
2021-01-01 至 2024-12-31, 64万元, 在研, 参与
(3) 国家自然科学基金委员会, 面上项目, 61972113, 多维边缘网络资源协同优化与运营, 2020-01-01 至
2023-12-31, 60万元, 在研, 参与
(4) 国家自然科学基金委员会, 面上项目, 61871147, 信息时效约束下的深空测控通信传输技术研究,
2019-01-01 至 2022-12-31, 63万元, 资助期满, 参与
(5) 国家自然科学基金委员会, 青年科学基金项目, 61801145, 面向无线传感器网络的联合交替优化算法
与能量收集策略研究, 2019-01-01 至 2021-12-31, 24万元, 结题, 参与
(6) 国家自然科学基金委员会, 面上项目, 61771162, 多样化电磁频谱资源的复用机理及生态研究, 2018-
01-01 至 2018-12-31, 16万元, 结题, 参与
(7) 国家自然科学基金委员会, 面上项目, 61771158, 高通量天基信息网络容量与多址技术研究, 2018-
01-01 至 2018-12-31, 16万元, 结题, 参与
(8) 国家自然科学基金委员会, 青年科学基金项目, 61701136, 面向深空通信网络中数据流业务的无码率
传输协议研究, 2018-01-01 至 2020-12-31, 24.5万元, 结题, 参与
近五年主持或参加的其他科研项目/课题（国家自然科学基金项目除外）：
(1) 广东省基础与应用基础研究基金委员会, 区域联合基金-青年基金项目, 2021A1515110071, 面向低轨
卫星组网通信的高时效极化编码技术研究, 2021-10 至 2024-09, 10万元, 在研, 主持
代表性研究成果和学术奖励情况（填写代表性论文时应根据其发表时的真实情况如实规范列
出所有作者署名，不再标注第一作者或通讯作者）：
一、代表性论著（请在“申请书详情”界面，点开“人员信息”-“代表性成果”卡片查看对
NSFC 2023
第 41 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 43 页 ===
应的全文）：
(1) Bowen Feng; Jian Jiao; Shaohua Wu; Ye Wang; Qinyu Zhang ; Iterative and Adjustable Soft
List Decoding for Polar Codes, IEEE Transactions on Signal Processing, 2020, 68: 5559-
5572      (期刊论文)
(2) Bowen Feng; Jian Jiao; Shaohua Wu; Qinyu Zhang ; How to apply polar codes in high
throughput space communications, Science China-Technological Sciences, 2020, 63(8): 1371-
1382      (期刊论文)
(3) Bowen Feng; Yi Yang; Jian Jiao; Qinyu Zhang ; On Tail-Biting Polarization-Adjusted
Convolutional (TB-PAC) Codes and Small-Sizes List Decoding, IEEE Communications Letters, 2023,
27(2): 433-437      (期刊论文)
(4) Bowen Feng; Qinyu Zhang; Jian Jiao ; An Efficient Rateless Scheme Based on the
Extendibility of Systematic Polar Codes, IEEE ACCESS, 2017, 5(5): 23223-23232      (期刊论文)
(5) Bowen Feng; Shushi Gu; Jian Jiao; Shaohua Wu; Qinyu Zhang ; Novel polar-coded space-time
transmit diversity scheme over Rician fading MIMO channels, EURASIP Journal on Wireless
Communications and Networking, 2018, 2018(1): 24      (期刊论文)
二、论著之外的代表性研究成果和学术奖励：
(1) 焦健; 冯博文; 顾术实; 吴绍华; 张钦宇 ; 一种极化码的简化译码方法, 2019-7-23, 中国,
CN201610045755.2      (专利)
(2) 焦健; 冯博文; 王莎; 吴绍华; 张钦宇 ; 一种极化码级联空时码系统及其级联极化码编码方法,
2019-7-23, 中国, CN201610216554.4      (专利)
(3) 焦健; 冯博文; 田园; 吴绍华; 张钦宇 ; 可调的串行抵消列表极化码译码方法和装置, 2022-5-6, 中
国, CN201911011582.2      (专利)
(4) 焦健; 王莎; 冯博文; 周刘; 吴绍华; 张钦宇 ; 基于打孔的码率兼容极化码编码方法及系统, 2020-
7-14, 中国, CN201710458844.4      (专利)
(5) 焦健; 冯博文; 田园; 吴绍华; 张钦宇 ; 极化码的迭代可调软串行抵消列表译码方法和装置, 2019-
12-27, 中国, CN201911380480.8      (专利)
NSFC 2023
第 42 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 44 页 ===
附件信息
序号
附件名称
备注
附件类型
1
代表性论文1
TSP2020
代表性论著
2
代表性论文2
SCTS2020
代表性论著
3
代表性论文3
LCOMM2023
代表性论著
4
代表性论文4
ACCESS2017
代表性论著
5
代表性论文5
EURASIP-JWCN2018
代表性论著
6
专利1
发明专利
其他
7
专利2
发明专利
其他
8
专利3
发明专利
其他
9
专利4
发明专利
其他
10
专利5
发明专利申请
其他
NSFC 2023
第 43 页
国家自然科学基金申请书
2023版
版本：23110305152743691


=== 第 45 页 ===
项目名称：面向低轨卫星通信的预变换极化编码传输机制研究
资助类型：青年科学基金项目
申请代码：F0106.空天通信
国家自然科学基金项目申请人和参与者承诺书
为了维护国家自然科学基金项目评审公平、公正，共同营造风清气正的科研生态，本人在此郑重
严格遵守《中华人民共和国科学技术进步法》《国家自然科学基金条例》《关于进一步加强科
承诺：
研诚信建设的若干意见》《关于进一步弘扬科学家精神加强作风和学风建设的意见》《关于加强科技
伦理治理的意见》以及科技部、自然科学基金委关于科研诚信建设有关规定和要求；申请材料信息真
实准确，不含任何涉密信息或敏感信息，不含任何违反法律法规或违反科研伦理规范的内容；在国家
自然科学基金项目申请、评审和执行全过程中，恪守职业规范和科学道德，遵守评审规则和工作纪
律，杜绝以下行为：
（一）抄袭、剽窃他人申请书、论文等科研成果或者伪造、篡改研究数据、研究结论；
（二）购买、代写申请书；购买、代写、代投论文，虚构同行评议专家及评议意见；购买实验数
据；
（三）违反成果发表规范、署名规范、引用规范，擅自标注或虚假标注获得科技计划等资助；
（四）在项目申请书中以高指标通过评审，在项目计划书中故意篡改降低相应指标；
（五）以任何形式探听或散布尚未公布的评审专家名单及其他评审过程中的保密信息；
（六）本人或委托他人通过各种方式和途径联系有关专家进行请托、游说、“打招呼”，违规到
评审会议驻地窥探、游说、询问等干扰评审或可能影响评审公正性的行为；
（七）向工作人员、评审专家等提供任何形式的礼品、礼金、有价证券、支付凭证、商业预付
卡、电子红包，或提供宴请、旅游、娱乐健身等任何可能影响评审公正性的活动；
（八）违反财经纪律和相关管理规定的行为；
（九）其他弄虚作假行为。
如违背上述承诺，本人愿接受国家自然科学基金委员会和相关部门做出的各项处理决定，包括但
不限于撤销科学基金资助项目，追回项目资助经费，向社会通报违规情况，取消一定期限国家自然科
学基金项目申请资格，记入科研诚信严重失信行为数据库以及接受相应的党纪政务处分等。
申请人签字：
国家自然科学基金申请书
2023版


=== 第 46 页 ===
项目名称：面向低轨卫星通信的预变换极化编码传输机制研究
资助类型：青年科学基金项目
申请代码：F0106.空天通信
国家自然科学基金项目申请单位承诺书
为了维护国家自然科学基金项目评审公平、公正，共同营造风清气正的科研生态，本单位郑重承
申请材料中不存在违背《中华人民共和国科学技术进步法》《国家自然科学基金条例》《关于进
诺：
一步加强科研诚信建设的若干意见》《关于进一步弘扬科学家精神加强作风和学风建设的意见》《关
于加强科技伦理治理的意见》以及科技部、自然科学基金委关于科研诚信建设有关规定和要求的情
况；申请材料符合《中华人民共和国保守国家秘密法》和《科学技术保密规定》等有关法律法规和规
章制度要求，不含任何涉密信息或敏感信息；申请材料不含任何违反法律法规或违反科研伦理规范的
内容；申请人符合相应项目的申请资格；依托单位、合作研究单位、申请人及主要参与者不在限制申
报、承担或参与财政性资金支持的科技活动的期限内；在项目申请和评审活动全过程中，遵守有关评
审规则和工作纪律，杜绝以下行为：
（一）以任何形式探听或公布未公开的项目评审信息、评审专家信息及其他评审过程中的保密信
息，干扰评审专家的评审工作；
（二）组织或协助申请人/参与者向工作人员、评审专家等给予任何形式的礼品、礼金、有价证
券、支付凭证、商业预付卡、电子红包等；宴请工作人员、评审专家，或组织任何可能影响科学基金
评审公正性的活动；
（三）支持、放任或对申请人/参与者抄袭、剽窃、重复申报、提供虚假信息（含身份和学术信
息）等不当手段申报国家自然科学基金项目疏于管理；
（四）支持或协助申请人/参与者采取“打招呼”“围会”等方式影响科学基金项目评审；
（五）其他违反财经纪律和相关管理规定的行为。
如违背上述承诺，本单位愿接受自然科学基金委和相关部门做出的各项处理决定，包括但不限于
停拨或核减经费、追回项目已拨经费、取消本单位一定期限国家自然科学基金项目申请资格、记入科
研诚信严重失信行为数据库以及主要责任人接受相应党纪政务处分等。
依托单位公章:
日期：
年
月
日
国家自然科学基金申请书
2023版
