

=== 第 1 页 ===
申请代码
F0105
接收部门
收件日期
接收编号
6240010986
    
国家自然科学基金
申 请 书
（2 0 2 4 版）
资助类别：
青年科学基金项目
亚类说明：
附注说明：
项目名称：
基于物理层安全的通感内生安全架构与网络性能动态适配机制研究
申 请 人：
苏南池
BRID：
09007.00.62796
办公电话：
0755-26404115
依托单位：
哈尔滨工业大学
通讯地址：
广东省深圳市南山区深圳大学城哈工大校区信息楼L1214室
邮政编码：
518055
单位电话：
0451-86414151
电子邮箱：
<EMAIL>
国家自然科学基金委员会
NSFC 2024


=== 第 2 页 ===
基本信息
申
请
人
信
息
姓        名
苏南池
性别
女
出生
年月
1993年08月
民族
汉族
学        位
博士
职称
无
是否在站博士后
是
电子邮箱
<EMAIL>
办公电话
0755-26404115
国别或地区
中国
申请人类别
依托单位全职
工   作   单   位
哈尔滨工业大学/哈尔滨工业大学（深圳）
主 要 研 究 领 域
无线通信安全，通信感知一体化，态势感知
依
托
单
位
信
息
名        称哈尔滨工业大学
联   系   人王雪
电子邮箱
<EMAIL>
电        话0451-86414151
网站地址
http://kjc.hit.edu.cn/
合
作
研
究
单
位
信
息
单 位 名 称
项
目
基
本
信
息
项目名称
基于物理层安全的通感内生安全架构与网络性能动态适配机制研究
英文名称
Research on Endogenous Security Architecture and Network Performance
Dynamic Adaptation Mechanism Based on Physical Layer Security for
Integrated Sensing and Communication
资助类别
青年科学基金项目
亚类说明
附注说明
申请代码
F0105.移动通信
F0102.信息系统与系统安全
研究期限
2025年01月01日 -- 2027年12月31日
研究方向：高能效通信
申请直接费用30.0000万元
研究属性
目标导向类基础研究
中文关键词
高能效资源分配；通信感知融合网络；一体化波形设计；内生安全；网络弹性
英文关键词
High-Efficiency Resource Allocation; Integrated Sensing and
Communications; Dual-functional Waveform; Endogenous Security;
Network Resilience
NSFC 2024
第 1 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 3 页 ===
中
文
摘
要
通感技术是实现6G业务沉浸化、智慧化、全域化发展的重要战略之一，随着高速数据传输
、广泛网络覆盖和先进智能服务的需求急剧增加，6G网络会更加复杂开放且具备更多数据形式
。但海量数据处理及广泛物联网设备接入造成的信息监听、隐私泄露以及网络防御能力有限等
为信息大数据量、高频率交互的通感网络带来安全挑战。针对上述问题，本项目以构建通感融
合系统内生安全架构为目标，基于物理层安全技术和通感一体化波形设计，探索通信感知网络
安全和隐私的表征方法，明晰通信、感知和安全的相互作用机理，解决通信-感知-安全集成网
络系统的多维性能动态适配问题。从内生先验信息、鲁棒性安全应对方案、网络弹性分析三个
维度综合提升通感系统的通信数据安全和感知数据隐私保障，并优化系统服务性能和安全资源
分配策略。研究成果将保障6G网络系统信息高可靠传输，有助于实现更为安全、智能和高效的
通信网络环境，促进社会信息化进程向更高层次演进。
英
文
摘
要
The integration of sensing and communication technology stands as a pivotal
strategy for realizing the immersive, intelligent, and all-encompassing evolution
of 6G services. Amidst the rapidly growing demands for high-speed data
transmission, broad network coverage, and sophisticated intelligent services, the
6G network is set to become increasingly complex and open, accommodating an
ever-expanding array of data types. Nevertheless, the challenges of processing
voluminous data and facilitating widespread IoT device connectivity introduce
significant security concerns, including susceptibility to eavesdropping,
potential privacy breaches, and constrained network defense capabilities,
particularly in an integrated sensing and communication network distinguished by
vast data quantities and frequent interactions. To address these challenges, this
project is dedicated to developing a comprehensive endogenous security
architecture for the integrated sensing system, grounded in physical layer
security technology and cohesive sensing waveform design. This initiative seeks to
identify effective methods for delineating the security and privacy landscape of
communication-sensing networks, elucidate the intricate interplay among
communication, sensing, and security, and tackle the complex issues of
multi-dimensional performance dynamic adaptation within the integrated network
system encompassing communication, sensing, and security. Utilizing intrinsic
prior knowledge, deploying robust security measures, and conducting network
resilience analysis, the project aims to significantly bolster the security of
communication data and the privacy protection of sensing data, all while enhancing
system service efficiency and refining strategies for the allocation of resources.
The findings of this research will guarantee the transmission of information with
high reliability within the 6G network system, contributing to the establishment
of a communication network environment that is not only safer and more intelligent
but also significantly more efficient. This, in turn, will propel the advancement
of the societal informatization process to unprecedented.
NSFC 2024
第 2 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 4 页 ===
报告正文 
参照以下提纲撰写，要求内容翔实、清晰，层次分明，标题突出。
请勿删除或改动下述提纲标题及括号中的文字。 
（一）立项依据与研究内容（建议8000 字以内）： 
1. 项目的立项依据（研究意义、国内外研究现状及发展动态分
析，需结合科学研究发展趋势来论述科学意义；或结合国民经济和社
会发展中迫切需要解决的关键科技问题来论述其应用前景。附主要参
考文献目录）； 
1.1 研究背景及科学意义 
随着中国在全球通信技术领域的迅速崛起，我国的研究与开发焦点已经转向
了下一代移动通信网络——6G。2019 年11 月，中国正式启动6G 技术研发工作，
成立了国家6G 技术研发（IMT-2030 6G）推进工作组和总体专家组[1]，标志着
对移动通信产业发展和科技创新的深度投入，以及对移动通信与信息安全领域
关键技术问题的探索和解决的决心。6G 网络预期将是一个开放而复杂的“巨系
统”，其应用场景和指标体系预计将实现数量级的提升，标志着从基站和网络设
备中心的传统通信模式，向通信、计算、感知与能源等多方面的深度整合转变，
以更全面地适应未来智能化应用的需求[2]。 
通信感知深度融合被视为6G 重要的战略性技术路径之一。通信感知一体化
的概念正日益受到学术界、工业界和标准化机构的关注，并被 ITU-R 正式纳入 
6G 的六大使用场景[3]。通感技术中，通信系统不仅仅是信息传递的媒介，它还
能充当传感器的角色，通过探索无线信号的传输、反射和散射特性来“感知”物理
世界，从而提供全新的用户体验。同时，利用无源感知技术实现的高精度定位、
成像和环境重构能够显著提升通信效率，这类技术可以归纳为“感知辅助通
信”[4]。这种集成了通信与感知的先进系统将促进各种创新应用的发展，包括精
确的定位技术、高清成像、模式识别，以及实时的3D 映射等[1]，这些应用将加
速自动驾驶、智能城市建设、工业自动化、数字医疗和沉浸式XR 体验等领域的
发展。这一切都强调了6G 网络作为一个开放复杂的巨系统的本质，以及感知通
信融合在实现未来通信技术革新中的核心地位。 
通信和感知的深度融合在推动智能应用发展的同时，也为信息安全和隐私带
来了新的挑战。通感融合系统中潜在的安全问题如图1 所示。首先，从通信的
NSFC 2024
第 3 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 5 页 ===
角度，骤增的应用类型和数据量使得通信系统端到端加密更加复杂，需要更高
效的加密算法来满足服务性能和安全性的双重需求。其次，感知数据往往包含
大量敏感信息，如用户位置、生物特征信息和图像信息等，这些信息的收集和
传输增加了数据泄露的风险。因此，随着网络功能的多维扩展，保护用户数据
的安全和隐私需要更加综合和动态的策略。针对面向6G 的通感网络所具备的动
态性和异构性，安全策略和隐私保护机制需要具备高度的灵活性和适应性，使
开放复杂的系统具有半确定性或者确定性。 
 
 
 
 
 
 
 
 
 
 
 
 
 
图1 存在安全和隐私威胁的通信感知融合系统场景 
当前移动通信系统把安全作为一种独立的技术，依靠“补丁式”“外挂式”，所
采用的安全手段主要围绕加密、认证及访问控制等传统机制，从外部抵御不同
种类的恶意攻击这些机制在1G 到5G 网络中的有效性，主要得益于网络架构的
相对固定和应用场景的有限复杂度。面向6G 的通信感知网络安全需求远远超出
了传统安全方案的应对范围，尤其是在物理层技术方面。传统安全措施，如加
密协议和应用层的HTTPS、传输层的SSH 等，主要关注数据在传输过程中的
加密和身份验证，而对于物理层的安全威胁和挑战则考虑不足。面对6G 通信感
知网络的新特性和安全需求，则需要重新审视物理层安全技术的角色和发展。
目前面向6G 的通信感知融合系统安全设计瓶颈具体表现为： 
（1）物理层安全高度依赖外部反馈获取窃听信道先验信息成为通感内生安
NSFC 2024
第 4 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 6 页 ===
全发展瓶颈：随着窃听技术的不断进步，攻击者可以更加精准地捕获和分析从
物理层泄露的信息，这使得传统依赖于固定先验信息的安全策略变得不再有效。
同时，高动态的移动环境和复杂的城市布局，这导致窃听者的行为和信道条件
难以预测，例如在动态网络环境下的用户设备频繁变换连接基站，这时由于传
统安全方案针对特定类型的威胁设计，无法全面应对新兴的多样化攻击。 
（2）感知、通信和安全多功能集成系统影响了安全应对方案的鲁棒性：6G
通感网络将在更加动态和不确定的环境中运行，一体化波束设计必须不断适应
环境变化，保持波束设计的鲁棒性。同时，在感知、通信和安全多功能集成的
系统中，每个功能的需求可能会相互冲突。一体化波束设计在试图满足这一多
功能集成系统的所有需求时，难以在保证安全的同时，还能维持通信和感知最
优性能，这种性能之间的折衷对波束设计的鲁棒性构成了极大挑战。 
（3）网络的复杂化和数据形式的多样化降低了系统资源分配的高效性：多
功能集成系统要求在通信、感知和安全之间动态分配有限的资源，如频谱、功
率和计算能力。传统安全方案往往需要固定的资源分配，难以适应动态变化的
需求。同时，对于需要实时处理的大数据应用，如智慧城市中的视频监控系统
需要实时分析大量视频流，同时保证数据传输的安全性，这对传统安全方案更
是一个巨大的挑战。 
 
图2 通信、感知、安全从分立到集成 
综合上述难点分析，面向6G 的通信感知网络的安全架构设计、安全方案鲁
棒性、资源分配高效性，均受到网络环境高动态、数据形式复杂、系统高度依
赖先验信息等外在因素与内源性缺陷联合制约，使得通信感知融合系统的内生
安全发展遭遇瓶颈。其本质原因在于，当前研究未能将通信感知融合系统与物
理层安全设计理论良好衔接，进而无法形成集通信、感知与内生安全为一体的
准确性系统。网络内生安全在系统设计之初进行系统级的安全考量，设计和实
施6G 通感网络时固有的、全面的安全措施，这些措施旨在保护网络免受各种安
NSFC 2024
第 5 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 7 页 ===
全威胁和攻击，同时确保用户隐私和数据安全，使系统自身具备免疫能力，不
仅能被动地抵御恶意攻击，还能主动地消灭各种安全风险。因此，基于物理层
安全理论开展通信、感知与安全深度融合的设计方案相关研究，是推动6G 系统
实现更为安全、智能和高效的通信网络环境的关键所在，具有重要的研究意义
和研究价值。 
物理层安全技术旨在通过利用信道的固有安全属性如随机性、多样性和时变
性，在物理层实现加密和认证。6G 通感系统的信道特点体现在其对高频段的利
用和对复杂环境的适应性，这导致了显著的时频双弥散特性，信道特征在时间
和频率维度上的变化复杂，因此，设计与信道状态信息相结合的安全机制可以
确保窃听者无法截取信息，推动了物理层技术与通感系统的结合。另一方面，
通感系统的数据形式多样性为系统内生先验信息赋能，其独有特性为物理层安
全使能的通信、感知和安全一体化集成系统设计提供了可行的契机。 
当前，我国关于“6G 网络中通信感知由分立走向一体”相关技术已经开展
了初步研究, 有一定的研究基础。面对海量数据传输、多样化的数据形式以及高
度动态的开放复杂网络环境所带来的数据安全和隐私风险，应及时开展全面的
安全策略更新和隐私保护措施的实施。针对大规模复杂通感融合网络中存在的
个人隐私泄露、通信信息监听、抵御未知风险能力差等安全问题，围绕着内生
先验信息、安全方案鲁棒性、动态资源分配三个方面提出创新的研究思路和可
行的技术方案，实现通信感知网络的内生安全，综合提升系统抵御风险、消灭
风险的能力，推动无线网络在保障信息安全和隐私的前提下飞速发展。 
1.2 国内外研究现状 
本项目针对面向6G 的通信感知融合网络内生安全技术展开研究，从通信感
知融合系统中安全和隐私的风险、内生安全技术设计、内生安全赋能的网络弹
性分析三个方面回顾国内外研究现状和发展动态，分析开放复杂的6G 系统的内
生安全技术研究的必要性和紧迫性。 
1.2.1 通感系统中安全和隐私的风险研究现状 
随着通信感知的深度融合技术的发展，同时执行通信和感知任务的系统为多
种应用提供了前所未有的便利和效率。然而，通感技术的发展在保护用户隐私
和系统安全方面也带来了新的挑战。 
当前，众多基站利用电磁波形成了广阔的无线网络，电磁信号的存在范围十
分广泛，通感技术的应用可能对用户对隐私的产生威胁[6]。具体来讲，感知功能
NSFC 2024
第 6 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 8 页 ===
主要分为三个层面：检测、估计和识别。检测关注的是确认目标是否存在的信
息获取；估计则是在目标已知的情况下，通过感知来获取目标的位置、速度等
具体参数；识别则进一步获取关于目标身份、特征、状态等更加详尽的信息[7-9]。
显然，当感知目标为人类时，这三层感知就可以像摄像头一样记录人的每个动
作。然而和摄像头相比，电磁波可以在黑暗中或即便有墙体阻隔的情况下继续
发挥感知作用。例如，文献[10]通过太赫兹频段的通感技术，成功实现了对受限
空间环境的重建和目标成像。这意味着，即便个人身处封闭空间，也难以逃避
电磁波的监视，这种全方位的监控使用户隐私几乎无所遁形。此外，如果存在
恶意终端捕获了通感设备的回波信号，且该设备拥有强大的处理能力，则直接
构成了对隐私的侵犯[11]。 
通感一体化基站的任务之一是将功率集中投向包含目标的方向，同时确保在
接收端，目标回波信号的信干噪比足够高，以满足既定的感知性能要求。然而，
当目标可能是潜在的窃听者时，问题变得复杂。由于感知波束设计时，最大化
信干噪比比中包含的角度与目标的角度相同，这就意味着目标对嵌入通信信号
的接收信干噪比非常高，从而很大程度地增加了信息被窃听的可能性[12-14]。例
如，文献[15]中针对此场景展开了研究，文中将感知目标看做通信信息的潜在窃
听者，当系统没有针对目标接收处不做任何干预时，目标有很大概率成功获取
通信信息。此外，在上行通信过程中通信用户可能会接入伪基站，导致电磁信
号泄漏给未授权的感知目标，这也是通感系统中存在的安全威胁之一[16,17]。 
综上所述，通信感知融合系统的研究近年已经取得了可观的进展，但是随之
带来的安全风险在通感系统的设计中也要考虑在内。首先，对于高动态变化的
信道，需要通过探究一体化波形增益的本质来设计适应性强、复杂度低且安全
可靠的确定性系统。同时，对于通信感知融合网络的安全问题的研究还处于初
级阶段，如何针对通感网络中数据类型多样、数据交互频繁的特征设计可靠的
安全方案，抵御未知的风险是是突破研究瓶颈的关键问题。 
1.2.2 通感系统内生安全研究现状 
 
内生安全理论由国家数字交换系统工程技术研究中心团队提出[18-21]，是利用
具有动态、异质和冗余属性的内生安全结构来实现内生安全功能。这一理论的
核心在于，它能够从一个异构且具备冗余的执行体中，动态地选取一组执行者
来完成系统所需的功能。这种方法不仅增加了系统对攻击者的不确定性，也大
大提高了系统的可靠性。通过这种方式，系统不仅能够抵御已知的安全威胁，
NSFC 2024
第 7 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 9 页 ===
更重要的是，它还能有效防御未知的安全威胁和攻击手段。因此，该机制使得
攻击者难以理解系统的结构和运行机制，从而难以发起有效的攻击。 
 
图3 网络安全发展特征与内生安全发展阶段 
无线系统内生安全的不同之处在于，由于电磁波传播的折射、散射、衍射、
反射等效应，使得无线通道自然具有动态、异质和冗余的内生安全属性。无线
内生安全是一种不同于传统安全机制的共生关系，安全内在融合与无线系统，
可以共生发展。例如，信道状态信息估计技术不仅可以更好地抵御随机信道衰
落，而且还可以更好地利用信道状态信息来对信号进行混淆，提高内生安全性。
同样，对于信道编码技术，一个良好的编码方法不仅可以提高通信系统的鲁棒
性，还可以帮助实现物理层安全传输技术。 
融合感知功能的无线通信系统中，安全问题可以分为两个方面，包括通信信
息安全和感知信息的隐私。在复杂多变的开放性应用场景中会产生大量数据交
互，提升系统内信息安全和隐私的风险。利用信道状态信息设计保证通信信息
安全近年被广泛研究[22-25]，其中包括通过在发送端加入人工噪声的手段开降低
非法用户端的信干噪比，从而降低其成功解码信息的概率[26]。此外，方向性调
制、共线性干扰设计也是作为通信信息传输安全的有效手段被广泛研究[27]。感
知隐私方面，罗格斯大学团队分析了在通信雷达同频共存的环境下，感知信息
的隐私风险[28-31]，其中包括基于信道信息获取感知雷达位置、基于机器学习估
计雷达方向等，并提出了在非法接收端不采用基于机器学习理论时，雷达方位
信息隐私保护机制。 
对于通信感知融合系统的内生安全研究目标在于，感知和通信等数据信息大
量交互的高动态环境中，如何提升该开放系统的确定性。由上述研究现状可以
看出，该方向的研究仍处于初级阶段，首先，在系统设计中需要分别考虑通信
安全和感知隐私，然而当前在通信感知融合系统的感知隐私安全相关研究几近
NSFC 2024
第 8 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 10 页 ===
空白，如何建立统一性能指标，量化通信安全和感知隐私，是设计通感融合系
统内生安全的关键。此外，深入研究保障系统内生安全机制，设计低功耗、低
复杂性、高效益的内生安全算法仍有待探索。 
1.2.3 内生安全赋能的多维性能适配网络弹性 
网络弹性工程是由美国、欧洲等发达国家和地区响应数字化转型和新兴网络
安全挑战而实施的技术措施。它基于网络弹性标准，旨在建立数字技术的准入
门槛，通过同时加强应用服务和设备供应两端，提升数字基础设施和产品的安
全性[32]。美国自2007 年起，通过一系列政策文件和指令，将网络弹性纳入国家
安全战略，强调提高关键基础设施的安全与弹性，并在2023 年提出将安全措施
内置于体系结构和设计中。欧盟自2008 年开始发布多份政策文件，推动网络弹
性的实践应用，并通过立法要求销售于欧盟的数字化设备和软件满足强制性安
全标准。2023 年11 月，欧盟就《网络弹性法案》技术议题达成一致，准备提交
审议[33]。英国则在2022 年发布了多项政策文件，将网络弹性作为国家网络战略
的重要组成部分，明确提出建立国防弹性网络体系的愿景，并推进相关立法。
这些措施体现了各自在提高网络弹性方面的战略布局和实施细节，旨在建立更
安全、有弹性的数字生态系统，以应对网络攻击和提高国家网络安全水平[34]。 
当前，国际上普遍采用的网络弹性工程方法面临几个技术挑战，包括处理未
知威胁的能力不足、缺少集成多种技术的框架、以及安全性评估的能力有限。
文献[32]中表明，内生安全可以解决这三大难题。该技术旨在增强网络弹性在预
防、抵御、恢复、适应四个既定目标维度上的能力。针对通信感知融合网络，
通信服务质量、感知准确性和数据安全性的性能权衡和理论边界分析可以为网
络弹性的刻画提供有力的理论基础支撑，现有文献仅针对通信和感知性能理论
边界，例如，文献[35-36]通过制定一个通用的通信感知权衡优化问题来优化输
入数据分布来研究时间-频率权衡。此外，文献[37]通过将通信感知波形视为感
知信号模型中的随机但已知的干扰参数，并表征克拉美罗界-速率区域，分析了
时间-频率权衡。在文献[38]中，作者基于不同的输入信号分布，从信息论的角
度考虑了通信感知融合系统的时分复用方案，并提出了容量-失真区域来表征通
信感知系统性能权衡。上述融合感知功能的无线网络无法保证信息的安全和隐
私，无法保证信息交互的安全性和可靠性，仍然是不确定的系统。 
内生安全赋能的通感网络弹性工程研究关键在于建立统一量化体系刻画网
络服务性能和安全之间的弹性关系，为深入探索通信、感知和安全三者之间的
NSFC 2024
第 9 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 11 页 ===
相互影响及权衡机理提供理论依据。目前，内生安全赋能的通信感知融合网络
理论内涵和可达合作增益尚不明确，因此由内生安全赋能的通信感知融合网络
性能权衡和理论边界分析是整体网络弹性的量化表征至关重要的研究基础。    
1.3 主要参考文献 
[1] 郎平, 田大新. 面向 6G 的车联网关键技术[J]. 中兴通讯技术, 2021, 27(2): 13. 
[2] 王晓云, 段晓东, 孙滔. 平台化服务网络——新型移动通信系统架构研究[J]. 电信
科学, 2023, 39(1): 20-29. 
[3] XIE F. 6G network architecture: a survey[J]. ZTE technology journal, 2023, 29(5): 28-37. 
[4] 何佳, 周知, 李先进, 等. 面向 6G 的通信感知一体化: 基于无线电波的感知与感
知辅助通信[J]. 信息通信技术与政策, 2022, 48(9): 9. 
[5] 程强, 刘姿杉. 电信网络智能化发展现状与未来展望[J]. 信息通信技术与政策, 2020, 
46(9): 16. 
[6] Lu S, Liu F, Li Y, et al. Integrated sensing and communications: Recent advances and ten 
open challenges[J]. IEEE Internet of Things Journal, 2024. 
[7] Zhang J A, Rahman M L, Wu K, et al. Enabling joint communication and radar sensing in 
mobile networks—A survey[J]. IEEE Communications Surveys & Tutorials, 2021, 24(1): 
306-345. 
[8] Gini F, Rangaswamy M. Knowledge-Based Radar Detection, Tracking, and 
Classification[J]. 2008. 
[9] Liu F, Masouros C, Petropulu A P, et al. Joint radar and communication design: 
Applications, state-of-the-art, and the road ahead[J]. IEEE Transactions on Communications, 
2020, 68(6): 3834-3862. 
[10] Jia H E, Zhi Z, Xianjin L I, et al. 6G integrated sensing and communication: wireless 
sensing and sensing assisted communication[J]. Information and Communications 
Technology and Policy, 2022, 48(9): 9., 
[11] Mavroudis V, Hao S, Fratantonio Y, et al. On the privacy and security of the ultrasound 
ecosystem[J]. Proceedings on Privacy Enhancing Technologies, 2017, 2017(2): 95-112. 
[12] Chu J, Liu R, Li M, et al. Joint secure transmit beamforming designs for integrated 
sensing and communication systems[J]. IEEE Transactions on Vehicular Technology, 2022. 
[13] Su N, Liu F, Masouros C, et al. Secure ISAC MIMO Systems: Exploiting Interference 
With Bayesian Cram\'er-Rao Bound Optimization[J]. arXiv preprint arXiv:2401.16778, 2024. 
NSFC 2024
第 10 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 12 页 ===
[14] Khan W U, Lagunas E, Ali Z, et al. Opportunities for physical layer security in UAV 
communication 
enhanced 
with 
intelligent 
reflective 
surfaces[J]. 
IEEE 
Wireless 
Communications, 2022, 29(6): 22-28. 
[15] Su N, Liu F, Masouros C. Sensing-assisted eavesdropper estimation: An ISAC 
breakthrough in physical layer security[J]. IEEE Transactions on Wireless Communications, 
2023. 
[16] Duan Z, Yang X, Gong Y, et al. Covert Communication in Uplink NOMA Systems under 
Channel Distribution Information Uncertainty[J]. IEEE Communications Letters, 2023. 
[17] Qu K, Li X, Guo S. Privacy and Security in Ubiquitous Integrated Sensing and 
Communication: 
Threats, 
Challenges 
and 
Future 
Directions[J]. 
arXiv 
preprint 
arXiv:2308.00253, 2023. 
[18] Guo W, Wu Z, Zhang F, et al. Scheduling sequence control method based on sliding 
window in cyberspace mimic defense[J]. IEEE Access, 2019, 8: 1517-1533. 
[19] Hu H, Wu J, Wang Z, et al. Mimic defense: a designed‐in cybersecurity defense 
framework[J]. IET Information Security, 2018, 12(3): 226-237. 
[20] Jin L, Hu X, Lou Y, et al. Introduction to wireless endogenous security and safety: 
Problems, attributes, structures and functions[J]. China Communications, 2021, 18(9): 88-99. 
[21] Ji X, Wu J, Jin L, et al. Discussion on a new paradigm of endogenous security towards 
6G networks[J]. Frontiers of Information Technology & Electronic Engineering, 2022, 23(10): 
1421-1450. 
[22] Mitev M, Chorti A, Poor H V, et al. What physical layer security can do for 6g 
security[J]. IEEE Open Journal of Vehicular Technology, 2023. 
[23] Feng K, Li X, Han Y, et al. Physical layer security enhancement exploiting intelligent 
reflecting surface[J]. IEEE Communications Letters, 2020, 25(3): 734-738. 
[24] Makarfi A U, Rabie K M, Kaiwartya O, et al. Physical layer security in vehicular 
networks with reconfigurable intelligent surfaces[C]//2020 IEEE 91st Vehicular Technology 
Conference (VTC2020-Spring). IEEE, 2020: 1-6. 
[25] Zhang C, Jia F, Zhang Z, et al. Physical layer security designs for 5G NOMA systems 
with a stronger near-end internal eavesdropper[J]. IEEE Transactions on Vehicular 
Technology, 2020, 69(11): 13005-13017. 
[26] Su N, Liu F, Masouros C. Secure radar-communication systems with malicious targets: 
NSFC 2024
第 11 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 13 页 ===
Integrating radar, communications and jamming functionalities[J]. IEEE Transactions on 
Wireless Communications, 2020, 20(1): 83-95. 
[27] Su N, Liu F, Wei Z, et al. Secure dual-functional radar-communication transmission: 
Exploiting interference for resilience against target eavesdropping[J]. IEEE Transactions on 
Wireless Communications, 2022, 21(9): 7238-7252. 
[28] Li B, Petropulu A P. Joint transmit designs for coexistence of MIMO wireless 
communications and sparse sensing radars in clutter[J]. IEEE Transactions on Aerospace and 
Electronic Systems, 2017, 53(6): 2846-2864. 
[29] Li B, Petropulu A P, Trappe W. Optimum co-design for spectrum sharing between matrix 
completion based MIMO radars and a MIMO communication system[J]. IEEE Transactions 
on Signal Processing, 2016, 64(17): 4562-4575. 
[30] Dimas A, Clark M A, Li B, et al. On radar privacy in shared spectrum 
scenarios[C]//ICASSP 2019-2019 IEEE International Conference on Acoustics, Speech and 
Signal Processing (ICASSP). IEEE, 2019: 7790-7794. 
[31] Dimas A, Li B, Clark M, et al. Spectrum sharing between radar and communication 
systems: Can the privacy of the radar be preserved?[C]//2017 51st Asilomar Conference on 
Signals, Systems, and Computers. IEEE, 2017: 1285-1289. 
[32] Wu J, Zou H, Xue X, et al. Cyber Resilience Enabled by Endogenous Security and 
Safety: Vision, Techniques, and Strategies[J]. Strategic Study of Chinese Academy of 
Engineering, 2024, 25(6): 106-115. 
[33] Car P, De Luca S. EU Cyber resilience act[J]. EPRS, European Parliament, 2022. 
[34] UK G O V. National cyber strategy 2022[J]. 2022. 
[35] Xiong Y, Liu F, Lops M. Generalized deterministic-random tradeoff in integrated sensing 
and 
communications: 
The 
sensing-optimal 
operating 
point[J]. 
arXiv 
preprint 
arXiv:2308.14336, 2023. 
[36] Zhang Y, Aditya S, Clerckx B. Input Distribution Optimization in OFDM Dual-Function 
Radar-Communication Systems[J]. arXiv preprint arXiv:2305.06635, 2023. 
[37] Xiong Y, Liu F, Cui Y, et al. On the fundamental tradeoff of integrated sensing and 
communications under Gaussian channels[J]. IEEE Transactions on Information Theory, 
2023. 
[38] Ahmadipour M, Kobayashi M, Wigger M, et al. An information-theoretic approach to 
NSFC 2024
第 12 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 14 页 ===
joint sensing and communication[J]. IEEE Transactions on Information Theory, 2022. 
2. 项目的研究内容、研究目标，以及拟解决的关键科学问题（此
部分为重点阐述内容）； 
2.1 研究内容 
本项目针对6G 开放融合、异构共存、智能互联的网络特性引发的更多未知
复杂安全威胁，研究面向6G 的通信感知融合网络内生安全问题，从通信感知融
合系统架构切入，探索如何通过物理层技术实现通感系统内生安全，并深入分
析内生安全赋能的通感融合系统的网络弹性。拟从 
➢ 基于通感系统特性获取先验信息 
➢ 物理层技术使能的通感系统内生安全 
➢ 内生安全赋能的多维性能适配系统网络弹性分析 
三个层面展开研究。以上三个研究内容联系紧密、层层递进。研究内容一中提
出的网络架构是研究内容二的基础，研究内容二针对研究内容一中的系统提出
内生安全技术方案，研究内容三针对研究内容二提出的方案深入分析网络弹性。
下面针对具体的研究内容进行详细阐述。 
 
 
 
 
 
 
 
 
 
 
 
图4 总体研究内容、目标和关键科学问题之间的关系 
2.1.1 研究内容一：基于通感系统特性获取先验信息 
通信感知一体化技术通过深度整合通感功能，旨在提高无线与硬件资源的使
用效率。波束赋形技术在通感系统中具有很大的发挥潜力，它逐步成为很多新
型平台的技术要求。对于通信和感知的性能指标，往往会产生波形设计上的矛
NSFC 2024
第 13 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 15 页 ===
盾，因此在通感融合系统下进行波束赋形具有一定的挑战性。因此，本项目基
于通信感知融合系统架构研究物理层增强技术，具体包括：1）通感系统一体化
鲁棒性波形设计。利用同一电磁信号和硬件平台完成通信和感知两项任务，由
此提升频谱效率、降低开销以满足6G 的高数据量交互需求。同时，在考虑系统
中通信信道信息及感知先验信息不确定性的情况下，设计高性能、高鲁棒性的
波束赋形以保证通感系统的服务质量指标；2）通信感知融合系统安全与隐私性
能刻画。通感系统的内生安全问题分别从通信安全和感知隐私两个方面考虑，
建立合理性能指标刻画系统安全性能是评估本项目研究采取的技术手段的基
础；3）通感辅助弥补系统内源性缺陷。传统通信中信道信息获取需要依赖导频
信号发送及反馈，在时延和能耗等方面产生了额外的开销，并且在发送导频的
过程中可能会造成信息泄露。在感知功能的辅助下，利用确定性信道建模方法，
从多源传感器中提取通信信道状态信息，实现基于感知辅助的低开销、高精度
的通信信道状态估计。同时复用感知数据，作为安全设计的重要先验信息。 
2.1.2  研究内容二：物理层技术使能的通感系统内生安全 
传统通信系统安全依赖于“外挂式”和“补丁式”技术，这些技术有效性与
安全问题的成因和威胁机制的精准掌控程度强相关，不仅开销很大，而且无法
完全对未知的威胁产生有效防御。内生安全设计不依赖 (但不排斥) 攻击者先验
知识或精确感知与行为分析的前提下, 有效抑制基于内生安全共性问题的“已知
的未知”随机性扰动及“未知的未知”不确定网络攻击。为此，本项目研究保障通
感系统的物理层安全技术，具体包括：1）感知辅助迭代优化物理层安全。通过
感知功能获取窃听者信道信息，建立加权优化模型，权衡感知功能估计精确度
和物理层安全性能；2）方向性调制合法接收信息。通过合法用户与基站的合作
获取信道状态信息，设计一体化波束，使得系统授权合法用户端收到正确信息，
而其他用户接收随机信息，该方法无需窃听信道先验信息； 3）基于信息论的
感知隐私保密。考虑合法通信用户在通信感知融合系统中作为双站雷达的非法
接收端，解析获取感知目标位置，利用互信息差刻画隐私安全性能表征，设计
优化问题在保证服务质量的同时防止感知数据的泄露。 
2.1.3 研究内容三：内生安全赋能的多维性能适配系统网络弹性分析 
通感网络的内生安全设计使得一个开放复杂的巨系统成为了确定系统，但是
系统中存在“开放与安全”的矛盾性，即在一定的功率限度内通信、感知和安
全的服务性能之间存在权衡关系。在大部分通感一体化研究中，或者采用泛化
NSFC 2024
第 14 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 16 页 ===
的信干噪比、互信息等衡量通感性能，或者采用通信速率和估计误差等分别衡
量通信和感知性能，未能建立精确统一的性能评价指标。因此，本项目研究内
生安全设计赋能的多维性能适配系统网络弹性，具体包括：1）统一通感服务质
量性能评价指标。基于速率失真理论的容量失真函数的概念刻画给定估计误差
下所需的最小信息速率，建立信息量和估计量之间的等价关系；2）通感系统整
体性能指标刻画。描述在给定感知精度条件下，可达到的通信容量及通信和感
知性能的最优实现水平，为进行通感整体性能和安全性能之间的权衡提供理论
基础；3）内生安全赋能的通感系统可达性能边界分析。基于不同的系统资源分
配方案，分析通感系统性能和安全性能的权衡，为通信感知融合系统的内生安
全设计提供理论支持。 
2.2 研究目标 
本项目总体研究目标是采用物理层技术实现通信感知融合系统的内生安
全，以应对由无线网络内源性缺陷产生的共性和本源安全问题，提升系统抵御
未知安全威胁的能力。遵循建立系统架构、提出通感系统内生安全物理层技术、
提出联合性能指标并通过性能权衡分析网络弹性的技术路线，探索物理层技术
如何保证通感系统的安全和隐私、感知功能如何辅助物理层安全技术摆脱对窃
听信道先验信息的依赖以实现内生性。具体目标如下： 
研究目标（1）：在达到通信感知服务用户质量要求的前提下，通过利用感
知功能获取的数据作为系统内生先验信息，摆脱物理层安全设计对额外反馈机
制的依赖，实现内生安全设计。 
研究目标（2）：通过通信感知网络架构建模以及鲁棒性一体化波形设计，
实现鲁棒性强的物理层内生安全设计，安全机制随动态信道环境而变化，解决
安全方案与高动态复杂环境不适配的问题。 
研究目标（3）：通过分析通信、感知、安全各性能之间的权衡，建立系统
高效适配机制，根据实际需要设计性能折衷方案，解决系统内服务性能与安全
性能冲突所导致的低能效问题 
2.3  拟解决的关键科学问题 
本项目从内生先验信息、鲁棒性安全应对方案、高效资源分配三个维度上，
逐步探索并明确通信、感知和安全一体化集成系统中的高效动态网络适配问题，
进而为开放复杂的巨系统大量数据交互传输提供具有高效性和可靠性的安全和
隐私保障。具体表述如下： 
NSFC 2024
第 15 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 17 页 ===
拟解决的关键科学问题：通信-感知-安全集成网络系统的多维性能动态适配。 
本项目拟面向通信、感知、安全一体化系统，基于物理层安全技术，设计
实现系统的内生安全，旨在大幅度提升复杂开放的通感融合巨系统抵御未知风
险的能力。 
首先，通信感知实现“深度融合、功能辅助、信息共享”还处于初期探索
阶段，通信信息和感知数据的多重协作机制仍有很大的研究空间，但是更深度
的通感协作势必会带来通信感知数据边界模糊的问题，一旦系统中出现恶意获
取信息的终端设备，将会造成大规模数据窃听和隐私泄露，因此设计安全方案
既可以支持多重数据交互，也能保障数据安全成为了挑战性问题。 
与此同时，对于向智能化、全场景、全连接、全频谱方向发展的高动态巨
系统，传统的“外挂式”安全技术由于其成本高、灵活性差，已经无法满足巨
系统的安全需求，因此探索安全技术和通感系统特定的衔接点，针对通感巨系
统设计适应其动态性的内生安全方案成为了另一个难点。 
最后，在多功能集成的通信、感知、安全系统中，性能之间会出现相互“冲
突”，例如，当系统中安全性能到达最优状态时，通信和感知的服务质量不一定
会满足用户质量需求，因此，在通感系统内生安全设计中对通信、感知和安全
的性能权衡问题同样是一个难点。 
本项目为应对上述问题难点，面向通感融合系统设计具有高度适应性的内
生安全方案，探索安全技术与通感系统的衔接点，并明晰通信、感知和安全高
效动态适配机制。 
 
图5 核心技术路线图 
本项目拟解决的关键科学问题与研究内容和研究目标之间的逻辑关系如图4
所示，核心技术路线场景示意图如图5 所示。项目立足于面向6G 的通信感知融
NSFC 2024
第 16 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 18 页 ===
合网络内生安全技术研究，围绕内生先验信息、鲁棒性安全应对方案、高效资
源分配三个维度研究通信、感知和安全的高效动态适配机制。本项目设置的研
究内容和蕴含的科学问题之间紧密联系、相互支撑，相关研究成果可以推动网
络安全技术发展，为飞速发展的通信技术提供信息安全可靠保障。 
3. 拟采取的研究方案及可行性分析（包括研究方法、技术路线、
实验手段、关键技术等说明）； 
3.1 研究方法 
 
本项目拟沿着基础理论构建、方案设计以及仿真实验性能分析层层递进的研
究思路，设计集通信、感知、安全一体的稳定系统。具体采用物理层技术，从
通信信息和感知信息的安全和隐私方面分别考虑，不通过通感系统外部反馈获
取恶意用户的先验信息，以此实现通感系统的内生安全，并设计通信、感知、
安全性能指标，分析一体化系统的性能权衡和内生安全赋能的多维性能适配系
统网络弹性。 
3.2 技术路线 
本项目根据三个研究内容的特点，按图6 所示的技术路线，详细阐述如下： 
 
图6 项目总体技术路线 
3.2.1 基于通感系统特性获取先验信息 
本研究内容中，通信感知融合系统在通信和雷达共享频谱、共享硬件平台、
共享功率的基础上设计通感一体化波形，从而提升无线频谱资源利用率、降低
开销，并为系统提供通信感知深度合作的性能增益。在通感融合基础上，设计
提升系统的鲁棒性以及感知辅助弥补内源性缺陷。 
NSFC 2024
第 17 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 19 页 ===
 
图7 研究内容（1）技术路线 
（1）通感系统一体化鲁棒性波形设计 
 
通信感知一体化波形设计关键在于如何设计波形同时满足通信与感知的服
务质量，并进一步深度融合以产生新的增益。根据系统实际需求，一种通信感
知波形设计可以写成如下加权优化问题形式 
 
( ) (
) ( )
maximize 
1
subject to  
C
S


+
−

X
X
X
X
 
(1) 
其中X 表示通感一体化波形矩阵，( )
C X 表示通信性能指标，包括误码率、信道
容量等，( )
S X 表示感知性能指标，包括估计精度、检测概率等，均为关于波形
X 的函数。表示给定的加权因子，决定系统对通信和感知性能优化的权重，
表示可行域，实际设计中包括系统功率约束等条件。上述优化问题适用于通信
和感知性能指标表达式较为简单的情况，否则会很大程度增加该问题求解的复
杂性。 
 
当考虑系统中先验信息无法精确已知的情况，需要针对系统进行鲁棒性强的
一体化波形设计。此时通信感知性能表达式较为复杂，设计中优化通式可以等
价表示为如下形式 
 
( )
( )
maximize  
subject to  
, 
C
S

X
X
X
X
 
(2) 
具体场景中，合法用户信道和感知信道的信道状态估计误差模型分别表示为： 
⚫ 合法用户信道信息估计误差 
已知误差范围的不精确信道估计：估计信道可以采用加性信道误差模型
NSFC 2024
第 18 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 20 页 ===
表示，即第i 个通信用户的信道可以写作 
 
i
i
i
=
+
h
h
e  
 (3) 
    其中
ih 表示发送端估计信道，
ie 表示信道不确定度，分布于如下球面集合 
 


2
2 ,
i
i
i
i
i

=


e
e
 
(4) 
⚫ 感知目标（窃听者）信道估计误差 
在感知通信融合网络中，感知信道是关于感知目标角度的函数。实际上，
感知目标的角度估计通常也伴随着估计误差。因此，在该场景中，虽然发送
端可以通过感知功能获取窃听信道状态信息，但信道信息仍存在估计误差。 
基于上述通信和感知信道先验信息获取不精确的情况，鲁棒性一体化波形设计
表达式可以写为如下形式 
 
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
0
0
0
0
0
0
ˆ
maximize  
,
subject to  
,
                 
1
,
                 
1
,
                 
C
H
H
x
m
x
m
m
H
H
k
x
k
x
k
H
H
k
x
k
x
k
C


















−




+



−



X
H
X
a
R a
a
R a
a
R a
a
R a
a
R a
a
R a
W
 
(5) 
其中ˆ
C
H 表示通信估计信道，
1
H
x
L
=
R
XX 表示感知端协方差矩阵，( )

a
表
示指向矢量。ˆ表示估计角度，当目标角度估计不准确时，假设估计误差为

，
则有
0
ˆ
ˆ
,







−
+ 

，式中
ˆ
ˆ
,






−
+ 

表示设计波形的主瓣角度
区间。由此可以看出上式优化问题最大化通信服务质量性能，同时约束条件一
至三设计生成宽主瓣波形以覆盖目标所有可能角度，从而保证目标一定落在主
瓣的角度区间内，保证感知性能。求解优化问题（5）可以采用S-过程原理重构
通信性能表达式，从而转化为凸问题求解。 
（2）系统安全和隐私性能刻画 
 
通感融合系统的内生安全设计需要从通信和感知两个方面考虑，分别对其构
建性能刻画指标是关键性问题。 
➢ 通信信息安全性能刻画 
融合感知功能的通信系统中，双功能基站发送波束用于感知目标，同时携带
发送给通信用户通信信息，系统中接收通信信息的通信用户为合法终端，其他
感知设备为潜在非法终端。具体应用场景如图所示。信息论中，香农-哈特利定
理定义了信道容量的概念，即在噪声存在的情况下，通过特定带宽的信道可靠
NSFC 2024
第 19 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 21 页 ===
传输通信信息的最高速率的严格上限。由此任何离散无记忆信道的容量定义为
输入X 和输出Y 之间的最大互信息(
)
;
I X Y 可以表示为 
 
(
)
(
)
max
;
p X
I X Y
C =
 
(6) 
上式表示，在输入X 上给定功率约束P 的情况下，具有加性高斯白噪声的高斯
信道的信道容量可以用香农公式表示，即
(
)
2
Gau
2
log
1
C
P 
=
+
，其中
2
是噪声
方差。基于上述信道容量引入了一个用于衡量通信安全的基本保密度量标准。
在物理层安全研究中，保密容量通常作为一个关键的评估指标，定义如下 
 
(
)
(
)
(
)


max
;
;
s
p X
C
I X Y
I X Z
=
−
 
(7) 
其中X 是发送信号，Y 和Z 分别是用户和窃听者端的接收信号。基于上述理论，
为了更方便地评估保密性并简化计算，在物理层安全研究中通常应用保密率评
估安全性能，保密速率通常被视为保密容量的下限。假设输入信号符合高斯分
布，可达保密率表达式为 
 


s
c
e
R
R
R
+
=
−
 
(8) 
其中
+ 表示


max
,0 ，
c
R 表示发送端到合法用户的信道传输率，
e
R 表示发送端
到窃听者的信道传输率。在不考虑信道衰落的前提下，通常采用保密率或保密
容量来衡量物理层安全的设计性能。 
图8 通感系统中感知隐私风险 
➢ 感知信息隐私性能刻画 
对于感知和通信功能一体化的基站，当感知端采用双站雷达时，双功能波束
经过目标反射的信号可能同时被通信用户和感知合法接收端接收，此时通信用
户即可以解析出目标的位置，从而造成目标位置的隐私泄露。该场景如图8 所
示，为衡量接收器的感知性能，采用接收到的雷达信号和目标响应矩阵之间的
NSFC 2024
第 20 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 22 页 ===
互信息作为雷达感知的性能指标，合法雷达接收器的感知互信息可以表示为 
 
(
)
(
)
;
|
;
|
S
S
S
S
I
I

=
Y
X
Y H
X  
(9) 
同理，通信用户和感知信息的互信息可以写为(
)
;
|
c
c
I Y H
X 。其中，
S
H 和
C
H 分
别对应二者的信道响应矩阵。根据上述信息模型和互信息表达式，互信息差
(
)
(
)
;
|
;
|
S
S
c
c
I
I
−
Y H
X
Y H
X 即可表示感知隐私性能指标。 
（3）通感辅助弥补系统内源性缺陷 
由通信感知安全的性能刻画中，安全和隐私性能指标都是关于信道信息的函
数，因此可以说明信道信息是保证网络安全的重要先验信息。然而，对于系统
中的恶意终端设备，现有的研究中通常假设其信道信息在发送端已知或部分已
知，实际中系统必须依赖由恶意终端设备参与的额外的反馈来获取其信道信息，
这是几乎不可能实现的，这个问题就是网络中的内源性缺陷。对于通感融合系
统，在感知功能的辅助下可以弥补此缺陷，具体如下： 
 
图9 通感系统中通信信息安全风险 
通信安全：对于图9 中所示场景，感知目标被看作窃听终端，所以系统设计中
需要防止其解码保密信息。在通信感知融合系统中，感知功能可以发送全向波
束，利用回波进行角度估计。由于基站到目标的信道（窃听信道）是关于角度
的函数，由角度估计即可获取窃听信道信息。具体回波可以写作 
 
( )
( )
( )
H
r
t


=
+
X
a
a
S
Z  
(10) 
其中
( )
t 
a
和
( )
r 
a
分别表示发送天线和接收天线的指向向量，( )
表示从角度
回波信号的幅度，S 表示发送信号，Z 表示包括干扰和噪声等剩余项。待估计
参数一般包括角度信息和幅度信息，其中角度信息可以采用Capon 算法，幅度
信可以采用渐进最大似然估计（AML）算法估计。 
感知隐私：在如图8 所示的场景中，由于该通信用户（非法接收端）与基站进
行合法通信，通信感知双功能融合基站也很容易获取通信用户位置。 
NSFC 2024
第 21 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 23 页 ===
 
综合通信和感知两方面的信息安全两个方面来看，利用物理层技术保障通信
感知融合网络安全隐私可以摆脱利用额外反馈获取窃听信道信息，有助于突破
物理层安全中的瓶颈问题，构成一个集通信、感知、安全为一体的具有稳定性
和准确性的系统。 
3.2.2 物理层技术使能的通感系统内生安全 
 
图10 研究内容（2）技术路线 
（1）感知辅助迭代优化物理层安全 
不同于传统的物理层安全对窃听信道的信息获取方式，章节3.2.1 中阐述了
在通信感知网络中，感知功能可以辅助获取窃听信道状态信息。 
图11 Capon 算法性能及克拉美罗下界 
 
采用Capon 算法的目标角度估计精度与系统的信噪比关系如图11 所示。在
NSFC 2024
第 22 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 24 页 ===
低信噪比的情况下，目标角度估计误差明显增大。此外，图中也给出了在不同
信噪比下，发送端发射全向波束进行目标探测时的角度估计方均根误差的克拉
美罗下界（CRB）。假设目标角度概率分布服从以估计角度为均值，以方均根误
差下界为估计方差的高斯分布，则第k 个目标落在角度区间
( )
( )
( )
0
0
0
ˆ
ˆ
ˆ
ˆ
3 CRB
,
3 CRB
k
k
k
k
k







=
−
+




的概率为0.9973。 
基于目标角度估计结果，如何提升感知通信融合系统的通信信息传输安全，
同时兼顾感知服务质量是设计的关键。值得注意的是，该问题中通信安全性能
指标（保密率）是关于感知服务质量（角度估计精确度）的函数，因此可以通
过迭代优化的方式，逐步提升通信物理层安全性能，并分析感知服务质量和通
信信息安全之间的权衡。具体优化问题可以表示为 
 
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
(
)
( )
(
)
(
)
(
)
(
)
(
)
( )
,0
,0
,
,
,
,
,
,0
,0
,
,
,
0
0
0
,
0
,
m
 
,
                   
1
,
                   
1
,
         
t
     
 
 
u
 
ax  
1
s bject 
 
 
o    
H
H
k
x
k
k m
x
k m
k m
H
H
k p
x
k p
k
x
k
k p
H
H
k p
x
k p
x
k
k p
s
k
R




















−




+
+




−

−


W n
a
R a
a
R a
a
R a
a
R a
J
a
R a
a
R a
1
2
,
 


W
n
 
(11) 
其中
( )
0
k

表示由第一阶段估计得出的目标角度区间，作为优化问题设计的波形主
瓣宽度。表示给定的加权系数，当
0
=
时该问题退化为通信安全最优问题。
问题（11）为非凸问题，本项目拟采用分式规划、半定松弛、凸近似等方法获得
问题的最优解或次优解，记问题的解为
( )
( )
* 1
* 1
,
W
n
。然后将问题（11）的解带回
Fisher 信息矩阵J 即可求得相应的CRB，记为
( )
1 ˆ
CRB
k

，当
0

时，显然
( )
( )
1
0
ˆ
ˆ
CRB
CRB
k
k



，这意味着目标估计精确度提升，下一次迭代中的波形的
主瓣宽度随之相应变窄，所以在下一次迭代中将表达式（11）中主瓣角度范围区
间
( )
0
k

更新为
( )
( )
( )
1
1
1
ˆ
ˆ
ˆ
ˆ
3 CRB
,
3 CRB
k
k
k
k
k







=
−
+




。依据上述步骤迭代解决
优化问题，直至主瓣角度范围在第t 次迭代中收敛至
( )t
k

，即得到物理层安全和
目标估计CRB 联合优化的最终解
(
)
(
)
*
1
*
1
,
t
t
+
+
W
n
。 
（2）基于方向性调制的符号级预编码 
方向性调制算法的特点在于物理层安全的设计不依赖于窃听信道信息，根据
发送端获取的合法信道信息或合法信道部分信息，对输入信息进行符号级预编
NSFC 2024
第 23 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 25 页 ===
码，是实现通信感知融合系统内生安全的有效手段。方向性调制设计具体可以
表示为 
 
(
)
(
)
(
)
(
)
arg
[ ]
arg
Re
[ ]
Re
H
k
k
H
k
k
k
l
s
l
s

−



h x
h x
 
(12) 
其中l
x
表示第l 个时隙的发送波束，表示经接收端去除噪声的接收信号与发
送信号在星座图上相位差门限值，
k
表示通信服务质量（即用户k 处信干噪比）
门限值。 
图12 贡献性干扰区域划分 
贡献性干扰设计在方向性调制机制的基础上，进一步设计优化问题，将多用
户干扰转化为贡献性信息，很大程度提升系统的能效。由此贡献性干扰设计机
制下，接收用户信噪比可以表示为 
 



2
2
SNR
H
k
k
k
l

=
h x
 
(13) 
其中
2
k
表示系统噪声方差，对于不同信息调制方式，贡献性干扰区域划分如图
12 所示（绿色区域）。基于贡献性干扰机制的方向性调制，感知网络内生安全优
化问题可以表达为 
 
( )
(
)
(
)
(
)
2
maximize  
subject to Im
Re
tan ,
H
H
k
k
k
k
S



−


X
X
h X
h X
X
 
(14) 
其中( )
S X 表示感知性能指标， 
*
k
k
ks
=
h
h
，
k
h 为基站到第k 个用户的信道，ks 为
向第k 个用户发送的信息，对于M-PSK 调制的输入信号，
M


= 
。优化问题
（14）中的设计是基于发送端已知的合法信道信息设计，因此合法用户收到的
信息是正确的星座图分布，而系统中非法终端设备收到的信息则是随机分布。
优化后在合法接收端和非法接收端的星座图如图13 所示，图中以QPSK 和8PSK
NSFC 2024
第 24 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 26 页 ===
调制为例，对两种调制方式分别选择其中一个星座点，可以看出非法用户所接
收的信号是完全随机的。由此，贡献性干扰机制可以在未知窃听信道信息的前
提下，有效地保护传输信息安全。 
 
 
 
 
 
 
 
 
图13 合法接收端和非法接收端的星座图 
（3）基于信息论的感知隐私保密 
对于图8 中所示场景，设计中反射经过目标后，在合法的雷达接收端和通
信端的接收信号可以分别表示为 
 
(
)
r
r
r
r

=
+
Y
H
X
Z  
(15) 
 
(
)
c
c
c
c

=
+
Y
H
X
Z  
(16) 
其中
(
)
2
~
0,
r
r

Z
I 和
(
)
2
~
0,
c
c

Z
I 分别表示合法接收端和通信用户端的高
斯加性白噪声，
=
X
WS 表示发送信号矩阵，
(
)
r
r
H
和
(
)
c
c

H
分别对应二者的
信道响应矩阵，通常情况下假设
r
c



。为衡量接收器的感知性能，采用接收到
的雷达信号和目标响应矩阵之间的互信息作为雷达感知的性能指标，合法雷达
接收器的感知互信息可以表示为 
 
(
)
(
)
(
)
(
)
2
;
|
;
|
                    
logdet
r
r
r
r
r
r
r
H
r
h
I
I

−
=
=
+

Y
X
Y H
X
I
R
XX
I
 
(17) 
其中
(
)
(
)
vec
vec
r
H
h
r
r
R
H
H
，同理通信用户和感知信息的互信息可以写为 
 
(
)
(
)
(
)
2
;
|
logdet
c
H
c
c
c
c
h
I
−
=
+

Y H
X
I
R
XX
I
 
(18) 
根据上述信息模型和互信息表达式，可以通过最大化互信息差以确保感知隐私
安全，同时满足通信感知服务质量等约束条件，优化问题表示为 
 
ma ximize 
subject to 
x
r
c
x
I
I
−

R
R
 
(19) 
其中
x
R 为发送信号的协方差矩阵。在系统总功率一定的情况下，当互信差最大
NSFC 2024
第 25 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 27 页 ===
化时，可以保证感知服务性能同时降低非法接收端获取目标位置信息的能力。
优化问题（19）是复杂非凸优化问题，可以采用连续凸近似等方法解决该优化
问题。 
综上所述，物理层技术可以分别从通信安全和感知隐私角度防止各自信息被
窃听，因此可以作为构建通信感知融合系统的内生安全的有效手段。 
3.2.3 内生安全赋能的通感系统多维性能适配系统网络弹性分析 
 
图13 研究内容（3）技术路线 
（1）通感系统联合性能指标刻画 
 
随着通信感知功能的融合演进路径逐渐清晰，设计目标转向了通信与感知整
体性能的联合提升，而非仅仅关注单一方面的性能增强。这意味着感知功能和
通信功能互为补充，紧密融合，共同优化。 
在此背景下，传统的单一性能指标已不足以全面评估系统的综合“效用”。因
此需要引入新的性能指标，以综合反映通信和感知的相互辅助作用及其对系统
整体性能的影响。基于通信感知功能常用的性能指标，系统联合性能评价指标
可以如下定义。 
通感效率指标：通感效率指标定义为信道容量与参数估计误差之比，用于刻画
在单位感知误差条件下的最大信道容量，在给定系统发送信噪比为的前提下，
表达式可以写作 
 
(
)
(
)
C
CRB
X
E
X




=
+
 
(20) 
其中
(
)
C
X

表示当信噪比为时通信的信道容量，
(
)
CRB
X

表示当信噪比为
时的参数估计克拉美罗下界（包括角度、幅度、速度等参数），表示给定常数
NSFC 2024
第 26 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 28 页 ===
以限制当克拉美罗下界的值较小时，通感系统效率E 的最大值。由上述表达式
可以看出，当通信信道容量上升或感知估计精确度提升（即CRB 减小），E会
随之增大。因此，E可以有效表征通感系统效率，并且E越大系统联合效率越
高。 
通感效用指标：用于刻画给定资源分配方案的情况下，系统对可用功率的利用
程度，包含可达通信信道容量和可达感知估计精度，具体可以表示为 
 
(
)
(
) (
)
(
)
(
)
LB
UB
C
CRB
+ 1
C
CRB
X
X
U
X
X







=
−
 
(21) 
其中


0,1

表示给定的加权因子，用于刻画通信和感知在系统中的比重。
(
)
UB
C
X

表示纯通信系统的可达信道容量，
(
)
LB
CRB
X

表示纯感知系统设计中克
拉美罗下界可达最小值。依据通感效用的定义，通信感知功能不同比重下，其
性能扰动对系统的效用指标影响也不同。因此，U可表征系统对于通信和感知
的最大可用性能的利用率，并且U越大，系统能效越高。 
综上可以看出，当新指标在达到系统效用最大化时，通信和感知的性能可能
都不处于各自的最优状态。这标志着通感深度融合网络面临的是一个多目标联
合优化的挑战。引入通感系统整体评估指标，一方面可以表征通信和感知性能
之间的折中关系，另一方面有助于打破独立优化时的多维资源限制。  
（2）“开放性”与“安全性”性能权衡 
 
在通信、感知、安全集成系统中，通信和感知的性能指标用于衡量系统的服
务质量，安全和隐私的性能指标用于衡量系统的可靠性。在给定功率和信噪比
的前提下，根据不同的需求，在设计中所分配的资源也会有相应的不同。因此
分析系统内性能权衡为满足实际需求的进一步设计提供重要理论基础。 
 
基于通感系统联合性能指标的提出，可通过构建如下形式优化问题具体分析
内生安全系统的网络弹性 
 
( ) (
) ( )
maximize 
1
subject to  
A
B


+
−

X
X
X
X
 
(22) 
其中
( )
A X 表示通感联合性能指标，( )
B X 表示安全或隐私性能指标。


0,1

表
示给定的加权系数，通过不同的加权系数取值可以分析系统服务性能和安全性
能之间的性能权衡。 
3.3 可行性分析 
本项目立足6G 网络的内生安全需求，针对通信感知融合网络的开放性而衍
NSFC 2024
第 27 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 29 页 ===
生出的更复杂安全风险展开研究，沿着通感架构设计、内生安全方案以及安全
网络弹性分析路线层层递进，紧跟下一代无线网络需求，设计集通信、感知、
安全为一体的具有稳定性和准确性的系统。项目的可行性分析主要包括： 
项目总体规划方面：本项目经过前期调研，制定了明确的研究目标、充实的
研究内容和具体的研究方案。 
申请人对本项目研究中涉及的通信感知融合网络、物理层安全、鲁棒性一体
化波束设计等关键技术进行了大量的调研。为满足6G 巨系统的复杂性和开放性
需求，通信感知融合技术使能的多维性能网络构建已经成为下一代无线网络的
研究重点，然而随着融合平台信息多样性和开放性增强、信息交互更加频繁，
设计适应性强且高能效的方案保障系统安全和隐私的研究意义重大。申请人从
通感融合网络对内生安全的需求牵引出发，从内生安全信息、鲁棒性安全方案
和高效资源分配三个维度，对通感融合网络的安全设计瓶颈进行剖析，明确提
出本项目研究目标，即为面向6G 巨系统设计通信、感知、安全高效动态适配方
案，为无线通信系统的技术革新奠定稳固的基础。 
本项目拟解决的关键科学问题是从通感融合网络的真实应用场景中提炼出
来，并进行凝练。相关研究内容的设立紧密围绕关键科学问题、服务于关键科
学问题的解决，通过利用通信感知融合网络特性获取物理层安全技术重要先验
信息，将物理层安全技术良好地与通感系统结合，有利于形成一个稳定的内生
安全架构，研究进而明晰设计方案中通信、感知和安全多维性能的动态匹配机
制，对研究目标形成具有针对性的突破以及能效上的提升。在研究方案和技术
路线设计方面，本项目制定了详细的方案实施过程，明确各技术要素之间的输
入输出关系，沿着完善理论基础、构建优化问题、分析系统性能的技术路线，
对项目开展制定了细致的实施方案，确保项目目标达成。 
申请人前期研究基础与平台团队支持：申请人在该研究方向前期基础扎实，
并取得一定成果，平台与团队建设完善，可以为申请人提供充分支持。 
申请人近年来一直从事感知通信融合网络相关技术研究，尤其在信息传输安
全方向具有较深的成果积累。具体而言，申请人在通信感知融合网络信息安全
设计、波束赋形等方向发表期刊和会议论文11 篇，截止目前（2024 年2 月）引
用量360 余次，以第一作者身份在IEEE TWC 发表论文3 篇，其中2019 年发表
的《Secure radar-communication systems with malicious targets: Integrating radar, 
communications and jamming functionalities》文章首次提出了通信感知融合网络
NSFC 2024
第 28 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 30 页 ===
中的通信信息安全保障方案，文章引用量150 余次。除此之外，申请人为专著
“Integrated Sensing and Communications”撰写章节综述通信感知融合系统的安
全和隐私。本项目的研究内容是申请人前期研究成果的进一步拓展和深化，申
请人有能力克服研究过程中的技术困难，保证研究方案的顺利实施并达到预期
目标。 
申请人依托哈尔滨工业大学电子与信息工程学院以及“广东省空天通信与网
络技术重点实验室”。哈尔滨工业大学信息与通信工程深圳研究院承担多项大型
科研项目与网络技术科研团队与国内航天科研院所（航天五院、航天八院）保
持长期合作，丰富的项目经验和成熟的平台载体，能够确保本项目中各研究内
容和技术方案的顺利实施。申请人所在团队已经在无线通信领域深耕多年，能
够为本项目提供有效的理论支撑。此外，申请人所在团队与鹏城实验室宽带通
信部建立了良好的人员设备资源共享机制，本项目可共享鹏城实验室宽带通信
部的部分实验资源，对项目的实施过程提供补充支撑。 
4. 本项目的特色与创新之处； 
本项目瞄准“针对复杂开放的通信感知融合网络提出高效动态适配的安全方
案”这一研究课题瓶颈，开展通信、感知、安全一体化集成系统方案研究，力
求在保证通信感知服务质量的同时，通信感知数据实现安全交互。基于物理层
安全理论，从内生先验信息、鲁棒性安全应对方案、高效资源分配三个维度，
联合预编码优化设计，明晰性能权衡与折衷机制，综合提升通感融合复杂网络
中服务性能与安全性能的动态适配高效性，满足下一代无线网络发展的安全保
障需求。本项目的特色与创新之处具体如下： 
通感系统与物理层安全衔接：探究通感深度融合机理，在保证通信和感知
服务质量需求的基础上，通过利用感知功能获取的信息实现系统架构内生先验
信息获取，摆脱依赖外部反馈为系统带来的额外开销和风险。内生先验信息作
为通信感知网络和物理层安全的衔接点，1）物理层安全利用通信感知系统获取
信息的多样性作为先验信息，突破物理层研究中的重要瓶颈问题——依赖额外
反馈获取窃听信道先验信息；2）通信感知利用物理层安全的灵活性和动态适配
性，不受信道环境动态变化的制约，将复杂开放的巨系统设计成为集通信、感
知、安全为一体的具有稳定性和准确性的系统。 
通信、感知、安全的高效动态适配机制：本项目在设计动态适配的基础上，
进一步性能权衡机制，系统中的通信、感知、安全性能之间的关系并设计性能
NSFC 2024
第 29 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 31 页 ===
折衷方案，为资源高效性提供理论基础。系统中的通信、感知、安全可以归为
两类性能，其中通信感知作为服务性能，研究中基于此提出系统联合性能指标，
包括通感效率指标和通感效用指标。联合性能指标的提出简化了系统性能指标
的定义，将通信、感知、安全之间的权衡问题转化为“开放”与“安全”折衷
设计问题，联合加权协同优化设计，为系统根据实际需求设计高效的动态适配
方案提供理论基础。 
这一设计在6G 网络发展中，为信息和数据的可靠传输提供了基础的保障方
案。 
5. 年度研究计划及预期研究结果（包括拟组织的重要学术交流
活动、国际合作与交流计划等）。 
5.1 年度研究计划 
（1）2025 年1 月1 日 -- 2025 年12 月31 日 
 
针对融合感知功能的6G 网络特性展开研究，广泛阅读国内外关于通信感知
融合系统特性、其潜在的安全风险和技术瓶颈等领域的相关文献，与国内外相
关课题组联系并进行调研。分析不同融合方式，建立人工噪声辅助的通信感知
信息传输安全的理论框架。在此基础上，发表SCI 检索期刊论文1~2 篇，参加
无线通信、信号处理国际旗舰会议1 次。 
（2）2026 年1 月1 日 -- 2026 年12 月31 日 
 
针对感知辅助提升物理层安全开展研究，深度分析通信感知融合系统信道特
性，以通信信息传输安全和感知服务质量为核心，构造性能权衡的优化函数，
并探究高效的问题求解方案。在此基础上，发表发表SCI 检索期刊论文1~2 篇，
参加无线通信、信号处理国际旗舰会议1 次。 
（3）2027 年1 月1 日 -- 2027 年12 月31 日 
 
针对通信感知融合网络的感知隐私展开研究，在广泛阅读国内外相关文献，
总结提取融合网络中感知信息窃取方式，分别针对通信感知频谱共享和感知通
信一体化融合网络设计相应隐私保障方案。在此基础上，发表SCI 检索期刊论
文1~2 篇，参加无线通信、信号处理国际旗舰会议1 次。 
5.2 预期研究成果 
➢ 技术指标：1. 提出合理方案，采用物理层技术分别从通信信息和感知数据
两个方面实现通信感知融合网络的内生安全；2. 提出通信感知系统联合性
能指标，并分析通感系统中服务性和安全性的性能权衡。 
NSFC 2024
第 30 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 32 页 ===
➢ 学术指标：发表SCI 检索期刊论文4~6 篇，其中IEEE TCOM，IEEE TWC
等本领域顶级期刊不少于3 篇，申请发明专利1~3 项。 
➢ 人才培养：协助培养相关领域研究生2 ~3 名。 
➢ 国际交流合作计划：以本项目作为合作契机进一步扩展国际交流的深度和广
度。计划参加3 次以上无线通信、信号处理和雷达领域的国际权威学术会议，
如IEEE International Conference on Communications (ICC)，IEEE Globecom
和IEEE Radar Conference 等。拟与英国伦敦大学学院Christos Masouros 教
授，英国皇家工程院院士Lajos Hanzo 教授和雅典国立和卡波迪斯特里安大
学George C. Alexandropoulos 教授等无线通信/信号处理领域世界知名专家
继续保持紧密合作，在研究过程中定期进行学术访问交流，共同推进研究进
度。 
（二）研究基础与工作条件 
1. 研究基础（与本项目相关的研究工作积累和已取得的研究工
作成绩）； 
申请人长期从事通信感知系统的波束赋形设计、物理层安全研究，并取得了
一定的创新性成果，在通信和感知系统设计方面累积了丰富的研究基础和研究
经验。具体而言，申请人已经在本领域发表期刊和会议论文11 篇，截止目前（2024
年2 月）引用量360 余次。此外，申请人参加IEEE GLOBECOM、ICC 等国际
通信旗舰会议，受邀参加SPAWC、Asilomar、EUSIPCO 等国际会议，开展学术
交流并作口头报告。担任IEEE Trans. Wireless Commun., IEEE Trans. Commun., 
和IEEE Trans. Signal Processing 等十余个期刊/会议审稿人，与国内外学者保持
着良好的学术交流和合作。下面将从三个方面介绍研究基础。 
➢ 人工噪声辅助下的波束赋形研究方面，主要致力于研究通信感知融合网络与
传统通信网络在物理层安全方面设计差异，定义在通信感知融合系统中的安
全性能指标，在人工噪声辅助下，基于波束赋形理论设计安全方案保护通信
信息安全，为研究内容一的一体化波形设计提供充分的研究基础。其中代表
性工作有： 
[1] Nanchi Su, Fan Liu, and Christos Masouros. "Secure radar-communication 
systems with malicious targets: Integrating radar, communications and jamming 
functionalities." IEEE Transactions on Wireless Communications 20.1 (2020): 
83-95. 
NSFC 2024
第 31 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 33 页 ===
[2] Nanchi Su, Fan Liu, and Christos Masouros. "Enhancing the physical layer 
security of dual-functional radar communication systems." 2019 IEEE Global 
Communications Conference (GLOBECOM). IEEE, 2019. 
[3] Zhongxiang Wei, Fan Liu, Christos Masouros, Nanchi Su,  Athina Petropulu. 
"Toward 
multi-functional 
6G 
wireless 
networks: 
Integrating 
sensing, 
communication, and security." IEEE Communications Magazine 60.4 (2022): 
65-71. 
其中代表性工作[1]首次提出通信感知融合系统中通信信息安全设计方案，
截至2024 年3 月，文章引用量达到150 余次。 
➢ 通信感知系统内生先验信息方面，在毫米波背景下，研究通信感知信道相似
性对保密性能和感知服务的影响，为单目标感知扩展到多目标感知的场景提
供理论依据，设计采用分式规划、连续凸近似等算法解决复杂非凸优化问题，
并分析对比优化性能，这也为研究内容二积累了经验。其中代表性工作有： 
[4] Nanchi Su, Fan Liu, and Christos Masouros. "Sensing-assisted eavesdropper 
estimation: An ISAC breakthrough in physical layer security." IEEE Transactions 
on Wireless Communications (2023). 
[5] Nanchi Su, Fan Liu, and Christos Masouros. "Sensing-assisted physical layer 
security." WSA & SCC 2023; 26th International ITG Workshop on Smart 
Antennas and 13th Conference on Systems, Communications, and Coding. VDE, 
2023. 
[6] Nanchi Su, Fan Liu, and Christos Masouros. "Secure Integrated Sensing and 
Communication Systems with the Assistance of Sensing Functionality." 2023 
31st European Signal Processing Conference (EUSIPCO). IEEE, 2023. 
➢ 通感内生安全系统性能权衡方面，主要贡献在于对已有感知通信频谱共享环
境下存在的雷达隐私窃取风险进行研究，针对非基于学习机制的窃听模式提
出相应的隐私保障方案，有效防止在恶意窃听端已知信道信息的情况下估计
出雷达所在位置。在此基础上，深入分析系统中服务性能与安全性能之间性
能权衡和折衷。申请人在此场景对信息安全隐私和性能权衡的研究基础，为
本项目的研究内容三中研究场景的扩展提供有力的支撑。其中代表性的工作
有： 
[7] Nanchi Su, Fan Liu, Christos Masouros, Ahmed Al Hilli. "Security and 
NSFC 2024
第 32 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 34 页 ===
Privacy in ISAC Systems." Integrated Sensing and Communications. Singapore: 
Springer Nature Singapore, 2023. 477-506. 
[8] Nanchi Su, Fan Liu, Zhongxiang Wei, Ya-Feng Liu, Christos Masouros. 
"Secure 
dual-functional 
radar-communication 
transmission: 
Exploiting 
interference for resilience against target eavesdropping." IEEE Transactions on 
Wireless Communications 21.9 (2022): 7238-7252. 
[9] Nanchi Su, Zhongxiang Wei, Christos Masouros. "Secure dual-functional 
radar-communication system via exploiting known interference in the presence of 
clutter." 2021 IEEE 22nd International Workshop on Signal Processing Advances 
in Wireless Communications (SPAWC). IEEE, 2021. 
[10] Nanchi Su, Fan Liu, Christos Masouros, Tharmalingam Ratnarajah, Athina 
Petropulu. 
"Secure 
Dual-functional 
Radar-Communication 
Transmission: 
Hardware-Efficient Design." 2021 55th Asilomar Conference on Signals, Systems, 
and Computers. IEEE, 2021. 
2. 工作条件（包括已具备的实验条件，尚缺少的实验条件和拟
解决的途径，包括利用国家实验室、国家重点实验室和部门重点实验
室等研究基地的计划与落实情况）； 
本项目依托单位为哈尔滨工业大学。申请人所在深圳校区通信工程研究中
心长期从事无线通信、空间通信、网络优化、人工智能、信号与信息处理技术、
芯片设计与安全等研究。申请人所在团队为广东省空天通信与网络技术重点实
验室，广东省空天通信与网络技术重点实验室面向国家网络强国与航天强国的
重大战略需求，及大湾区推动卫星应用装备和空天信息技术发展的战略需求，
聚焦深空探测通信、大规模卫星网络及无人自主通信系统等电子信息与航空航
天领域的重大科技问题。该实验室面向空间信息网络领域的基础设施较为完善，
现有专用实验室面积1000 平方米，拥有各类通用、专用测试测试仪器超过100 
台（套）。其中包括中星10 号Ku 频段收发天线2 套，实验用无人机5 套，USRP 
软件无线电平台（USRP-2930、USRP-2974）35 套，LabVIEW 软件套装1 套，
R&S 的信号分析系统（FSQ26），泰克的高性能示波器（DPO070804）、频谱分
析仪（RSA3308A）、逻辑分析仪（TLA5204B），以及安捷伦的信号发生器
（E8267D）等高端设备，总价值超过3000 万元，2022 年底计划建成天基物联
网分布式数据中心（具有≥3000T的存储能力，≥5000 颗计算核心，可支持≥
NSFC 2024
第 33 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 35 页 ===
300 物理节点的10Gbps 高速数据交换机组），具备良好的科研平台基础保障。 
重点实验室主任为张钦宇教授，团队成员在职共计36人，近三年科研团队
产出了面向深空探测-测控-通信任务的高可靠信息传输技术、面向空-天-海广域
覆盖网络的高时效组网与传输技术、基于集群运动的无人机可重构编队通信与
网络技术等重要研究成果，解决了深空超远距离可靠通信、卫星广域覆盖网络
实时互联、无自主系统高效组网传输等技术难题。基于以上研究成果，获得国
家发明专利授权20余项，国家行业标准1项，发表高水平学术论文60余篇，获得
3项领域内优秀论文奖励，实现5项关键技术的成果转化。科研团队承担省部级
以上纵向科研项目25项，包含国家自然科学基金重点项目、重大仪器研制项目、
国家重点研发计划等国家重大科研项目，在研经费超过8000万。实验室具有高
水平科技人才8人，包含国家杰青及国家海内外高层次人才计划入选者。实验室
广泛开展科研合作，设立开放基金10项，服务10家企业单位，获得3项技术应用
证明。 
此外，申请人与伦敦大学学院的Christos Masouros教授、英国爱丁堡大学的
Tharm Ratnarajah教授、美国罗格斯大学的Athina Petropulu教授、雅典国立和卡
波迪斯特里安大学George C. Alexandropoulos教授和南方科技大学刘凡教授等知
名专家学者建立了良好的合作关系，共同发表了多篇学术论文，为本项目开展
国内外学术交流与合作提供了良好的条件。 
综上，本项目团队拥有良好的实验条件和科学仪器、强大的科研创新载体
和丰富的科研项目经验，可为本项目的科研活动开展、总体思路设计、技术方
案实施和项目成果验证提供充分的平台支撑。 
3. 正在承担的与本项目相关的科研项目情况（申请人正在承担
的与本项目相关的科研项目情况，包括国家自然科学基金的项目和国
家其他科技计划项目，要注明项目的资助机构、项目类别、批准号、
项目名称、获资助金额、起止年月、与本项目的关系及负责的内容等）； 
无。 
4. 完成国家自然科学基金项目情况（对申请人负责的前一个已
资助期满的科学基金项目（项目名称及批准号）完成情况、后续研究
进展及与本申请项目的关系加以详细说明。另附该项目的研究工作总
结摘要（限500 字）和相关成果详细目录）。 
无。 
NSFC 2024
第 34 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 36 页 ===
（三）其他需要说明的情况 
1. 申请人同年申请不同类型的国家自然科学基金项目情况（列
明同年申请的其他项目的项目类型、项目名称信息，并说明与本项目
之间的区别与联系）。 
无。 
2. 具有高级专业技术职务（职称）的申请人是否存在同年申请
或者参与申请国家自然科学基金项目的单位不一致的情况；如存在上
述情况，列明所涉及人员的姓名，申请或参与申请的其他项目的项目
类型、项目名称、单位名称、上述人员在该项目中是申请人还是参与
者，并说明单位不一致原因。 
无。 
3. 具有高级专业技术职务（职称）的申请人是否存在与正在承
担的国家自然科学基金项目的单位不一致的情况；如存在上述情况，
列明所涉及人员的姓名，正在承担项目的批准号、项目类型、项目名
称、单位名称、起止年月，并说明单位不一致原因。 
无。 
4. 其他。 
无。 
 
NSFC 2024
第 35 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 37 页 ===
2024版
苏南池 ( BRID: 09007.00.62796 )  简历 
哈尔滨工业大学,  哈尔滨工业大学（深圳）,  无
教育经历：
(1) 2018-09 至 2023-04, University College London, Electrical and Electronic Engineering, 博
士
(2) 2016-09 至 2018-07, 哈尔滨工业大学, 信息与通信工程, 硕士
(3) 2011-09 至 2015-07, 哈尔滨工业大学, 通信工程, 学士
博士后工作经历：
(1) 2023-11 至 今, 在站, 哈尔滨工业大学，哈尔滨工业大学（深圳）
科研与学术工作经历（博士后工作经历除外）：
无
曾使用其他证件信息：
无
近五年主持或参加的国家自然科学基金项目/课题：
无
近五年主持或参加的其他科研项目/课题（国家自然科学基金项目除外）：
(1) Engineering and Physical Sciences Research Council, N/A, EP/S028455/1, Learning to
Communicate: Deep Learning based solutions for the Physical Layer of Machine Type Communications,
2019-11 至 2023-11, 765万元, 结题, 参与
(2) Engineering and Physical Sciences Research Council, Research Grant, EP/S026622/1, Signal
Sensing, Design and Delivery for Electronic Warfare, 2019-01 至 2022-03, 445万元, 结题, 参与
(3) University Defence Research Collaboration, N/A, N/A, Signal Sensing, Design and Delivery
for Electronic Warfare, 2019-04 至 2022-03, 900万元, 结题, 参与
代表性研究成果和学术奖励情况（填写代表性论文时应根据其发表时的真实情况如实规范列
出所有作者署名，并对本人署名情况进行标注，包括：①作者署名按姓氏排序；②唯一第一
作者；③共同第一作者；④唯一通讯作者；⑤共同通讯作者；⑥其他情况）：
一、代表性论著（请在“申请书详情”界面，点开“人员信息”-“代表性成果”卡片查看对
应的全文）：
(1) Nanchi Su; Fan Liu; Zhongxiang Wei; Ya-Feng Liu; Christos Masouros ; Secure Dual-
Functional Radar-Communication Transmission: Exploiting Interference for Resilience Against
Target Eavesdropping, IEEE Transactions on Wireless Communications, 2022, 21 (9): 7238-
7252      (期刊论文)  ( 本人标注: 唯一第一作者 )
(2) Nanchi Su; Fan Liu; Christos Masouros ; Secure Radar-Communication Systems With Malicious
Targets: Integrating Radar, Communications and Jamming Functionalities, IEEE Transactions on
Wireless Communications, 2021, 20(1): 83-95      (期刊论文)  ( 本人标注: 唯一第一作者 )
(3) Nanchi Su; Fan Liu; Christos Masouros ; Sensing-Assisted Eavesdropper Estimation: An ISAC
NSFC 2024
第 36 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 38 页 ===
Breakthrough in Physical Layer Security, IEEE Transactions on Wireless Communications, 2023,
1(1): 1-1      (期刊论文)  ( 本人标注: 唯一第一作者 )
(4) Nanchi Su; Fan Liu; Christos Masouros ; Enhancing the Physical Layer Security of Dual-
Functional Radar Communication Systems, 2019 IEEE Global Communications Conference (GLOBECOM),
Waikoloa, HI, USA, 2019-12-9至2019-12-13      (会议论文)  ( 本人标注: 唯一第一作者 )
(5) Nanchi Su; Fan Liu; Christos Masouros; Tharmalingam Ratnarajah; Athina Petropulu ; Secure
Dual-functional Radar-Communication Transmission: Hardware-Efficient Design, 2021 55th Asilomar
Conference on Signals, Systems, and Computers, Pacific Grove, CA, USA, 2021-10-31至2021-11-
3      (会议论文)  ( 本人标注: 唯一第一作者 )
二、论著之外的代表性研究成果和学术奖励：
无
NSFC 2024
第 37 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 39 页 ===
附件信息
序号
附件名称
备注
附件类型
1
TWC 1
一作期刊，TWC
代表性论著
2
TWC 2
一作期刊，TWC
代表性论著
3
TWC 3
一作期刊，TWC
代表性论著
4
Conf
Globecom
代表性论著
5
Invited Conf
会议邀约投稿
代表性论著
NSFC 2024
第 38 页
国家自然科学基金申请书
2024版
版本：24110314030239435


=== 第 40 页 ===
项目名称：基于物理层安全的通感内生安全架构与网络性能动态适配机制研究
资助类型：青年科学基金项目
申请代码：F0105.移动通信
国家自然科学基金项目申请人和参与者承诺书
为了维护国家自然科学基金项目评审公平、公正，共同营造风清气正的科研生态，本人在此郑重
严格遵守《中华人民共和国科学技术进步法》《国家自然科学基金条例》《关于进一步加强科
承诺：
研诚信建设的若干意见》《关于进一步弘扬科学家精神加强作风和学风建设的意见》《关于加强科技
伦理治理的意见》以及科技部、自然科学基金委关于科研诚信建设有关规定和要求；申请材料信息真
实准确，不含任何涉密信息或敏感信息，不含任何违反法律法规或违反科研伦理规范的内容；在国家
自然科学基金项目申请、评审和执行全过程中，恪守职业规范和科学道德，遵守评审规则和工作纪
律，杜绝以下行为：
（一）抄袭、剽窃他人申请书、论文等科研成果或者伪造、篡改研究数据、研究结论；
（二）购买、代写申请书；购买、代写、代投论文，虚构同行评议专家及评议意见；购买实验数
据；
（三）违反成果发表规范、署名规范、引用规范，擅自标注或虚假标注获得科技计划等资助；
（四）在项目申请书中以高指标通过评审，在项目计划书中故意篡改降低相应指标；
（五）以任何形式探听或散布尚未公布的评审专家名单及其他评审过程中的保密信息；
（六）本人或委托他人通过各种方式和途径联系有关专家进行请托、游说、“打招呼”，违规到
评审会议驻地窥探、游说、询问等干扰评审或可能影响评审公正性的行为；
（七）向工作人员、评审专家等提供任何形式的礼品、礼金、有价证券、支付凭证、商业预付
卡、电子红包，或提供宴请、旅游、娱乐健身等任何可能影响评审公正性的活动；
（八）违反财经纪律和相关管理规定的行为；
（九）其他弄虚作假行为。
如违背上述承诺，本人愿接受国家自然科学基金委员会和相关部门做出的各项处理决定，包括但
不限于撤销科学基金资助项目，追回项目资助经费，向社会通报违规情况，取消一定期限国家自然科
学基金项目申请资格，记入科研诚信严重失信行为数据库以及接受相应的党纪政务处分等。
申请人签字：
国家自然科学基金申请书
2024版


=== 第 41 页 ===
项目名称：基于物理层安全的通感内生安全架构与网络性能动态适配机制研究
资助类型：青年科学基金项目
申请代码：F0105.移动通信
国家自然科学基金项目申请单位承诺书
为了维护国家自然科学基金项目评审公平、公正，共同营造风清气正的科研生态，本单位郑重承
申请材料中不存在违背《中华人民共和国科学技术进步法》《国家自然科学基金条例》《关于进
诺：
一步加强科研诚信建设的若干意见》《关于进一步弘扬科学家精神加强作风和学风建设的意见》《关
于加强科技伦理治理的意见》以及科技部、自然科学基金委关于科研诚信建设有关规定和要求的情
况；申请材料符合《中华人民共和国保守国家秘密法》和《科学技术保密规定》等有关法律法规和规
章制度要求，不含任何涉密信息或敏感信息；申请材料不含任何违反法律法规或违反科研伦理规范的
内容；申请人符合相应项目的申请资格；依托单位、合作研究单位、申请人及主要参与者不在限制申
报、承担或参与财政性资金支持的科技活动的期限内；在项目申请和评审活动全过程中，遵守有关评
审规则和工作纪律，杜绝以下行为：
（一）以任何形式探听或公布未公开的项目评审信息、评审专家信息及其他评审过程中的保密信
息，干扰评审专家的评审工作；
（二）组织或协助申请人/参与者向工作人员、评审专家等给予任何形式的礼品、礼金、有价证
券、支付凭证、商业预付卡、电子红包等；宴请工作人员、评审专家，或组织任何可能影响科学基金
评审公正性的活动；
（三）支持、放任或对申请人/参与者抄袭、剽窃、重复申报、提供虚假信息（含身份和学术信
息）等不当手段申报国家自然科学基金项目疏于管理；
（四）支持或协助申请人/参与者采取“打招呼”“围会”等方式影响科学基金项目评审；
（五）其他违反财经纪律和相关管理规定的行为。
如违背上述承诺，本单位愿接受自然科学基金委和相关部门做出的各项处理决定，包括但不限于
停拨或核减经费、追回项目已拨经费、取消本单位一定期限国家自然科学基金项目申请资格、记入科
研诚信严重失信行为数据库以及主要责任人接受相应党纪政务处分等。
依托单位公章:
日期：
年
月
日
国家自然科学基金申请书
2024版
