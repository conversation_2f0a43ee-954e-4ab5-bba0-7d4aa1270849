#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档读取和分析工具
用于读取PDF和Word文档
"""

import os
import pdfplumber
import fitz  # PyMuPDF
from pathlib import Path
from docx import Document

def read_pdf_with_pdfplumber(pdf_path):
    """使用pdfplumber读取PDF文件"""
    print(f"\n=== 使用 pdfplumber 读取: {pdf_path} ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            print(f"总页数: {len(pdf.pages)}")
            
            full_text = ""
            for i, page in enumerate(pdf.pages, 1):
                print(f"\n--- 第 {i} 页 ---")
                text = page.extract_text()
                if text:
                    print(text[:500] + "..." if len(text) > 500 else text)
                    full_text += f"\n\n=== 第 {i} 页 ===\n{text}"
                else:
                    print("(此页无文本内容)")
            
            return full_text
    except Exception as e:
        print(f"读取PDF时出错: {e}")
        return None

def read_pdf_with_pymupdf(pdf_path):
    """使用PyMuPDF读取PDF文件"""
    print(f"\n=== 使用 PyMuPDF 读取: {pdf_path} ===")
    
    try:
        doc = fitz.open(pdf_path)
        print(f"总页数: {doc.page_count}")
        
        full_text = ""
        for page_num in range(doc.page_count):
            page = doc[page_num]
            text = page.get_text()
            print(f"\n--- 第 {page_num + 1} 页 ---")
            if text.strip():
                print(text[:500] + "..." if len(text) > 500 else text)
                full_text += f"\n\n=== 第 {page_num + 1} 页 ===\n{text}"
            else:
                print("(此页无文本内容)")
        
        doc.close()
        return full_text
    except Exception as e:
        print(f"读取PDF时出错: {e}")
        return None

def save_text_to_file(text, output_path):
    """将提取的文本保存到文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text)
        print(f"\n文本已保存到: {output_path}")
    except Exception as e:
        print(f"保存文件时出错: {e}")

def read_docx_file(docx_path):
    """读取Word文档"""
    print(f"\n=== 读取Word文档: {docx_path} ===")

    try:
        doc = Document(docx_path)
        print(f"总段落数: {len(doc.paragraphs)}")

        full_text = ""
        for i, paragraph in enumerate(doc.paragraphs, 1):
            text = paragraph.text.strip()
            if text:
                print(f"\n--- 第 {i} 段 ---")
                print(text[:300] + "..." if len(text) > 300 else text)
                full_text += f"\n\n=== 第 {i} 段 ===\n{text}"

        # 读取表格内容
        if doc.tables:
            print(f"\n找到 {len(doc.tables)} 个表格")
            for table_idx, table in enumerate(doc.tables, 1):
                print(f"\n--- 表格 {table_idx} ---")
                table_text = f"\n\n=== 表格 {table_idx} ===\n"
                for row_idx, row in enumerate(table.rows):
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        row_text.append(cell_text)
                    table_text += " | ".join(row_text) + "\n"
                full_text += table_text
                print(table_text[:200] + "..." if len(table_text) > 200 else table_text)

        return full_text
    except Exception as e:
        print(f"读取Word文档时出错: {e}")
        return None

def main():
    """主函数"""
    # 获取当前目录下的所有PDF和Word文件
    current_dir = Path(".")
    pdf_files = list(current_dir.glob("*.pdf"))
    docx_files = list(current_dir.glob("*.docx"))

    all_files = pdf_files + docx_files

    if not all_files:
        print("当前目录下没有找到PDF或Word文件")
        return

    print(f"找到 {len(pdf_files)} 个PDF文件和 {len(docx_files)} 个Word文件:")
    for i, file in enumerate(all_files, 1):
        print(f"{i}. {file.name}")

    # 处理每个文件
    for file in all_files:
        print(f"\n{'='*60}")
        print(f"正在处理: {file.name}")
        print(f"{'='*60}")

        if file.suffix.lower() == '.pdf':
            # 处理PDF文件
            # 使用pdfplumber读取
            text_plumber = read_pdf_with_pdfplumber(file)

            # 使用PyMuPDF读取
            text_pymupdf = read_pdf_with_pymupdf(file)

            # 选择效果更好的文本
            if text_plumber and len(text_plumber.strip()) > len(text_pymupdf.strip() if text_pymupdf else ""):
                final_text = text_plumber
                method = "pdfplumber"
            elif text_pymupdf:
                final_text = text_pymupdf
                method = "PyMuPDF"
            else:
                print("无法提取文本内容")
                continue

            print(f"使用 {method} 方法提取文本，共 {len(final_text)} 个字符")

        elif file.suffix.lower() == '.docx':
            # 处理Word文件
            final_text = read_docx_file(file)
            if not final_text:
                print("无法提取文本内容")
                continue
            print(f"提取Word文档文本，共 {len(final_text)} 个字符")

        # 保存提取的文本
        output_filename = f"{file.stem}_extracted_text.txt"
        save_text_to_file(final_text, output_filename)

if __name__ == "__main__":
    main()
