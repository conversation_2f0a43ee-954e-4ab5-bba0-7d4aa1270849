#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四维帕累托边界仿真图生成
Four-Dimensional Pareto Frontier Simulation
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
from matplotlib.patches import Polygon
import matplotlib.patches as mpatches

# 设置英文字体和图表样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def generate_pareto_points(n_points=1000):
    """生成四维帕累托边界点"""
    np.random.seed(42)
    
    # 生成随机权重向量
    weights = np.random.dirichlet(np.ones(4), n_points)
    
    # 四维性能点：[Rc, Rs, PM, SA]
    points = []
    
    for w in weights:
        # 基于权重生成帕累托最优点
        # 考虑性能间的权衡关系
        
        # 通信速率 Rc
        rc = w[0] * 0.9 + (1 - w[0]) * 0.3 + np.random.normal(0, 0.05)
        
        # 保密速率 Rs (与Rc负相关)
        rs = w[1] * 0.9 + (1 - w[1]) * 0.2 - 0.3 * rc + np.random.normal(0, 0.05)
        
        # 隐私度量 PM
        pm = w[2] * 0.95 + (1 - w[2]) * 0.4 + np.random.normal(0, 0.03)
        
        # 感知精度 SA (与PM负相关)
        sa = w[3] * 0.9 + (1 - w[3]) * 0.3 - 0.2 * pm + np.random.normal(0, 0.05)
        
        # 确保在[0,1]范围内
        point = [max(0, min(1, rc)), max(0, min(1, rs)), 
                max(0, min(1, pm)), max(0, min(1, sa))]
        points.append(point)
    
    return np.array(points)

def is_pareto_optimal(point, points, tolerance=1e-6):
    """判断点是否为帕累托最优"""
    dominated = False
    for other in points:
        if np.all(other >= point - tolerance) and np.any(other > point + tolerance):
            dominated = True
            break
    return not dominated

def filter_pareto_frontier(points):
    """筛选帕累托边界点"""
    pareto_points = []
    for i, point in enumerate(points):
        if is_pareto_optimal(point, points):
            pareto_points.append(point)
    return np.array(pareto_points)

def plot_3d_projections(points, pareto_points):
    """绘制三维投影图"""
    fig = plt.figure(figsize=(20, 12))
    
    # Projection 1: Rc-Rs-PM
    ax1 = fig.add_subplot(231, projection='3d')
    ax1.scatter(points[:, 0], points[:, 1], points[:, 2],
               c='lightblue', alpha=0.3, s=20, label='Feasible Points')
    ax1.scatter(pareto_points[:, 0], pareto_points[:, 1], pareto_points[:, 2],
               c='red', alpha=0.8, s=50, label='Pareto Frontier')
    ax1.set_xlabel('Communication Rate Rc')
    ax1.set_ylabel('Secrecy Rate Rs')
    ax1.set_zlabel('Privacy Metric PM')
    # ax1.set_title('Rc-Rs-PM Space')  # Title removed
    ax1.legend()

    # Projection 2: Rc-SA-PM
    ax2 = fig.add_subplot(232, projection='3d')
    ax2.scatter(points[:, 0], points[:, 3], points[:, 2],
               c='lightgreen', alpha=0.3, s=20, label='Feasible Points')
    ax2.scatter(pareto_points[:, 0], pareto_points[:, 3], pareto_points[:, 2],
               c='red', alpha=0.8, s=50, label='Pareto Frontier')
    ax2.set_xlabel('Communication Rate Rc')
    ax2.set_ylabel('Sensing Accuracy SA')
    ax2.set_zlabel('Privacy Metric PM')
    # ax2.set_title('Rc-SA-PM Space')  # Title removed
    ax2.legend()

    # Projection 3: Rs-SA-PM
    ax3 = fig.add_subplot(233, projection='3d')
    ax3.scatter(points[:, 1], points[:, 3], points[:, 2],
               c='lightyellow', alpha=0.3, s=20, label='Feasible Points')
    ax3.scatter(pareto_points[:, 1], pareto_points[:, 3], pareto_points[:, 2],
               c='red', alpha=0.8, s=50, label='Pareto Frontier')
    ax3.set_xlabel('Secrecy Rate Rs')
    ax3.set_ylabel('Sensing Accuracy SA')
    ax3.set_zlabel('Privacy Metric PM')
    # ax3.set_title('Rs-SA-PM Space')  # Title removed
    ax3.legend()
    
    # 2D Cross-section 1: Rc-Rs (PM≈0.8)
    ax4 = fig.add_subplot(234)
    pm_mask = np.abs(points[:, 2] - 0.8) < 0.1
    pareto_pm_mask = np.abs(pareto_points[:, 2] - 0.8) < 0.1

    ax4.scatter(points[pm_mask, 0], points[pm_mask, 1],
               c='lightblue', alpha=0.5, s=30, label='Feasible Points')
    ax4.scatter(pareto_points[pareto_pm_mask, 0], pareto_points[pareto_pm_mask, 1],
               c='red', alpha=0.8, s=60, label='Pareto Frontier')
    ax4.set_xlabel('Communication Rate Rc')
    ax4.set_ylabel('Secrecy Rate Rs')
    ax4.set_title('Cross-section 1: Rc-Rs Plane (PM≈0.8)')
    ax4.legend()
    ax4.grid(True)

    # 2D Cross-section 2: PM-SA (Rc≈0.8)
    ax5 = fig.add_subplot(235)
    rc_mask = np.abs(points[:, 0] - 0.8) < 0.1
    pareto_rc_mask = np.abs(pareto_points[:, 0] - 0.8) < 0.1

    ax5.scatter(points[rc_mask, 2], points[rc_mask, 3],
               c='lightgreen', alpha=0.5, s=30, label='Feasible Points')
    ax5.scatter(pareto_points[pareto_rc_mask, 2], pareto_points[pareto_rc_mask, 3],
               c='red', alpha=0.8, s=60, label='Pareto Frontier')
    ax5.set_xlabel('Privacy Metric PM')
    ax5.set_ylabel('Sensing Accuracy SA')
    ax5.set_title('Cross-section 2: PM-SA Plane (Rc≈0.8)')
    ax5.legend()
    ax5.grid(True)

    # Performance Distribution Histogram
    ax6 = fig.add_subplot(236)
    metrics = ['Rc', 'Rs', 'PM', 'SA']
    pareto_means = np.mean(pareto_points, axis=0)
    pareto_stds = np.std(pareto_points, axis=0)

    x_pos = np.arange(len(metrics))
    ax6.bar(x_pos, pareto_means, yerr=pareto_stds,
           capsize=5, alpha=0.7, color=['blue', 'green', 'orange', 'purple'])
    ax6.set_xlabel('Performance Metrics')
    ax6.set_ylabel('Average Value')
    ax6.set_title('Pareto Frontier Performance Distribution')
    ax6.set_xticks(x_pos)
    ax6.set_xticklabels(metrics)
    ax6.grid(True)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)  # 为标题留出更多空间
    return fig

def plot_pareto_analysis(pareto_points):
    """绘制帕累托分析图"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Performance Correlation Analysis
    correlation_matrix = np.corrcoef(pareto_points.T)
    im = axes[0, 0].imshow(correlation_matrix, cmap='RdBu', vmin=-1, vmax=1)
    axes[0, 0].set_title('Performance Metrics Correlation Matrix')
    axes[0, 0].set_xticks(range(4))
    axes[0, 0].set_yticks(range(4))
    axes[0, 0].set_xticklabels(['Rc', 'Rs', 'PM', 'SA'])
    axes[0, 0].set_yticklabels(['Rc', 'Rs', 'PM', 'SA'])
    
    # 添加数值标注
    for i in range(4):
        for j in range(4):
            axes[0, 0].text(j, i, f'{correlation_matrix[i, j]:.2f}', 
                           ha='center', va='center', color='white' if abs(correlation_matrix[i, j]) > 0.5 else 'black')
    
    plt.colorbar(im, ax=axes[0, 0])
    
    # Rc vs Rs Trade-off Relationship
    axes[0, 1].scatter(pareto_points[:, 0], pareto_points[:, 1], alpha=0.6, c='blue')
    axes[0, 1].set_xlabel('Communication Rate Rc')
    axes[0, 1].set_ylabel('Secrecy Rate Rs')
    axes[0, 1].set_title('Communication-Security Trade-off')
    axes[0, 1].grid(True)

    # PM vs SA Trade-off Relationship
    axes[0, 2].scatter(pareto_points[:, 2], pareto_points[:, 3], alpha=0.6, c='green')
    axes[0, 2].set_xlabel('Privacy Metric PM')
    axes[0, 2].set_ylabel('Sensing Accuracy SA')
    axes[0, 2].set_title('Privacy-Sensing Trade-off')
    axes[0, 2].grid(True)

    # Comprehensive Performance Distribution
    comprehensive_performance = np.mean(pareto_points, axis=1)
    axes[1, 0].hist(comprehensive_performance, bins=30, alpha=0.7, color='orange')
    axes[1, 0].set_xlabel('Comprehensive Performance (4D Average)')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].set_title('Comprehensive Performance Distribution')
    axes[1, 0].grid(True)

    # Performance Variance Analysis
    performance_vars = np.var(pareto_points, axis=1)
    axes[1, 1].scatter(comprehensive_performance, performance_vars, alpha=0.6, c='red')
    axes[1, 1].set_xlabel('Comprehensive Performance')
    axes[1, 1].set_ylabel('Performance Variance')
    axes[1, 1].set_title('Performance Balance Analysis')
    axes[1, 1].grid(True)
    
    # 典型工作点标注
    # 找到几个典型点
    high_rc_idx = np.argmax(pareto_points[:, 0])
    high_rs_idx = np.argmax(pareto_points[:, 1])
    high_pm_idx = np.argmax(pareto_points[:, 2])
    high_sa_idx = np.argmax(pareto_points[:, 3])
    balanced_idx = np.argmin(performance_vars)
    
    typical_points = {
        'Communication-Priority': pareto_points[high_rc_idx],
        'Security-Priority': pareto_points[high_rs_idx],
        'Privacy-Priority': pareto_points[high_pm_idx],
        'Sensing-Priority': pareto_points[high_sa_idx],
        'Balanced-Point': pareto_points[balanced_idx]
    }
    
    # Draw Typical Points Radar Chart
    angles = np.linspace(0, 2 * np.pi, 4, endpoint=False).tolist()
    angles += angles[:1]  # Close the plot

    ax_radar = fig.add_subplot(2, 3, 6, projection='polar')

    colors = ['red', 'blue', 'green', 'orange', 'purple']
    for i, (name, point) in enumerate(typical_points.items()):
        values = point.tolist()
        values += values[:1]  # Close the plot
        ax_radar.plot(angles, values, 'o-', linewidth=2, label=name, color=colors[i])
        ax_radar.fill(angles, values, alpha=0.25, color=colors[i])

    ax_radar.set_xticks(angles[:-1])
    ax_radar.set_xticklabels(['Rc', 'Rs', 'PM', 'SA'])
    ax_radar.set_ylim(0, 1)
    ax_radar.set_title('Typical Operating Points Comparison')
    ax_radar.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    return fig, typical_points

def main():
    """Main Function"""
    print("Generating 4D Pareto Frontier Simulation...")

    # Generate data points
    points = generate_pareto_points(2000)
    print(f"Generated {len(points)} candidate points")

    # Filter Pareto frontier
    pareto_points = filter_pareto_frontier(points)
    print(f"Filtered {len(pareto_points)} Pareto optimal points")

    # Plot 3D projections and cross-sections
    fig1 = plot_3d_projections(points, pareto_points)
    fig1.suptitle('4D Pareto Frontier Multi-View Visualization', fontsize=16, fontweight='bold', y=0.96)
    plt.savefig('pareto_4d_projections.png', dpi=300, bbox_inches='tight')

    # Plot Pareto analysis
    fig2, typical_points = plot_pareto_analysis(pareto_points)
    fig2.suptitle('4D Performance Trade-off Analysis', fontsize=16, fontweight='bold')
    plt.savefig('pareto_4d_analysis.png', dpi=300, bbox_inches='tight')

    # Output typical points information
    print("\nTypical Operating Points:")
    for name, point in typical_points.items():
        print(f"{name}: Rc={point[0]:.3f}, Rs={point[1]:.3f}, PM={point[2]:.3f}, SA={point[3]:.3f}")

    # Calculate performance statistics
    print(f"\nPareto Frontier Statistics:")
    print(f"Average Performance: Rc={np.mean(pareto_points[:, 0]):.3f}, Rs={np.mean(pareto_points[:, 1]):.3f}, "
          f"PM={np.mean(pareto_points[:, 2]):.3f}, SA={np.mean(pareto_points[:, 3]):.3f}")

    correlation_matrix = np.corrcoef(pareto_points.T)
    print(f"\nPerformance Correlations:")
    print(f"Rc-Rs Correlation: {correlation_matrix[0, 1]:.3f}")
    print(f"PM-SA Correlation: {correlation_matrix[2, 3]:.3f}")
    print(f"(Rc,Rs)-(PM,SA) Cross-Correlation: {np.mean(correlation_matrix[:2, 2:]):.3f}")

    # plt.show()  # Comment out to avoid blocking
    print("\nSimulation Complete! Images saved as 'pareto_4d_projections.png' and 'pareto_4d_analysis.png'")

if __name__ == "__main__":
    main()
