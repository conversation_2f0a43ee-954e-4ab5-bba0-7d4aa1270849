（2）隐私去标识性量化体系构建

为完成对物理层隐私的科学度量，解决传统安全指标无法衡量身份信息安全性的问题，本部分将基于前述建立的信号模型，从信息论和估计理论两个互补的维度，构建一套全新的、能够定量评估隐私保护水平的指标体系。

⚫ 基于信息熵的身份模糊度（Identity Ambiguity）构建

该指标旨在从"分类"的角度量化窃听者对目标真实身份的不确定性。假设窃听者试图从一个包含K个可能身份的集合Ψ = {ψ₁, ψ₂, ..., ψₖ}中识别出当前目标。身份模糊度可定义为窃听者在获得观测信号y^E后，对目标身份的后验熵：

IA(Ψ|y^E) = -∑ᵢ₌₁ᴷ P(ψᵢ|y^E) log₂ P(ψᵢ|y^E)                    (6)

其中P(ψᵢ|y^E)是窃听者基于观测信号y^E对身份ψᵢ的后验概率，可通过贝叶斯定理计算：

P(ψᵢ|y^E) = P(y^E|ψᵢ)P(ψᵢ) / ∑ⱼ₌₁ᴷ P(y^E|ψⱼ)P(ψⱼ)                (7)

其中P(y^E|ψᵢ)是在身份为ψᵢ条件下的似然函数，P(ψᵢ)是身份的先验概率分布。身份模糊度IA的取值范围为[0, log₂K]，其中：
- IA = 0表示窃听者能够完全确定目标身份，隐私完全泄露；
- IA = log₂K表示窃听者对所有身份的后验概率相等，隐私得到最佳保护。

为进一步刻画隐私保护的相对水平，定义归一化身份模糊度：

IA_norm = IA(Ψ|y^E) / log₂K                                      (8)

该指标的物理意义在于：它直接反映了窃听者进行身份识别的困难程度，数值越大表示隐私保护效果越好。

⚫ 基于克拉美-罗下界（CRLB）的特征估计误差界构建

该指标从"估计"的角度量化窃听者对用户敏感物理特征的理论最佳估计精度。考虑窃听者试图估计目标的敏感特征参数向量θ = [A, f]ᵀ（包括微动幅度A和频率f），其中θ包含了可用于身份识别的关键生物特征信息。

根据估计理论，任何无偏估计器θ̂的协方差矩阵都满足克拉美-罗下界：

Cov(θ̂) ≽ J⁻¹(θ)                                               (9)

其中J(θ)是Fisher信息矩阵，其第(i,j)个元素定义为：

[J(θ)]ᵢⱼ = E[∂²ln p(y^E|θ)/∂θᵢ∂θⱼ]                           (10)

基于前述建立的似然函数p(y^E|θ)，可以推导出Fisher信息矩阵的具体表达式。对于简谐振动模型，Fisher信息矩阵可表示为：

J(θ) = 2Re{∑ₜ₌₁ᵀ [∂μₜ/∂θ]ᴴ R⁻¹ [∂μₜ/∂θ]}                      (11)

其中μₜ = E[y^E(t)]是信号的期望值，R是噪声协方差矩阵，T是观测时长。

定义特征估计误差界（Feature Estimation Error Bound, FEEB）为：

FEEB = √(tr(J⁻¹(θ)))                                           (12)

该指标的物理意义为：窃听者对敏感特征参数估计的理论最小均方根误差。FEEB值越大，表示窃听者的估计精度越差，隐私保护效果越好。

为便于不同场景下的比较，进一步定义相对特征估计误差界：

FEEB_rel = FEEB / ||θ||₂                                        (13)

⚫ 综合隐私度量指标构建

为全面评估系统的隐私保护水平，结合上述两个互补的度量维度，构建综合隐私度量指标：

PM = α · IA_norm + (1-α) · (FEEB_rel/FEEB_max)                  (14)

其中α ∈ [0,1]是权重因子，用于平衡身份分类不确定性和特征估计困难度两个维度的重要性；FEEB_max是在给定系统参数下FEEB的理论最大值。综合隐私度量PM的取值范围为[0,1]，数值越大表示隐私保护水平越高。

⚫ 隐私泄露率（Privacy Leakage Rate）定义

为了与传统的保密容量等安全指标形成对应，进一步定义隐私泄露率，用于刻画单位时间内身份信息的泄露速度：

PLR = I(Ψ; Y^E) / T                                            (15)

其中I(Ψ; Y^E)是身份集合Ψ与窃听者观测信号Y^E之间的互信息，T是观测时长。互信息可表示为：

I(Ψ; Y^E) = H(Ψ) - H(Ψ|Y^E)                                   (16)

其中H(Ψ) = log₂K是身份的先验熵，H(Ψ|Y^E)是前述定义的身份模糊度IA。因此：

PLR = (log₂K - IA) / T                                         (17)

隐私泄露率PLR的物理意义为：窃听者每单位时间获得的关于目标身份的信息量（以比特为单位）。PLR = 0表示完全隐私保护，PLR越大表示隐私泄露越严重。

⚫ 多维隐私风险评估框架

考虑到实际应用中目标可能具有多种类型的敏感特征（如步态特征、呼吸模式、设备类型等），建立多维隐私风险评估框架。设目标具有M种不同类型的敏感特征，对应参数向量为θ₁, θ₂, ..., θₘ，则多维隐私度量可表示为：

PM_multi = ∑ᵢ₌₁ᴹ wᵢ · PMᵢ                                     (18)

其中wᵢ是第i种特征的重要性权重（∑ᵢ₌₁ᴹ wᵢ = 1），PMᵢ是针对第i种特征计算的隐私度量。权重wᵢ可根据不同特征的敏感程度和应用场景需求进行调整。

⚫ 动态隐私度量

考虑到通感系统的时变特性，进一步建立动态隐私度量模型。定义时刻t的瞬时隐私度量为：

PM(t) = α · IA_norm(t) + (1-α) · (FEEB_rel(t)/FEEB_max)        (19)

以及滑动窗口内的平均隐私度量：

PM_avg(t) = (1/W) ∑ₜ'₌ₜ₋ₘ₊₁ᵗ PM(t')                           (20)

其中W是滑动窗口长度。动态隐私度量能够实时反映系统隐私保护水平的变化，为自适应隐私保护策略提供依据。

通过上述量化体系的建立，物理层隐私首次实现了从"定性描述"到"定量分析"的跨越，为后续的隐私保护方案设计和性能优化提供了科学、精确的目标函数和约束条件。该量化体系具有以下显著特点：

1）理论严谨性：基于信息论和估计理论的坚实数学基础，确保了度量指标的理论正确性；
2）实用可操作性：所有指标均可通过系统参数和信号模型进行计算，具备工程实现的可行性；
3）全面性：从身份分类和特征估计两个互补维度，全面刻画了隐私泄露风险；
4）灵活性：支持多维特征和动态场景，能够适应不同的应用需求和系统配置。

该量化体系的建立，不仅填补了ISAC系统隐私度量的理论空白，更为构建下一代具备内生隐私保护能力的通感一体化网络奠定了重要的理论基石。
