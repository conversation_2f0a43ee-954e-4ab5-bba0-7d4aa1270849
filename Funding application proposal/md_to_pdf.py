#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown to PDF converter with math support
"""

import markdown
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import sys
import os

def convert_md_to_pdf(md_file, output_file=None):
    """Convert Markdown file to PDF"""
    
    if not os.path.exists(md_file):
        print(f"Error: File {md_file} not found!")
        return False
    
    # Read markdown file
    with open(md_file, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # Convert markdown to HTML
    md = markdown.Markdown(extensions=['extra', 'codehilite', 'toc'])
    html_content = md.convert(md_content)
    
    # Create complete HTML document with CSS for better formatting
    html_doc = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Research Content</title>
        <style>
            body {{
                font-family: "Times New Roman", serif;
                line-height: 1.6;
                margin: 2cm;
                font-size: 12pt;
            }}
            h1, h2, h3, h4, h5, h6 {{
                color: #333;
                margin-top: 1.5em;
                margin-bottom: 0.5em;
            }}
            h1 {{ font-size: 18pt; }}
            h2 {{ font-size: 16pt; }}
            h3 {{ font-size: 14pt; }}
            p {{
                margin-bottom: 1em;
                text-align: justify;
            }}
            code {{
                background-color: #f5f5f5;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: "Courier New", monospace;
            }}
            pre {{
                background-color: #f5f5f5;
                padding: 1em;
                border-radius: 5px;
                overflow-x: auto;
            }}
            .math {{
                font-family: "Times New Roman", serif;
                font-style: italic;
            }}
            ol, ul {{
                margin-bottom: 1em;
            }}
            li {{
                margin-bottom: 0.5em;
            }}
        </style>
    </head>
    <body>
        {html_content}
    </body>
    </html>
    """
    
    # Generate output filename if not provided
    if output_file is None:
        base_name = os.path.splitext(md_file)[0]
        output_file = f"{base_name}.pdf"
    
    try:
        # Convert HTML to PDF
        font_config = FontConfiguration()
        html_obj = HTML(string=html_doc)
        
        # Create CSS for better PDF formatting
        css = CSS(string='''
            @page {
                size: A4;
                margin: 2cm;
            }
            body {
                font-size: 11pt;
            }
        ''', font_config=font_config)
        
        html_obj.write_pdf(output_file, stylesheets=[css], font_config=font_config)
        print(f"Successfully converted {md_file} to {output_file}")
        return True
        
    except Exception as e:
        print(f"Error converting to PDF: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python md_to_pdf.py <markdown_file> [output_file]")
        sys.exit(1)
    
    md_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    success = convert_md_to_pdf(md_file, output_file)
    sys.exit(0 if success else 1)
