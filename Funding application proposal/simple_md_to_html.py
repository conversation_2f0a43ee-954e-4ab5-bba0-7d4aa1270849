#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Markdown to HTML converter
"""

import markdown
import sys
import os
import re

def convert_md_to_html(md_file, output_file=None):
    """Convert Markdown file to HTML with math support"""
    
    if not os.path.exists(md_file):
        print(f"Error: File {md_file} not found!")
        return False
    
    # Read markdown file
    with open(md_file, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # Convert markdown to HTML
    md = markdown.Markdown(extensions=['extra', 'codehilite', 'toc'])
    html_content = md.convert(md_content)
    
    # Add MathJax support for math formulas
    html_doc = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>研究内容1：隐私去标识性量化体系构建</title>
        <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
        <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
        <script>
            window.MathJax = {{
                tex: {{
                    inlineMath: [['$', '$'], ['\\\\(', '\\\\)']],
                    displayMath: [['$$', '$$'], ['\\\\[', '\\\\]']],
                    tags: 'ams'
                }},
                options: {{
                    skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
                }}
            }};
        </script>
        <style>
            body {{
                font-family: "Times New Roman", "SimSun", serif;
                line-height: 1.6;
                max-width: 800px;
                margin: 0 auto;
                padding: 2em;
                font-size: 14px;
                color: #333;
            }}
            h1, h2, h3, h4, h5, h6 {{
                color: #2c3e50;
                margin-top: 2em;
                margin-bottom: 1em;
                font-weight: bold;
            }}
            h1 {{ 
                font-size: 24px; 
                border-bottom: 2px solid #3498db;
                padding-bottom: 0.5em;
            }}
            h2 {{ 
                font-size: 20px; 
                border-bottom: 1px solid #bdc3c7;
                padding-bottom: 0.3em;
            }}
            h3 {{ 
                font-size: 18px; 
                color: #34495e;
            }}
            p {{
                margin-bottom: 1.2em;
                text-align: justify;
                text-indent: 2em;
            }}
            .math {{
                font-family: "Times New Roman", serif;
            }}
            ol, ul {{
                margin-bottom: 1.5em;
                padding-left: 2em;
            }}
            li {{
                margin-bottom: 0.8em;
                line-height: 1.8;
            }}
            code {{
                background-color: #f8f9fa;
                padding: 2px 6px;
                border-radius: 3px;
                font-family: "Courier New", monospace;
                font-size: 0.9em;
                color: #e74c3c;
            }}
            pre {{
                background-color: #f8f9fa;
                padding: 1.5em;
                border-radius: 8px;
                overflow-x: auto;
                border-left: 4px solid #3498db;
                margin: 1.5em 0;
            }}
            blockquote {{
                border-left: 4px solid #3498db;
                margin: 1.5em 0;
                padding-left: 1em;
                color: #7f8c8d;
                font-style: italic;
            }}
            .mjx-chtml {{
                font-size: 1.1em !important;
            }}
            .equation-number {{
                float: right;
                margin-top: 0.5em;
                color: #7f8c8d;
            }}
            @media print {{
                body {{
                    font-size: 12px;
                    line-height: 1.4;
                }}
                h1, h2, h3 {{
                    page-break-after: avoid;
                }}
                .mjx-chtml {{
                    font-size: 1em !important;
                }}
            }}
        </style>
    </head>
    <body>
        {html_content}
        <script>
            // 等待MathJax加载完成后可以打印
            window.addEventListener('load', function() {{
                setTimeout(function() {{
                    console.log('Document ready for printing');
                }}, 2000);
            }});
        </script>
    </body>
    </html>
    """
    
    # Generate output filename if not provided
    if output_file is None:
        base_name = os.path.splitext(md_file)[0]
        output_file = f"{base_name}.html"
    
    try:
        # Write HTML file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_doc)
        
        print(f"Successfully converted {md_file} to {output_file}")
        print(f"You can open {output_file} in a browser and print to PDF")
        return True
        
    except Exception as e:
        print(f"Error converting to HTML: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python simple_md_to_html.py <markdown_file> [output_file]")
        sys.exit(1)
    
    md_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    success = convert_md_to_html(md_file, output_file)
    sys.exit(0 if success else 1)
