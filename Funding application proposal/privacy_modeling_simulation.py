#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2.2.1节 通感系统物理层隐私去标识性建模与量化表征仿真
Privacy De-identification Modeling and Quantitative Characterization Simulation
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
from scipy.stats import norm
from scipy.optimize import minimize
import matplotlib.patches as mpatches

# 设置英文字体和图表样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def generate_mimo_isac_system():
    """生成MIMO通感一体化系统参数"""
    # 系统参数
    Nt = 8  # 基站天线数
    Ne = 4  # 窃听者天线数
    fc = 28e9  # 载频 28GHz
    c = 3e8  # 光速
    wavelength = c / fc
    
    # 信道参数
    np.random.seed(42)
    h_BT = (np.random.randn(1, Nt) + 1j * np.random.randn(1, Nt)) / np.sqrt(2)  # 基站到目标
    h_TE = (np.random.randn(Ne, 1) + 1j * np.random.randn(Ne, 1)) / np.sqrt(2)  # 目标到窃听者
    h_BE = (np.random.randn(Ne, Nt) + 1j * np.random.randn(Ne, Nt)) / np.sqrt(2)  # 基站到窃听者
    
    return Nt, Ne, wavelength, h_BT, h_TE, h_BE

def scattering_coefficient_model(t, A, f, phi):
    """散射系数模型 α(t) = α₀·exp(j·4π/λ·d(t))"""
    alpha_0 = 1.0  # 基础散射强度
    wavelength = 0.01071  # 28GHz对应波长
    d_t = A * np.cos(2 * np.pi * f * t + phi)  # 目标位移
    alpha_t = alpha_0 * np.exp(1j * 4 * np.pi / wavelength * d_t)
    return alpha_t

def calculate_identity_ambiguity(likelihood_matrix):
    """计算身份模糊度 IA"""
    # 归一化似然函数得到后验概率
    posterior_prob = likelihood_matrix / np.sum(likelihood_matrix, axis=1, keepdims=True)
    
    # 计算信息熵
    entropy = -np.sum(posterior_prob * np.log2(posterior_prob + 1e-10), axis=1)
    
    # 归一化身份模糊度
    K = likelihood_matrix.shape[1]  # 候选身份数
    IA_norm = entropy / np.log2(K)
    
    return IA_norm

def calculate_feeb(fisher_info_matrix):
    """计算特征估计误差界 FEEB"""
    # CRLB = trace(J^(-1))
    try:
        crlb = np.trace(np.linalg.inv(fisher_info_matrix))
        feeb = np.sqrt(crlb)
    except:
        feeb = np.inf
    return feeb

def calculate_privacy_metric(IA, FEEB, alpha=0.5):
    """计算综合隐私度量 PM"""
    FEEB_max = 10.0  # 设定最大FEEB值用于归一化
    FEEB_rel = np.minimum(FEEB / FEEB_max, 1.0)
    PM = alpha * IA + (1 - alpha) * FEEB_rel
    return PM

def plot_individual_figures():
    """分别绘制6个独立的图"""
    figures = []
    
    # 图1: 散射系数时域特性
    fig1 = plt.figure(figsize=(10, 6))
    ax1 = fig1.add_subplot(111)
    t = np.linspace(0, 1, 1000)
    A_values = [0.01, 0.02, 0.03]  # 不同振幅
    f = 10  # 频率
    phi = 0  # 相位

    for A in A_values:
        alpha_t = scattering_coefficient_model(t, A, f, phi)
        ax1.plot(t, np.abs(alpha_t), label=f'A={A}m', linewidth=2)

    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('|α(t)|')
    ax1.set_title('Scattering Coefficient Magnitude')
    ax1.legend()
    ax1.grid(True)
    plt.tight_layout()
    figures.append(('scattering_coefficient.png', fig1))
    
    # 图2: 微多普勒效应
    fig2 = plt.figure(figsize=(10, 6))
    ax2 = fig2.add_subplot(111)
    frequencies = np.linspace(-50, 50, 1000)
    A_values = [0.01, 0.02, 0.03]

    for A in A_values:
        # 简化的微多普勒谱
        doppler_spectrum = np.exp(-(frequencies - 20*A*1000)**2 / (2*(5*A*1000)**2))
        ax2.plot(frequencies, doppler_spectrum, label=f'A={A}m', linewidth=2)

    ax2.set_xlabel('Frequency (Hz)')
    ax2.set_ylabel('Spectrum Magnitude')
    ax2.set_title('Micro-Doppler Spectrum')
    ax2.legend()
    ax2.grid(True)
    plt.tight_layout()
    figures.append(('micro_doppler_spectrum.png', fig2))
    
    # 图3: 似然函数分布
    fig3 = plt.figure(figsize=(10, 6))
    ax3 = fig3.add_subplot(111)
    theta_range = np.linspace(0, 0.05, 100)
    snr_values = [10, 20, 30]  # 不同SNR

    for snr in snr_values:
        # 简化的似然函数（高斯形式）
        sigma = 0.01 / np.sqrt(snr)
        likelihood = norm.pdf(theta_range, 0.025, sigma)
        ax3.plot(theta_range, likelihood, label=f'SNR={snr}dB', linewidth=2)

    ax3.set_xlabel('Parameter θ')
    ax3.set_ylabel('Likelihood p(y^E|θ)')
    ax3.set_title('Likelihood Function Distribution')
    ax3.legend()
    ax3.grid(True)
    plt.tight_layout()
    figures.append(('likelihood_function.png', fig3))
    
    # 图4: 身份模糊度随SNR变化
    fig4 = plt.figure(figsize=(10, 6))
    ax4 = fig4.add_subplot(111)
    snr_range = np.linspace(0, 30, 31)
    K_values = [4, 8, 16]  # 不同候选身份数

    for K in K_values:
        IA_values = []
        for snr in snr_range:
            # 模拟似然矩阵
            likelihood_matrix = np.random.exponential(1 + snr/10, (100, K))
            IA = calculate_identity_ambiguity(likelihood_matrix)
            IA_values.append(np.mean(IA))

        ax4.plot(snr_range, IA_values, 'o-', label=f'K={K}', linewidth=2, markersize=4)

    ax4.set_xlabel('SNR (dB)')
    ax4.set_ylabel('Identity Ambiguity IA')
    ax4.set_title('Identity Ambiguity vs SNR')
    ax4.legend()
    ax4.grid(True)
    plt.tight_layout()
    figures.append(('identity_ambiguity.png', fig4))
    
    # 图5: FEEB随参数变化
    fig5 = plt.figure(figsize=(10, 6))
    ax5 = fig5.add_subplot(111)
    param_range = np.linspace(0.01, 0.1, 20)
    snr_values = [10, 20, 30]

    for snr in snr_values:
        feeb_values = []
        for param in param_range:
            # 简化的Fisher信息矩阵
            fisher_matrix = np.array([[snr * param**2, 0], [0, snr * param**2]])
            feeb = calculate_feeb(fisher_matrix)
            feeb_values.append(feeb)

        ax5.plot(param_range, feeb_values, 'o-', label=f'SNR={snr}dB', linewidth=2, markersize=4)

    ax5.set_xlabel('Parameter Value')
    ax5.set_ylabel('FEEB')
    ax5.set_title('Feature Estimation Error Bound')
    ax5.legend()
    ax5.grid(True)
    ax5.set_yscale('log')
    plt.tight_layout()
    figures.append(('feeb_analysis.png', fig5))
    
    # 图6: 综合隐私度量3D图
    fig6 = plt.figure(figsize=(12, 9))
    ax6 = fig6.add_subplot(111, projection='3d')
    IA_grid = np.linspace(0, 1, 20)
    FEEB_grid = np.linspace(0, 1, 20)
    IA_mesh, FEEB_mesh = np.meshgrid(IA_grid, FEEB_grid)

    alpha_values = [0.3, 0.5, 0.7]
    colors = ['blue', 'red', 'green']

    for i, alpha in enumerate(alpha_values):
        PM_mesh = calculate_privacy_metric(IA_mesh, FEEB_mesh, alpha)
        ax6.plot_surface(IA_mesh, FEEB_mesh, PM_mesh, alpha=0.6, color=colors[i], label=f'α={alpha}')

    # 设置轴标签，使用更保守的参数
    ax6.set_xlabel('Identity Ambiguity IA')
    ax6.set_ylabel('FEEB (normalized)')
    ax6.set_zlabel('Privacy Metric PM')
    ax6.set_title('Comprehensive Privacy Metric')

    # 设置轴的范围确保标签可见
    ax6.set_xlim(0, 1)
    ax6.set_ylim(0, 1)
    ax6.set_zlim(0, 1)

    # 调整视角
    ax6.view_init(elev=25, azim=45)

    # 使用tight_layout而不是subplots_adjust
    plt.tight_layout()
    figures.append(('privacy_metric_3d.png', fig6))

    return figures

def plot_performance_benchmark():
    """绘制性能基准分析图"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 四维性能空间可视化
    ax1 = axes[0, 0]
    np.random.seed(42)
    n_points = 200
    
    # 生成四维性能点
    Rc = np.random.beta(2, 2, n_points)
    Rs = np.random.beta(2, 3, n_points) * (1 - 0.5 * Rc)  # 与Rc负相关
    PM = np.random.beta(3, 2, n_points)
    SA = np.random.beta(2, 2, n_points) * (1 - 0.3 * PM)  # 与PM负相关
    
    # 绘制Rc-Rs散点图，颜色表示PM
    scatter = ax1.scatter(Rc, Rs, c=PM, s=50, alpha=0.7, cmap='viridis')
    ax1.set_xlabel('Communication Rate Rc')
    ax1.set_ylabel('Secrecy Rate Rs')
    ax1.set_title('4D Performance Space (Rc-Rs-PM)')
    plt.colorbar(scatter, ax=ax1, label='Privacy Metric PM')
    ax1.grid(True)
    
    # 2. 归一化性能指标
    ax2 = axes[0, 1]
    metrics = ['Rc', 'Rs', 'PM', 'SA']
    original_means = [np.mean(Rc), np.mean(Rs), np.mean(PM), np.mean(SA)]
    normalized_means = [0.6, 0.4, 0.7, 0.5]  # 归一化后的值
    
    x = np.arange(len(metrics))
    width = 0.35
    
    ax2.bar(x - width/2, original_means, width, label='Original', alpha=0.7)
    ax2.bar(x + width/2, normalized_means, width, label='Normalized', alpha=0.7)
    ax2.set_xlabel('Performance Metrics')
    ax2.set_ylabel('Value')
    ax2.set_title('Performance Normalization')
    ax2.set_xticks(x)
    ax2.set_xticklabels(metrics)
    ax2.legend()
    ax2.grid(True)
    
    # 3. 加权综合性能指标
    ax3 = axes[0, 2]
    weight_scenarios = {
        'Communication-Priority': [0.5, 0.2, 0.15, 0.15],
        'Security-Priority': [0.2, 0.5, 0.2, 0.1],
        'Privacy-Priority': [0.15, 0.15, 0.6, 0.1],
        'Sensing-Priority': [0.2, 0.1, 0.2, 0.5],
        'Balanced': [0.25, 0.25, 0.25, 0.25]
    }
    
    scenario_names = list(weight_scenarios.keys())
    phi_values = []
    
    for weights in weight_scenarios.values():
        phi = np.dot(weights, normalized_means)
        phi_values.append(phi)
    
    bars = ax3.bar(scenario_names, phi_values, color=['blue', 'red', 'green', 'orange', 'purple'], alpha=0.7)
    ax3.set_xlabel('Scenarios')
    ax3.set_ylabel('Weighted Performance Φ')
    ax3.set_title('Weighted Comprehensive Performance')
    ax3.tick_params(axis='x', rotation=45)
    ax3.grid(True)
    
    # 添加数值标签
    for bar, value in zip(bars, phi_values):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{value:.3f}', ha='center', va='bottom')
    
    # 4. 隐私泄露率分析
    ax4 = axes[1, 0]
    time_range = np.linspace(0, 10, 100)
    K_values = [4, 8, 16]
    
    for K in K_values:
        # 模拟隐私泄露率随时间变化
        base_rate = np.log2(K) / 10  # 基础泄露率
        noise = 0.1 * np.random.randn(len(time_range))
        PLR = base_rate * (1 + 0.1 * time_range) + noise
        PLR = np.maximum(PLR, 0)  # 确保非负
        
        ax4.plot(time_range, PLR, label=f'K={K}', linewidth=2)
    
    ax4.set_xlabel('Time (s)')
    ax4.set_ylabel('Privacy Leakage Rate PLR')
    ax4.set_title('Privacy Leakage Rate Analysis')
    ax4.legend()
    ax4.grid(True)
    
    # 5. 性能基准点对比
    ax5 = axes[1, 1]
    benchmark_points = {
        'Baseline': [0.3, 0.2, 0.4, 0.3],
        'Optimized': [0.6, 0.4, 0.7, 0.5],
        'Target': [0.8, 0.6, 0.8, 0.7]
    }
    
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    ax5 = plt.subplot(2, 3, 5, projection='polar')
    colors = ['red', 'blue', 'green']
    
    for i, (name, values) in enumerate(benchmark_points.items()):
        values_plot = values + values[:1]  # 闭合
        ax5.plot(angles, values_plot, 'o-', linewidth=2, label=name, color=colors[i])
        ax5.fill(angles, values_plot, alpha=0.25, color=colors[i])
    
    ax5.set_xticks(angles[:-1])
    ax5.set_xticklabels(metrics)
    ax5.set_ylim(0, 1)
    ax5.set_title('Performance Benchmark Comparison')
    ax5.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    # 6. 优化收敛过程
    ax6 = axes[1, 2]
    iterations = np.arange(1, 51)
    
    # 模拟不同算法的收敛过程
    algorithms = {
        'Gradient Descent': 0.5 * np.exp(-iterations/20) + 0.4,
        'Newton Method': 0.6 * np.exp(-iterations/10) + 0.4,
        'Proposed Method': 0.7 * np.exp(-iterations/8) + 0.4
    }
    
    for name, convergence in algorithms.items():
        ax6.plot(iterations, convergence, label=name, linewidth=2)
    
    ax6.set_xlabel('Iterations')
    ax6.set_ylabel('Objective Function Value')
    ax6.set_title('Optimization Convergence')
    ax6.legend()
    ax6.grid(True)
    ax6.set_yscale('log')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    return fig

def main():
    """主函数"""
    print("Generating Privacy Modeling and Quantification Simulation...")

    # 生成系统参数
    Nt, Ne, wavelength, h_BT, h_TE, h_BE = generate_mimo_isac_system()
    print(f"MIMO-ISAC System: Nt={Nt}, Ne={Ne}, λ={wavelength:.4f}m")

    # 绘制6个独立的图
    figures = plot_individual_figures()

    # 保存所有图像
    print("\nSaving individual figures...")
    for filename, fig in figures:
        plt.figure(fig.number)
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"- Saved: {filename}")
        plt.close(fig)

    print("\nSimulation Complete!")
    print("Generated 6 individual figures for Section 2.2.1")

if __name__ == "__main__":
    main()
