graph TB
    subgraph "智慧农业物联网传感器网络"
        subgraph "农田区域A"
            SENSOR1[温湿度传感器1<br/>节点ID: S001<br/>位置: 东北角]
            SENSOR2[土壤传感器1<br/>节点ID: S002<br/>pH值监测]
            SENSOR3[光照传感器1<br/>节点ID: S003<br/>光强度检测]
            SENSOR4[风速传感器1<br/>节点ID: S004<br/>气象监测]
        end
        
        subgraph "农田区域B"
            SENSOR5[温湿度传感器2<br/>节点ID: S005<br/>位置: 东南角]
            SENSOR6[土壤传感器2<br/>节点ID: S006<br/>NPK监测]
            SENSOR7[水分传感器1<br/>节点ID: S007<br/>土壤湿度]
            SENSOR8[CO2传感器1<br/>节点ID: S008<br/>大气监测]
        end
        
        subgraph "农田区域C"
            SENSOR9[温湿度传感器3<br/>节点ID: S009<br/>位置: 西北角]
            SENSOR10[土壤传感器3<br/>节点ID: S010<br/>EC值监测]
            SENSOR11[光照传感器2<br/>节点ID: S011<br/>紫外线检测]
            SENSOR12[雨量传感器1<br/>节点ID: S012<br/>降雨量监测]
        end
        
        subgraph "农田区域D"
            SENSOR13[温湿度传感器4<br/>节点ID: S013<br/>位置: 西南角]
            SENSOR14[土壤传感器4<br/>节点ID: S014<br/>有机质监测]
            SENSOR15[水分传感器2<br/>节点ID: S015<br/>灌溉控制]
            SENSOR16[病虫害传感器<br/>节点ID: S016<br/>图像识别]
        end
        
        subgraph "中心设施"
            GATEWAY[汇聚节点<br/>农田中心<br/>数据收集处理]
            BACKUP_GW[备用汇聚节点<br/>冗余设计<br/>故障切换]
            WEATHER[气象站<br/>环境基准<br/>校准数据]
        end
        
        subgraph "云端服务"
            CLOUD[云端服务器<br/>数据存储分析<br/>AI决策支持]
            DATABASE[数据库<br/>历史数据<br/>模式分析]
            AI_ENGINE[AI分析引擎<br/>智能决策<br/>预测模型]
        end
    end
    
    subgraph "通信网络"
        subgraph "第一层密钥管理"
            KEY_S1[传感器-汇聚节点密钥<br/>K_S001_G, K_S002_G, ...<br/>物理层密钥生成]
            KEY_UPDATE[密钥更新机制<br/>24小时周期<br/>3个备用密钥]
        end
        
        subgraph "第二层加密"
            PKI[PKI公钥加密<br/>RSA-2048<br/>汇聚节点-云端]
            AES[AES-256数据加密<br/>对称加密<br/>高速数据传输]
            CERT[数字证书验证<br/>身份认证<br/>完整性保护]
        end
        
        subgraph "通信协议"
            LORA[LoRa通信<br/>433MHz频段<br/>长距离低功耗]
            WIFI[WiFi备用<br/>2.4GHz频段<br/>高速数据传输]
            CELLULAR[4G/5G蜂窝<br/>应急通信<br/>广域覆盖]
        end
    end
    
    subgraph "安全威胁与防护"
        subgraph "威胁检测"
            THREAT1[通信中断威胁<br/>传感器S006<br/>信道增益异常下降]
            THREAT2[定向干扰攻击<br/>433MHz频段<br/>恶意信号源]
            THREAT3[数据篡改威胁<br/>传感器数据<br/>异常值检测]
        end
        
        subgraph "防护措施"
            FREQ_SWITCH[频率切换<br/>433MHz→470MHz<br/>备用频段启用]
            ERROR_CORRECT[纠错编码增强<br/>冗余度1/2→1/3<br/>可靠性提升]
            MULTI_HOP[多跳路由<br/>邻近节点中继<br/>绕过故障节点]
            ALERT[告警机制<br/>网络管理中心<br/>实时监控]
        end
    end
    
    subgraph "性能监测"
        METRICS[网络性能指标<br/>通信恢复时间: 15秒<br/>数据完整性: 99.8%<br/>能耗增加: <10%<br/>覆盖范围: 10公里半径<br/>节点容量: 1000个传感器<br/>数据传输成功率: 99.7%]
    end
    
    %% 数据传输连接
    SENSOR1 -.->|K_S001| GATEWAY
    SENSOR2 -.->|K_S002| GATEWAY
    SENSOR3 -.->|K_S003| GATEWAY
    SENSOR4 -.->|K_S004| GATEWAY
    SENSOR5 -.->|K_S005| GATEWAY
    SENSOR6 -.->|K_S006| GATEWAY
    SENSOR7 -.->|K_S007| GATEWAY
    SENSOR8 -.->|K_S008| GATEWAY
    SENSOR9 -.->|K_S009| GATEWAY
    SENSOR10 -.->|K_S010| GATEWAY
    SENSOR11 -.->|K_S011| GATEWAY
    SENSOR12 -.->|K_S012| GATEWAY
    SENSOR13 -.->|K_S013| GATEWAY
    SENSOR14 -.->|K_S014| GATEWAY
    SENSOR15 -.->|K_S015| GATEWAY
    SENSOR16 -.->|K_S016| GATEWAY
    
    GATEWAY -.->|PKI+AES| CLOUD
    BACKUP_GW -.->|备用链路| CLOUD
    WEATHER -.->|校准数据| GATEWAY
    
    CLOUD --> DATABASE
    CLOUD --> AI_ENGINE
    
    %% 威胁攻击
    THREAT1 -.->|攻击| SENSOR6
    THREAT2 -.->|干扰| LORA
    THREAT3 -.->|篡改| SENSOR10
    
    %% 防护响应
    THREAT1 --> FREQ_SWITCH
    THREAT2 --> ERROR_CORRECT
    THREAT3 --> MULTI_HOP
    
    FREQ_SWITCH --> ALERT
    ERROR_CORRECT --> ALERT
    MULTI_HOP --> ALERT
    
    %% 备用机制
    SENSOR6 -.->|切换| WIFI
    SENSOR10 -.->|中继| SENSOR9
    GATEWAY -.->|故障切换| BACKUP_GW
    BACKUP_GW -.->|应急| CELLULAR
    
    %% 样式设置
    style GATEWAY fill:#F3E5F5,stroke:#9C27B0,stroke-width:3px
    style BACKUP_GW fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style WEATHER fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    
    style CLOUD fill:#E3F2FD,stroke:#2196F3,stroke-width:3px
    style DATABASE fill:#E1F5FE,stroke:#0277BD,stroke-width:2px
    style AI_ENGINE fill:#E8EAF6,stroke:#3F51B5,stroke-width:2px
    
    style SENSOR1 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR2 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR3 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR4 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR5 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR6 fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    style SENSOR7 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR8 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR9 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR10 fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    style SENSOR11 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR12 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR13 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR14 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR15 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style SENSOR16 fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    
    style THREAT1 fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    style THREAT2 fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    style THREAT3 fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    
    style FREQ_SWITCH fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style ERROR_CORRECT fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style MULTI_HOP fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style ALERT fill:#FF8A65,stroke:#FF5722,stroke-width:2px
    
    style METRICS fill:#F5F5F5,stroke:#757575,stroke-width:2px
