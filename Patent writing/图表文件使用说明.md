# 图1架构图文件使用说明

## 📁 已创建的文件

### 1. `图1-低空通感安全架构图.svg`
- **格式**：SVG矢量图
- **用途**：可直接在浏览器中打开查看，或插入到Word/PowerPoint等文档中
- **特点**：矢量格式，可无损缩放，适合打印和展示

### 2. `图1-架构图-Mermaid源码.mmd`
- **格式**：Mermaid源码
- **用途**：可在支持Mermaid的工具中渲染为图表
- **特点**：文本格式，易于修改和版本控制

## 🛠️ 使用方法

### 方法1：直接使用SVG文件
1. 双击`图1-低空通感安全架构图.svg`文件
2. 浏览器会自动打开并显示图表
3. 右键选择"另存为"可保存为其他格式

### 方法2：在线Mermaid编辑器
1. 访问 https://mermaid.live/
2. 将`图1-架构图-Mermaid源码.mmd`文件内容复制粘贴到编辑器
3. 可以实时编辑和预览
4. 支持导出为SVG、PNG等格式

### 方法3：使用Mermaid CLI工具
```bash
# 安装Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 转换为SVG
mmdc -i 图1-架构图-Mermaid源码.mmd -o 输出图表.svg

# 转换为PNG
mmdc -i 图1-架构图-Mermaid源码.mmd -o 输出图表.png
```

### 方法4：在Markdown文档中使用
在支持Mermaid的Markdown编辑器（如Typora、GitHub等）中：
```markdown
```mermaid
[将mmd文件内容粘贴到这里]
```
```

## 🎨 图表说明

### 颜色编码
- **蓝色**：无人机节点
- **紫色**：物联网传感器节点
- **绿色**：汇聚节点/基站
- **橙色**：核心处理模块
- **红色**：安全加固模块
- **红色虚线**：威胁攻击

### 连接线说明
- **实线箭头**：主要数据流
- **虚线箭头**：控制信号和反馈
- **标注文字**：数据类型和流向

## 📝 修改建议

如需修改图表，建议：
1. 编辑`.mmd`源码文件
2. 在Mermaid在线编辑器中预览
3. 重新生成SVG文件

## 🔧 技术支持

如果需要其他格式或有修改需求，可以：
1. 使用在线转换工具
2. 安装专业的图表编辑软件
3. 联系技术支持进行定制

---
*此图表为专利交底书《一种低空网络通信感知一体化物理层安全架构》的核心技术架构图*
