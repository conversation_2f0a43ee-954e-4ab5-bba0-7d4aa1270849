flowchart TD
    Start([开始]) --> InputF[接收特征向量F]
    InputF --> CheckF{特征向量有效性检查}
    CheckF -->|无效| ErrorF[特征向量错误]
    CheckF -->|有效| InitParams[初始化量化参数]
    
    subgraph S201 ["步骤S201: 特征量化"]
        InitParams --> CalcRange[计算特征值范围]
        CalcRange --> Formula1["计算量化间隔: Δ = (F_max - F_min) / 2^b"]
        Formula1 --> AdaptQuant[自适应量化比特数调整]
        AdaptQuant --> Formula2["量化公式: Q_i = floor((F_i - F_min) / Δ)"]
        Formula2 --> GenBits[生成量化比特序列B_A]
        GenBits --> ValidateQuant{量化质量检查}
        ValidateQuant -->|质量不足| AdjustBits[调整量化参数]
        AdjustBits --> Formula2
        ValidateQuant -->|质量合格| SendIndex[发送量化索引]
    end
    
    subgraph S202 ["步骤S202: 信息协商"]
        SendIndex --> ReceiveIndex[接收对方量化索引]
        ReceiveIndex --> CompareIndex[比较量化结果]
        CompareIndex --> CalcError[计算量化误差]
        CalcError --> ErrorCorrect[级联码纠错处理]
        
        ErrorCorrect --> Syndrome[计算校验子]
        Syndrome --> CheckSyndrome{校验子为零?}
        CheckSyndrome -->|否| CorrectBits[纠正错误比特]
        CorrectBits --> UpdateBits[更新比特序列]
        UpdateBits --> CheckSyndrome
        CheckSyndrome -->|是| Reconciled[获得一致比特序列B_reconciled]
        
        Reconciled --> VerifyConsistency{一致性验证}
        VerifyConsistency -->|失败| RetryRecon[重新协商]
        RetryRecon --> SendIndex
        VerifyConsistency -->|成功| ConsistentBits[确认一致比特序列]
    end
    
    subgraph S203 ["步骤S203: 密钥提取"]
        ConsistentBits --> PrivacyAmp[隐私放大处理]
        PrivacyAmp --> HashFunc["应用哈希函数: K = Hash(B_reconciled)"]
        HashFunc --> KeyLength[确定密钥长度]
        KeyLength --> ExtractKey[提取最终密钥K]
        ExtractKey --> KeyValidation{密钥有效性检查}
        KeyValidation -->|无效| RegenerateKey[重新生成密钥]
        RegenerateKey --> PrivacyAmp
        KeyValidation -->|有效| FinalKey[输出最终密钥K]
    end
    
    subgraph Security ["安全性评估"]
        FinalKey --> EntropyCheck[熵值评估]
        EntropyCheck --> RandomnessTest[随机性测试]
        RandomnessTest --> SecurityLevel{安全等级评估}
        SecurityLevel -->|不足| EnhanceSecurity[增强安全处理]
        SecurityLevel -->|充足| SecureKey[安全密钥确认]
        
        EnhanceSecurity --> AddEntropy[增加熵源]
        AddEntropy --> PrivacyAmp
    end
    
    SecureKey --> StoreKey[存储密钥]
    StoreKey --> NotifyNext[通知安全加固模块]
    NotifyNext --> End([结束])
    
    ErrorF --> End
    
    subgraph Params ["关键参数说明"]
        ParamList["• b: 量化比特数<br/>• Δ: 量化间隔<br/>• B_A, B_B: 双方比特序列<br/>• B_reconciled: 协商后序列<br/>• K: 最终密钥<br/>• Hash: 哈希函数"]
    end
    
    %% 样式设置
    style Start fill:#4CAF50,stroke:#2E7D32,stroke-width:3px,color:#fff
    style End fill:#F44336,stroke:#C62828,stroke-width:3px,color:#fff
    style Formula1 fill:#2196F3,stroke:#1565C0,stroke-width:2px,color:#fff
    style Formula2 fill:#2196F3,stroke:#1565C0,stroke-width:2px,color:#fff
    style HashFunc fill:#2196F3,stroke:#1565C0,stroke-width:2px,color:#fff
    style FinalKey fill:#FF9800,stroke:#E65100,stroke-width:2px,color:#fff
    style SecureKey fill:#4CAF50,stroke:#2E7D32,stroke-width:2px,color:#fff
    style NotifyNext fill:#9C27B0,stroke:#6A1B9A,stroke-width:2px,color:#fff
    style ParamList fill:#F5F5F5,stroke:#757575,stroke-width:1px
    
    style ErrorF fill:#F44336,stroke:#C62828,stroke-width:2px,color:#fff
    style EnhanceSecurity fill:#FF5722,stroke:#D84315,stroke-width:2px,color:#fff
    
    %% 决策节点样式
    style CheckF fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style ValidateQuant fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style CheckSyndrome fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style VerifyConsistency fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style KeyValidation fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style SecurityLevel fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    
    %% 处理节点样式
    style CalcRange fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style AdaptQuant fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style GenBits fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style SendIndex fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style ReceiveIndex fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style CompareIndex fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style CalcError fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style ErrorCorrect fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style Syndrome fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style CorrectBits fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style UpdateBits fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style Reconciled fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style ConsistentBits fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style PrivacyAmp fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    style KeyLength fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    style ExtractKey fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    style EntropyCheck fill:#E3F2FD,stroke:#2196F3,stroke-width:1px
    style RandomnessTest fill:#E3F2FD,stroke:#2196F3,stroke-width:1px
    style AddEntropy fill:#E3F2FD,stroke:#2196F3,stroke-width:1px
    style StoreKey fill:#FFEBEE,stroke:#F44336,stroke-width:1px
