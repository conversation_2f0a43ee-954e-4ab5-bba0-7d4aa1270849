# 图2：物理层特征确定模块流程图 - 使用指南

## 📁 已创建的文件

### 1. `图2-物理层特征确定模块流程图-优化版.mmd`
- **特点**：完整详细版本，包含所有技术细节
- **适用**：技术文档、专利申请、学术论文
- **内容**：四个主要处理阶段，完整的错误处理机制

### 2. `图2-物理层特征确定模块流程图-简化版.mmd`
- **特点**：简化版本，更容易渲染和显示
- **适用**：演示文稿、概要说明、快速预览
- **内容**：核心流程步骤，简化的决策分支

## 🛠️ 使用方法

### 方法1：在线Mermaid编辑器
1. 访问：https://mermaid.live/
2. 复制任一mmd文件内容
3. 粘贴到左侧编辑器
4. 右侧实时预览
5. 导出为SVG/PNG格式

### 方法2：本地Mermaid CLI
```bash
# 安装
npm install -g @mermaid-js/mermaid-cli

# 渲染优化版
mmdc -i 图2-物理层特征确定模块流程图-优化版.mmd -o 图2-优化版.svg

# 渲染简化版
mmdc -i 图2-物理层特征确定模块流程图-简化版.mmd -o 图2-简化版.png
```

### 方法3：在Markdown中使用
```markdown
```mermaid
[将mmd文件内容粘贴到这里]
```
```

## 🎯 流程图说明

### 核心处理阶段：

#### 1. 信道状态信息获取
- 生成导频信号
- 发送和接收处理
- 信道响应测量
- 数学模型：R(t) = H(t) × P(t) + N(t)

#### 2. 信道特征参数提取
- 计算四个关键参数：增益、相位、时延、噪声
- 系统类型判断：OFDM vs 非OFDM
- 特征参数汇总

#### 3. 特征向量构建
- 构建完整特征向量F
- 归一化和滤波处理
- 有效性验证

#### 4. 质量评估与优化
- SNR质量评估
- 自动重试机制
- 错误处理和恢复

### 关键参数：
- **K**：子载波数量
- **L**：多径分量数量
- **H(t)**：信道冲激响应
- **N(t)**：加性白高斯噪声
- **SNR**：信噪比阈值

## 🎨 颜色编码

### 优化版颜色方案：
- **绿色**：开始节点和成功状态
- **红色**：结束节点和错误状态
- **蓝色**：数学公式和关键计算
- **橙色**：主要处理步骤
- **紫色**：模块间接口
- **浅蓝色**：决策判断节点

### 简化版颜色方案：
- **绿色**：开始
- **红色**：结束
- **橙色**：输出
- **紫色**：模块传递
- **蓝色**：决策节点

## 📝 修改建议

### 常见修改需求：
1. **节点名称**：根据具体实现调整
2. **处理步骤**：增加或简化某些步骤
3. **决策条件**：修改判断阈值
4. **颜色方案**：匹配文档风格

### 修改示例：
```mermaid
# 修改节点名称
A[系统启动] --> B[参数配置]

# 修改决策条件
K{SNR > 15dB?}

# 修改颜色
style A fill:#新颜色,color:#文字颜色
```

## 🔧 故障排除

### 常见问题：
1. **渲染失败**：检查语法错误，特别是括号匹配
2. **中文显示问题**：确保编辑器支持UTF-8编码
3. **样式不生效**：检查style语句的节点ID是否正确
4. **连接线错误**：确认箭头语法正确

### 解决方案：
- 使用简化版进行测试
- 逐步添加复杂元素
- 检查在线编辑器的错误提示
- 参考Mermaid官方文档

## 📤 导出建议

### 高质量导出设置：
- **格式**：SVG（矢量图，适合缩放）
- **分辨率**：300 DPI（用于打印）
- **尺寸**：A4横向或自定义
- **背景**：透明或白色

### 用途建议：
- **专利文档**：使用优化版，SVG格式
- **演示文稿**：使用简化版，PNG格式
- **网页展示**：使用简化版，SVG格式
- **打印材料**：使用优化版，高分辨率PNG

---

**推荐使用流程**：
1. 先用简化版测试渲染效果
2. 确认无误后使用优化版
3. 根据用途选择合适的导出格式
4. 必要时进行微调和定制
