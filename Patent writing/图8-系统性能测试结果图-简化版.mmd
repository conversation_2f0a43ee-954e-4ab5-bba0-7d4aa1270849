graph TB
    subgraph "密钥生成性能"
        SNR_LOW[低信噪比<br/>5-10dB<br/>成功率: 85-92%]
        SNR_MID[中信噪比<br/>15-20dB<br/>成功率: 97-99%]
        SNR_HIGH[高信噪比<br/>25dB<br/>成功率: 99.8%]
    end
    
    subgraph "威胁检测性能"
        DETECT_GOOD[高性能算法<br/>神经网络/SVM<br/>准确率: >96%]
        DETECT_MID[中性能算法<br/>随机森林<br/>准确率: 95%]
        DETECT_LOW[快速算法<br/>决策树<br/>准确率: 91%]
    end
    
    subgraph "系统开销"
        SMALL[小规模<br/>10-50节点<br/>CPU: <25%]
        MEDIUM[中规模<br/>100节点<br/>CPU: 45%]
        LARGE[大规模<br/>500-1000节点<br/>CPU: 78-95%]
    end
    
    subgraph "动态调整"
        NORMAL[正常状态<br/>100%性能]
        THREAT[威胁检测<br/>25ms响应]
        ADJUST[参数调整<br/>35ms执行]
        RECOVER[性能恢复<br/>95%水平]
    end
    
    subgraph "综合评估"
        PROPOSED[本发明方案<br/>高安全低开销]
        TRADITIONAL[传统方案<br/>中安全高开销]
        IMPROVE[性能提升<br/>安全+35%<br/>效率+28%]
    end
    
    %% 连接
    NORMAL --> THREAT
    THREAT --> ADJUST
    ADJUST --> RECOVER
    
    PROPOSED --> IMPROVE
    TRADITIONAL --> IMPROVE
    
    %% 样式
    style SNR_HIGH fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style SNR_MID fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style SNR_LOW fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    
    style DETECT_GOOD fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style DETECT_MID fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style DETECT_LOW fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    
    style SMALL fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style MEDIUM fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style LARGE fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    
    style NORMAL fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style THREAT fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style ADJUST fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style RECOVER fill:#E3F2FD,stroke:#2196F3,stroke-width:2px
    
    style PROPOSED fill:#E8F5E8,stroke:#4CAF50,stroke-width:3px
    style TRADITIONAL fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style IMPROVE fill:#E3F2FD,stroke:#2196F3,stroke-width:3px
