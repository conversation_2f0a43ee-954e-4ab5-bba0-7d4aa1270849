graph TB
    subgraph "空域环境"
        subgraph "无人机集群"
            UAV1[无人机A<br/>主控节点]
            UAV2[无人机B<br/>协作节点]
            UAV3[无人机C<br/>协作节点]
            UAV4[无人机D<br/>协作节点]
            UAV5[无人机E<br/>边缘节点]
        end

        subgraph "地面设施"
            GCS[地面控制站<br/>指挥中心]
            BS1[基站1<br/>通信中继]
            BS2[基站2<br/>备用基站]
        end

        subgraph "威胁源"
            JAMMER[恶意干扰源<br/>2.45GHz干扰]
            SPOOF[GPS欺骗器<br/>位置欺骗]
            EAVES[窃听设备<br/>信号截获]
        end
    end
    
    subgraph "通信链路"
        subgraph "主要通信链路"
            LINK1[UAV-A ↔ UAV-B<br/>密钥: K_AB<br/>频率: 2.4GHz]
            LINK2[UAV-A ↔ UAV-C<br/>密钥: K_AC<br/>频率: 2.42GHz]
            LINK3[UAV-B ↔ UAV-C<br/>密钥: K_BC<br/>频率: 2.44GHz]
            LINK4[UAV-A ↔ GCS<br/>密钥: K_AG<br/>频率: 5.8GHz]
        end
        
        subgraph "备用通信链路"
            BACKUP1[备用链路1<br/>470MHz]
            BACKUP2[备用链路2<br/>900MHz]
            SATELLITE[卫星通信<br/>应急链路]
        end
    end
    
    subgraph "安全机制"
        subgraph "物理层安全"
            CSI[信道状态信息<br/>H_t = α·e^jφ]
            KEY_GEN[密钥生成<br/>K = Hash_F]
            ENCRYPT[数据加密<br/>C = AES_M_K]
        end
        
        subgraph "威胁检测"
            SPECTRUM[频谱监测<br/>-60dBm干扰检测]
            GPS_CHECK[GPS验证<br/>位置偏差>500m]
            BEHAVIOR[行为分析<br/>异常轨迹检测]
        end
        
        subgraph "自适应调整"
            POWER_CTRL[功率控制<br/>20dBm→23dBm]
            FREQ_HOP[频率跳跃<br/>10ms间隔]
            MOD_ADAPT[调制自适应<br/>64QAM→16QAM]
        end
    end
    
    %% 通信连接
    UAV1 -.->|K_AB| UAV2
    UAV1 -.->|K_AC| UAV3
    UAV1 -.->|K_AD| UAV4
    UAV2 -.->|K_BC| UAV3
    UAV3 -.->|K_CD| UAV4
    UAV4 -.->|K_DE| UAV5
    
    UAV1 -.->|K_AG| GCS
    UAV2 -.->|中继| BS1
    UAV5 -.->|边缘| BS2
    
    %% 威胁攻击
    JAMMER -.->|干扰攻击| UAV2
    SPOOF -.->|GPS欺骗| UAV3
    EAVES -.->|窃听| LINK1
    
    %% 安全防护
    UAV1 --> CSI
    UAV2 --> CSI
    UAV3 --> CSI
    CSI --> KEY_GEN
    KEY_GEN --> ENCRYPT
    
    UAV1 --> SPECTRUM
    UAV2 --> GPS_CHECK
    UAV3 --> BEHAVIOR
    
    SPECTRUM --> POWER_CTRL
    GPS_CHECK --> FREQ_HOP
    BEHAVIOR --> MOD_ADAPT
    
    %% 备用机制
    UAV2 -.->|切换| BACKUP1
    UAV3 -.->|切换| BACKUP2
    GCS -.->|应急| SATELLITE
    
    subgraph "性能指标"
        METRICS[实时性能指标<br/>密钥生成成功率: 99.2%<br/>威胁检测准确率: 95.8%<br/>通信中断时间: <50ms<br/>数据传输速率: 85%正常速率<br/>覆盖范围: 半径10km<br/>同时在线: 50架无人机]
    end
    
    %% 样式设置
    style UAV1 fill:#E3F2FD,stroke:#1976D2,stroke-width:3px
    style UAV2 fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style UAV3 fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style UAV4 fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style UAV5 fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    
    style GCS fill:#F3E5F5,stroke:#9C27B0,stroke-width:3px
    style BS1 fill:#F0F4C3,stroke:#827717,stroke-width:2px
    style BS2 fill:#F0F4C3,stroke:#827717,stroke-width:2px
    
    style JAMMER fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    style SPOOF fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    style EAVES fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    
    style CSI fill:#E1F5FE,stroke:#0277BD,stroke-width:2px
    style KEY_GEN fill:#E1F5FE,stroke:#0277BD,stroke-width:2px
    style ENCRYPT fill:#E1F5FE,stroke:#0277BD,stroke-width:2px
    
    style SPECTRUM fill:#FCE4EC,stroke:#C2185B,stroke-width:2px
    style GPS_CHECK fill:#FCE4EC,stroke:#C2185B,stroke-width:2px
    style BEHAVIOR fill:#FCE4EC,stroke:#C2185B,stroke-width:2px
    
    style POWER_CTRL fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style FREQ_HOP fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style MOD_ADAPT fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    
    style SATELLITE fill:#E8EAF6,stroke:#3F51B5,stroke-width:2px
    style METRICS fill:#F5F5F5,stroke:#757575,stroke-width:2px
