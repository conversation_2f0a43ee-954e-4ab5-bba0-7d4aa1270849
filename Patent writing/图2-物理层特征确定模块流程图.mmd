flowchart TD
    Start([开始]) --> Init[系统初始化]
    Init --> PilotGen[生成导频信号/训练序列]
    
    subgraph "信道状态信息获取 (步骤S101)"
        PilotGen --> SendPilot[发送节点发送导频序列P(t)]
        SendPilot --> Receive[接收节点接收信号R(t)]
        Receive --> Formula1["R(t) = H(t) × P(t) + N(t)"]
        Formula1 --> MeasureCSI[测量信道冲激响应H(t)]
    end
    
    subgraph "信道特征参数提取 (步骤S102)"
        MeasureCSI --> CalcGain[计算信道增益|H(f)|]
        CalcGain --> CalcPhase[计算相位φ(f)]
        CalcPhase --> CalcDelay[计算多径时延τ]
        CalcDelay --> CalcNoise[计算噪声功率σ²]
        
        CalcNoise --> CheckOFDM{是否为OFDM系统?}
        CheckOFDM -->|是| OFDMProc["频域处理: H_k = |H_k| × e^(jφ_k)"]
        CheckOFDM -->|否| DirectProc[直接处理时域信号]
        
        OFDMProc --> ExtractSub[提取各子载波特征]
        DirectProc --> ExtractTime[提取时域特征]
        ExtractSub --> Combine[特征参数汇总]
        ExtractTime --> Combine
    end
    
    subgraph "特征向量构建 (步骤S103)"
        Combine --> BuildVector["构建特征向量F"]
        BuildVector --> VectorFormula["F = [|H_1|, |H_2|, ..., |H_K|, φ_1, φ_2|, ..., φ_K, τ_1, τ_2, ..., τ_L]"]
        VectorFormula --> Normalize[特征归一化处理]
        Normalize --> Filter[滤波去噪]
        Filter --> Validate[特征有效性验证]
    end
    
    subgraph "质量评估与优化"
        Validate --> QualityCheck{信道质量评估}
        QualityCheck -->|SNR > 阈值| HighQuality[高质量特征]
        QualityCheck -->|SNR ≤ 阈值| LowQuality[低质量特征]
        
        LowQuality --> Enhance[特征增强处理]
        Enhance --> Retry{重试次数 < 最大值?}
        Retry -->|是| PilotGen
        Retry -->|否| ErrorHandle[错误处理]
        
        HighQuality --> Output[输出特征向量F]
        ErrorHandle --> Output
    end
    
    Output --> NextModule[传递给密钥生成模块]
    NextModule --> End([结束])
    
    %% 参数标注
    subgraph "关键参数说明"
        Params["• K: 子载波数量<br/>• L: 多径分量数量<br/>• H(t): 信道冲激响应<br/>• N(t): 加性白高斯噪声<br/>• SNR: 信噪比阈值"]
    end
    
    %% 样式设置
    style Start fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    style End fill:#ffebee,stroke:#f44336,stroke-width:2px
    style Formula1 fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    style VectorFormula fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    style Output fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    style NextModule fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    style Params fill:#f5f5f5,stroke:#757575,stroke-width:1px
    
    style HighQuality fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    style LowQuality fill:#ffebee,stroke:#f44336,stroke-width:2px
    style ErrorHandle fill:#ffebee,stroke:#f44336,stroke-width:2px
    
    classDef processBox fill:#fff3e0,stroke:#ff9800,stroke-width:1px
    classDef decisionBox fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef formulaBox fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    
    class SendPilot,Receive,MeasureCSI,CalcGain,CalcPhase,CalcDelay,CalcNoise processBox
    class CheckOFDM,QualityCheck,Retry decisionBox
    class Formula1,VectorFormula formulaBox
