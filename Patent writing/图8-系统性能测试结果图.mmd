graph TB
    subgraph "密钥生成性能测试"
        subgraph "不同信噪比下的密钥生成速率"
            SNR_TEST[信噪比测试条件]
            SNR_5[SNR = 5dB<br/>密钥生成速率: 0.2 bits/channel use<br/>成功率: 85.3%]
            SNR_10[SNR = 10dB<br/>密钥生成速率: 0.4 bits/channel use<br/>成功率: 92.7%]
            SNR_15[SNR = 15dB<br/>密钥生成速率: 0.6 bits/channel use<br/>成功率: 97.8%]
            SNR_20[SNR = 20dB<br/>密钥生成速率: 0.8 bits/channel use<br/>成功率: 99.2%]
            SNR_25[SNR = 25dB<br/>密钥生成速率: 1.0 bits/channel use<br/>成功率: 99.8%]
        end
        
        subgraph "不同信道环境测试"
            LOS[视距传播LOS<br/>密钥生成速率: 0.8 bits/channel use<br/>一致性: 98.5%]
            NLOS[非视距传播NLOS<br/>密钥生成速率: 0.5 bits/channel use<br/>一致性: 95.2%]
            RICH[多径丰富环境<br/>密钥生成速率: 1.0 bits/channel use<br/>一致性: 99.1%]
            MOBILE[高移动性场景<br/>密钥生成速率: 0.6 bits/channel use<br/>一致性: 93.8%]
        end
    end
    
    subgraph "威胁检测性能测试"
        subgraph "各种攻击场景下的防御成功率"
            ATTACK_TEST[攻击场景测试]
            EAVESDROP[窃听攻击<br/>检测准确率: N/A<br/>防御成功率: >99.9%<br/>信息泄露: <10^-6]
            JAMMING[干扰攻击<br/>检测准确率: 96.5%<br/>响应时间: <50ms<br/>防御成功率: 94.2%]
            SPOOFING[欺骗攻击<br/>检测准确率: 93.8%<br/>响应时间: <100ms<br/>防御成功率: 91.7%]
            REPLAY[重放攻击<br/>检测准确率: 98.2%<br/>响应时间: <30ms<br/>防御成功率: 97.5%]
        end
        
        subgraph "威胁检测算法性能对比"
            ISOLATION[孤立森林算法<br/>检测准确率: 94.2%<br/>误报率: 3.1%<br/>处理时间: 15ms]
            SVM_ALG[支持向量机<br/>检测准确率: 96.8%<br/>误报率: 2.3%<br/>处理时间: 25ms]
            NEURAL[神经网络<br/>检测准确率: 97.5%<br/>误报率: 1.8%<br/>处理时间: 35ms]
            DECISION[决策树<br/>检测准确率: 91.3%<br/>误报率: 4.2%<br/>处理时间: 8ms]
            RANDOM[随机森林<br/>检测准确率: 95.7%<br/>误报率: 2.7%<br/>处理时间: 18ms]
        end
    end
    
    subgraph "系统开销分析"
        subgraph "系统开销随网络规模变化"
            SCALE_TEST[网络规模测试]
            NODES_10[10个节点<br/>CPU使用率: 8%<br/>内存占用: 256MB<br/>带宽开销: 2%]
            NODES_50[50个节点<br/>CPU使用率: 25%<br/>内存占用: 512MB<br/>带宽开销: 4%]
            NODES_100[100个节点<br/>CPU使用率: 45%<br/>内存占用: 1GB<br/>带宽开销: 5%]
            NODES_500[500个节点<br/>CPU使用率: 78%<br/>内存占用: 3GB<br/>带宽开销: 8%]
            NODES_1000[1000个节点<br/>CPU使用率: 95%<br/>内存占用: 5GB<br/>带宽开销: 12%]
        end
        
        subgraph "不同安全等级的性能影响"
            SECURITY_TEST[安全等级测试]
            LOW_SEC[低安全等级<br/>吞吐量: 100Mbps<br/>延迟: 5ms<br/>CPU开销: +5%]
            MID_SEC[中安全等级<br/>吞吐量: 85Mbps<br/>延迟: 8ms<br/>CPU开销: +15%]
            HIGH_SEC[高安全等级<br/>吞吐量: 70Mbps<br/>延迟: 12ms<br/>CPU开销: +25%]
            ULTRA_SEC[超高安全等级<br/>吞吐量: 55Mbps<br/>延迟: 18ms<br/>CPU开销: +40%]
        end
    end
    
    subgraph "动态参数调整效果"
        subgraph "威胁等级变化时的参数调整过程"
            THREAT_CHANGE[威胁等级变化测试]
            NORMAL_STATE[正常状态<br/>威胁等级: 低<br/>功率: 20dBm<br/>调制: 64QAM<br/>吞吐量: 100%]
            THREAT_DETECT[威胁检测<br/>检测时间: 25ms<br/>威胁等级: 中<br/>决策时间: 15ms]
            PARAM_ADJUST[参数调整<br/>功率: 20→23dBm<br/>调制: 64QAM→16QAM<br/>调整时间: 35ms]
            PERFORMANCE[性能变化<br/>吞吐量: 100%→75%<br/>可靠性: 95%→98%<br/>抗干扰: +15dB]
            RECOVERY[威胁消除<br/>恢复时间: 45ms<br/>性能恢复: 95%<br/>稳定时间: 2秒]
        end
        
        subgraph "通信质量指标实时变化"
            QUALITY_TEST[通信质量测试]
            BER_NORMAL[正常误码率<br/>BER: 10^-6<br/>稳定性: 优秀]
            BER_THREAT[威胁下误码率<br/>BER: 10^-4<br/>稳定性: 一般]
            BER_ADJUST[调整后误码率<br/>BER: 10^-5<br/>稳定性: 良好]
            
            THROUGHPUT[吞吐量变化<br/>正常: 50Mbps<br/>威胁: 30Mbps<br/>调整: 42Mbps]
            LATENCY[延迟变化<br/>正常: 10ms<br/>威胁: 25ms<br/>调整: 15ms]
        end
    end
    
    subgraph "综合性能评估"
        subgraph "系统整体性能指标"
            OVERALL[综合性能评估]
            AVAILABILITY[系统可用性<br/>正常运行时间: 99.9%<br/>故障恢复时间: <30秒<br/>平均无故障时间: 8760小时]
            SCALABILITY[可扩展性<br/>最大节点数: 10000<br/>并发连接: 1000<br/>水平扩展: 支持]
            RELIABILITY[可靠性<br/>数据完整性: 99.99%<br/>传输成功率: 99.7%<br/>错误恢复率: 99.5%]
            EFFICIENCY[效率指标<br/>能耗效率: 90%<br/>频谱效率: 85%<br/>计算效率: 88%]
        end
        
        subgraph "与传统方案对比"
            COMPARISON[对比测试结果]
            TRADITIONAL[传统PKI方案<br/>安全强度: 中等<br/>计算开销: 高<br/>密钥分发: 复杂]
            PROPOSED[本发明方案<br/>安全强度: 高<br/>计算开销: 低<br/>密钥分发: 自动]
            IMPROVEMENT[性能提升<br/>安全性: +35%<br/>效率: +28%<br/>可靠性: +22%]
        end
    end
    
    %% 测试流程连接
    SNR_TEST --> SNR_5
    SNR_TEST --> SNR_10
    SNR_TEST --> SNR_15
    SNR_TEST --> SNR_20
    SNR_TEST --> SNR_25
    
    ATTACK_TEST --> EAVESDROP
    ATTACK_TEST --> JAMMING
    ATTACK_TEST --> SPOOFING
    ATTACK_TEST --> REPLAY
    
    SCALE_TEST --> NODES_10
    SCALE_TEST --> NODES_50
    SCALE_TEST --> NODES_100
    SCALE_TEST --> NODES_500
    SCALE_TEST --> NODES_1000
    
    THREAT_CHANGE --> NORMAL_STATE
    NORMAL_STATE --> THREAT_DETECT
    THREAT_DETECT --> PARAM_ADJUST
    PARAM_ADJUST --> PERFORMANCE
    PERFORMANCE --> RECOVERY
    
    QUALITY_TEST --> BER_NORMAL
    QUALITY_TEST --> BER_THREAT
    QUALITY_TEST --> BER_ADJUST
    QUALITY_TEST --> THROUGHPUT
    QUALITY_TEST --> LATENCY
    
    OVERALL --> AVAILABILITY
    OVERALL --> SCALABILITY
    OVERALL --> RELIABILITY
    OVERALL --> EFFICIENCY
    
    COMPARISON --> TRADITIONAL
    COMPARISON --> PROPOSED
    TRADITIONAL --> IMPROVEMENT
    PROPOSED --> IMPROVEMENT
    
    %% 样式设置
    style SNR_25 fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style SNR_20 fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style SNR_15 fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style SNR_10 fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style SNR_5 fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    
    style EAVESDROP fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style JAMMING fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style SPOOFING fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style REPLAY fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    
    style NEURAL fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style SVM_ALG fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style RANDOM fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style ISOLATION fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style DECISION fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    
    style NODES_1000 fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    style NODES_500 fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style NODES_100 fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style NODES_50 fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style NODES_10 fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    
    style ULTRA_SEC fill:#FFCDD2,stroke:#F44336,stroke-width:2px
    style HIGH_SEC fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style MID_SEC fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style LOW_SEC fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    
    style NORMAL_STATE fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style THREAT_DETECT fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style PARAM_ADJUST fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style PERFORMANCE fill:#E3F2FD,stroke:#2196F3,stroke-width:2px
    style RECOVERY fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    
    style PROPOSED fill:#E8F5E8,stroke:#4CAF50,stroke-width:3px
    style TRADITIONAL fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style IMPROVEMENT fill:#E3F2FD,stroke:#2196F3,stroke-width:3px
    
    style AVAILABILITY fill:#F3E5F5,stroke:#9C27B0,stroke-width:2px
    style SCALABILITY fill:#F3E5F5,stroke:#9C27B0,stroke-width:2px
    style RELIABILITY fill:#F3E5F5,stroke:#9C27B0,stroke-width:2px
    style EFFICIENCY fill:#F3E5F5,stroke:#9C27B0,stroke-width:2px
