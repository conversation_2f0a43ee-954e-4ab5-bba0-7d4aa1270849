graph TD
    A[开始] --> B[系统初始化]
    B --> C[生成导频信号]
    
    C --> D[发送导频序列P_t]
    D --> E[接收信号R_t]
    E --> F[信道响应测量]
    F --> G[计算信道增益]
    
    G --> H[计算相位]
    H --> I[计算时延]
    I --> J[计算噪声功率]
    
    J --> K{OFDM系统?}
    K -->|是| L[频域处理]
    K -->|否| M[时域处理]
    
    L --> N[提取子载波特征]
    M --> O[提取时域特征]
    N --> P[特征汇总]
    O --> P
    
    P --> Q[构建特征向量F]
    Q --> R[特征归一化]
    R --> S[滤波去噪]
    S --> T[有效性验证]
    
    T --> U{质量评估}
    U -->|高质量| V[输出特征向量]
    U -->|低质量| W[特征增强]
    
    W --> X{重试?}
    X -->|是| C
    X -->|否| Y[错误处理]
    Y --> V
    
    V --> Z[传递给密钥生成模块]
    Z --> END[结束]
    
    %% 样式
    style A fill:#4CAF50,color:#fff
    style END fill:#F44336,color:#fff
    style V fill:#FF9800,color:#fff
    style Z fill:#9C27B0,color:#fff
    style K fill:#03A9F4,color:#fff
    style U fill:#03A9F4,color:#fff
    style X fill:#03A9F4,color:#fff
