graph TB
    subgraph "低空网络通信环境"
        subgraph "通信节点层"
            UAV1[无人机节点A]
            UAV2[无人机节点B]
            UAV3[无人机节点C]
            IoT1[传感器节点1]
            IoT2[传感器节点2]
            GW[汇聚节点/基站]
        end
        
        subgraph "感知设备层"
            SA[频谱分析仪]
            GPS[GPS定位模块]
            NM[网络监测器]
            ES[环境传感器]
        end
    end
    
    subgraph "物理层安全架构核心模块"
        subgraph "模块100: 物理层特征确定"
            CSI[信道状态信息获取]
            FE[特征参数提取]
            FV[特征向量构建]
        end
        
        subgraph "模块200: 密钥生成"
            QUAN[特征量化]
            RECON[信息协商]
            KE[密钥提取]
        end
        
        subgraph "模块300: 感知数据处理"
            SDC[多源数据收集]
            TD[威胁检测算法]
            SIG[安全增强信息生成]
        end
        
        subgraph "模块400: 安全加固"
            PS[参数调整策略]
            EC[加密通信]
            DA[动态适应]
        end
        
        subgraph "模块500: 通信节点控制"
            RF[射频收发器]
            DSP[数字信号处理]
            SEC[安全控制单元]
        end
    end
    
    subgraph "威胁环境"
        THREAT1[窃听攻击]
        THREAT2[干扰攻击]
        THREAT3[欺骗攻击]
        THREAT4[重放攻击]
    end
    
    %% 数据流连接
    UAV1 -.->|信道测量| CSI
    UAV2 -.->|信道测量| CSI
    UAV3 -.->|信道测量| CSI
    IoT1 -.->|信道测量| CSI
    IoT2 -.->|信道测量| CSI
    GW -.->|信道测量| CSI
    
    CSI -->|特征数据| FE
    FE -->|处理结果| FV
    FV -->|特征向量| QUAN
    QUAN -->|量化数据| RECON
    RECON -->|协商结果| KE
    
    SA -->|频谱数据| SDC
    GPS -->|位置数据| SDC
    NM -->|网络数据| SDC
    ES -->|环境数据| SDC
    SDC -->|融合数据| TD
    TD -->|检测结果| SIG
    
    KE -->|密钥| PS
    SIG -->|威胁信息| PS
    PS -->|控制指令| EC
    PS -->|调整参数| DA
    
    EC -->|加密数据| RF
    DA -->|参数更新| DSP
    RF -->|安全通信| SEC
    
    SEC -.->|安全链路| UAV1
    SEC -.->|安全链路| UAV2
    SEC -.->|安全链路| UAV3
    SEC -.->|安全链路| IoT1
    SEC -.->|安全链路| IoT2
    SEC -.->|安全链路| GW
    
    %% 威胁检测
    THREAT1 -.->|攻击| UAV1
    THREAT2 -.->|攻击| UAV2
    THREAT3 -.->|攻击| IoT1
    THREAT4 -.->|攻击| GW
    
    TD -.->|检测| THREAT1
    TD -.->|检测| THREAT2
    TD -.->|检测| THREAT3
    TD -.->|检测| THREAT4
    
    %% 反馈回路
    DA -.->|自适应反馈| CSI
    SIG -.->|威胁反馈| FE
    
    %% 样式设置
    style UAV1 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UAV2 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UAV3 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style IoT1 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style IoT2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style GW fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    style CSI fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style KE fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style SIG fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style EC fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    style DA fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    style THREAT1 fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px,stroke-dasharray: 5 5
    style THREAT2 fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px,stroke-dasharray: 5 5
    style THREAT3 fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px,stroke-dasharray: 5 5
    style THREAT4 fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px,stroke-dasharray: 5 5
