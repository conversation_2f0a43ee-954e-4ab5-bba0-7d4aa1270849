flowchart TD
    Start([开始]) --> InputKey[接收物理层密钥K]
    InputKey --> InputThreat[接收威胁信息]
    
    subgraph "威胁评估与决策"
        InputThreat --> ParseThreat[解析威胁信息]
        ParseThreat --> ThreatLevel{威胁等级判断}
        ThreatLevel -->|低威胁| LowThreat[低威胁处理]
        ThreatLevel -->|中威胁| MidThreat[中威胁处理]
        ThreatLevel -->|高威胁| HighThreat[高威胁处理]
        ThreatLevel -->|严重威胁| CriticalThreat[严重威胁处理]
    end
    
    subgraph "参数调整策略 (步骤S401)"
        LowThreat --> PowerLow[功率控制: P_tx = P_base × 1.0]
        MidThreat --> PowerMid[功率控制: P_tx = P_base × 1.2]
        HighThreat --> PowerHigh[功率控制: P_tx = P_base × 1.5]
        CriticalThreat --> PowerMax[功率控制: P_tx = P_max]
        
        PowerLow --> FreqLow[频率策略: 固定频率]
        PowerMid --> FreqMid[频率策略: 慢速跳频]
        PowerHigh --> FreqHigh[频率策略: 快速跳频]
        PowerMax --> FreqCritical[频率策略: 超快跳频]
        
        FreqLow --> ModLow[调制方式: 64QAM]
        FreqMid --> ModMid[调制方式: 16QAM]
        FreqHigh --> ModHigh[调制方式: QPSK]
        FreqCritical --> ModCritical[调制方式: BPSK]
        
        ModLow --> CodingLow[编码方案: 1/2码率]
        ModMid --> CodingMid[编码方案: 1/3码率]
        ModHigh --> CodingHigh[编码方案: 1/4码率]
        ModCritical --> CodingCritical[编码方案: 1/6码率]
    end
    
    subgraph "加密通信流程 (步骤S402)"
        CodingLow --> EncryptData[数据加密处理]
        CodingMid --> EncryptData
        CodingHigh --> EncryptData
        CodingCritical --> EncryptData
        
        EncryptData --> SelectAlgo{选择加密算法}
        SelectAlgo -->|标准安全| AES128[AES-128加密]
        SelectAlgo -->|高安全| AES256[AES-256加密]
        SelectAlgo -->|超高安全| ChaCha20[ChaCha20加密]
        
        AES128 --> AddAuth[添加认证标签]
        AES256 --> AddAuth
        ChaCha20 --> AddAuth
        
        AddAuth --> PacketFormat[数据包格式化]
        PacketFormat --> TransmitData[数据传输]
    end
    
    subgraph "动态适应机制"
        TransmitData --> MonitorChannel[信道质量监测]
        MonitorChannel --> QualityCheck{信道质量评估}
        QualityCheck -->|质量良好| MaintainParams[维持当前参数]
        QualityCheck -->|质量下降| AdjustParams[调整通信参数]
        QualityCheck -->|质量严重下降| EmergencySwitch[紧急切换]
        
        AdjustParams --> UpdatePower[更新发射功率]
        UpdatePower --> UpdateFreq[更新频率参数]
        UpdateFreq --> UpdateMod[更新调制参数]
        
        EmergencySwitch --> BackupChannel[切换备用信道]
        BackupChannel --> BackupProtocol[启用备用协议]
        
        MaintainParams --> ContinueMonitor[继续监测]
        UpdateMod --> ContinueMonitor
        BackupProtocol --> ContinueMonitor
        
        ContinueMonitor --> MonitorChannel
    end
    
    subgraph "性能优化"
        MonitorChannel --> CollectMetrics[收集性能指标]
        CollectMetrics --> AnalyzePerf[性能分析]
        AnalyzePerf --> OptimizeParams{参数优化}
        OptimizeParams -->|需要优化| TuneParams[参数微调]
        OptimizeParams -->|无需优化| RecordStats[记录统计信息]
        
        TuneParams --> TestPerf[性能测试]
        TestPerf --> ValidateImprove{性能改善验证}
        ValidateImprove -->|改善| ApplyChanges[应用参数变更]
        ValidateImprove -->|无改善| RevertParams[恢复原参数]
        
        ApplyChanges --> RecordStats
        RevertParams --> RecordStats
    end
    
    subgraph "反馈与学习"
        RecordStats --> UpdateModel[更新威胁模型]
        UpdateModel --> LearnPattern[学习攻击模式]
        LearnPattern --> UpdateStrategy[更新防御策略]
        UpdateStrategy --> FeedbackLoop[反馈给特征确定模块]
    end
    
    FeedbackLoop -.-> InputThreat
    
    RecordStats --> OutputStatus[输出系统状态]
    OutputStatus --> End([结束])
    
    %% 样式设置
    style Start fill:#4CAF50,stroke:#2E7D32,stroke-width:3px,color:#fff
    style End fill:#F44336,stroke:#C62828,stroke-width:3px,color:#fff
    
    style ThreatLevel fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style QualityCheck fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style OptimizeParams fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style ValidateImprove fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style SelectAlgo fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    
    style LowThreat fill:#4CAF50,stroke:#2E7D32,stroke-width:2px,color:#fff
    style MidThreat fill:#FF9800,stroke:#E65100,stroke-width:2px,color:#fff
    style HighThreat fill:#FF5722,stroke:#D84315,stroke-width:2px,color:#fff
    style CriticalThreat fill:#F44336,stroke:#C62828,stroke-width:2px,color:#fff
    
    style EncryptData fill:#9C27B0,stroke:#6A1B9A,stroke-width:2px,color:#fff
    style AES128 fill:#E1BEE7,stroke:#8E24AA,stroke-width:2px
    style AES256 fill:#E1BEE7,stroke:#8E24AA,stroke-width:2px
    style ChaCha20 fill:#E1BEE7,stroke:#8E24AA,stroke-width:2px
    
    style EmergencySwitch fill:#FF1744,stroke:#D50000,stroke-width:2px,color:#fff
    style BackupChannel fill:#FF8A65,stroke:#FF5722,stroke-width:2px
    style BackupProtocol fill:#FF8A65,stroke:#FF5722,stroke-width:2px
    
    style FeedbackLoop fill:#795548,stroke:#5D4037,stroke-width:2px,color:#fff
    style OutputStatus fill:#607D8B,stroke:#455A64,stroke-width:2px,color:#fff
