graph TB
    subgraph "云端管理层"
        subgraph "云端服务集群"
            CLOUD_MASTER[主控云服务器<br/>负载均衡<br/>全局调度]
            CLOUD_BACKUP[备用云服务器<br/>故障切换<br/>数据同步]
            CLOUD_EDGE[边缘云节点<br/>就近服务<br/>延迟优化]
        end
        
        subgraph "数据存储与分析"
            DATABASE[分布式数据库<br/>MongoDB集群<br/>数据分片存储]
            DATA_LAKE[数据湖<br/>Hadoop生态<br/>大数据分析]
            AI_PLATFORM[AI分析平台<br/>TensorFlow集群<br/>模型训练推理]
        end
        
        subgraph "管理与监控"
            MONITOR[监控中心<br/>Prometheus+Grafana<br/>实时监控告警]
            CONFIG[配置管理<br/>统一配置下发<br/>版本控制]
            LOG[日志中心<br/>ELK Stack<br/>日志收集分析]
        end
    end
    
    subgraph "网络接入层"
        subgraph "核心网络设备"
            CORE_ROUTER[核心路由器<br/>高性能转发<br/>QoS保障]
            FIREWALL[防火墙<br/>安全防护<br/>访问控制]
            LOAD_BALANCER[负载均衡器<br/>流量分发<br/>健康检查]
        end
        
        subgraph "接入网络"
            FIBER[光纤网络<br/>10Gbps骨干<br/>低延迟传输]
            WIRELESS[无线网络<br/>5G/WiFi6<br/>移动接入]
            SATELLITE[卫星网络<br/>广域覆盖<br/>应急通信]
        end
    end
    
    subgraph "区域控制层"
        subgraph "区域控制中心A"
            REGION_A[区域控制器A<br/>覆盖范围: 100km²<br/>管理节点: 500个]
            GATEWAY_A1[汇聚网关A1<br/>LoRa基站<br/>433MHz频段]
            GATEWAY_A2[汇聚网关A2<br/>WiFi热点<br/>2.4/5GHz双频]
            BACKUP_A[备用控制器A<br/>热备模式<br/>30秒切换]
        end
        
        subgraph "区域控制中心B"
            REGION_B[区域控制器B<br/>覆盖范围: 100km²<br/>管理节点: 500个]
            GATEWAY_B1[汇聚网关B1<br/>蜂窝基站<br/>4G/5G网络]
            GATEWAY_B2[汇聚网关B2<br/>微波链路<br/>点对点传输]
            BACKUP_B[备用控制器B<br/>冷备模式<br/>5分钟切换]
        end
        
        subgraph "区域控制中心C"
            REGION_C[区域控制器C<br/>覆盖范围: 100km²<br/>管理节点: 500个]
            GATEWAY_C1[汇聚网关C1<br/>混合网关<br/>多协议支持]
            GATEWAY_C2[汇聚网关C2<br/>边缘计算<br/>本地处理]
            BACKUP_C[备用控制器C<br/>分布式备份<br/>自动故障转移]
        end
    end
    
    subgraph "终端设备层"
        subgraph "无人机集群部署"
            UAV_SWARM[无人机集群<br/>50架无人机<br/>协同作业]
            UAV_LEADER[领导无人机<br/>集群控制<br/>任务协调]
            UAV_RELAY[中继无人机<br/>通信中继<br/>覆盖扩展]
        end
        
        subgraph "物联网传感器网络"
            IOT_FARM[农业传感器<br/>1000个节点<br/>环境监测]
            IOT_CITY[城市传感器<br/>500个节点<br/>智慧城市]
            IOT_INDUSTRY[工业传感器<br/>200个节点<br/>工业4.0]
        end
        
        subgraph "移动终端设备"
            MOBILE_DEVICE[移动设备<br/>智能手机/平板<br/>用户接入]
            VEHICLE[车载终端<br/>车联网<br/>移动通信]
            WEARABLE[可穿戴设备<br/>健康监测<br/>个人物联网]
        end
    end
    
    subgraph "安全防护体系"
        subgraph "物理层安全"
            PHY_KEY[物理层密钥生成<br/>信道特征提取<br/>自动密钥分发]
            PHY_ENCRYPT[物理层加密<br/>AES/ChaCha20<br/>硬件加速]
            PHY_AUTH[物理层认证<br/>设备指纹<br/>身份验证]
        end
        
        subgraph "网络层安全"
            NET_VPN[VPN隧道<br/>IPSec/WireGuard<br/>端到端加密]
            NET_IDS[入侵检测<br/>Suricata<br/>实时威胁检测]
            NET_FIREWALL[网络防火墙<br/>iptables/pfSense<br/>访问控制]
        end
        
        subgraph "应用层安全"
            APP_AUTH[应用认证<br/>OAuth2.0/JWT<br/>统一身份认证]
            APP_ENCRYPT[应用加密<br/>TLS1.3<br/>HTTPS通信]
            APP_AUDIT[安全审计<br/>操作日志<br/>合规检查]
        end
    end
    
    subgraph "运维管理体系"
        subgraph "自动化运维"
            AUTO_DEPLOY[自动部署<br/>Kubernetes<br/>容器编排]
            AUTO_SCALE[自动扩缩容<br/>HPA/VPA<br/>弹性伸缩]
            AUTO_HEAL[自动修复<br/>故障自愈<br/>服务恢复]
        end
        
        subgraph "监控告警"
            HEALTH_CHECK[健康检查<br/>存活探测<br/>就绪探测]
            ALERT_SYSTEM[告警系统<br/>多级告警<br/>智能降噪]
            DASHBOARD[运维面板<br/>可视化监控<br/>实时大屏]
        end
        
        subgraph "运维工具"
            CI_CD[CI/CD流水线<br/>Jenkins/GitLab<br/>持续集成部署]
            CONFIG_MGMT[配置管理<br/>Ansible/Puppet<br/>配置自动化]
            BACKUP[备份恢复<br/>定期备份<br/>灾难恢复]
        end
    end
    
    %% 网络连接
    CLOUD_MASTER -.->|主备同步| CLOUD_BACKUP
    CLOUD_MASTER -.->|边缘分发| CLOUD_EDGE
    CLOUD_MASTER --> DATABASE
    CLOUD_MASTER --> AI_PLATFORM
    
    CORE_ROUTER --> REGION_A
    CORE_ROUTER --> REGION_B
    CORE_ROUTER --> REGION_C
    
    REGION_A --> GATEWAY_A1
    REGION_A --> GATEWAY_A2
    REGION_A -.->|热备| BACKUP_A
    
    REGION_B --> GATEWAY_B1
    REGION_B --> GATEWAY_B2
    REGION_B -.->|冷备| BACKUP_B
    
    REGION_C --> GATEWAY_C1
    REGION_C --> GATEWAY_C2
    REGION_C -.->|分布式备份| BACKUP_C
    
    GATEWAY_A1 -.->|LoRa| IOT_FARM
    GATEWAY_A2 -.->|WiFi| UAV_SWARM
    GATEWAY_B1 -.->|5G| MOBILE_DEVICE
    GATEWAY_B2 -.->|微波| VEHICLE
    GATEWAY_C1 -.->|多协议| IOT_CITY
    GATEWAY_C2 -.->|边缘计算| IOT_INDUSTRY
    
    %% 安全防护连接
    PHY_KEY --> PHY_ENCRYPT
    PHY_ENCRYPT --> PHY_AUTH
    NET_VPN --> NET_IDS
    NET_IDS --> NET_FIREWALL
    APP_AUTH --> APP_ENCRYPT
    APP_ENCRYPT --> APP_AUDIT
    
    %% 运维管理连接
    AUTO_DEPLOY --> AUTO_SCALE
    AUTO_SCALE --> AUTO_HEAL
    HEALTH_CHECK --> ALERT_SYSTEM
    ALERT_SYSTEM --> DASHBOARD
    CI_CD --> CONFIG_MGMT
    CONFIG_MGMT --> BACKUP
    
    %% 样式设置
    style CLOUD_MASTER fill:#E3F2FD,stroke:#1976D2,stroke-width:3px
    style CLOUD_BACKUP fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style CLOUD_EDGE fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    
    style REGION_A fill:#F3E5F5,stroke:#9C27B0,stroke-width:2px
    style REGION_B fill:#F3E5F5,stroke:#9C27B0,stroke-width:2px
    style REGION_C fill:#F3E5F5,stroke:#9C27B0,stroke-width:2px
    
    style UAV_SWARM fill:#E1F5FE,stroke:#0277BD,stroke-width:2px
    style IOT_FARM fill:#E8F5E8,stroke:#388E3C,stroke-width:2px
    style IOT_CITY fill:#E8F5E8,stroke:#388E3C,stroke-width:2px
    style IOT_INDUSTRY fill:#E8F5E8,stroke:#388E3C,stroke-width:2px
    
    style PHY_KEY fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style PHY_ENCRYPT fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    style PHY_AUTH fill:#FFEBEE,stroke:#F44336,stroke-width:2px
    
    style AUTO_DEPLOY fill:#E8EAF6,stroke:#3F51B5,stroke-width:2px
    style AUTO_SCALE fill:#E8EAF6,stroke:#3F51B5,stroke-width:2px
    style AUTO_HEAL fill:#E8EAF6,stroke:#3F51B5,stroke-width:2px
    
    style BACKUP_A fill:#FFCDD2,stroke:#F44336,stroke-width:1px
    style BACKUP_B fill:#FFCDD2,stroke:#F44336,stroke-width:1px
    style BACKUP_C fill:#FFCDD2,stroke:#F44336,stroke-width:1px
