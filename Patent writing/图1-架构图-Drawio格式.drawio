<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="低空通感安全架构" id="architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="图1：低空网络通信感知一体化物理层安全架构总体框图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="527" y="20" width="600" height="30" as="geometry" />
        </mxCell>
        
        <!-- 低空网络通信环境容器 -->
        <mxCell id="env_container" value="低空网络通信环境" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#333333;strokeWidth=2;verticalAlign=top;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="70" width="350" height="250" as="geometry" />
        </mxCell>
        
        <!-- 通信节点层 -->
        <mxCell id="comm_layer" value="通信节点层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;verticalAlign=top;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="100" width="310" height="120" as="geometry" />
        </mxCell>
        
        <!-- 无人机节点 -->
        <mxCell id="uav1" value="无人机节点A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="130" width="90" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="uav2" value="无人机节点B" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="180" y="130" width="90" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="uav3" value="无人机节点C" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="130" width="90" height="40" as="geometry" />
        </mxCell>
        
        <!-- 传感器节点 -->
        <mxCell id="iot1" value="传感器节点1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="180" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="iot2" value="传感器节点2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="180" y="180" width="90" height="30" as="geometry" />
        </mxCell>
        
        <!-- 汇聚节点 -->
        <mxCell id="gateway" value="汇聚节点/基站" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="180" width="90" height="30" as="geometry" />
        </mxCell>
        
        <!-- 感知设备层 -->
        <mxCell id="sensor_layer" value="感知设备层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;verticalAlign=top;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="230" width="310" height="80" as="geometry" />
        </mxCell>
        
        <!-- 感知设备 -->
        <mxCell id="spectrum" value="频谱分析仪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" vertex="1" parent="1">
          <mxGeometry x="80" y="250" width="70" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="gps" value="GPS定位模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" vertex="1" parent="1">
          <mxGeometry x="160" y="250" width="70" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="network_monitor" value="网络监测器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" vertex="1" parent="1">
          <mxGeometry x="240" y="250" width="70" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="env_sensor" value="环境传感器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" vertex="1" parent="1">
          <mxGeometry x="320" y="250" width="70" height="25" as="geometry" />
        </mxCell>
        
        <!-- 物理层安全架构核心模块容器 -->
        <mxCell id="core_container" value="物理层安全架构核心模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#333333;strokeWidth=2;verticalAlign=top;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="70" width="800" height="500" as="geometry" />
        </mxCell>
        
        <!-- 模块100: 物理层特征确定 -->
        <mxCell id="module100" value="模块100: 物理层特征确定" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#f57c00;strokeWidth=2;verticalAlign=top;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="470" y="100" width="220" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="csi" value="信道状态信息获取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="480" y="130" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="feature_extract" value="特征参数提取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="480" y="160" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="feature_vector" value="特征向量构建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="480" y="190" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- 模块200: 密钥生成 -->
        <mxCell id="module200" value="模块200: 密钥生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#f57c00;strokeWidth=2;verticalAlign=top;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="710" y="100" width="220" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="quantization" value="特征量化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="720" y="130" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="reconciliation" value="信息协商" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="720" y="160" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="key_extract" value="密钥提取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="720" y="190" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- 模块300: 感知数据处理 -->
        <mxCell id="module300" value="模块300: 感知数据处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#f57c00;strokeWidth=2;verticalAlign=top;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="950" y="100" width="220" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="data_collect" value="多源数据收集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="960" y="130" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="threat_detect" value="威胁检测算法" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="960" y="160" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="security_info" value="安全增强信息生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="960" y="190" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- 模块400: 安全加固 -->
        <mxCell id="module400" value="模块400: 安全加固" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#d32f2f;strokeWidth=2;verticalAlign=top;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="470" y="250" width="220" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="param_adjust" value="参数调整策略" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;" vertex="1" parent="1">
          <mxGeometry x="480" y="280" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="encryption" value="加密通信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;" vertex="1" parent="1">
          <mxGeometry x="480" y="310" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="dynamic_adapt" value="动态适应" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;" vertex="1" parent="1">
          <mxGeometry x="480" y="340" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- 模块500: 通信节点控制 -->
        <mxCell id="module500" value="模块500: 通信节点控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#388e3c;strokeWidth=2;verticalAlign=top;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="710" y="250" width="220" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="rf_transceiver" value="射频收发器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;" vertex="1" parent="1">
          <mxGeometry x="720" y="280" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="dsp" value="数字信号处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;" vertex="1" parent="1">
          <mxGeometry x="720" y="310" width="200" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="security_control" value="安全控制单元" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;" vertex="1" parent="1">
          <mxGeometry x="720" y="340" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- 威胁环境 -->
        <mxCell id="threat_env" value="威胁环境" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#d32f2f;strokeWidth=2;strokeDashArray=5 5;verticalAlign=top;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="950" y="400" width="220" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="threat1" value="窃听攻击" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcdd2;strokeColor=#d32f2f;strokeDashArray=5 5;" vertex="1" parent="1">
          <mxGeometry x="960" y="430" width="90" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="threat2" value="干扰攻击" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcdd2;strokeColor=#d32f2f;strokeDashArray=5 5;" vertex="1" parent="1">
          <mxGeometry x="1070" y="430" width="90" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="threat3" value="欺骗攻击" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcdd2;strokeColor=#d32f2f;strokeDashArray=5 5;" vertex="1" parent="1">
          <mxGeometry x="960" y="460" width="90" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="threat4" value="重放攻击" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcdd2;strokeColor=#d32f2f;strokeDashArray=5 5;" vertex="1" parent="1">
          <mxGeometry x="1070" y="460" width="90" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
