graph TD
    A[开始] --> B[接收特征向量F]
    B --> C{特征向量检查}
    C -->|无效| D[错误处理]
    C -->|有效| E[初始化参数]
    
    E --> F[计算量化间隔]
    F --> G[自适应量化]
    G --> H[生成比特序列]
    H --> I{量化质量检查}
    I -->|不足| J[调整参数]
    J --> G
    I -->|合格| K[发送量化索引]
    
    K --> L[接收对方索引]
    L --> M[比较量化结果]
    M --> N[计算误差]
    N --> O[级联码纠错]
    
    O --> P[计算校验子]
    P --> Q{校验子为零?}
    Q -->|否| R[纠正错误]
    R --> S[更新比特序列]
    S --> Q
    Q -->|是| T[获得一致序列]
    
    T --> U{一致性验证}
    U -->|失败| V[重新协商]
    V --> K
    U -->|成功| W[确认一致序列]
    
    W --> X[隐私放大]
    X --> Y[应用哈希函数]
    Y --> Z[提取密钥]
    Z --> AA{密钥验证}
    AA -->|无效| BB[重新生成]
    BB --> X
    AA -->|有效| CC[输出密钥]
    
    CC --> DD[熵值评估]
    DD --> EE[随机性测试]
    EE --> FF{安全评估}
    FF -->|不足| GG[增强安全]
    GG --> HH[增加熵源]
    HH --> X
    FF -->|充足| II[安全确认]
    
    II --> JJ[存储密钥]
    JJ --> KK[通知下一模块]
    KK --> LL[结束]
    
    D --> LL
    
    %% 样式
    style A fill:#4CAF50,color:#fff
    style LL fill:#F44336,color:#fff
    style CC fill:#FF9800,color:#fff
    style II fill:#4CAF50,color:#fff
    style KK fill:#9C27B0,color:#fff
    style C fill:#03A9F4,color:#fff
    style I fill:#03A9F4,color:#fff
    style Q fill:#03A9F4,color:#fff
    style U fill:#03A9F4,color:#fff
    style AA fill:#03A9F4,color:#fff
    style FF fill:#03A9F4,color:#fff
    style D fill:#F44336,color:#fff
    style GG fill:#FF5722,color:#fff
