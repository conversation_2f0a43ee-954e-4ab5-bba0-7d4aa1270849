flowchart TD
    Start([开始]) --> Init[系统初始化]
    Init --> PilotGen[生成导频信号/训练序列]
    
    subgraph S101 ["信道状态信息获取 (步骤S101)"]
        PilotGen --> SendPilot[发送节点发送导频序列P_t]
        SendPilot --> Receive[接收节点接收信号R_t]
        Receive --> Formula1[R_t = H_t × P_t + N_t]
        Formula1 --> MeasureCSI[测量信道冲激响应H_t]
    end
    
    subgraph S102 ["信道特征参数提取 (步骤S102)"]
        MeasureCSI --> CalcGain[计算信道增益]
        CalcGain --> CalcPhase[计算相位]
        CalcPhase --> CalcDelay[计算多径时延]
        CalcDelay --> CalcNoise[计算噪声功率]
        
        CalcNoise --> CheckOFDM{是否为OFDM系统?}
        CheckOFDM -->|是| OFDMProc[频域处理]
        CheckOFDM -->|否| DirectProc[时域处理]
        
        OFDMProc --> ExtractSub[提取子载波特征]
        DirectProc --> ExtractTime[提取时域特征]
        ExtractSub --> Combine[特征参数汇总]
        ExtractTime --> Combine
    end
    
    subgraph S103 ["特征向量构建 (步骤S103)"]
        Combine --> BuildVector[构建特征向量F]
        BuildVector --> VectorFormula[F = H1,H2,...,HK,φ1,φ2,...,φK,τ1,τ2,...,τL]
        VectorFormula --> Normalize[特征归一化处理]
        Normalize --> Filter[滤波去噪]
        Filter --> Validate[特征有效性验证]
    end
    
    subgraph QA ["质量评估与优化"]
        Validate --> QualityCheck{信道质量评估}
        QualityCheck -->|SNR > 阈值| HighQuality[高质量特征]
        QualityCheck -->|SNR ≤ 阈值| LowQuality[低质量特征]
        
        LowQuality --> Enhance[特征增强处理]
        Enhance --> Retry{重试次数 < 最大值?}
        Retry -->|是| PilotGen
        Retry -->|否| ErrorHandle[错误处理]
        
        HighQuality --> Output[输出特征向量F]
        ErrorHandle --> Output
    end
    
    Output --> NextModule[传递给密钥生成模块]
    NextModule --> End([结束])
    
    subgraph Legend ["关键参数说明"]
        Params[K: 子载波数量<br/>L: 多径分量数量<br/>H_t: 信道冲激响应<br/>N_t: 加性白高斯噪声<br/>SNR: 信噪比阈值]
    end
    
    %% 样式设置
    style Start fill:#4CAF50,stroke:#2E7D32,stroke-width:3px,color:#fff
    style End fill:#F44336,stroke:#C62828,stroke-width:3px,color:#fff
    style Formula1 fill:#2196F3,stroke:#1565C0,stroke-width:2px,color:#fff
    style VectorFormula fill:#2196F3,stroke:#1565C0,stroke-width:2px,color:#fff
    style Output fill:#FF9800,stroke:#E65100,stroke-width:2px,color:#fff
    style NextModule fill:#9C27B0,stroke:#6A1B9A,stroke-width:2px,color:#fff
    style Params fill:#F5F5F5,stroke:#757575,stroke-width:1px
    
    style HighQuality fill:#4CAF50,stroke:#2E7D32,stroke-width:2px,color:#fff
    style LowQuality fill:#F44336,stroke:#C62828,stroke-width:2px,color:#fff
    style ErrorHandle fill:#F44336,stroke:#C62828,stroke-width:2px,color:#fff
    
    style CheckOFDM fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style QualityCheck fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    style Retry fill:#03A9F4,stroke:#0277BD,stroke-width:2px,color:#fff
    
    %% 处理节点样式
    style SendPilot fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style Receive fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style MeasureCSI fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style CalcGain fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style CalcPhase fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style CalcDelay fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style CalcNoise fill:#FFF3E0,stroke:#FF9800,stroke-width:1px
    style OFDMProc fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style DirectProc fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style ExtractSub fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style ExtractTime fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style Combine fill:#E8F5E8,stroke:#4CAF50,stroke-width:1px
    style BuildVector fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    style Normalize fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    style Filter fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    style Validate fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    style Enhance fill:#FFEBEE,stroke:#F44336,stroke-width:1px
