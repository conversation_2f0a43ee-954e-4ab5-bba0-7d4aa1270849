graph TD
    A[开始] --> B[接收密钥和威胁信息]
    B --> C{威胁等级判断}
    
    C -->|低| D[低威胁处理]
    C -->|中| E[中威胁处理]
    C -->|高| F[高威胁处理]
    C -->|严重| G[严重威胁处理]
    
    D --> H[功率: 基准值]
    E --> I[功率: 1.2倍]
    F --> J[功率: 1.5倍]
    G --> K[功率: 最大值]
    
    H --> L[频率: 固定]
    I --> M[频率: 慢跳]
    J --> N[频率: 快跳]
    K --> O[频率: 超快跳]
    
    L --> P[调制: 64QAM]
    M --> Q[调制: 16QAM]
    N --> R[调制: QPSK]
    O --> S[调制: BPSK]
    
    P --> T[数据加密]
    Q --> T
    R --> T
    S --> T
    
    T --> U{选择加密算法}
    U -->|标准| V[AES-128]
    U -->|高级| W[AES-256]
    U -->|超级| X[ChaCha20]
    
    V --> Y[数据传输]
    W --> Y
    X --> Y
    
    Y --> Z[信道质量监测]
    Z --> AA{质量评估}
    
    AA -->|良好| BB[维持参数]
    AA -->|下降| CC[调整参数]
    AA -->|严重下降| DD[紧急切换]
    
    BB --> EE[继续监测]
    CC --> FF[更新参数]
    DD --> GG[备用信道]
    
    FF --> EE
    GG --> EE
    EE --> Z
    
    Z --> HH[性能优化]
    HH --> II[记录统计]
    II --> JJ[反馈学习]
    JJ -.-> B
    
    II --> KK[输出状态]
    KK --> LL[结束]
    
    %% 样式
    style A fill:#4CAF50,color:#fff
    style LL fill:#F44336,color:#fff
    style C fill:#03A9F4,color:#fff
    style AA fill:#03A9F4,color:#fff
    style U fill:#03A9F4,color:#fff
    
    style D fill:#4CAF50,color:#fff
    style E fill:#FF9800,color:#fff
    style F fill:#FF5722,color:#fff
    style G fill:#F44336,color:#fff
    
    style T fill:#9C27B0,color:#fff
    style DD fill:#FF1744,color:#fff
    style JJ fill:#795548,color:#fff
