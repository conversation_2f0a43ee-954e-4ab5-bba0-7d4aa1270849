<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
      .module-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .node-text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .uav-node { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .iot-node { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .gateway-node { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .process-node { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .security-node { fill: #ffebee; stroke: #d32f2f; stroke-width: 2; }
      .threat-node { fill: #ffcdd2; stroke: #d32f2f; stroke-width: 2; stroke-dasharray: 5,5; }
      .sensor-node { fill: #f0f4c3; stroke: #827717; stroke-width: 2; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #999; stroke-width: 1; fill: none; stroke-dasharray: 3,3; marker-end: url(#arrowhead-dashed); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="arrowhead-dashed" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#999" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title">图1：低空网络通信感知一体化物理层安全架构总体框图</text>
  
  <!-- 低空网络通信环境 -->
  <rect x="50" y="60" width="300" height="200" fill="none" stroke="#333" stroke-width="2" rx="10"/>
  <text x="200" y="80" class="module-title">低空网络通信环境</text>
  
  <!-- 通信节点 -->
  <rect x="70" y="100" width="80" height="40" class="uav-node" rx="5"/>
  <text x="110" y="125" class="node-text">无人机A</text>
  
  <rect x="160" y="100" width="80" height="40" class="uav-node" rx="5"/>
  <text x="200" y="125" class="node-text">无人机B</text>
  
  <rect x="250" y="100" width="80" height="40" class="uav-node" rx="5"/>
  <text x="290" y="125" class="node-text">无人机C</text>
  
  <rect x="70" y="150" width="80" height="40" class="iot-node" rx="5"/>
  <text x="110" y="175" class="node-text">传感器1</text>
  
  <rect x="160" y="150" width="80" height="40" class="iot-node" rx="5"/>
  <text x="200" y="175" class="node-text">传感器2</text>
  
  <rect x="250" y="150" width="80" height="40" class="gateway-node" rx="5"/>
  <text x="290" y="175" class="node-text">汇聚节点</text>
  
  <!-- 感知设备层 -->
  <rect x="70" y="200" width="70" height="30" class="sensor-node" rx="3"/>
  <text x="105" y="220" class="small-text">频谱分析</text>
  
  <rect x="150" y="200" width="70" height="30" class="sensor-node" rx="3"/>
  <text x="185" y="220" class="small-text">GPS定位</text>
  
  <rect x="230" y="200" width="70" height="30" class="sensor-node" rx="3"/>
  <text x="265" y="220" class="small-text">网络监测</text>
  
  <!-- 物理层安全架构核心模块 -->
  <rect x="400" y="60" width="750" height="500" fill="none" stroke="#333" stroke-width="2" rx="10"/>
  <text x="775" y="80" class="module-title">物理层安全架构核心模块</text>
  
  <!-- 模块100: 物理层特征确定 -->
  <rect x="420" y="100" width="200" height="120" fill="none" stroke="#f57c00" stroke-width="2" rx="5"/>
  <text x="520" y="120" class="module-title">模块100: 物理层特征确定</text>
  
  <rect x="430" y="130" width="180" height="25" class="process-node" rx="3"/>
  <text x="520" y="147" class="small-text">信道状态信息获取</text>
  
  <rect x="430" y="160" width="180" height="25" class="process-node" rx="3"/>
  <text x="520" y="177" class="small-text">特征参数提取</text>
  
  <rect x="430" y="190" width="180" height="25" class="process-node" rx="3"/>
  <text x="520" y="207" class="small-text">特征向量构建</text>
  
  <!-- 模块200: 密钥生成 -->
  <rect x="640" y="100" width="200" height="120" fill="none" stroke="#f57c00" stroke-width="2" rx="5"/>
  <text x="740" y="120" class="module-title">模块200: 密钥生成</text>
  
  <rect x="650" y="130" width="180" height="25" class="process-node" rx="3"/>
  <text x="740" y="147" class="small-text">特征量化</text>
  
  <rect x="650" y="160" width="180" height="25" class="process-node" rx="3"/>
  <text x="740" y="177" class="small-text">信息协商</text>
  
  <rect x="650" y="190" width="180" height="25" class="process-node" rx="3"/>
  <text x="740" y="207" class="small-text">密钥提取</text>
  
  <!-- 模块300: 感知数据处理 -->
  <rect x="860" y="100" width="200" height="120" fill="none" stroke="#f57c00" stroke-width="2" rx="5"/>
  <text x="960" y="120" class="module-title">模块300: 感知数据处理</text>
  
  <rect x="870" y="130" width="180" height="25" class="process-node" rx="3"/>
  <text x="960" y="147" class="small-text">多源数据收集</text>
  
  <rect x="870" y="160" width="180" height="25" class="process-node" rx="3"/>
  <text x="960" y="177" class="small-text">威胁检测算法</text>
  
  <rect x="870" y="190" width="180" height="25" class="process-node" rx="3"/>
  <text x="960" y="207" class="small-text">安全增强信息生成</text>
  
  <!-- 模块400: 安全加固 -->
  <rect x="420" y="250" width="200" height="120" fill="none" stroke="#d32f2f" stroke-width="2" rx="5"/>
  <text x="520" y="270" class="module-title">模块400: 安全加固</text>
  
  <rect x="430" y="280" width="180" height="25" class="security-node" rx="3"/>
  <text x="520" y="297" class="small-text">参数调整策略</text>
  
  <rect x="430" y="310" width="180" height="25" class="security-node" rx="3"/>
  <text x="520" y="327" class="small-text">加密通信</text>
  
  <rect x="430" y="340" width="180" height="25" class="security-node" rx="3"/>
  <text x="520" y="357" class="small-text">动态适应</text>
  
  <!-- 模块500: 通信节点控制 -->
  <rect x="640" y="250" width="200" height="120" fill="none" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="740" y="270" class="module-title">模块500: 通信节点控制</text>
  
  <rect x="650" y="280" width="180" height="25" class="gateway-node" rx="3"/>
  <text x="740" y="297" class="small-text">射频收发器</text>
  
  <rect x="650" y="310" width="180" height="25" class="gateway-node" rx="3"/>
  <text x="740" y="327" class="small-text">数字信号处理</text>
  
  <rect x="650" y="340" width="180" height="25" class="gateway-node" rx="3"/>
  <text x="740" y="357" class="small-text">安全控制单元</text>
  
  <!-- 威胁环境 -->
  <rect x="900" y="400" width="200" height="120" fill="none" stroke="#d32f2f" stroke-width="2" stroke-dasharray="5,5" rx="5"/>
  <text x="1000" y="420" class="module-title">威胁环境</text>
  
  <rect x="910" y="430" width="80" height="20" class="threat-node" rx="3"/>
  <text x="950" y="443" class="small-text">窃听攻击</text>
  
  <rect x="1000" y="430" width="80" height="20" class="threat-node" rx="3"/>
  <text x="1040" y="443" class="small-text">干扰攻击</text>
  
  <rect x="910" y="460" width="80" height="20" class="threat-node" rx="3"/>
  <text x="950" y="473" class="small-text">欺骗攻击</text>
  
  <rect x="1000" y="460" width="80" height="20" class="threat-node" rx="3"/>
  <text x="1040" y="473" class="small-text">重放攻击</text>
  
  <!-- 主要数据流箭头 -->
  <!-- 通信节点到物理层特征确定 -->
  <line x1="350" y1="120" x2="420" y2="142" class="dashed-arrow"/>
  <line x1="350" y1="170" x2="420" y2="142" class="dashed-arrow"/>
  
  <!-- 感知设备到感知数据处理 -->
  <line x1="300" y1="215" x2="870" y2="142" class="dashed-arrow"/>
  
  <!-- 模块间连接 -->
  <line x1="620" y1="160" x2="650" y2="160" class="arrow"/>
  <line x1="740" y1="215" x2="520" y2="280" class="arrow"/>
  <line x1="960" y1="215" x2="520" y2="280" class="arrow"/>
  <line x1="520" y1="370" x2="650" y2="310" class="arrow"/>
  
  <!-- 反馈回路 -->
  <line x1="520" y1="340" x2="520" y2="220" class="dashed-arrow"/>
  
  <!-- 威胁检测 -->
  <line x1="960" y1="190" x2="950" y2="430" class="dashed-arrow"/>
  
</svg>
