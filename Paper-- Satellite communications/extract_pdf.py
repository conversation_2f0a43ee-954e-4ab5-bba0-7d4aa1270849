#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import PyPDF2
import sys

def extract_pdf_text(pdf_path):
    """Extract text from PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            print(f"PDF信息:")
            print(f"页数: {len(pdf_reader.pages)}")
            print(f"文件路径: {pdf_path}")
            print("=" * 50)
            
            full_text = ""
            for page_num, page in enumerate(pdf_reader.pages, 1):
                print(f"\n--- 第 {page_num} 页 ---")
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        print(page_text)
                        full_text += f"\n--- 第 {page_num} 页 ---\n" + page_text + "\n"
                    else:
                        print("(此页无法提取文本或为空白页)")
                except Exception as e:
                    print(f"提取第 {page_num} 页时出错: {e}")
            
            return full_text
            
    except Exception as e:
        print(f"读取PDF文件时出错: {e}")
        return None

if __name__ == "__main__":
    pdf_file = "人工噪声辅助通感安全.pdf"
    extracted_text = extract_pdf_text(pdf_file)
    
    if extracted_text:
        # 保存提取的文本到文件
        with open("extracted_text.txt", "w", encoding="utf-8") as f:
            f.write(extracted_text)
        print(f"\n文本已保存到 extracted_text.txt")
