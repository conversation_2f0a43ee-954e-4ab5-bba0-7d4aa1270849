
--- 第 1 页 ---
IEEE TRANSACTIONS ON WIRELESS COMMUNI CATIONS, VOL. 20, NO. 1, JANUARY 2021 83
Secure Radar-Communication Systems
With Malicious Targets: Integrating
Radar, Communications and
Jamming Functionalities
<PERSON><PERSON> ,Graduate Student Member, IEEE ,F a nL i u ,Member, IEEE ,
and <PERSON><PERSON> ,Senior Member, IEEE
Abstract — This article studies the physical layer security in
a multiple-input-multiple-output (MIMO) dual-functional radar-communication (DFRC) system, which communicates with down-link cellular users and tracks radar targets simultaneously. Here,the radar targets are considered as potential eavesdropperswhich might eavesdrop the information from the communicationtransmitter to legitimate users. To ensure the transmissionsecrecy, we employ artiﬁcial noise (AN) at the transmitter andformulate optimization problems by minimizing the signal-to-noise ratio (SNR) received at radar targets, while guaranteeingthe signal-to-interference-plus-noise ratio (SINR) requirement atlegitimate users. We ﬁrst consider the ideal case where boththe target angle and the channel state information (CSI) areprecisely known. The scenario is further extended to more generalcases with target location uncertainty and CSI errors, where wepropose robust optimization approaches to guarantee the worst-case performance. Accordingly, the computational complexityis analyzed for each proposed method. Our numerical resultsshow the feasibility of the algorithms with the existence ofinstantaneous and statistical CSI error. In addition, the secrecyrate of secure DFRC system grows with the increasing angularinterval of location uncertainty.
Index Terms — Dual-functional radar-communication system,
secrecy rate, artiﬁcial noise, channel state information.
I. I NTRODUCTION
THE increasing spectrum congestion has intensiﬁed the
efforts in dynamic spectrum licensing and soon spectrum
is to be shared between radar and communication applications.
Manuscript received September 9, 2019; revised February 26, 2020,
June 28, 2020, and August 24, 2020; accepted September 5, 2020. Date
of publication September 17, 2020; date of current version January 8,2021. This work was supported in part by the European Union’s Horizon2020 Research and Innovation Programme through the Marie Skłodowska-
Curie Grant 793345, in part by the Engineering and Physical Sciences
Research Council (EPSRC) of the U.K. under Grant EP/R007934/1 andGrant EP/S026622/1, in part by the U.K. MOD University Defence Research
Collaboration (UDRC) in Signal Processing, and in part by the China Schol-
arship Council (CSC). This article was presented in part at the IEEE GlobalCommunications Conference (GLOBECOM), Hawaii, USA, December 2019.The associate editor coordinating the re view of this article and approving it
for publication was S. Buzzi. (Corresponding author: Fan Liu.)
The authors are with the Department of Electronics and Electrical Engi-
neering, University College London, London WC1E 7JE, U.K. (e-mail:
<EMAIL>; <EMAIL>; <EMAIL>).
Color versions of one or more of the ﬁgures in this article are available
online at https://ieeexplore.ieee.org.
Digital Object Identiﬁer 10.1109/TWC.2020.3023164Govermental organizations such as the US Department of
Defence (DoD) have a documented requirement of releas-
ing 865 MHz to support telemetry by the year of 2025,
but only 445 MHz is available at present [1]. As a result,
the operating frequency bands of communication and radar
are overlapped with each other [2], which leads to mutual
interference between two systems. Furthermore, both systems
have been recently given a common spectrum portion by theFederal Communication Commission (FCC) [3]–[5]. To enable
the efﬁcient usage of the spectrum, research efforts are well
underway to address the issue of communication and radar
spectrum sharing (CRSS).
Aiming for realizing the spectral coexistence of individual
radar and communication systems, several interference miti-
gation techniques have been proposed in [6]–[11]. As a step
further, dual-functional radar-communication (DFRC) systemthat is capable of realizing not only the spectral coexis-
tence, but also the shared use of the hardware platform, has
been regarded as a promising research direction [12]–[15].
It is noteworthy that the DFRC technique has already been
widely explored in numerous civilian and military applica-tions, including 5G vehicular network [16], WiFi based indoor
positioning [17], low-probability-of-intercept (LPI) commu-
nication [18] as well as the advanced multi-function radiofrequency concept (AMRFC) [19].
In the DFRC system, radar and communication function-
alities are realized by a well-designed probing waveform
that carries communication signalling and data. Evidently,
this operation implicates secur ity concerns, which are largely
overlooked in the relevant DFRC literature. It is known that
typical radar requires to focus the transmit power towards
the directions of interest to obtain a good estimation ofthe targets. Nevertheless, in the case of DFRC transmission,
critical information embedded in the probing waveform could
be leaked to the radar targets, which might be potential
eavesdroppers at the adversary’s side. To this end, it is
essential to take information security into consideration for theDFRC design. In the communication literature, physical layer
security has been widely investigated, where the eavesdrop-
pers’ reception can be crippled by exploiting transmit degreesof freedom (DoFs) [20]. MIMO secrecy capacity problems
1536-1276 © 2020 I EEE. Personal u se is perm itted, but republication/redistri bution requires IEEE permission.
See https://www.ieee.org/publications/rights/index.html for more information.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 2 页 ---
84 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
were considered in [21]–[23]. Besides, another meaningful
technique for enabling physical layer secrecy was presented
in [20], [24], namely artiﬁcial noise (AN) aided transmission.
Furthermore, the AN generation algorithm studied in [24], [25]were with the premise of publicly known channel state infor-
mation (CSI) in a fading environment, where AN lies in
null-space of transmission channel. As the perfect CSI is notavailable accurately in practice, AN will leak into the signal
space and deteriorate the use ful SNR. To tackle the AN in the
design of transmission techniques with imperfect CSI, robust
schemes have been investigated in recent work [26], [27].
Since these techniques are conceived to address the PHY layersecurity for communication-only scenarios, they are unable to
be straightforwardly applied to the secure DFRC transmis-
sion. Moreover, some concurrent AN-aided studies employedcooperative jammers to improve secure communication
[28], [29]. Cooperative jamming is to implement user cooper-
ation for wireless networks with secrecy constraints.
Given the dual-functional nature of the DFRC systems,
the secrecy issue can be addressed on the aspect of eitherradar or communication. From the perspective of the radar
system, existing works focus on the radar privacy mainte-
nance [8], [30], [31]. A functional architecture was presentedin [8] for the control center aiming at coordinating the coop-
eration between radar and communication while maintaining
the privacy of the radar system. In [30], obfuscation tech-
niques have been proposed to counter the inference attacks
in the scenario of spectrum sharing between military radarsand commercial communication systems. Besides, the work
of [31] showed the probability for an adversary to infer radar’s
location by exploiting the communication precoding matrices.On the other hand, the works of [32], [33] have studied
the secrecy problems from the viewpoint of communications.
In [32], the MIMO radar transmits two different signals simul-
taneously, one of which is embedded with desired information
for the legitimate receiver, the other one consists of false infor-mation to confuse the eavesdroppers. Both of the signals are
used to detect the target. Several optimization problems were
presented, including secrecy rat e maximization, target return
signal-to-noise ratio (SNR) maximization and transmit power
minimization. Then, a uniﬁed joint system of passive radar
and communication systems was considered in [33], where
the communication receivers might be eavesdropped by the
target of passive radar. As the radar system typically addressesuncooperative or even malicious targets, it is essential to
guarantee the physical layer security in the safety-critical
DFRC systems. To guarantee the secrecy of legitimate userin the communication system, the optimization problem was
designed to maximize the SNR at the passive radar receiver
(RR) while keeping the secrecy r ate above a certain threshold.
While the aforementioned approaches are well-designed by
sophisticated techniques, the AN-aided physical layer security
remains to be explored for the DFRC systems under practical
constraints.
To the best of our knowledge, most of the present works
regarding secure transmission in DFRC system rely on the
assumption of precisely known channel state information (CSI)
at the transmitter. To address the beamforming design ina more general context, we take the imperfect CSI into account
in our work, which includes instantaneous and statistical
CSI with norm-bounded errors. Moreover, the well-known
S-procedure and Lagrange dual function have been adoptedto reformulate the optimization problem, which can be solved
by Semi-deﬁnite Relaxation (SDR) approach. In addition to
the CSI issues, we also explore the radar-speciﬁc targetuncertainty, where we employ a robust adaptation technique
for target tracking.
Accordingly, in this article, we propose several optimization
problems aiming at ensuring information transmission security
of the DFRC system. To be speciﬁc, we consider a MIMODFRC base station (BS) that is serving multiple legitimate
users while detecting targets. It should be noted that these
targets are assumed to be potential eavesdroppers. Moreover,spatially focused AN is employed in our methods. Throughout
the paper, we aim to minimize the SNR at the target while
ensuring the signal-to-interference-plus-noise ratio (SINR) at
each legitimate user. Within this scope, we summarize our
contributions as follows:
•We ﬁrst consider the ideal scenario under the assumptionsof perfect CSI and known precise location of targets. The
beampattern is formed by appr oaching to a given bench-
mark radar beampattern. By doing so, the formulated
optimization problem can be ﬁrstly recast as Fractional
programming (FP) problem [34], and then solved bythe SDR.
•We investigate the problem under the practical conditionof target location uncertainty, where we formulate abeampattern with a given angular interval that the targets
might fall into.
•We impose the imperfect communication CSI to theoptimization in addition to the above constraints, where
worst-case FP problems are fo rmulated to minimize the
maximum SNR at the target with bounded CSI errors.
•We consider the statistical CSI, which is more practicaldue to signiﬁcantly reduced feedback requirements [35].To tackle this scenario, we f urther formulate the eaves-
dropper SNR minimization problem considering the error
bound of statistical CSI.
•We derive the computational complexity for eachproposed algorithm.
Moreover, for clarity, some important insights are listed as
follows:
•When there is uncertainty on the target direction,the DFRC beam width inevita bly needs to be widened,
hence increasing the range of angles where the signal can
be eavesdropped. Accordingly, ensuring security requires
higher AN power, which impacts the power allocated tosignal transmission, to transmit conﬁdential information;
•When the peak to sidelobe ratio (PSLR) requirement forthe radar increases with a ﬁxed power budget, more power
is spent on satisfying the radar requirement. Accordingly,
the reduced power budget for communications deterio-rates the secrecy rate;
•When the CSI is only imperfectly or statistically known,with ﬁxed power budget, the secrecy rate deteriorates.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 3 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 85
Fig. 1. Dual-functional Radar-Communi cation system detecting target which
comprise a potential eavesdropper.
The system achieves higher secrecy rate when both the
target location and CSI are precisely known.
This article is organized as follows. Section II gives the system
model. The optimization problems based on perfect CSI are
addressed in Section III and IV for precise location and uncer-
tain direction of targets, respectively. In Section V and VI,more general context of imperfect CSI is considered, which
addresses issues with imperfect CSI under norm-bounded and
statistical errors, respectivel y. Section VII provides numerical
results, and Section VIII concludes the paper.
Notations : Unless otherwise speciﬁed, matrices are denoted
by bold uppercase letters (i.e., H), vectors are represented by
bold lowercase letters (i.e., x), and scalars are denoted by
normal font (i.e., α). Subscripts indicate the location of the
entry in the matrices or vectors (i.e., s
i,jandlnare the (i,j)-th
and the n-th element in Sandl, respectively). tr (·)and
vec (·)denote the trace and the vectorization operations. (·)T,
(·)H,(·)∗and(·)†stand for transpose, Hermitian transpose,
complex conjugate and Moore-Penrose pseudo-inverse of thematrices, respectively. diag (·)represents the vector formed
by the diagonal elements of the matrices and rank (·)is rank
operator. /bardbl·/bardbl,/bardbl·/bardbl
∞and/bardbl·/bardblFdenote the l2norm, l∞and
the Frobenius norm respectively. E{·}denotes the statistical
expectation. [·]+denotes max{·,0}.
II. S YSTEM MODEL
We consider a dual-functional MIMO DFRC system, which
consists of a DFRC base station, legitimate users and target
which is a potential eavesdropper, as shown in Fig. 1. TheDFRC system is equipped with uniform linear array (ULA) of
Nantennas, serving Ksingle-antenna users, while detecting
point-like target, which is a single-antenna eavesdropper. For
convenience, the multi-antenna transmitter, the legitimate users
and the target will be referred as Alice, Bobs and Everespectively.
A. Signal Model
In the scenario shown in Fig. 1, the DFRC base station Alice
intends to send conﬁdential information to single-antenna
legitimate users, i.e. Bobs, with the presence of the potential
eavesdropper, i.e. Eve. The received symbol vector at Bobsand Eve can be respectively modeled as
y=Hx+z
r=αa
H(θ)x+e, (1)
where H=[h1,h2,···,hK]T∈CK×Nis the channel
matrix, x∈CNis the transmitted signal vector, αrepresents
the complex path-loss coefﬁcient, θis the angle of tar-
get,a(θ)=/bracketleftbig
1ej2πΔs i n( θ)···ej2π(N−1)Δ sin( θ)/bracketrightbigT∈CN×1
denotes the steering vector of the transmit antenna array, with
Δbeing the normalized interval between adjacent antennas.
zandeare the noise vector and scalar, respectively, with
zi∼C N/parenleftbig
0,σ2
zi/parenrightbig
,e∼C N/parenleftbig
0,σ2
e/parenrightbig
.
Consider AN-aided transmit beamforming, the transmit
vectorxcan be written as
x=Ws+n (2)
wheres∈CKis the desired symbol vector of Bobs, where
we assume E/bracketleftbig
ssH/bracketrightbig
=I,W=[w1,w2,···,wK]∈CN×K
is the beamforming matrix, each column of the beamforming
matrixWrepresents the beamforming vector of each user, nis
an artiﬁcial noise vector1generated by Alice to avoid leaking
information to Eves. It is assumed that n∼C N (0,RN).
Additionally, we assume that the desired symbol vector sand
the artiﬁcial noise vector nare independent with each other.
According to [9], it is presumed that the above signal is
used for both radar and communication operations, where each
communication symbol is considered as a snapshot of a radar
pulse. Then, the covariance matrix of the transmitted dual-functional waveform
2can be given as
RX=E/bracketleftbig
xxH/bracketrightbig
=K/summationdisplay
i=1Wi+RN, (3)
where Wi/defineswiwH
i. Then, the beampattern can be
expressed as
Pbp=aH(θ)RXa(θ). (4)
B. Metrics
To evaluate the performance of the system, we deﬁne a
number of performance metrics in this subsection. Initially,
based on the aforementioned system model, the SINR of the
i-th user can be written as
SINR i=E/bracketleftBig/vextendsingle/vextendsinglehT
iwisi/vextendsingle/vextendsingle2/bracketrightBig
/summationtextK
k/negationslash=i,k=1E/bracketleftBig/vextendsingle/vextendsinglehT
iwksk/vextendsingle/vextendsingle2/bracketrightBig
+E/bracketleftBig/vextendsingle/vextendsinglehT
in/vextendsingle/vextendsingle2/bracketrightBig
+σ2zi
=hT
iWih∗i
/summationtextK
k/negationslash=i,k=1/parenleftbig
hT
iWkh∗i/parenrightbig
+/parenleftbig
hT
iRNh∗i/parenrightbig
+σ2zi.(5)
1Note that the generated AN is colored Gaussian noise, which has favorable
auto-correlation properties, and will hence impose negligible impact on
reconstructing the target at the radar receiver.
2In general radar system, the transmitted waveform is required to be known
at the receiver side in order to estimate the target. As the waveform changessymbol by symbol in DFRC systems, overhead generated by the sharing of
the transmitted waveform with the receiver is signiﬁcant. Thus, monostatic
radar is assumed to be employed in our system model rather than the bistaticradar, i.e., the transmitter and the receiver are collocated.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 4 页 ---
86 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
Equation (5) can be simpliﬁed
SINR i=tr/parenleftbig
h∗
ihTiWi/parenrightbig
/summationtextK
k/negationslash=i,k=1tr/parenleftbig
h∗
ihTiWk/parenrightbig
+tr/parenleftbig
h∗
ihTiRN/parenrightbig
+σ2zi.
(6)
The achievable transmission rate of legitimate users is given as
RCi=l o g2(1 + SINR i). (7)
Likewise, based on the given signal model in (2) and (3),
SNR at Eve can be given as [36]
SNR E=|α|2aH(θ)/summationtextK
i=1Wia(θ)
|α|2aH(θ)RNa(θ)+σ2e. (8)
In practice, the precise location of the target is unlikely to be
known in advance. Herewith, we d eﬁne a region of target loca-
tion angular uncertainty, which is given as [θ0−Δθ,θ0+Δθ],
withΔθbeing the uncertainty region given a priori. This
scenario will be elaborated in S ection IV . Then, the achievable
transmission rate of Eve can be expressed as
RE=l o g2(1 + SNR E). (9)
Additionally, the transmit power is expressed as
Pt=tr(RX). (10)
Given the achievable transmission rates of Bobs and Eve,
the worst-case secrecy rate of the system is deﬁned as3[37]
SR=m i n
i[RCi−RE]+. (11)
Note that since we focus on optimizing the covariance
matrices of both the DFRC signal and the AN, the range
sidelobe is not considered in this article due to its reliance onsymbol-level waveform designs, which is designated as our
future work.
C. Channel Model With Imperfect CSI and Statistical CSI
1) Imperfect CSI: According to [38], an additive channel
error model of i-th downlink user can be formulated as h
i=
˜hi+ei,w h e r e ˜hiis the estimated channel vector known
at Alice, and eidenotes the channel uncertainty within the
spherical region /Ifracturi={ei|/bardblei/bardbl2≤μ2
i}.
2) Statistical CSI: As the statistical CSI is known to the
BS instead of instantaneous CSI, we rewrite the SINR of the
i-th user as
SINR i=tr/parenleftBig
˜RhiWi/parenrightBig
/summationtextK
k/negationslash=i,k=1tr/parenleftBig
˜RhiWk/parenrightBig
+tr/parenleftBig
˜RhiRN/parenrightBig
+σ2z,(12)
where ˜Rhi=E/braceleftbig
h∗
ihTi/bracerightbig
denotes the i-th user’s down-
link channel covariance matrix with uncertainty. As a result,
the true channel covariance matrix can be modeled as Rhi=
˜Rhi+Δi,∀i,w h e r e Δi,∀iare the estimated error matrices.
The Frobenius norm of the error matrix of the i-th user is
assumed to be upper-bounded by a known constant δi,w h i c h
can be expressed as /bardblΔi/bardbl≤δi.
3The secrecy is deﬁned as the worst-case secrecy against all potential Bob-
Eve pairs in a multi-user multi-Eve scenario. We minimize the targets eaves-
dropping SNR subject to users SNR thresholds, expressed as the minimum
difference between the users and the Eves rates as we indicate in the followingsections.III. M INIMUM SNR OFEVEWITHPREMISE OF PERFECT
CSI AND TARGET DIRECTION
In this section, we aim to enhance the secrecy rate by
minimizing the Eves SNR while guaranteeing the required
SINR thresholds for the legitimate users, i.e. the SINR of
Bobs. Firstly, the formulated optimization problem is formu-
lated based on the assumption that the channel informationfrom Alice to Bobs in the communication system is perfectly
known. Meanwhile, the precise direction of the detected target
is assumed to be known to the transmitter, which will befurther relaxed by considering the uncertainty in the targets
location in the later sections. The complexity analysis is given
at the end of this section.
A. Problem Formulation
Let us ﬁrstly consider the SNR
Eminimization problem,
which should guarantee: a) individual SINR requirement ateach legitimate user, b) transmit power budget and c) a desired
radar spatial beampattern. It is noteworthy that the time-
domain metric peak-to-average power ratio (PAPR) in the
DFRC system can be explicitly dealt with by including PAPR
constraints in our optimization problems such as PAPR mini-mization convex optimization method adopted in [39], or con-
stant modulus (CM) constraints in [12], [40]–[42]. To keep
the focus on the secrecy aspect, PAPR is not considered inour optimization problem throughout this article, which is
designated as our future work. Note that an ideal radar beam-
pattern should be obtained before designing the beamforming
and artiﬁcial noise, which can be generated by solving the
following constrained least-squares (LS) problem [9], [43] asan example
min
η,RdM/summationdisplay
m=1/vextendsingle/vextendsingleηPd(θm)−aH(θm)Rda(θm)/vextendsingle/vextendsingle2
s.t.tr(Rd)=P0,
Rd/followsequal0,Rd=RH
d,
η≥0, (13)
where ηis a scaling factor, P0represents the transmission
power budget, {θm}M
m=1denotes an angular grid covering
the detection angular range in [−π/2,π/2],a(θm)denotes
steering vector, Pd(θm)is the desired ideal beampattern gain
atθm,Rdrepresents the desired wave form covariance matrix.
Given a covariance matrix Rdthat corresponds to a well-
designed MIMO radar beampattern, the fractional program-
ming optimization problem of minimizing SNR Ecan be
formulated as
min
Wi,RN|α|2aH(θ0)/summationtextK
i=1Wia(θ0)
|α|2aH(θ0)RNa(θ0)+σ2e, (14a)
s.t./bardblRX−Rd/bardbl2≤γbp, (14b)
SINR i≥γb,∀i, (14c)
tr(RX)=P0, (14d)
Wi=WH
i,Wi/followsequal0,∀i, (14e)
rank(Wi)=1,∀i, (14f)
RN=RH
N,RN/followsequal0, (14g)
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 5 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 87
where the constraints Wi=WH
i,Wi/followsequal0,rank(Wi)=
1,∀i,are equivalent to constraining Wi=wiwH
i[20].θ0
represents the direction of Eve known at Alice,4γbpis the
pre-deﬁned threshold that constraints the mismatch betweendesigned covariance matrix R
Xand the desired Rd,a n d
ﬁnally γbdenotes the predeﬁned SINR threshold of each
legitimate user.
For simplicity, we deﬁne the set of I×Ipositive semi-
deﬁnite matrices as P=/braceleftbig
A|A∈CI×I,AT=A,A/followsequal0/bracerightbig
.
Accordingly, (14e) and (14g) can be written as Wi∈P,∀i
andRN∈P, respectively. To solve the problem (14), let us
employ the SDR approach by omitting the rank (Wi)=1
constraint in (14f), based on which we relax the optimization
problem as
min
Wi,RN|α|2aH(θ0)/summationtextK
i=1Wia(θ0)
|α|2aH(θ0)RNa(θ0)+σ2e, (15a)
s.t./bardblRX−Rd/bardbl2≤γbp, (15b)
SINR i≥γb,∀i, (15c)
tr(RX)=P0, (15d)
Wi∈P,∀i, (15e)
RN∈P. (15f)
By noting the fact that problem (15) is still non-convex due to
the fractional objective function, we propose in the following
an iterative approach to solve the problem efﬁciently.
B. Efﬁcient Solver
Following [34], (15) is single-ratio FP problem, which can
be solved by employing the Dinkelbach’s transform demon-
strated in [44], where the globally optimal solution can be
obtained by solving a sequence of SDPs. To develop thealgorithm, we ﬁrstly introduce a scaling factor c=SNR
E,
which is an auxiliary variable. We then deﬁne two scal-
ing variables UandV, which are nonnegative and positive
respectively, where U=|α|2aH(θ)/summationtextK
i=1Wia(θ),∀i,V=
|α|2aH(θ)RNa(θ)+σ2
e. As a result, the FP problem (15) is
equivalent to
min
Wi,RNU−cV, (16a)
s.t./bardblRX−Rd/bardbl2≤γbp, (16b)
SINR i≥γb,∀i, (16c)
tr(RX)=P0, (16d)
Wi∈P,∀i, (16e)
RN∈P, (16f)
where ccan be iteratively updated by
c[iter+1 ]=U[iter]
V[iter], (17)
4The MIMO radar is assumed to be with two working modes including
searching and tracking. In the search mode, the radar transmits a spatiallyorthogonal waveform, which formulate s the omni-directional beampattern.
Potential targets can be searched via the beampattern. Then, the radar is
able to track potential targets via transmitting directional waveforms. Thus,the precise location is available to be known at Alice.where iteris the index of iteration. For clarity, we summarize
the above in Algorithm 1. According to [34], it is easy
to prove the convergence of the algorithm given the non-
increasing property of cduring each iteration. It is noted
that the SDR approach generates an approximated solution
to the optimization problem (14) by neglecting the rank-one
constraint. Accordingly, eigenvalue decomposition or Gaussianrandomization techniques are commonly employed to obtain
a suboptimal solution. In our case, it is noteworthy that the
solution obtained from the SDR solver can be guaranteed to
be rank-1
5[45].
Algorithm 1 Alogrithm for Solving FP Problem (15)
Input: H,a(θ0),σ2
e,σ2
z,α ,γ bp,γb,P0,it er max≥2,ε
Output: W(iter)
i,R(iter)
N,i=1,···,K
1. Compute Rd. Reformulate problem (13a) by (14).
Set the iteration threshold ε> 0. Initialize c(0),c(1),/vextendsingle/vextendsinglec(1)−c(0)/vextendsingle/vextendsingle>ε.
while iter≤itermax and/vextendsingle/vextendsingleciter+1−citer/vextendsingle/vextendsingle≥εdo
2. Obtain W(iter)
i,R(iter)
N,i=1,···,Kby solving the
SDP problem (16).3. Update cby (17).
4.iter=iter+1.
end while
C. Complexity Analysis
In this subsection, the computational complexity of
Algorithm 1 is analyzed as follows. Note that SDP problems
are commonly solved by the interior point method (IPM) [46],
which obtains an /epsilon1-optimal solution after a sequence of
iterations with the given /epsilon1. In problem (14), it is noted
that the constraints are linear matrix inequality (LMI) except
for (14b), which is a second-order cone (SOC) [47] constraint.Thus, we demonstrate the complexity in Table I, where N
iter
represents iteration times. For simplicity, the computational
complexity can be given as O/parenleftbig√
2Niterln (1//epsilon1)K3.5N6.5/parenrightbig
by reserving the highest order term.
IV . E VE’SSNR M INIMIZATION WITHUNCERTAINTY IN
THETARGET DIRECTION AND PERFECT CSI
In practice, the precise location of the target is difﬁcult to
be known at the transmitter. In this section, we consider the
scenario where a rough estimate of the target’s angle, instead
of its precise counterpart, is available at Alice. Accordingly,the following beampattern design aims at achieving both a
desired main-beam width covering the possible angle uncer-
tainty interval of the target as well as a minimized sidelobepower in a prescribed region. First of all, the optimization
problem is formulated to form a wide main-beam beampat-
tern, following which an efﬁcient solver is proposed. Finally,
the complexity analysis is given at the end of this section.
5Letkandmdenote the number of n×nsquare matrix variables and linear
constraints, separately. For a compl ex separable QCQP, the SDR is tight if
m≤k+2, with the assumption that none of the solution
/A8X∗
i
/A9k
i=1to SDR
satisﬁesX∗
i=0 for some i[45].
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 6 页 ---
88 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
A. Problem Formulation
In this subsection, we consider the case that the angle
uncertainty interval of the target is roughly known within the
angular interval [θ0−Δθ,θ0+Δθ]. To this end, the target
from every possible direction should be taken in to considera-
tion when formulating the optimization problem. Accordingly,
the objective is given as the sum of Eve’s SNR at all the
possible locations as follows. Due to the uncertainty of target
location, wider beampattern needs to be formulated towardsthe uncertain angular interval to avoid missing the target.
Inspired by the 3dB main-beam width beampattern design for
MIMO radar [48], we propose a scheme aiming at keeping aconstant power in the uncertain angular interval, which can be
formulated as the following optimization problem
min
Wi,RN/summationdisplay
θm∈Φ|α|2aH(θm)/summationtextK
i=1Wia(θm)
|α|2aH(θm)RNa(θm)+σ2e(18a)
s.t.aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (18b)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (18c)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (18d)
SINR i≥γb,∀i, (18e)
tr(RX)=P0, (18f)
Wi∈P,∀i, (18g)
rank(Wi)=1,∀i, (18h)
RN∈P, (18i)
where θ0is the main-beam location, Ωdenotes the sidelobe
region of interest, Φdenotes the wide main-beam region, γs
is the bound of the sidelobe power.
Likewise, recall the probl em (14), SDR technique is
adopted. To solve the above su m-of-ratio problem, according
to [34], we equivalently recast the minimization problem by
neglecting rank-1 constraint in (18h) as
max
Wi,RN/summationdisplay
θm∈Φ|α|2aH(θm)RNa(θm)+σ2
e
|α|2aH(θm)/summationtextK
i=1Wia(θm)(19a)
s.t.aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (19b)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (19c)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (19d)
SINR i≥γb,∀i, (19e)
tr(RX)=P0, (19f)
Wi∈P,∀i, (19g)
RN∈P, (19h)
It is noted that problem (19) is still non-convex. The approach
to solve this sum-of-ratio FP problem is described in the
following.B. Efﬁcient Solver
To present the solution to problem (19), we ﬁrstly refer
to [34] and denote
A(θm)=|α|2aH(θm)RNa(θm)+σ2
e
B(θm)=|α|2aH(θm)/summationdisplayK
i=1Wia(θm)
One step further, the sum-of-ratio problem is equivalent to the
following optimization problem, which can be rewritten in theform
max
Wi,RN,y/summationdisplay
θm∈Φ/parenleftBig
2ym/radicalbig
A(θm)−y2
mB(θm)/parenrightBig
, (20a)
s.t.aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (20b)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (20c)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (20d)
SINR i≥γb,∀i, (20e)
tr(RX)=P0, (20f)
Wi∈P,∀i, (20g)
RN∈P, (20h)
whereydenotes a collection of variables {y1,···,yM}.T h e
optimal ymcan be obtained in the following closed form when
θmis ﬁxed
y∗
m=/radicalbig
A(θm)
B(θm). (21)
To this end, the reformulated optimization problem (20) can
be solved. Then, eigenvalue decomposition or Gaussian ran-
domization is required to get the approximated solution. For
clarity, the above procedure is summarized in Algorithm 2.
Algorithm 2 Algorithm for Solving Sum-of-Ratio
Problem (19)
Input: H,a(θ0),ε>0,σ2
e,σ2
z,α ,γ b,γs,P0,it er max≥2,
Δθ.
Output: W(iter)
i,R(iter)
N,i=1,···,K.
1. Reformulate problem (19) by (20).while iter≤iter
max and/vextenddouble/vextenddoubleyiter+1−yiter/vextenddouble/vextenddouble≥εdo
2. Obtain W(iter)
i,R(iter)
N,i=1,···,Kby solving the
new convex optimization problem (20).3. Update yby (21).
4.iter=iter+1.
end while
C. Complexity Analysis
We end this section by computing the complexity of solving
problem (18). It is noted that all the constraints can beconsidered as LMIs in optimization problem (18). We denote
Φ
0=card(Φ) andΩ0=card(Ω) as the cardinality of
ΦandΩ, respectively. Thus, referring to [46], we give the
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 7 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 89
computational complexity in Table I, which can be simpliﬁed
asO/parenleftbig
3√
2Niterln (1//epsilon1)K3.5N6.5/parenrightbig
by reserving the highest
order.
V. R OBUST BEAMFORMING WITHIMPERFECT CSI
ANDTARGET DIRECTION UNCERTAINTY
In this section, based on the models presented in the
previous sections, we consider the case that the perfect channelinformation is not available at the base station. By relying
on the method of robust optimization, we formulate an opti-
mization problem aiming for designing the dual-functionalbeamformer that is robust to the channel uncertainty, which
is bounded in a spherical region. Meanwhile, to guarantee
the generality, we minimize the worst-case SNR received
at the target in the angular interval of possible location of
potential eavesdropper. Then, an efﬁcient solver tailored forthe considered fractional optimization problem is developed,
following by a detailed complexity analysis.
A. Problem Formulation
Recalling the channel model demonstrated in Section II,
we formulate the optimization problem when the CSI isimperfectly known to the transmitter as follows. According to
the well-known S-procedure [49], ∀e
H
iei≤μ2
i, the constraint
that guarantees the worst-case SINR of legitimates users can
be reformulated as [38]
/parenleftBig
˜hi+ei/parenrightBigH⎛
⎝Wi−γbK/summationdisplay
k=1,k/negationslash=iWk−γbRN⎞
⎠/parenleftBig
˜hi+ei/parenrightBig
−γbσ2≥0,∀i.(22)
Then, we minimize the possible maximum Eve’s SNR in
the main-beam region of interest, which yields the followingrobust optimization problem
min
Wi,RN,timax
θm∈Φ|α|2aH(θm)/summationtextK
i=1Wia(θm)
|α|2aH(θm)RNa(θm)+σ2e(23a)
s.t./parenleftbigg˜hT
iYi˜h∗
i−γbσ2−tiμ2
i˜hT
iYi
Yi˜h∗
i Yi+tiIN/parenrightbigg
/followsequal0,∀i,
(23b)
Yi:=Wi−γb⎛
⎝/summationdisplay
k/negationslash=iWk⎞
⎠−γbRN
aH(θ0)RXa(θ0)−aH(θm)RXa(θm)/greaterorequalslantγs,
∀θm∈Ω (23c)
aH(θk)RXa(θk)/lessorequalslant(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (23d)
(1−α)aH(θ0)RXa(θ0)/lessorequalslantaH(θk)RXa(θk),
∀θk∈Φ (23e)
tr(RX)=P0, (23f)
ti/greaterorequalslant0,∀i, (23g)
Wi∈P,∀i, (23h)
rank(Wi)=1,∀i, (23i)
RN∈P, (23j)where Φ=[ θ0−Δθ,θ0+Δθ]is the main-beam region of
interest, m=1,···,M.Mrepresents the number of detecting
angles in the interval Φ, and ﬁnally t=[t1,···,tK]is an
auxiliary vector relying on the S-procedure.
B. Efﬁcient Solver
To solve problem (23), the SDR approach is adopted
again by dropping the rank-1 constraint in (23i). Moreover,
the objective function (23a) can be transformed to a max-min
problem initially which is given as
max
Wi,RN,timin
θm∈Φ|α|2aH(θm)RNa(θm)+σ2
e
|α|2aH(θm)/summationtextK
i=1Wia(θm). (24)
To verify this, we introduce a variable zand deﬁne
A(θm)= a(θm)aH(θm). The objective function (24)
can be rewritten as max
Wi,RN,ti,zz, which subjects to
z≤/parenleftBig
tr(A(θm)RN)+σ2/slashBig
|α|2/parenrightBig/slashBig
tr/parenleftBig
A(θm)/summationtextK
i=1Wi/parenrightBig
and any other constraints in (23). Likewise, we denote
C(θm)=tr(A(θm)RN)+σ2/slashBig
|α|2
D(θm)=tr/parenleftbigg
A(θm)/summationdisplayK
i=1Wi/parenrightbigg
The aforementioned constraint is equivalent to
z≤max
ym/parenleftBig
2ym/radicalbig
C(θm)−y2
mD(θm)/parenrightBig
,
which is a less-than-max inequality, so max
ymcan be integrated
into the objective. Consequently, problem (23) is reformu-
lated as
max
Wi,RN,y,ti,zz, (25a)
s.t.2ym/radicalbig
C(θm)−y2
mD(θm)≥z,θm∈Φ,∀m,
(25b)/parenleftbigg˜hT
iYi˜h∗
i−γbσ2−tiμ2
i˜hT
iYi
Yi˜h∗
i Yi+tiIN/parenrightbigg
/followsequal0,∀i,
(25c)
Yi:=Wi−γb⎛
⎝/summationdisplay
k/negationslash=iWk⎞
⎠−γbRN
aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (25d)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (25e)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (25f)
tr(RX)=P0, (25g)
ti≥0,∀i, (25h)
Wi∈P,∀i, (25i)
RN∈P, (25j)
where ymis an auxiliary variable, each ymcorresponds to
the radar detecting angles θmin the main-beam region of
interest Φ. We refer the rest variables to the deﬁnitions which
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 8 页 ---
90 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
we presented in the previous sections. Note that problem
(25) is convex and can be readily tackled. Here, we deﬁne
a collection of variables y={y1,···,yM}. To solve this
problem, we apply the quadratic transform and optimize theprimal variables W
i,RN,tiand the auxiliary variable collec-
tionyin an alternating manner. When the primal variables are
obtained by initializing the collection y, the optimal ymcan
be updated by
y∗
m=/radicalbig
C(θm)
D(θm). (26)
To this end, eigenvalue decomposition or Gaussian randomiza-
tion is required to obtain approximated solutions. For clarity,solution to problem (25) can be summarized as Algorithm 3.
Algorithm 3 Method for Solving Multiple-Ratio FP
Problem (24)
Input: a(θ0),˜hi,σ2
e,σ2
z,α ,γ b,γs,P0, CSI estimation error
threshold μi>0,Δθ, iteration threshold ε> 0,
itermax/greaterorequalslant2.
Initialization : Set initial values for y(0),y(1),w h i c h/vextenddouble/vextenddoubley(1)−y(0)/vextenddouble/vextenddouble>ε.
while iter/lessorequalslantitermax and/vextenddouble/vextenddoubley(iter)−y(iter−1)/vextenddouble/vextenddouble/greaterorequalslantεdo
1. Reformulate problem (24) by replacing the fractional
objective function with the form in (25b).
2. Reconstruct the problem with variable z.
3. Obtain W(iter)
i,R(iter)
N,∀iby solving the optimization
problem, and then update yby (26).
4. Update the primal variables by (25), over RN,Wi,∀i
for ﬁxed y.
end while
Output: RN,Wi,ti,z,∀i.
C. Complexity Analysis
The complexity of Algorithm 3 is analyzed in this sub-
section. Similarly, ΦandΩcan be regarded as discrete
domains. We denote Φ0=card(Φ) andΩ0=card(Ω)as
the cardinality of ΦandΩ, respectively. All the constraints
in problem (23) are LMIs. Speciﬁcally, we notice that the
problem is composed by 3Φ0+Ω0+K+1LMI constraints
of size 1, 2K+2 LMI constraints of size N,a n d KLMI
constraints of size N+1. For simplicity, we reserve the highest
order of computational complexity, which can be given asO/parenleftbig
4√
3Niterln (1//epsilon1)K3.5N6.5/parenrightbig
.
VI. R OBUST OPTIMAL BEAMFORMING WITH
STATISTICAL CSI A NDTARGET
DIRECTION UNCERTAINTY
In this section, we consider the extension of the scenario in
section V , where the channels from Alice to Bobs vary rapidly.
As a result, the instantaneous CSI is difﬁcult to be estimated.
Note that the second-order channel statistics, which vary muchmore slowly, can be obtained by the BS through long-term
feedback. Nevertheless, even in the event that the statistical
CSI is known at Alice, the uncertainty is always inevitable.We therefore take the uncertainty matrix into consideration by
employing additive errors to the channel covariance matrix.
Likewise, the complexity analysis is given at the end of this
section.
A. Problem Formulation
We ﬁrstly recall the reformulation of the SINR of the
i-th user and the channel model with statistical CSI presented
in Section II. To this end, based on Lagrange dual function[35], [50], the constraint corresponding to QoS of i-th user
can be formulated as
−δ
i/bardblAi+Zi/bardbl−tr(Rhi(Zi+Ai))−γbtr(RhiRN)
−γbσ2
z≥0Zi∈P,∀i
whereAi=γb/summationtextK
k=1,k/negationslash=iWk−Wi,∀i. Recalling the opti-
mization problem in Section V-A, likewise, the robust beam-
forming problem with erroneous statistical CSI is given as
min
Wi,RN,Zimax
θm∈Φ|α|2aH(θm)/summationtextK
i=1Wia(θm)
|α|2aH(θm)RNa(θm)+σ2e(27a)
s.t.−δi/bardblAi+Zi/bardbl−tr(Rhi(Zi+Ai))
−γbtr(RhiRN)
−γbσ2
z≥0,∀i, (27b)
aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (27c)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (27d)
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (27e)
tr(RX)=P0, (27f)
Zi∈P,∀i, (27g)
Wi∈P,∀i, (27h)
rank(Wi)=1,∀i, (27i)
RN∈P. (27j)
We note that the problem (27) can be solved with SDR
approach by dropping the rank-one constraint in (27i). One
step further, similar to (23), problem (27) can be reformulated
in a similar way, given by
max
Wi,RN,Zi,zz (28a)
s.t.2ym/radicalbig
C(θm)−y2
mD(θm)≥z,θm∈Φ,∀m,
(28b)
−δi/bardblAi+Zi/bardbl−tr(Rhi(Zi+Ai))
−γbtr(RhiRN)
−γbσ2
z≥0,∀i, (28c)
aH(θ0)RXa(θ0)−aH(θm)RXa(θm)≥γs,
∀θm∈Ω (28d)
aH(θk)RXa(θk)≤(1 +α)aH(θ0)RXa(θ0),
∀θk∈Φ (28e)
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 9 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 91
TABLE I
COMPLEXITY ANALYSIS
Fig. 2. Beampatterns with various target direction uncertainty interval when (a) CSI is known, (b) CSI is imperfectly known and (c) statistical CSI is
imperfectly known.
(1−α)aH(θ0)RXa(θ0)≤aH(θk)RXa(θk),
∀θk∈Φ (28f)
tr(RX)=P0, (28g)
Zi∈P,∀i, (28h)
Wi∈P,∀i, (28i)
RN∈P. (28j)
Note that problem (28) is SDP feasibility and can be solved
in polynomial time using interior-point algorithms6[35].
B. Complexity Analysis
The complexity of problem (27) is given as follows. As is
noted in problem (28), almost all the constrains are LMI exceptfor the SOC constraint (28c). Likewise, we denote Φ
0=
card(Φ)andΩ0=card(Ω)as the cardinality of ΦandΩ.
Note that the problem is composed by KSOC constraints of
size 1, Ω0+3 Φ 0+1LMI constraints of size 1, and 4K+2
LMIs of size N. Accordingly, we compute the complexity
as is shown in Table I. When the CSI is statistically known,
the computational complexity can be simply demonstrated as
O/parenleftbig
5√
2Niterln (1//epsilon1)K3.5N6.5/parenrightbig
. which is the complexity of
each iteration. Then, The calculated complexities of all the
proposed optimizations are summarised in Table I.
VII. N UMERICAL RESULTS
To evaluate the proposed methods, numerical results based
on Monte Carlo simulations are shown in this section to
6Since solutions Wi,i=1,···,Kobtained from solving the convex
relaxation problems (15), (19), (24) and (28) are all rank-one, the rank-
one approximation procedures, e.g., eigenvalue decomposition or Gaussianrandomization, can be omitted in general for our study.validate the effectiveness of the proposed beamforming
method. Without loss of generality, each entry of channel
matrix His assumed to obey standard Complex Gaussian dis-
tribution, i.e. hi,j∼C N (0,1). We assume that the DFRC base
station employs a ULA with half-wavelength spacing between
adjacent antennas. In the follo wing simulations, the number of
antennas is set as N=1 8 and the number of legitimate users
isK=4. Moreover, for convenience, the noise level of the
eavesdropper is assumed to be the same as that of the intendedreceivers. The constrained beamforming design problems in
Section II-Section V are solved by the classic SDR technique
using the CVX toolbox [51].
A. Beam Gain And Secrecy Rate Analysis
We ﬁrst show the resultant radar beampattern in Fig. 2 with
different angular interval of target location uncertainty, i.e.
[−5
◦,5◦]and[−10◦,10◦]. The SINR threshold of each legiti-
mate user is set as γb=1 0 dB. The narrow beampattern when
the target location is precisely known at the BS is set as a
benchmark. It is found that the desired beampattern with widemain-beam is obtained by solving the proposed algorithms,
which maintain the same power in the region of possible target
location. Additionally, it is noted that with the expansion oflocation uncertainty angular interval, the power gain of main-
beam reduces.
The worst-case secrecy rate in terms of increasing SINR
threshold of each user is demonstrated in Fig. 3, where
the power budget is set as P
0=2 0 dBm and P0=
30dBm respectively. In this case, we set the sidelobe power
threshold γs=4 0 dB. Basically, in the SNR Eminimization
problem, the secrecy rate increases with the growth of γb.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 10 页 ---
92 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
Fig. 3. Worst-case secrecy rate versus the threshold of SINR at legitimate
users, with various transmission powe r budget, where solid and dashed lines
represent power budget P0=3 0 dBm and P0=2 0 dBm respectively,
N=1 8 ,K=4,Δθ=5◦.
Fig. 4. Convergence of (a) SNR of Eve and (b) secrecy rate for the target
SNR minimization algorithm, N=1 8,K=4,P0=3 0 dBm,γb=1 0 dB.
It is noteworthy that the system achieves higher secrecy rate
when both the target location and CSI are precisely known.When we increase the power budget, the secrecy rate grows
to some extent. In addition, the erroneous instantaneous and
statistical CSI affects the performance of secrecy rate slightlycomparing to the perfect CSI scenario.
In Fig. 4, we evaluate the convergence of target SNR and
secrecy rate. In these cases, the same system parameters are set
as previous simulations. In Fig. 4(a), the SINR of the target is
conﬁrmed to convergent to a minimum. In robust beamformingdesign problems, the SNR of target decreases slightly with the
increasing iteration number, which results in the slight growth
of secrecy rate as is shown in Fig. 4(b).
B. Trade-off Between the Performance of Radar And
Communication System
In this subsection, we evaluate the performance trade-off
between radar and communication system. Fig. 5 shows theFig. 5. Secrecy rate with different angular intervals, N=1 8 ,K=4,
P0=3 0 dBm, with γb=1 0 dB and γb=1 5 dB, respectively.
Fig. 6. Worst-case secrecy rate versus the sidelobe power with various
SINR threshold of legitimate users for the Algorithm 2, N=1 8 ,K=4,
P0=3 0 dBm,Δθ=5◦.
secrecy rate performance with various angular intervals for
γb=1 0 dB and γb=1 5 dB. The main-beam power decreases
when the target uncertainty incr eases, then the leaking infor-
mation would get less, which improve the secrecy rate. As is
demonstrated in Fig. 5, the secrecy rate increases with the
growth of target uncertainty interval. Besides, with 5dB growth
of legitimate user SINR threshold, the secrecy rate increases
0.5bit/s/Hz approximately.
Fig. 6 demonstrates the secrecy rate performance versus the
threshold of sidelobe with P0=3 0 dBm,Δθ=5◦,w h i c h
reveals the trade-off between the performance of radar and
communication systems. In Algorithm 2, the power difference
between main beam and sidelobe increases with the growth of
γs, which results in the increasing possibility of information
leaking. As the numerical result shown in Fig. 6, it is notable
that the secrecy rate decreases with the growth of γs, especially
the tendency gets obvious when γsis greater than 30dB.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 11 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 93
Fig. 7. Achieved secrecy rate with different error bounds in the scenario of
known imperfect CSI, N=1 8,K=4,P0=3 0 dBm.
Fig. 8. Worst-case secrecy rate versus different error bounds when statistical
CSI is imperfectly known, N=1 8,K=4,P0=3 0 dBm,γb=1 0 dB.
C. Robust Beamforming Performance
As the norm of CSI error is bounded by a constant,
the secrecy rate performance versus error bound is illustrated
in Fig. 7, with different location uncertainty. With the growth
of error bound, the achievable SINR at each legitimate userkeeps being above the given threshold but not a constant
according to constraints (25c) and (27b). We note that the
worst-case secrecy rate reduces after a certain value with the
increasing error bound, because of the different changing rate
between target SNR and user SINR corresponding to various
error bounds in Fig. 7. Whereas, as is shown in Fig. 8,
the secrecy rate keeps increas ing with the growth of error
bound. In addition, the robust beamforming designs achievehigher secrecy rate when the location uncertainty is limited in
a larger interval.VIII. C
ONCLUSION
In this article, optimization based beamforming designs
have been addressed for MIMO DFRC system, which aimed
at ensuring the security of information transmission in case of
leaking to targets by adding AN at the transmitter to confuse
the potential eavesdropper. Speciﬁcally, we have minimizedthe SNR of the target which is regarded as the potential
eavesdropper while keeping the each legitimate user’s SINR
above a certain constant to ensure the secrecy rate of the DFRCsystem. Throughout this article, the optimization beamforming
problem has been designed with perfect CSI and imperfect
CSI, as well as with the accurate and inaccurate target location
information.
First of all, both precise location of target and perfect CSI
have been assumed to be known at BS, which gained the
highest secrecy rate according to the numerical results. When
the target location was uncer tain, the main-beam power has
decreased with the growth of the uncertainty angular interval.
Moreover, the secrecy rate versus different thresholds of
sidelobe has been demonstrated, which revealed the trade-off
between radar and communication system performance. Then,
we have formulated target SNR minimization problem withimperfect instantaneous CSI and statistical CSI known to the
base station respectively. As shown in the numerical results,
the beamforming design has b een feasible in both robust
scenarios. Finally, simulation r esults have been presented to
show the secrecy rate tendency effected by error bound with
various target location uncertainty.
R
EFERENCES
[1] D. Oyediran, Spectrum Sharing: Overview and Challenges of Small
Cells Innovation in the Proposed 3.5 GHz Band .S a nD i e g o ,C A ,U S A :
International Foundation for Teleme tering, 2015. [Online]. Available:
https://repository.arizona.edu/handle/10150/596402
[2] B. Li, A. P. Petropulu, and W. Trappe, “Optimum co-design for spectrum
sharing between matrix completion based MIMO radars and a MIMO
communication system,” IEEE Trans. Signal Process. , vol. 64, no. 17,
pp. 4562–4575, Sep. 2016.
[3] A. Ghasemi and E. S. Sousa, “Collaborative spectrum sensing for
opportunistic access in fading environments,” in Proc. 1st IEEE Int.
Symp. New Frontiers Dyn. Spectr. Access Netw. (DySPAN) , Nov. 2005,
pp. 131–136.
[4] C. W. Kim, J. Ryoo, and M. M. Buddhikot, “Design and implementation
of an end-to-end architecture for 3.5 GHz shared spectrum,” in Proc.
IEEE Int. Symp. Dyn. Spectr. Access Netw. (DySPAN) , Sep. 2015,
pp. 23–34.
[5] G. Staple and K. Werbach, “The end of spectrum scarcity [spectrum
allocation and utilization],” IEEE Spectr. , vol. 41, no. 3, pp. 48–52,
Mar. 2004.
[6] S. Sodagari, A. Khawar, T. C. Clan cy, and R. McGwier, “A projection
based approach for radar and telecommunication systems coexistence,”
inProc. IEEE Global Commun. Conf. (GLOBECOM) , Dec. 2012,
pp. 5010–5014.
[7] A. Turlapaty and Y . Jin, “A joint design of transmit waveforms for
radar and communications systems in coexistence,” in Proc. IEEE Radar
Conf. , May 2014, pp. 315–319.
[8] B. Li and A. P. Petropulu, “Joint transmit designs for coexistence of
MIMO wireless communications and spa rse sensing radars in clutter,”
IEEE Trans. Aerosp. Electron. Syst. , vol. 53, no. 6, pp. 2846–2864,
Dec. 2017.
[ 9 ]F .L i u ,C .M a s o u r o s ,A .L i ,H .S u n ,a n dL .H a n z o ,“ M U - M I M O
communications with MIMO radar: From co-existence to joint transmis-
sion,” IEEE Trans. Wireless Commun. , vol. 17, no. 4, pp. 2755–2770,
Apr. 2018.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 12 页 ---
94 IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, VOL. 20, NO. 1, JANUARY 2021
[10] F. Liu, C. Masouros, A. Li, and T. Ratnarajah, “Robust MIMO beam-
forming for cellular and radar coexistence,” IEEE Wireless Commun.
Lett., vol. 6, no. 3, pp. 374–377, Jun. 2017.
[11] F. Liu, C. Masouros, A. Li, T. Ratnarajah, and J. Zhou, “MIMO
radar and cellular coexistence: A power-efﬁcient approach enabled by
interference exploitation,” IEEE Trans. Signal Process. , vol. 66, no. 14,
pp. 3681–3695, Jul. 2018.
[12] F. Liu, L. Zhou, C. Masouros, A. Li, W. Luo, and A. Petrop-
ulu, “Toward dual-functional radar-communication systems: Optimal
waveform design,” IEEE Trans. Signal Process. , vol. 66, no. 16,
pp. 4264–4279, Aug. 2018.
[13] F. Liu, L. Zhou, C. Masouros, A. Lit, W. Luo, and A. Petropulu, “Dual-
functional cellular and radar tra nsmission: Beyond coexistence,” in
Proc. IEEE 19th Int. Workshop Signal Process. Adv. Wireless Commun.
(SPAWC) , Jun. 2018, pp. 1–5.
[14] L. Zhou et al. , “Optimal waveform design for dual-functional MIMO
radar-communication systems,” in Proc. IEEE/CIC Int. Conf. Commun.
China (ICCC) , Aug. 2018, pp. 661–665.
[15] A. Hassanien, M. G. Amin, Y . D. Zhang, and F. Ahmad, “Dual-function
radar-communications: Information e mbedding using sidelobe control
and waveform diversity,” IEEE Trans. Signal Process. , vol. 64, no. 8,
pp. 2168–2181, Apr. 2016.
[16] V. Va, T. Shimizu, G. Bansal, and R. W. Heath, Jr., “Millimeter wave
vehicular communications: A survey,” Found. Trends Netw. , vol. 10,
no. 1, pp. 1–113, 2016.
[17] C.-H. Lim, Y . Wan, B.-P. Ng, and C.-M. See, “A real-time indoor
WiFi localization system utilizing smart antennas,” IEEE Trans. Consum.
Electron. , vol. 53, no. 2, pp. 618–622, May 2007.
[18] G. M. Dillard, M. Reuter, J. Zeiddler, and B. Zeidler, “Cyclic code shift
keying: A low probability of intercept communication technique,” IEEE
Trans. Aerosp. Electron. Syst. , vol. 39, no. 3, pp. 786–798, Jul. 2003.
[19] P. M. McCormick, B. Ravenscroft, S. D. Blunt, A. J. Duly, and
J. G. Metcalf, “Simultaneous rada r and communication emissions from
a common aperture, part II: Experimentation,” in Proc. IEEE Radar
Conf. (RadarConf) , May 2017, pp. 1697–1702.
[20] W.-C. Liao, T.-H. Chang, W.-K. Ma, and C.-Y. Chi, “QoS-based transmit
beamforming in the presence of eave sdroppers: An optimized artiﬁcial-
noise-aided approach,” IEEE Trans. Signal Process. , vol. 59, no. 3,
pp. 1202–1216, Mar. 2011.
[21] S. Shaﬁee, N. Liu, and S. Ulukus, “Towards the secrecy capacity
of the Gaussian MIMO wire-tap channel: The 2-2-1 channel,” 2007,
arXiv:0709.3541 . [Online]. Available: http://arxiv.org/abs/0709.3541
[22] F. Oggier and B. Hassibi, “The secrecy capacity of the MIMO
wiretap channel,” 2007, arXiv:0710.1920 . [Online]. Available:
http://arxiv.org/abs/0710.1920
[23] E. Ekrem and S. Ulukus, “The secrecy capacity region of the Gaussian
MIMO multi-receiver wiretap channel,” IEEE Trans. Inf. Theory , vol. 57,
no. 4, pp. 2083–2114, Apr. 2011.
[24] S. Goel and R. Negi, “Guaranteeing secrecy using artiﬁcial noise,” IEEE
Trans. Wireless Commun. , vol. 7, no. 6, pp. 2180–2189, Jun. 2008.
[25] R. Negi and S. Goel, “Secret communication using artiﬁcial noise,” in
Proc. IEEE Veh. Technol. Conf. , 2005, vol. 62, no. 3, p. 1906.
[26] H. Ma, J. Cheng, X. Wang, and P. Ma, “Robust MISO beamforming with
cooperative jamming for secure transmission from perspectives of QoSand secrecy rate,” IEEE Trans. Commun. , vol. 66, no. 2, pp. 767–780,
Feb. 2018.
[27] Z. Lin, M. Lin, J. Ouyang, W.-P. Zhu, A. D. Panagopoulos, and
M.-S. Alouini, “Robust secure beamforming for multibeam satellite
communication systems,” IEEE Trans. Veh. Technol. , vol. 68, no. 6,
pp. 6202–6206, Jun. 2019.
[28] J. P. Vilela, M. Bloch, J. Barros, and S. W. McLaughlin, “Wireless
secrecy regions with friendly jamming,” IEEE Trans. Inf. Forensics
Security , vol. 6, no. 2, pp. 256–266, Jun. 2011.
[29] Z. Chu, K. Cumanan, Z. Ding, M. Johnston, and S. Y . Le Goff, “Secrecy
rate optimizations for a MIMO secrecy channel with a cooperativejammer,” IEEE Trans. Veh. Technol. , vol. 64, no. 5, pp. 1833–1847,
May 2015.
[30] P. R. Vaka, S. Bhattarai, and J.-M. Park, “Location privacy of non-
stationary incumbent systems in spectrum sharing,” in Proc. IEEE
Global Commun. Conf. (GLOBECOM) , Dec. 2016, pp. 1–6.
[31] A. Dimas, B. Li, M. Clark, K. Psounis, and A. Petropulu, “Spectrum
sharing between radar and communication systems: Can the privacy of
the radar be preserved?” in Proc. 51st Asilomar Conf. Signals, Syst.,
Comput. , Oct. 2017, pp. 1285–1289.[32] A. Deligiannis, A. Da niyan, S. Lambotharan, and J. A. Chambers,
“Secrecy rate optimizations for MIMO communication radar,” IEEE
Trans. Aerosp. Electron. Syst. , vol. 54, no. 5, pp. 2481–2492, Oct. 2018.
[33] B. K. Chalise and M. G. Amin, “Performance tradeoff in a uniﬁed system
of communications and passive radar: A secrecy capacity approach,”Digit. Signal Process. , vol. 82, pp. 282–293, Nov. 2018.
[34] K. Shen and W. Yu, “Fractional programming for communication
systems—Part I: Power control and beamforming,” IEEE Trans. Signal
Process. , vol. 66, no. 10, pp. 2616–2630, May 2018.
[35] I. Wajid, Y. C. Eldar, and A. Gershman, “Robust downlink beamforming
using covariance channel state information,” in Proc. IEEE Int. Conf.
Acoust., Speech Signal Process. , Apr. 2009, pp. 2285–2288.
[36] E. Telatar, “Capacity of multi-antenna Gaussian channels,” Eur. Trans.
Telecommun. , vol. 10, no. 6, pp. 585–595, Nov. 1999.
[37] M. F. Hanif, L.-N. Tran, M. Juntti, and S. Glisic, “On linear precod-
ing strategies for secrecy rate maximization in multiuser multiantennawireless networks,” IEEE Trans. Signal Process. , vol. 62, no. 14,
pp. 3536–3551, Jul. 2014.
[38] F. Wang, X. Wang, and Y . Zhu, “Transmit beamforming for multiuser
downlink with per-antenna power constraints,” in Proc. IEEE Int. Conf.
Commun. (ICC) , Jun. 2014, pp. 4692–4697.
[39] A. Aggarwal and T. H. Meng, “Min imizing the peak-to-average power
ratio of OFDM signals via convex optimization,” in Proc. IEEE Global
Telecommun. Conf. (GLOBECOM) , vol. 4, Jun. 2003, pp. 2385–2389.
[40] P. V . Amadori and C. Masouros, “C onstant envelope precoding by inter-
ference exploitation in phase shift k eying-modulated multiuser trans-
mission,” IEEE Trans. Wireless Commun. , vol. 16, no. 1, pp. 538–550,
Jan. 2017.
[41] F. Liu, C. Masouros, P. V . Amadori, and H. Sun, “An efﬁcient man-
ifold algorithm for constructive in terference based constant envelope
precoding,” IEEE Signal Process. Lett. , vol. 24, no. 10, pp. 1542–1546,
Oct. 2017.
[42] F. Liu, C. Masouros, A. Petr opulu, H. Grifﬁths, and L. Hanzo,
“Joint radar and communication design: Applications, State-of-the-art,
and the road ahead,” 2019, arXiv:1906.00789 . [Online]. Available:
http://arxiv.org/abs/1906.00789
[43] D. R. Fuhrmann and G. San Antonio, “Transmit beamforming for MIMO
radar systems using si gnal cross-correlation,” IEEE Trans. Aerosp.
Electron. Syst. , vol. 44, no. 1, pp. 171–186, Jan. 2008.
[44] W. Dinkelbach, “On nonlinear fractional programming,” Manage. Sci. ,
vol. 13, no. 7, pp. 492–498, Mar. 1967.
[45] Z.-Q. Luo, W.-K. Ma, A. So, Y. Ye, and S. Zhang, “Semideﬁnite
relaxation of quadratic optimization problems,” IEEE Signal Process.
Mag. , vol. 27, no. 3, pp. 20–34, May 2010.
[46] K.-Y . Wang, A. M.-C. So, T.-H. Chang, W.-K. Ma, and C.-Y . Chi,
“Outage constrained robust transmit optimization for multiuser MISO
downlinks: Tractable approxima tions by conic optimization,”
IEEE
Trans. Signal Process. , vol. 62, no. 21, pp. 5690–5705, Nov. 2014.
[47] M. S. Lobo, L. Vandenberghe, S. B oyd, and H. Lebret, “Applications
of second-order cone programming,” Linear Algebra Appl. , vol. 284,
nos. 1–3, pp. 193–228, 1998.
[48] J. Li and P. Stoica, “MIMO radar with colocated antennas,” IEEE Signal
Process. Mag. , vol. 24, no. 5, pp. 106–114, Sep. 2007.
[49] S. Boyd and L. Vandenberghe, Convex Optimization . Cambridge, U.K.:
Cambridge Univ. Press, 2004.
[50] K. L. Law, I. Wajid, and M. Pesavento, “Optimal downlink beamforming
for statistical CSI with robustness to estimation errors,” Signal Process. ,
vol. 131, pp. 472–482, Feb. 2017.
[51] M. Grant, S. Boyd, and Y . Ye. (2008). CVX: MATLAB Software for Dis-
ciplined Convex Programming . [Online]. Available: http://cvxr.com/cvx/
Nanchi Su (Graduate Student Member, IEEE)
received the B.E. and M.E. degrees from the
Harbin Institute of Technology, Heilongjiang, China,in 2015 and 2018, respectively. She is currentlypursuing the Ph.D. degree with the Information
and Communications Engineering Research Group,
Department of Electronics and Electrical Engineer-ing, University College London, London, U.K. Her
research interests include constructive interference
design, physical-layer security, radar signal process-ing, and convex optimization.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 

--- 第 13 页 ---
SUet al. : SECURE RADAR-COMMUNICATION SYSTEMS WITH MALICIOUS TARGETS 95
Fan Liu (Member, IEEE) r eceived the B.Eng. and
Ph.D. degrees from the Beijing Institute of Technol-ogy, Beijing, China, in 2013 and 2018, respectively.
He has been a Visiting Ph.D. Student with the
Department of Electronics and Electrical Engineer-ing, University College London, from 2016 to 2018,where he is currently a Marie Curie Research Fellow.
His research interests include vehicular networks,
massive MIMO and mmWave communications, andradar signal processing. He is also the Founding
Member of the IEEE Wireless Communications
Technical Committee (WTC) Special Interest Group (SIG) on Integrated Sens-ing and Communication (ISAC). He was a recipient of the Best Ph.D. ThesisAward of the Chinese Institute of Electronics in 2019 and the Marie Curie
Individual Fellowship in 2018. He has b een named as an Exemplary Reviewer
of the IEEE T
RANSACTIONS ON WIRELESS COMMUNICATIONS , the IEEE
TRANSACTIONS ON COMMUNICATIONS , and the IEEE C OMMUNICATIONS
LETTERS . He has served as the Co-Chair for the IEEE ICC 2020 Workshop
on Communication and Radar Spectrum Sharing.
Christos Masouros (Senior Member, IEEE)
received the Diploma degree in electrical andcomputer engineering from the University of Patras,
Greece, in 2004, and the M.Sc. and Ph.D. degrees
in electrical and electronic engineering from TheUniversity of Manchester, U.K., in 2006 and 2009,respectively.
In 2008, he was a Research Intern at the Philips
Research Laboratories, U.K. From 2009 to 2010,he was a Research Associate with The University
of Manchester and a Research Fellow with Queen’s
University Belfast from 2010 to 2012. In 2012, he joined University CollegeLondon as a Lecturer. He has held a R oyal Academy of Engineering
Research Fellowship from 2011 to 2016. He is currently a Full Professor
with the Information and Communi cation Engineering Research Group,
Department of Electrical and Electroni c Engineering, and afﬁliated with the
Institute for Communications and Connected Systems, University College
London. His research interests include wireless communications and signal
processing with a particular focus on green communications, large scaleantenna systems, communications and radar co-existence, interferencemitigation techniques for MIMO, and multicarrier communications. He is
also an Elected Member of the EURASIP SAT Committee on Signal
Processing for Communications and N etworking. He was a recipient of the
best paper awards at the IEEE GLOBECOM 2015 and the IEEE WCNC
2019 conferences. He has been recogni zed as an Exemplary Editor of the
IEEE C
OMMUNICATIONS LETTERS and an Exemplary Reviewer of the
IEEE T RANSACTIONS ON COMMUNICATIONS .H ei sa nE d i t o ro ft h eI E E E
TRANSACTIONS ON COMMUNICATIONS , the IEEE T RANSACTIONS ON
WIRELESS COMMUNICATIONS , and the IEEE O PENJOURNAL OF SIGNAL
PROCESSING , and an Editor-at-Large of the IEEE O PEN JOURNAL OF
THE COMMUNICATIONS SOCIETY . He has been an Associate Editor of
the IEEE C OMMUNICATIONS LETTERS and a Guest Editor of the IEEE
JOURNAL ON SELECTED TOPICS IN SIGNAL PROCESSING Issues Exploiting
Interference towards Energy Efﬁcient and Secure Wireless Communications,
Hybrid Analog/Digital Signal Processi ng for Hardware-Efﬁcient Large Scale
Antenna Arrays, and Joint Communication and Radar Sensing for Emerging
Applications.
Authorized licensed use limited to: University Town Library of Shenzhen. Downloaded on March 22,2025 at 12:54:13 UTC from IEEE Xplore.  Restrictions apply. 
