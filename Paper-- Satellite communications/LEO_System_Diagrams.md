# LEO卫星通感系统图表集

## 1. 系统几何模型

```mermaid
graph TD
    A["LEO卫星<br/>高度h=500-2000km"] --> B["地面用户Bob_k"]
    A --> C["雷达目标/窃听者Eve"]
    A --> D["地面基准点"]
    
    B --> E["仰角θ_k ≥ 10°"]
    B --> F["方位角φ_k"]
    C --> G["目标仰角θ_t"]
    C --> H["目标方位角φ_t"]
    
    I["地心坐标系"] --> J["卫星位置r_sat(t)"]
    I --> K["用户位置r_user"]
    I --> L["目标位置r_target(t)"]
    
    style A fill:#ff9999
    style C fill:#ffcc99
    style I fill:#99ff99
```

## 2. 信号处理流程

```mermaid
flowchart TD
    A["轨道预测"] --> B["信道预测"]
    B --> C["CSI估计"]
    C --> D["波束成形设计"]
    D --> E["人工噪声生成"]
    E --> F["信号发射"]
    
    G["目标检测"] --> H["角度估计"]
    H --> I["威胁评估"]
    I --> D
    
    F --> J["用户接收"]
    F --> K["目标窃听"]
    
    J --> L["SINR测量"]
    K --> M["SNR测量"]
    
    L --> N["性能反馈"]
    M --> N
    N --> D
    
    style A fill:#e1f5fe
    style D fill:#fff3e0
    style E fill:#fce4ec
    style I fill:#ffebee
```

## 3. 优化问题结构

```mermaid
graph TB
    A["主优化问题<br/>min max SNR_e"] --> B["约束条件"]
    
    B --> C["QoS约束<br/>SINR_k ≥ γ_k"]
    B --> D["功率约束<br/>P_total ≤ P_sat"]
    B --> E["仰角约束<br/>θ_k ≥ θ_min"]
    B --> F["CSI误差约束<br/>||e_k|| ≤ μ_k"]
    B --> G["轨道不确定性<br/>||Δr|| ≤ δ_orbit"]
    
    A --> H["求解算法"]
    H --> I["SDP松弛"]
    H --> J["分式规划"]
    H --> K["鲁棒优化"]
    
    I --> L["半定规划求解器"]
    J --> M["Dinkelbach算法"]
    K --> N["S-procedure"]
    
    style A fill:#e8f5e8
    style H fill:#fff8e1
    style L fill:#f3e5f5
    style M fill:#f3e5f5
    style N fill:#f3e5f5
```

## 4. 算法执行时序

```mermaid
sequenceDiagram
    participant O as 轨道预测器
    participant C as 信道估计器
    participant B as 波束成形器
    participant A as 人工噪声生成器
    participant T as 发射机
    
    Note over O,T: 时刻 t
    O->>C: 卫星位置 r_sat(t)
    C->>C: 信道外推 h_k(t)
    C->>B: CSI估计 ĥ_k(t)
    B->>B: 优化求解
    B->>A: 波束成形矩阵 W_k
    A->>A: 噪声协方差设计
    A->>T: R_N(t)
    B->>T: W_k(t)
    T->>T: 信号发射
    
    Note over O,T: 时刻 t+Δt
    O->>C: 更新位置 r_sat(t+Δt)
    C->>B: 预测CSI ĥ_k(t+Δt)
    Note over B: 快速更新
    B->>A: 增量更新 ΔW_k
    A->>T: 增量更新 ΔR_N
```

## 5. 多星协作架构

```mermaid
graph TD
    subgraph "LEO星座"
        S1["卫星1"]
        S2["卫星2"]
        S3["卫星3"]
        S4["卫星4"]
    end
    
    subgraph "地面段"
        U1["用户1"]
        U2["用户2"]
        U3["用户3"]
        T1["目标1"]
        T2["目标2"]
    end
    
    subgraph "控制中心"
        CC["星座控制中心"]
        OC["轨道计算"]
        SC["安全协调"]
    end
    
    S1 -.->|星间链路| S2
    S2 -.->|星间链路| S3
    S3 -.->|星间链路| S4
    S4 -.->|星间链路| S1
    
    S1 --> U1
    S2 --> U2
    S3 --> U3
    
    S1 -.->|雷达探测| T1
    S2 -.->|雷达探测| T2
    
    CC --> S1
    CC --> S2
    CC --> S3
    CC --> S4
    
    OC --> CC
    SC --> CC
    
    style S1 fill:#ffcdd2
    style S2 fill:#ffcdd2
    style S3 fill:#ffcdd2
    style S4 fill:#ffcdd2
    style T1 fill:#fff3e0
    style T2 fill:#fff3e0
    style CC fill:#e8f5e8
```

## 6. 威胁模型

```mermaid
graph LR
    subgraph "合法通信"
        A["LEO卫星"] --> B["合法用户"]
    end
    
    subgraph "威胁层次"
        C["地面窃听站<br/>大口径天线"]
        D["空中平台<br/>无人机/飞机"]
        E["敌方卫星<br/>信号截获"]
    end
    
    subgraph "防护措施"
        F["人工噪声"]
        G["波束成形"]
        H["功率控制"]
    end
    
    A -.->|信息泄露| C
    A -.->|信息泄露| D
    A -.->|信息泄露| E
    
    F --> A
    G --> A
    H --> A
    
    style A fill:#4caf50
    style B fill:#2196f3
    style C fill:#ff5722
    style D fill:#ff5722
    style E fill:#ff5722
    style F fill:#9c27b0
    style G fill:#9c27b0
    style H fill:#9c27b0
```

## 7. 性能对比雷达图

```mermaid
graph TD
    A["性能指标对比"] --> B["LEO系统"]
    A --> C["地面系统"]
    
    B --> B1["覆盖范围: 95%"]
    B --> B2["实时性: 80%"]
    B --> B3["安全性: 90%"]
    B --> B4["功率效率: 70%"]
    B --> B5["复杂度: 60%"]
    
    C --> C1["覆盖范围: 30%"]
    C --> C2["实时性: 95%"]
    C --> C3["安全性: 85%"]
    C --> C4["功率效率: 90%"]
    C --> C5["复杂度: 85%"]
    
    style B fill:#e3f2fd
    style C fill:#fff3e0
    style B1 fill:#c8e6c9
    style B2 fill:#ffcdd2
    style B3 fill:#c8e6c9
    style B4 fill:#fff9c4
    style B5 fill:#ffcdd2
```
