# ISAC系统双泄漏安全隐私联合评估 MATLAB仿真

## 📋 概述

基于专利交底书《一种毫米波ISAC系统的双泄漏安全隐私联合评估方法及装置》的完整MATLAB仿真实现。

### 🎯 主要功能

1. **通信和感知性能权衡分析** - 展示不同权重配置下的系统性能
2. **安全设计有效性验证** - 对比有/无安全设计的性能差异
3. **Fisher信息矩阵和CRB分析** - 量化感知泄漏的安全风险
4. **多场景应用对比** - 验证不同应用场景的适配性

## 📁 文件结构

```
├── run_ISAC_simulation.m          # 主运行脚本
├── ISAC_Security_Simulation.m     # 性能权衡分析
├── ISAC_CRB_Analysis.m           # CRB安全分析
├── MATLAB_Simulation_README.md   # 使用说明
└── 输出文件/
    ├── 性能对比图表
    ├── CRB分析图表
    ├── 场景对比图表
    └── ISAC_Simulation_Report.txt
```

## 🚀 快速开始

### 运行完整仿真
```matlab
% 在MATLAB中运行主脚本
run_ISAC_simulation
```

### 单独运行模块
```matlab
% 仅运行性能权衡分析
ISAC_Security_Simulation

% 仅运行CRB分析
ISAC_CRB_Analysis
```

## 📊 仿真结果说明

### 1. 通信感知性能权衡

**图表内容：**
- 通信性能 vs 通信权重α
- 感知性能 vs 通信权重α  
- 安全性能 vs 通信权重α
- 综合性能权衡分析

**关键发现：**
- 增加通信权重α提高通信性能，但降低感知性能
- 安全设计在保证基本性能的同时显著提升保密速率
- 存在最优权重配置平衡点

### 2. 安全设计有效性

**对比指标：**
- 保密速率提升：50-80%
- CRB增加：2-5倍（降低泄漏风险）
- 泄漏SNR降低：5-10dB

**安全策略：**
- 无安全设计：纯性能优化
- 中等安全设计：平衡性能与安全
- 高安全设计：优先保护隐私

### 3. Fisher信息矩阵分析

**基于专利公式：**
```
FIM[i,j] = (2/σ²) * Re{(∂μ/∂φᵢ)ᴴ(∂μ/∂φⱼ)}
```

**分析内容：**
- 目标角度估计精度（CRB）
- 感知信息泄漏强度
- 不同用户的泄漏风险评估

## 🔧 系统参数配置

### 基本参数（基于专利实施例1）
```matlab
N_BS = 64;              % 基站天线数
K = 4;                  % 用户数
M = 2;                  % 目标数
fc = 28e9;              % 载频 28 GHz
B = 1e9;                % 带宽 1 GHz
P_total = 30;           % 发射功率 30 dBm
```

### 场景配置
```matlab
% 基本实施方式
weights = [0.5, 0.5, 0.0];  % [α, β, γ]

% 高安全场景（军用）
weights = [0.3, 0.7, 0.0];

% 商用场景（5G/6G）
weights = [0.6, 0.4, 0.0];
```

## 📈 核心算法实现

### 1. 信号模型（基于专利公式）

**发射信号：**
```matlab
x = w * s;  % 公式(1): x(l) = w·s(l)
```

**基站接收：**
```matlab
y_BS = G_T * x + H_SI * x + n_BS;  % 公式(2)
```

**目标窃听：**
```matlab
y_Target = H_T' * x + z_Target;  % 公式(3)
```

**用户泄漏：**
```matlab
y_CU = H_C' * x + G_TC' * G_T * x + z_CU;  % 公式(4)
```

### 2. 安全性能指标

**保密速率：**
```matlab
R_sec = max(0, R_C - R_Target);  % 公式(7)
```

**Fisher信息矩阵：**
```matlab
FIM(i,j) = (2/sigma2) * real(conj(dmu_dphi_i) * dmu_dphi_j);  % 公式(24)
```

### 3. 优化算法

**安全导向波束成形：**
```matlab
% 基于公式(13)-(17)的联合优化
maximize: S_joint = α*R_sec + β*P_sensing - γ*P_outage
subject to: ||w||² ≤ P_total
```

## 🎨 可视化功能

### 性能对比图
- 有/无安全设计的性能曲线
- 通信、感知、安全三维权衡
- 归一化综合性能分析

### CRB分析图
- 角度估计精度对比
- 泄漏SNR强度分析
- 安全风险等级评估

### 场景对比图
- 三种应用场景性能
- 安全设计改进效果
- 综合性能雷达图

## ⚙️ 自定义配置

### 修改系统参数
```matlab
% 在各脚本开头修改参数
N_BS = 128;     % 增加天线数
K = 8;          % 增加用户数
fc = 60e9;      % 改为60GHz
```

### 调整安全策略
```matlab
% 修改安全权重
security_weights = [0, 0.3, 0.6, 0.9];  % 添加更多安全级别
```

### 扩展场景分析
```matlab
% 添加新的应用场景
scenarios = {
    '新场景', [α, β, γ];
    % 其他场景...
};
```

## 📋 输出文件说明

### 图表文件
- `performance_tradeoff.fig` - 性能权衡分析图
- `crb_analysis.fig` - CRB安全分析图
- `scenario_comparison.fig` - 场景对比图

### 报告文件
- `ISAC_Simulation_Report.txt` - 综合仿真报告
- 包含主要发现、技术贡献、应用建议

## 🔍 结果解读

### 性能权衡分析
1. **通信优先（α>0.7）**：适合数据传输密集场景
2. **感知优先（β>0.7）**：适合环境监测应用
3. **平衡配置（α≈β≈0.5）**：适合一般ISAC应用

### 安全设计效果
1. **保密速率提升**：验证了安全设计的有效性
2. **CRB增加**：降低了感知参数泄漏风险
3. **性能代价**：安全性提升伴随适度性能损失

### 应用场景适配
1. **军用/关键设施**：高安全配置，优先保护隐私
2. **商用网络**：平衡配置，兼顾性能与安全
3. **一般应用**：基础配置，提供基本安全保障

## 🛠️ 故障排除

### 常见问题
1. **矩阵奇异**：检查信道条件数，调整正则化参数
2. **收敛问题**：减小步长，增加迭代次数
3. **内存不足**：减少天线数或用户数

### 性能优化
1. **并行计算**：使用`parfor`加速循环
2. **向量化**：避免显式循环，使用矩阵运算
3. **预分配**：提前分配结果数组

## 📚 参考文献

1. 专利交底书：《一种毫米波ISAC系统的双泄漏安全隐私联合评估方法及装置》
2. 原始论文：Signal Model and Security Performance Metrics for Dual Leakage ISAC System in Millimeter Wave Band
3. 相关标准：3GPP毫米波信道模型

---

**注意**：本仿真代码基于专利交底书的理论模型实现，用于验证技术方案的有效性。实际工程应用中需要考虑更多实际因素和约束条件。
