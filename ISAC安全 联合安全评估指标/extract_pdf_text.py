#!/usr/bin/env python3
import PyPDF2
import sys

def extract_pdf_text(pdf_path):
    """Extract text from PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            print(f"PDF has {len(pdf_reader.pages)} pages")
            
            for page_num, page in enumerate(pdf_reader.pages):
                print(f"Extracting page {page_num + 1}...")
                page_text = page.extract_text()
                text += f"\n--- PAGE {page_num + 1} ---\n"
                text += page_text
                text += "\n"
            
            return text
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None

if __name__ == "__main__":
    pdf_file = "Signal_Model_and_Security_Performance_Metrics_for_Dual_Leakage_ISAC_System_in_Millimeter_Wave_Band.pdf"
    
    print(f"Extracting text from: {pdf_file}")
    extracted_text = extract_pdf_text(pdf_file)
    
    if extracted_text:
        # Save to text file
        with open("extracted_text.txt", "w", encoding="utf-8") as f:
            f.write(extracted_text)
        print("Text extracted and saved to 'extracted_text.txt'")
        
        # Also print first 2000 characters as preview
        print("\n--- PREVIEW (first 2000 characters) ---")
        print(extracted_text[:2000])
        if len(extracted_text) > 2000:
            print("... (truncated)")
    else:
        print("Failed to extract text from PDF")
