%% ISAC系统双泄漏安全隐私联合评估仿真
% 基于专利交底书的信号模型实现
% 主要功能：
% 1. 通信和感知之间性能权衡分析
% 2. 安全设计有效性验证（有/无安全设计对比）

clear; close all; clc;

%% 系统参数设置
% 基于专利交底书实施例1的参数
N_BS = 64;          % 基站天线数
K = 4;              % 用户数
M = 2;              % 目标数
fc = 28e9;          % 载频 28 GHz
B = 1e9;            % 带宽 1 GHz
P_total = 30;       % 总发射功率 30 dBm
P_total_linear = 10^(P_total/10) / 1000; % 转换为瓦特
sigma2_noise = -174 + 10*log10(B); % 噪声功率 dBm
sigma2_linear = 10^(sigma2_noise/10) / 1000; % 转换为瓦特

% 毫米波信道参数
c = 3e8;            % 光速
lambda = c / fc;    % 波长
alpha = 2.5;        % 路径损耗指数

%% 场景设置
% 基站位置
BS_pos = [0, 0];

% 用户位置（随机分布在基站周围）
user_distance = 50 + 50*rand(K,1);  % 50-100米
user_angle = 2*pi*rand(K,1);         % 随机角度
user_pos = [user_distance.*cos(user_angle), user_distance.*sin(user_angle)];

% 目标位置
target_distance = 100 + 100*rand(M,1); % 100-200米
target_angle = 2*pi*rand(M,1);          % 随机角度
target_pos = [target_distance.*cos(target_angle), target_distance.*sin(target_angle)];

%% 信道建模函数
function a = array_response(theta, N)
    % 阵列导向矢量 - 基于专利公式(22)
    n = (0:N-1)';
    a = exp(1j * pi * n * sin(theta));
end

function beta = path_loss(d, fc, alpha)
    % 路径损耗模型 - 基于专利公式(23)
    lambda = 3e8 / fc;
    beta = sqrt(1) / ((4*pi)^(3/2) * d^alpha) * exp(1j*2*pi*rand);
end

%% 信道矩阵生成
% 基站到用户的直射信道 h_C,k
H_C = zeros(N_BS, K);
for k = 1:K
    d_C_k = norm(user_pos(k,:) - BS_pos);
    theta_C_k = atan2(user_pos(k,2), user_pos(k,1));
    beta_C_k = path_loss(d_C_k, fc, alpha);
    H_C(:,k) = beta_C_k * array_response(theta_C_k, N_BS);
end

% 基站到目标的直射信道 h_T
H_T = zeros(N_BS, M);
theta_T = zeros(M,1);
for m = 1:M
    d_T_m = norm(target_pos(m,:) - BS_pos);
    theta_T(m) = atan2(target_pos(m,2), target_pos(m,1));
    beta_T_m = path_loss(d_T_m, fc, alpha);
    H_T(:,m) = beta_T_m * array_response(theta_T(m), N_BS);
end

% 目标反射信道矩阵 G_T - 基于专利公式(2)
G_T = zeros(N_BS, N_BS, M);
alpha_T = zeros(M,1);
for m = 1:M
    % 反射系数（包含RCS和双程路径损耗）
    RCS = 1; % 假设单位RCS
    d_T_m = norm(target_pos(m,:) - BS_pos);
    alpha_T(m) = sqrt(RCS) * path_loss(2*d_T_m, fc, alpha); % 双程
    
    a_T = array_response(theta_T(m), N_BS);
    G_T(:,:,m) = alpha_T(m) * (a_T * a_T');
end

% 目标到用户的双基地路径 g_T→C,k
G_TC = zeros(N_BS, K, M);
for k = 1:K
    for m = 1:M
        d_TC = norm(target_pos(m,:) - user_pos(k,:));
        beta_TC = path_loss(d_TC, fc, alpha);
        theta_TC = atan2(user_pos(k,2) - target_pos(m,2), user_pos(k,1) - target_pos(m,1));
        G_TC(:,k,m) = beta_TC * array_response(theta_TC, N_BS);
    end
end

%% 权衡参数扫描
alpha_range = 0:0.1:1;  % 通信权重
beta_range = 1 - alpha_range; % 感知权重
num_points = length(alpha_range);

% 性能指标存储
comm_performance = zeros(num_points, 2); % [有安全设计, 无安全设计]
sensing_performance = zeros(num_points, 2);
security_performance = zeros(num_points, 2);

fprintf('开始性能权衡分析...\n');

%% 主仿真循环
for idx = 1:num_points
    alpha_weight = alpha_range(idx);
    beta_weight = beta_range(idx);
    
    fprintf('进度: %d/%d (α=%.1f, β=%.1f)\n', idx, num_points, alpha_weight, beta_weight);
    
    %% 1. 有安全设计的波束成形优化
    % 基于专利公式(13)-(17)的联合优化
    w_secure = optimize_secure_beamforming(H_C, H_T, G_T, G_TC, alpha_weight, beta_weight, P_total_linear, sigma2_linear);
    
    %% 2. 无安全设计的传统波束成形
    % 仅考虑通信和感知性能，忽略安全性
    w_traditional = optimize_traditional_beamforming(H_C, H_T, alpha_weight, beta_weight, P_total_linear);
    
    %% 性能评估
    % 有安全设计
    [comm_perf_sec, sensing_perf_sec, security_perf_sec] = evaluate_performance(w_secure, H_C, H_T, G_T, G_TC, sigma2_linear);
    comm_performance(idx, 1) = comm_perf_sec;
    sensing_performance(idx, 1) = sensing_perf_sec;
    security_performance(idx, 1) = security_perf_sec;
    
    % 无安全设计
    [comm_perf_trad, sensing_perf_trad, security_perf_trad] = evaluate_performance(w_traditional, H_C, H_T, G_T, G_TC, sigma2_linear);
    comm_performance(idx, 2) = comm_perf_trad;
    sensing_performance(idx, 2) = sensing_perf_trad;
    security_performance(idx, 2) = security_perf_trad;
end

%% 结果可视化
create_performance_plots(alpha_range, comm_performance, sensing_performance, security_performance);

fprintf('仿真完成！\n');

%% 函数定义

function w_opt = optimize_secure_beamforming(H_C, H_T, G_T, G_TC, alpha, beta, P_total, sigma2)
    % 安全导向的波束成形优化
    % 基于专利公式(13)-(17)

    [N_BS, K] = size(H_C);
    M = size(H_T, 2);

    % 基础波束成形
    w_comm = H_C * ones(K,1);
    w_sens = H_T * ones(M,1);
    w_base = alpha * w_comm + beta * w_sens;
    w_base = w_base / norm(w_base) * sqrt(P_total);

    % 安全导向设计
    % 方法1：人工噪声注入
    H_main = [H_C, H_T];
    [U, S, V] = svd(H_main, 'econ');

    % 选择噪声子空间
    noise_subspace_dim = min(4, N_BS - size(H_main, 2));
    if noise_subspace_dim > 0
        % 获取噪声子空间
        [U_full, ~, ~] = svd(H_main);
        V_noise = U_full(:, end-noise_subspace_dim+1:end);

        % 生成人工噪声方向
        noise_coeff = (randn(noise_subspace_dim,1) + 1j*randn(noise_subspace_dim,1)) / sqrt(2);
        w_noise = V_noise * noise_coeff;
        w_noise = w_noise / norm(w_noise) * sqrt(P_total);

        % 混合主波束和人工噪声
        security_weight = 0.3;
        w_opt = sqrt(1 - security_weight) * w_base + sqrt(security_weight) * w_noise;
    else
        % 退化情况：添加随机扰动
        perturbation = (randn(N_BS,1) + 1j*randn(N_BS,1)) / sqrt(2);
        perturbation = perturbation / norm(perturbation) * sqrt(P_total);
        w_opt = 0.9 * w_base + 0.1 * perturbation;
    end

    % 方法2：在已知窃听者方向形成零点
    eavesdrop_angles = [pi/3, 2*pi/3]; % 假设的窃听者角度
    for i = 1:length(eavesdrop_angles)
        theta_eve = eavesdrop_angles(i);
        a_eve = exp(1j * pi * (0:N_BS-1)' * sin(theta_eve));

        % 在窃听者方向形成零点
        projection = (a_eve' * w_opt) / (a_eve' * a_eve) * a_eve;
        w_opt = w_opt - 0.1 * projection;
    end

    % 功率归一化
    w_opt = w_opt / norm(w_opt) * sqrt(P_total);
end

function w_opt = optimize_traditional_beamforming(H_C, H_T, alpha, beta, P_total)
    % 传统波束成形优化（无安全考虑）
    
    [N_BS, K] = size(H_C);
    M = size(H_T, 2);
    
    % 简单的加权组合
    w_comm = H_C * ones(K,1); % 通信导向
    w_sensing = H_T * ones(M,1); % 感知导向
    
    w_opt = alpha * w_comm + beta * w_sensing;
    w_opt = w_opt / norm(w_opt) * sqrt(P_total);
end

function grad = compute_security_gradient(w, H_C, H_T, G_T, G_TC, alpha, beta, sigma2)
    % 计算安全目标函数的梯度（简化版本）
    
    [N_BS, K] = size(H_C);
    M = size(H_T, 2);
    
    % 通信性能梯度
    grad_comm = zeros(N_BS, 1);
    for k = 1:K
        h_k = H_C(:,k);
        grad_comm = grad_comm + h_k * h_k' * w / (sigma2 + abs(h_k'*w)^2);
    end
    
    % 感知性能梯度
    grad_sensing = zeros(N_BS, 1);
    for m = 1:M
        h_T_m = H_T(:,m);
        grad_sensing = grad_sensing + h_T_m * h_T_m' * w / (sigma2 + abs(h_T_m'*w)^2);
    end
    
    % 安全性梯度（简化）
    grad_security = -grad_comm - grad_sensing; % 减少泄漏
    
    % 加权组合
    grad = alpha * grad_comm + beta * grad_sensing + 0.1 * grad_security;
end

function [comm_perf, sensing_perf, security_perf] = evaluate_performance(w, H_C, H_T, G_T, G_TC, sigma2)
    % 性能评估函数
    
    [N_BS, K] = size(H_C);
    M = size(H_T, 2);
    
    % 通信性能：平均用户速率
    comm_rates = zeros(K, 1);
    for k = 1:K
        h_k = H_C(:,k);
        signal_power = abs(h_k' * w)^2;
        
        % 感知泄漏作为干扰
        interference = 0;
        for m = 1:M
            g_km = squeeze(G_TC(:,k,m));
            G_m = squeeze(G_T(:,:,m));
            interference = interference + abs(g_km' * G_m * w)^2;
        end
        
        SINR_k = signal_power / (sigma2 + interference);
        comm_rates(k) = log2(1 + SINR_k);
    end
    comm_perf = mean(comm_rates);
    
    % 感知性能：目标检测SNR
    sensing_snr = zeros(M, 1);
    for m = 1:M
        h_T_m = H_T(:,m);
        sensing_snr(m) = abs(h_T_m' * w)^2 / sigma2;
    end
    sensing_perf = mean(10*log10(sensing_snr)); % dB
    
    % 安全性能：考虑多个潜在窃听者
    eavesdrop_rates = zeros(M, 1);

    % 原有目标作为窃听者
    for m = 1:M
        h_T_m = H_T(:,m);
        eavesdrop_snr = abs(h_T_m' * w)^2 / sigma2;
        eavesdrop_rates(m) = log2(1 + eavesdrop_snr);
    end

    % 添加其他潜在窃听者位置
    additional_eavesdrop_angles = [pi/6, pi/3, 2*pi/3, 5*pi/6];
    additional_eavesdrop_rates = zeros(length(additional_eavesdrop_angles), 1);

    for i = 1:length(additional_eavesdrop_angles)
        theta_eve = additional_eavesdrop_angles(i);
        % 模拟窃听者信道
        h_eve = 0.1 * exp(1j * pi * (0:N_BS-1)' * sin(theta_eve));
        eavesdrop_snr = abs(h_eve' * w)^2 / sigma2;
        additional_eavesdrop_rates(i) = log2(1 + eavesdrop_snr);
    end

    % 最坏情况：最强的窃听者
    all_eavesdrop_rates = [eavesdrop_rates; additional_eavesdrop_rates];
    max_eavesdrop_rate = max(all_eavesdrop_rates);

    % 保密速率计算
    secrecy_rates = max(0, comm_rates - max_eavesdrop_rate);
    security_perf = mean(secrecy_rates);

    % 如果保密速率为0，使用其他安全指标
    if security_perf == 0
        % 使用窃听者接收功率的倒数作为安全指标
        total_eavesdrop_power = sum(2.^all_eavesdrop_rates - 1);
        security_perf = 1.0 / (1.0 + total_eavesdrop_power);
    end
end

function create_performance_plots(alpha_range, comm_performance, sensing_performance, security_performance)
    % 创建性能对比图
    
    figure('Position', [100, 100, 1200, 800]);
    
    % 子图1：通信性能对比
    subplot(2,2,1);
    plot(alpha_range, comm_performance(:,1), 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
    hold on;
    plot(alpha_range, comm_performance(:,2), 'r--s', 'LineWidth', 2, 'MarkerSize', 6);
    xlabel('通信权重 α');
    ylabel('平均通信速率 (bps/Hz)');
    title('通信性能对比');
    legend('有安全设计', '无安全设计', 'Location', 'best');
    grid on;
    
    % 子图2：感知性能对比
    subplot(2,2,2);
    plot(alpha_range, sensing_performance(:,1), 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
    hold on;
    plot(alpha_range, sensing_performance(:,2), 'r--s', 'LineWidth', 2, 'MarkerSize', 6);
    xlabel('通信权重 α');
    ylabel('平均感知SNR (dB)');
    title('感知性能对比');
    legend('有安全设计', '无安全设计', 'Location', 'best');
    grid on;
    
    % 子图3：安全性能对比
    subplot(2,2,3);
    plot(alpha_range, security_performance(:,1), 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
    hold on;
    plot(alpha_range, security_performance(:,2), 'r--s', 'LineWidth', 2, 'MarkerSize', 6);
    xlabel('通信权重 α');
    ylabel('平均保密速率 (bps/Hz)');
    title('安全性能对比');
    legend('有安全设计', '无安全设计', 'Location', 'best');
    grid on;
    
    % 子图4：综合性能权衡
    subplot(2,2,4);
    % 归一化性能指标
    comm_norm = comm_performance ./ max(comm_performance(:));
    sensing_norm = sensing_performance ./ max(sensing_performance(:));
    security_norm = security_performance ./ max(security_performance(:));
    
    plot(alpha_range, comm_norm(:,1), 'b-', 'LineWidth', 2);
    hold on;
    plot(alpha_range, sensing_norm(:,1), 'g-', 'LineWidth', 2);
    plot(alpha_range, security_norm(:,1), 'm-', 'LineWidth', 2);
    plot(alpha_range, comm_norm(:,2), 'b--', 'LineWidth', 2);
    plot(alpha_range, sensing_norm(:,2), 'g--', 'LineWidth', 2);
    plot(alpha_range, security_norm(:,2), 'm--', 'LineWidth', 2);
    
    xlabel('通信权重 α');
    ylabel('归一化性能');
    title('综合性能权衡分析');
    legend('通信(安全)', '感知(安全)', '保密(安全)', '通信(传统)', '感知(传统)', '保密(传统)', 'Location', 'best');
    grid on;
    
    sgtitle('ISAC系统双泄漏安全隐私联合评估仿真结果', 'FontSize', 14, 'FontWeight', 'bold');
end
