# ISAC系统双泄漏安全隐私联合评估理论分析

## 1. 系统模型完善

### 1.1 基本假设
- 毫米波ISAC系统，基站配备N_BS个天线
- 单基地雷达配置，目标数量为M个
- 通信用户数量为K个，每个用户单天线
- 信道模型：视距主导的毫米波传播特性

### 1.2 信号模型

#### 发射信号
基站在时隙l发射的复合信号：
```
x(l) = W_c s_c(l) + W_s s_s(l)
```
其中：
- W_c ∈ C^(N_BS×K): 通信预编码矩阵
- W_s ∈ C^(N_BS×L): 感知波形矩阵  
- s_c(l) ∈ C^(K×1): 通信符号向量
- s_s(l) ∈ C^(L×1): 感知波形向量

#### 信道建模
毫米波信道采用几何信道模型：

**基站到目标m的信道：**
```
h_T,m = β_T,m a_BS(θ_T,m, φ_T,m)
```

**基站到用户k的信道：**
```
h_C,k = β_C,k a_BS(θ_C,k, φ_C,k)
```

**目标m到用户k的双基地信道：**
```
g_T→C,m,k = β_bistatic,m,k a_CU,k(θ_bistatic,m,k)
```

其中：
- β包含路径损耗和阴影衰落
- a_BS(θ,φ)为基站阵列导向矢量
- θ,φ分别为方位角和俯仰角

## 2. 双泄漏安全模型

### 2.1 通信泄漏模型

目标m作为窃听者接收到的信号：
```
y_T,m(l) = h_T,m^H x(l) + n_T,m(l)
         = h_T,m^H W_c s_c(l) + h_T,m^H W_s s_s(l) + n_T,m(l)
```

目标m的通信窃听容量：
```
C_T,m = log_2(1 + γ_T,m)
```
其中：
```
γ_T,m = |h_T,m^H W_c|^2 P_c / (|h_T,m^H W_s|^2 P_s + σ_T,m^2)
```

### 2.2 感知泄漏模型

用户k接收到的信号包含感知泄漏：
```
y_C,k(l) = h_C,k^H x(l) + Σ_m g_T→C,m,k^H G_T,m(θ_T,m) x(l-τ_m) + n_C,k(l)
```

其中G_T,m(θ_T,m) = α_T,m a_BS(θ_T,m) a_BS^H(θ_T,m)为目标m的反射信道矩阵。

用户k对目标m参数的估计精度用Cramér-Rao界限衡量：
```
CRB_k,m(θ_T,m) = [J_k,m^(-1)]_{θ_T,m, θ_T,m}
```

Fisher信息矩阵元素：
```
[J_k,m]_{i,j} = (2/σ_C,k^2) * Re{(∂μ_k,m/∂ϕ_i)^H (∂μ_k,m/∂ϕ_j)}
```

其中μ_k,m为用户k接收的目标m回波信号均值。

## 3. 联合安全性能评估指标

### 3.1 通信安全指标

#### 保密速率
对于用户k，考虑所有目标的窃听威胁：
```
R_sec,k = [R_C,k - max_m R_T,m]^+
```

其中：
- R_C,k为用户k的合法通信速率
- R_T,m为目标m的窃听速率
- [·]^+ = max(0, ·)

#### 多用户保密速率
系统总保密速率：
```
R_sec,total = Σ_k R_sec,k
```

最小保密速率（最坏情况）：
```
R_sec,min = min_k R_sec,k
```

### 3.2 感知安全指标

#### 目标参数泄漏度量
定义目标m的位置泄漏指标：
```
L_sensing,m = Σ_k w_k / CRB_k,m(θ_T,m)
```
其中w_k为用户k的权重因子。

#### 感知隐私保护度
```
P_sensing = Σ_m exp(-λ_m * L_sensing,m)
```
其中λ_m为目标m的敏感度参数。

### 3.3 联合安全评估指标

#### 加权联合安全指标
```
S_joint = α * S_comm + β * S_sensing
```

其中：
- S_comm = R_sec,total / R_max为归一化通信安全指标
- S_sensing = P_sensing为感知安全指标
- α + β = 1为权重系数

#### 安全中断概率
定义联合安全中断事件：
```
Outage = {R_sec,min < R_th} ∪ {L_sensing,m > L_th, ∀m}
```

联合安全中断概率：
```
P_outage = P(Outage)
```

## 4. 优化问题建模

### 4.1 联合波束成形优化
```
max_{W_c, W_s} S_joint
s.t. ||W_c||_F^2 + ||W_s||_F^2 ≤ P_total
     R_C,k ≥ R_min,k, ∀k
     CRB_k,m(θ_T,m) ≥ CRB_min,m, ∀k,m
```

### 4.2 功率分配优化
在固定波束成形下，优化通信和感知功率分配：
```
max_{P_c, P_s} S_joint(P_c, P_s)
s.t. P_c + P_s ≤ P_total
     P_c, P_s ≥ 0
```

## 5. 关键创新点总结

1. **双泄漏统一建模**：首次建立了ISAC系统中通信泄漏和感知泄漏的统一数学模型

2. **联合安全评估框架**：提出了综合考虑通信保密性和感知隐私性的联合评估指标

3. **多维优化策略**：设计了波束成形和功率分配的联合优化方法

4. **实用性评估工具**：建立了可量化的安全中断概率模型
