#!/bin/bash

# LaTeX编译脚本
# 用于编译专利交底书LaTeX文档

echo "开始编译专利交底书LaTeX文档..."

# 检查是否安装了xelatex
if ! command -v xelatex &> /dev/null; then
    echo "错误: 未找到xelatex命令。请安装TeX Live或MacTeX。"
    exit 1
fi

# 设置文件名
FILENAME="专利交底书_ISAC安全隐私联合评估"

echo "正在编译 ${FILENAME}.tex..."

# 第一次编译
xelatex -interaction=nonstopmode "${FILENAME}.tex"

# 检查第一次编译是否成功
if [ $? -eq 0 ]; then
    echo "第一次编译成功"
    
    # 第二次编译（处理交叉引用）
    echo "进行第二次编译以处理交叉引用..."
    xelatex -interaction=nonstopmode "${FILENAME}.tex"
    
    if [ $? -eq 0 ]; then
        echo "编译完成！生成的PDF文件: ${FILENAME}.pdf"
        
        # 清理临时文件
        echo "清理临时文件..."
        rm -f "${FILENAME}.aux" "${FILENAME}.log" "${FILENAME}.out" "${FILENAME}.toc"
        
        # 检查PDF是否生成
        if [ -f "${FILENAME}.pdf" ]; then
            echo "✅ PDF文件已成功生成: ${FILENAME}.pdf"
            echo "文件大小: $(ls -lh "${FILENAME}.pdf" | awk '{print $5}')"
        else
            echo "❌ PDF文件生成失败"
            exit 1
        fi
    else
        echo "❌ 第二次编译失败"
        exit 1
    fi
else
    echo "❌ 第一次编译失败"
    echo "请检查LaTeX语法错误"
    exit 1
fi

echo "编译过程完成！"
