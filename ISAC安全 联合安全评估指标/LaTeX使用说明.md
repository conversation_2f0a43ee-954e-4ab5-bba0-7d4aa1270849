# LaTeX专利交底书使用说明

## 📄 文件说明

### 生成的文件
- **`专利交底书_ISAC安全隐私联合评估.tex`** - LaTeX源文件
- **`专利交底书_ISAC安全隐私联合评估.pdf`** - 编译生成的PDF文档（11页，194KB）
- **`compile_latex.sh`** - 自动编译脚本

## 🔧 编译环境要求

### 必需软件
1. **TeX Live 2025** 或更新版本
2. **XeLaTeX** 编译器（支持中文）
3. **中文字体支持**（macOS自带，Linux需安装中文字体包）

### 安装方法
**macOS:**
```bash
# 使用Homebrew安装MacTeX
brew install --cask mactex

# 或下载完整版MacTeX
# https://www.tug.org/mactex/
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get update
sudo apt-get install texlive-full
sudo apt-get install texlive-xetex
sudo apt-get install texlive-lang-chinese
```

**Windows:**
```bash
# 下载并安装TeX Live
# https://www.tug.org/texlive/windows.html
```

## 🚀 编译方法

### 方法1：使用自动编译脚本（推荐）
```bash
# 给脚本执行权限
chmod +x compile_latex.sh

# 运行编译脚本
./compile_latex.sh
```

### 方法2：手动编译
```bash
# 第一次编译
xelatex -interaction=nonstopmode "专利交底书_ISAC安全隐私联合评估.tex"

# 第二次编译（处理交叉引用）
xelatex -interaction=nonstopmode "专利交底书_ISAC安全隐私联合评估.tex"
```

### 方法3：使用LaTeX编辑器
- **推荐编辑器**: TeXShop (macOS), TeXworks (跨平台), Overleaf (在线)
- **编译器设置**: XeLaTeX
- **编码设置**: UTF-8

## 📋 LaTeX文档结构

### 文档类和包
```latex
\documentclass[12pt,a4paper]{article}  % 12pt字体，A4纸张
\usepackage[UTF8]{ctex}                % 中文支持
\usepackage{amsmath,amsfonts,amssymb}  % 数学公式
\usepackage{geometry}                  % 页面布局
\usepackage{hyperref}                  % 超链接
```

### 主要章节
1. **发明名称** - 专利标题
2. **技术领域** - 技术分类和应用领域
3. **技术背景** - 现有技术问题
4. **发明内容** - 核心技术方案
5. **有益效果** - 技术优势和应用价值
6. **具体实施方式** - 实施例和参数配置
7. **关键数学公式详细推导** - 理论基础

### 数学公式示例
```latex
% 发射信号模型
\begin{equation}
\mathbf{x}(l) = \mathbf{w} \cdot s(l) \in \CC^{N_{BS} \times 1}
\end{equation}

% Fisher信息矩阵
\begin{equation}
[\mathbf{FIM}]_{i,j} = \frac{2}{\sigma^2_{C,k}} \cdot \text{Re}\left\{\left(\frac{\partial \mu_k}{\partial \phi_i}\right)^H \left(\frac{\partial \mu_k}{\partial \phi_j}\right)\right\}
\end{equation}
```

## ✏️ 自定义修改

### 修改页面布局
```latex
% 在geometry包中修改页边距
\geometry{left=2.5cm,right=2.5cm,top=3cm,bottom=3cm}
```

### 修改字体大小
```latex
% 修改文档类选项
\documentclass[11pt,a4paper]{article}  % 改为11pt
```

### 添加新的数学符号
```latex
% 在导言区添加新命令
\newcommand{\RR}{\mathbb{R}}           % 实数集
\newcommand{\CC}{\mathbb{C}}           % 复数集
\newcommand{\EE}{\mathbb{E}}           % 期望
```

### 修改页眉页脚
```latex
% 修改页眉内容
\fancyhead[C]{您的专利标题}

% 修改页脚内容
\fancyfoot[C]{第 \thepage 页}
```

## 🔍 常见问题解决

### 1. 编译错误：找不到中文字体
**解决方案**：
```bash
# macOS: 确保安装了中文字体
fc-list :lang=zh

# Linux: 安装中文字体包
sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei
```

### 2. 页眉高度警告
**解决方案**：在导言区添加
```latex
\setlength{\headheight}{14.5pt}
```

### 3. 数学公式显示异常
**解决方案**：确保使用了正确的数学环境
```latex
% 行内公式
$\mathbf{x}(l)$

% 行间公式
\begin{equation}
\mathbf{x}(l) = \mathbf{w} \cdot s(l)
\end{equation}
```

### 4. 超链接颜色设置
```latex
% 在hyperref包选项中设置
\usepackage[colorlinks=true,linkcolor=blue,citecolor=red]{hyperref}
```

## 📊 输出文档信息

### 当前生成的PDF特性
- **页数**: 11页
- **文件大小**: 194KB
- **字体**: 支持中英文混排
- **数学公式**: 完整的LaTeX数学渲染
- **目录**: 自动生成的章节目录
- **超链接**: 支持内部交叉引用

### 打印建议
- **纸张**: A4
- **方向**: 纵向
- **边距**: 2.5cm（左右），3cm（上下）
- **字体大小**: 12pt（适合阅读和打印）

## 🔄 版本控制建议

### Git管理
```bash
# 只跟踪源文件，忽略编译产物
echo "*.aux" >> .gitignore
echo "*.log" >> .gitignore
echo "*.out" >> .gitignore
echo "*.toc" >> .gitignore

# 保留重要文件
git add *.tex *.md compile_latex.sh
```

### 备份建议
- 定期备份`.tex`源文件
- 保存最终的PDF版本
- 记录重要的修改历史

这个LaTeX版本的专利交底书提供了专业的排版效果，适合正式的专利申请使用。
