
--- PAGE 1 ---
Signal Model and Security Performance Metrics
for Dual-Leakage ISAC System in
Millimeter-Wave Band
xx
July 21, 2025
1 Introduction
In the described millimeter-wave (mmWave) Integrated Sensing and Communi-
cation (ISAC) system, the base station (BS) transmits a dual-functional beam
that serves both sensing (e.g., target detection/localization) and communication
purposes. Based on the provided diagram, the system includes: - A BS (depicted
as a tower antenna) transmitting the composite signal. - A target (airplane, la-
beled Target/Eve), which receives the direct communication signal (leading to
communication data leakage) and reflects echoes back to the BS (for sensing).
- Communication users (CUs, depicted as phones, labeled C<PERSON>/Peeper), which
receive the intended communication signal but also potentially the target’s echo
(leading to sensing data leakage, e.g., target location inference).
There are no additional malicious nodes; instead, the target acts as a poten-
tial eavesdropper (Eve) for communication signals, and the CUs act as ”peepers”
for sensing echoes. This creates dual information leakage: - Communication
leakage : The target receives and may decode the downlink communication
data intended for CUs. - Sensing leakage : CUs receive target echoes and
may infer sensitive sensing information (e.g., target position via angle/distance
estimation).
Since no security design (e.g., artificial noise or beamforming optimization)
is applied, we evaluate the inherent security risks. The mmWave band implies
high directivity (narrow beams), large bandwidth (for precise ranging), and
significant path loss, which affects leakage severity.
We build a signal model for the monostatic ISAC setup and propose per-
formance metrics to quantify security, focusing on leakage without mitigation.
Metrics include secrecy rates for communication and CRB-based bounds for
sensing precision at unintended receivers.
1

--- PAGE 2 ---
2 Signal Model
Assume a monostatic BS with NBSantennas transmitting a composite signal
x(l)∈CNBS×1at time slot l, which embeds both communication symbols and
sensing waveforms (e.g., via beamforming x(l) =ws(l), where wis a dual-
functional precoder and s(l) is the symbol).
The system operates in mmWave, so channels are modeled with limited
scattering: line-of-sight (LoS) dominant, with path loss ∝d−α(α≈2−3) and
array steering vectors for beamforming.
2.1 Received Signals
1.At the BS (Self-Reception for Sensing) :
yBS(l) =GT(θT)x(l) +HSIx(l) +nBS(l),
where: - GT(θT) =αT(l)a(θT)aH(θT) is the target reflection channel ( αT(l) is
the reflection coefficient including RCS and path loss, θTis the target’s angle rel-
ative to BS, a(θT)∈CNBS×1is the steering vector). - HSIis the self-interference
channel (strong in full-duplex monostatic). - nBS(l)∼ CN (0, σ2
BSI) is noise.
2.At the Target (Acting as Eve for Communication Leakage) : The
target receives the direct signal from BS (communication leakage):
yTarget (l) =hH
Tx(l) +zTarget (l),
where: - hT∈CNBS×1is the direct channel from BS to target (mmWave LoS:
hT=βTa(θT), with βTincluding path loss). - zTarget (l)∼ CN (0, σ2
T) is noise
(assume target has single antenna for simplicity; extendable to multi-antenna).
- Since the target is passive for sensing but may actively decode if malicious, its
SNR for communication is γT=|hH
Tw|2P/σ2
T(P=E[|s(l)|2] ).
3.At the CU (Intended Communication + Sensing Leakage as
Peeper) : Assume Ksingle-antenna CUs. For the k-th CU:
yCU,k(l) =hH
C,kx(l) +gH
T→C,kGT(θT)x(l) +zCU,k(l),
where: - hC,k∈CNBS×1is the direct communication channel from BS to CU
k(mmWave: hC,k=βC,ka(θC,k)). - gT→C,kmodels the bistatic path from
target to CU k(reflection leakage term: the CU receives the target’s echo
as interference/leakage). - The second term gH
T→C,kGTx(l) represents sens-
ing leakage, allowing CU to potentially estimate target parameters (e.g., θT). -
zCU,k(l)∼ CN (0, σ2
C,k).
In mmWave, the reflection to CU may be weak due to high path loss, but if
CU is in the beam path, leakage occurs.
3 Security Performance Metrics
Without security design, we assess leakage via: - Communication security: Se-
crecy rate considering target as Eve. - Sensing security: CRB for target pa-
2

--- PAGE 3 ---
rameter estimation at CU (peeper). - Joint metrics: Outage probabilities or
combined leakage indicators.
3.1 Communication Security: Secrecy Rate Against Tar-
get Eve
The achievable rate at legitimate CU k:
RC,k= log2 
1 +|hH
C,kw|2P
σ2
C,k+IT→C,k!
,
where IT→C,k=|gH
T→C,kGTw|2Pis interference from reflection (sensing leakage
acts as noise for communication).
The eavesdropping rate at target (Eve):
RTarget = log2
1 +|hH
Tw|2P
σ2
T
.
The secrecy rate (worst-case, assuming target decodes the multi-user signal):
Rsec= min
k[RC,k−RTarget ]+,
where [ ·]+= max(0 ,·). IfRsec= 0, full leakage occurs.
In mmWave, due to beam alignment, if target is aligned with CU beam,
RTarget may be high.
3.2 Sensing Security: CRB for Target Parameter Estima-
tion at CU Peeper
CUs can use the leakage term to estimate target parameters (e.g., θT) like a
passive radar.
For CU k, the effective sensing signal is the reflection component. Assuming
CU knows the waveform x(l), the parameter vector ϕ= [θT, αT]T(angle and
reflection coefficient for position inference).
The CRB is derived from Fisher Information Matrix (FIM):
CRB = FIM−1,
with
FIM i,j=2
σ2
C,kℜ ∂µk
∂ϕiH∂µk
∂ϕj!
,
where µk=gH
T→C,kGTws(l) (mean of leakage signal).
Partial derivatives (simplified):
∂µk
∂θT=gH
T→C,k
αTs(l)∂a
∂θTaHw+αTs(l)a∂aH
∂θTw
,
3

--- PAGE 4 ---
∂µk
∂αT=gH
T→C,kaaHws(l).
The diagonal of CRB gives MSE bounds: high CRB means low peeping pre-
cision (better sensing security). In mmWave, narrow beams may limit leakage
if CU is off-beam.
3.3 Joint Security Metrics
1.Joint Secrecy Outage Probability (SOP) : Under fading (mmWave chan-
nels fluctuate), define:
SOP joint=P(Rsec< R thor MSECU
θT< ϵ),
approximated as SOP comm + SOP sensing −their product (assuming indepen-
dence).
- Communication SOP: P(Rsec< R th) = 1−exp
−2Rth−1
¯γC
¯γC
¯γC+(2Rth−1)¯γT
(Rayleigh fading, ¯ γaverage SNRs). - Sensing SOP: P(CRB( θT)< ϵ)≈exp
−1
Kϵ¯γleak
,
where ¯ γleakis average leakage SNR at CU.
2.Leakage Mutual Information : Joint leakage: I(yTarget ;s)+I(yCU;θT),
but computationally intensive.
These metrics quantify risks: low secrecy rate or low CRB indicates high
leakage.
4 Conclusion
This model highlights dual leakage in unprotected mmWave ISAC. Numerical
evaluation (e.g., via Monte Carlo) can compute metrics for specific parameters
like distances and array sizes.
4
