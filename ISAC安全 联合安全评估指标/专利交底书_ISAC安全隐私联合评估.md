# 专利交底书

## 发明名称
一种毫米波ISAC系统的双泄漏安全隐私联合评估方法及装置

## 技术领域
本发明涉及无线通信技术领域，特别涉及集成感知与通信（ISAC）系统的安全隐私保护技术，具体为一种毫米波频段下ISAC系统双泄漏安全隐私联合评估方法及装置。

## 技术背景

### 现有技术
集成感知与通信（ISAC）技术作为6G移动通信的关键技术之一，能够在同一硬件平台上同时实现无线通信和环境感知功能。然而，现有ISAC系统在安全隐私保护方面存在以下技术问题：

1. **双重泄漏威胁**：ISAC系统面临通信数据泄漏和感知信息泄漏的双重安全威胁
2. **缺乏统一评估**：现有技术缺乏对通信安全和感知隐私的统一评估方法
3. **优化目标单一**：传统优化方法仅考虑通信或感知性能，忽略安全性要求
4. **实时性不足**：缺乏实时安全状态监测和动态调整机制

### 技术问题
针对上述现有技术的不足，本发明要解决的技术问题是：
- 如何建立ISAC系统双泄漏的统一数学模型
- 如何设计综合通信保密性和感知隐私性的联合评估指标
- 如何实现安全导向的波束成形和功率分配优化
- 如何提供实时的安全状态监测和动态调整能力

## 发明内容

### 技术方案
为解决上述技术问题，本发明提供一种毫米波ISAC系统的双泄漏安全隐私联合评估方法，包括以下步骤：

#### 步骤1：双泄漏信号建模
基于毫米波ISAC系统的单基地雷达配置，建立完整的双泄漏信号传播模型。

1.1 建立ISAC系统发射信号模型：
考虑配备N_BS个天线的单基地基站，在时隙l发射复合信号：
```
x(l) = w·s(l) ∈ C^(N_BS×1)
```
其中：
- w ∈ C^(N_BS×1)为双功能预编码向量，同时服务通信和感知
- s(l)为复合符号，嵌入通信数据和感知波形
- 发射功率约束：E[|s(l)|^2] = P

毫米波信道特性：
- 视距主导传播，有限散射
- 路径损耗：∝ d^(-α)，其中α ≈ 2-3
- 阵列导向矢量：a(θ) ∈ C^(N_BS×1)

1.2 建立基站自接收信号模型（感知功能）：
```
y_BS(l) = G_T(θ_T)x(l) + H_SI x(l) + n_BS(l)
```
其中：
- G_T(θ_T) = α_T(l)a(θ_T)a^H(θ_T)为目标反射信道矩阵
- α_T(l)为反射系数，包含RCS和路径损耗
- θ_T为目标相对基站的角度
- H_SI为自干扰信道（全双工单基地系统中较强）
- n_BS(l) ~ CN(0, σ²_BS I)为噪声

1.3 建立通信泄漏模型（目标作为窃听者Eve）：
目标接收基站直射信号，产生通信数据泄漏：
```
y_Target(l) = h_T^H x(l) + z_Target(l)
```
其中：
- h_T ∈ C^(N_BS×1)为基站到目标的直射信道
- 毫米波视距信道：h_T = β_T a(θ_T)，β_T包含路径损耗
- z_Target(l) ~ CN(0, σ²_T)为目标处噪声
- 目标通信SNR：γ_T = |h_T^H w|²P/σ²_T

1.4 建立感知泄漏模型（用户作为感知窥探者Peeper）：
假设K个单天线通信用户，第k个用户接收信号：
```
y_CU,k(l) = h_C,k^H x(l) + g_T→C,k^H G_T(θ_T) x(l) + z_CU,k(l)
```
其中：
- h_C,k ∈ C^(N_BS×1)为基站到用户k的直射通信信道
- 毫米波信道：h_C,k = β_C,k a(θ_C,k)
- g_T→C,k为目标到用户k的双基地路径（反射泄漏项）
- 第二项g_T→C,k^H G_T x(l)表示感知泄漏，用户可能估计目标参数
- z_CU,k(l) ~ CN(0, σ²_C,k)为用户k处噪声

毫米波特性影响：由于高路径损耗，目标到用户的反射可能较弱，但若用户在波束路径内，则发生泄漏。

#### 步骤2：联合安全评估指标计算
基于双泄漏信号模型，建立通信保密性和感知隐私性的联合评估框架。

2.1 通信安全指标：针对目标窃听者的保密速率
合法用户k的可达速率：
```
R_C,k = log_2(1 + |h_C,k^H w|²P / (σ²_C,k + I_T→C,k))
```
其中I_T→C,k = |g_T→C,k^H G_T w|²P为反射干扰（感知泄漏作为通信噪声）。

目标处的窃听速率：
```
R_Target = log_2(1 + |h_T^H w|²P / σ²_T)
```

系统保密速率（最坏情况，假设目标解码多用户信号）：
```
R_sec = min_k [R_C,k - R_Target]^+
```
其中[·]^+ = max(0, ·)。若R_sec = 0，则发生完全泄漏。

毫米波特性：由于波束对准，若目标与用户波束对准，R_Target可能很高。

2.2 感知安全指标：用户窥探者的目标参数估计精度
用户可利用泄漏项估计目标参数（如θ_T），类似无源雷达。

对于用户k，有效感知信号为反射分量。假设用户知道波形x(l)，参数向量φ = [θ_T, α_T]^T（角度和反射系数用于位置推断）。

Cramér-Rao界限由Fisher信息矩阵导出：
```
CRB = FIM^(-1)
```

Fisher信息矩阵元素：
```
FIM_{i,j} = (2/σ²_C,k) * Re{(∂μ_k/∂φ_i)^H (∂μ_k/∂φ_j)}
```
其中μ_k = g_T→C,k^H G_T w s(l)为泄漏信号均值。

关键偏导数（简化形式）：
```
∂μ_k/∂θ_T = g_T→C,k^H [α_T s(l) (∂a/∂θ_T) a^H w + α_T s(l) a (∂a^H/∂θ_T) w]

∂μ_k/∂α_T = g_T→C,k^H a a^H w s(l)
```

CRB对角线给出MSE界限：高CRB意味着低窥探精度（更好的感知安全性）。
毫米波中，窄波束可能限制泄漏（若用户偏离波束）。

2.3 联合安全评估指标
定义联合保密中断概率（SOP）：
在衰落环境下（毫米波信道波动），定义：
```
SOP_joint = P(R_sec < R_th or MSE_CU^θT < ε)
```

近似为：SOP_comm + SOP_sensing - 两者乘积（假设独立性）

- 通信SOP：P(R_sec < R_th) = 1 - exp(-(2^R_th - 1)/γ̄_C * γ̄_C/(γ̄_C + (2^R_th - 1)γ̄_T))
  （瑞利衰落，γ̄为平均SNR）

- 感知SOP：P(CRB(θ_T) < ε) ≈ exp(-1/(K·ε·γ̄_leak))
  其中γ̄_leak为用户处平均泄漏SNR

泄漏互信息：联合泄漏I(y_Target; s) + I(y_CU; θ_T)，但计算密集。

这些指标量化风险：低保密速率或低CRB表示高泄漏。

#### 步骤3：安全导向优化
基于双泄漏联合评估指标，设计安全导向的系统优化策略。

3.1 双功能波束成形优化：
考虑到原文中的双功能预编码器w，扩展为多用户多目标场景：
```
maximize: S_joint = α·R_sec + β·P_sensing - γ·P_outage
subject to:
- ||w||² ≤ P_total                    (功率约束)
- R_C,k ≥ R_min,k, ∀k                (通信QoS约束)
- SINR_sensing ≥ γ_min               (感知性能约束)
- CRB_k(θ_T) ≥ CRB_min, ∀k          (感知安全约束)
```

其中：
- P_sensing = Σ_k exp(-λ_k·CRB_k(θ_T))为感知隐私保护度
- P_outage为联合安全中断概率
- α + β + γ = 1为权重系数

3.2 自适应功率分配策略：
在固定波束成形下，优化通信和感知功率分配：
```
maximize: f(P_c, P_s) = w₁·R_sec(P_c, P_s) + w₂·P_sensing(P_c, P_s)
subject to:
- P_c + P_s ≤ P_total
- P_c, P_s ≥ 0
- R_C,k(P_c) ≥ R_min,k, ∀k
```

3.3 动态威胁响应机制：
根据实时威胁评估调整系统参数：
- 高威胁场景：增加β权重，优先保护感知隐私
- 低威胁场景：增加α权重，优先保证通信保密
- 紧急情况：最大化γ权重，降低中断概率

#### 步骤4：实时安全监测
4.1 信道状态实时估计
4.2 安全威胁等级评估  
4.3 系统参数动态调整

### 装置方案
本发明还提供一种毫米波ISAC系统的双泄漏安全隐私联合评估装置，包括：

1. **信号建模模块**：用于建立双泄漏信号传播模型
2. **安全评估模块**：用于计算联合安全评估指标
3. **优化控制模块**：用于执行安全导向的系统优化
4. **监测调整模块**：用于实时安全状态监测和参数调整

## 有益效果

### 技术效果
1. **统一建模**：首次建立了ISAC系统双泄漏的统一数学模型，为安全评估提供理论基础
2. **全面评估**：提出了综合通信保密性和感知隐私性的联合评估指标体系
3. **智能优化**：实现了安全导向的波束成形和功率分配联合优化
4. **实时响应**：提供了实时安全监测和动态调整能力

### 应用价值
1. **5G/6G通信**：为下一代移动通信系统提供安全保障
2. **智能交通**：保护车联网中的位置隐私和通信安全
3. **物联网**：提升IoT设备的安全防护能力
4. **国防安全**：为军用通信感知系统提供安全技术支撑

## 具体实施方式

### 实施例1：基本实施方式
考虑一个配置N_BS=64天线的毫米波基站，服务K=4个用户，同时感知M=2个目标。

1. **系统参数设置**：
   - 载频：28 GHz
   - 带宽：1 GHz  
   - 发射功率：30 dBm
   - 噪声功率：-174 dBm/Hz

2. **信道建模**：
   采用3GPP标准的毫米波信道模型，考虑视距和非视距传播。

3. **算法实现**：
   - 使用交替优化算法求解波束成形
   - 采用梯度下降法优化功率分配
   - 设置安全阈值进行实时监测

### 实施例2：高安全场景应用
针对军用或关键基础设施场景，采用增强安全配置：

1. **安全参数调整**：
   - 提高感知隐私权重β=0.7
   - 降低通信保密权重α=0.3
   - 设置更严格的安全阈值

2. **优化策略**：
   - 优先保证感知信息不泄漏
   - 在满足基本通信需求下最大化安全性
   - 实施动态威胁响应机制

### 实施例3：商用场景应用
针对5G/6G商用网络，平衡性能与安全：

1. **参数配置**：
   - 平衡权重设置α=β=0.5
   - 适中的安全阈值设置
   - 考虑用户体验的QoS约束

2. **实现方式**：
   - 集成到基站的基带处理单元
   - 通过软件定义的方式实现算法
   - 支持网络切片的差异化安全策略

## 关键数学公式详细推导

基于原始论文的信号模型，详细推导专利的核心数学表达式。

### 公式1：毫米波信道建模
基于原文的毫米波信道特性，阵列导向矢量为：
```
a(θ) = [1, e^(jπsin(θ)), e^(j2πsin(θ)), ..., e^(j(N_BS-1)πsin(θ))]^T
```

路径损耗模型：
```
β = √(G_t G_r λ²) / ((4π)^(3/2) d^α) * e^(jφ)
```
其中G_t, G_r为发射接收天线增益，λ为波长，d为距离，α ≈ 2-3。

### 公式2：Fisher信息矩阵详细推导
基于原文公式，对于用户k估计目标角度θ_T，Fisher信息矩阵为：
```
[FIM]_{i,j} = (2/σ²_C,k) * Re{(∂μ_k/∂φ_i)^H (∂μ_k/∂φ_j)}
```

其中μ_k = g_T→C,k^H G_T w s(l)为泄漏信号均值。

关键偏导数推导：
对于角度参数θ_T：
```
∂μ_k/∂θ_T = g_T→C,k^H * α_T * s(l) * [(∂a(θ_T)/∂θ_T) a^H(θ_T) w + a(θ_T) (∂a^H(θ_T)/∂θ_T) w]
```

其中：
```
∂a(θ_T)/∂θ_T = jπcos(θ_T) * [0, 1·e^(jπsin(θ_T)), 2·e^(j2πsin(θ_T)), ..., (N_BS-1)·e^(j(N_BS-1)πsin(θ_T))]^T
```

对于反射系数α_T：
```
∂μ_k/∂α_T = g_T→C,k^H * a(θ_T) a^H(θ_T) w s(l)
```

### 公式2：保密速率计算
考虑多目标威胁的保密速率：
```
R_sec,k = log_2(1 + γ_C,k) - max_m log_2(1 + γ_T,m)
```

其中：
```
γ_C,k = |h_C,k^H W_c|^2 P_c / (σ_C,k^2 + I_sensing,k)
γ_T,m = |h_T,m^H W_c|^2 P_c / (σ_T,m^2 + |h_T,m^H W_s|^2 P_s)
```

### 公式3：联合优化目标函数
完整的联合优化问题表述：
```
maximize: S_joint = α * (Σ_k R_sec,k / K) + β * (Σ_m exp(-λ_m * L_sensing,m) / M)

subject to:
- ||W_c||_F^2 + ||W_s||_F^2 ≤ P_total
- R_C,k ≥ R_min,k, ∀k ∈ {1,...,K}
- SINR_sensing ≥ γ_min
- CRB_k,m(θ_T,m) ≥ CRB_min,m, ∀k,m
```

### 公式4：基于原文的精确信号模型推导

#### 4.1 基站自接收信号详细分析
基于原文公式，基站接收信号为：
```
y_BS(l) = G_T(θ_T)x(l) + H_SI x(l) + n_BS(l)
```

其中目标反射信道矩阵：
```
G_T(θ_T) = α_T(l) a(θ_T) a^H(θ_T)
```

反射系数α_T(l)包含：
- 雷达截面积(RCS)：σ_RCS
- 双程路径损耗：(4π)^2 d_T^4 / (λ^2 G_t G_r)
- 时变相位：e^(j4πd_T/λ)

完整表达式：
```
α_T(l) = √(σ_RCS λ^2 G_t G_r) / ((4π)^2 d_T^2) * e^(j4πd_T/λ + jφ_T(l))
```

#### 4.2 通信泄漏的SNR分析
基于原文，目标处的通信SNR为：
```
γ_T = |h_T^H w|^2 P / σ_T^2
```

其中直射信道：
```
h_T = β_T a(θ_T)
```

路径损耗系数：
```
β_T = √(G_t G_r λ^2) / ((4π) d_T^α) * e^(jφ_T)
```

因此：
```
γ_T = |β_T|^2 |a^H(θ_T) w|^2 P / σ_T^2
```

#### 4.3 感知泄漏的CRB详细推导
基于原文的Fisher信息矩阵，对于参数向量φ = [θ_T, α_T]^T：

```
[FIM]_{θ_T,θ_T} = (2/σ_C,k^2) * Re{(∂μ_k/∂θ_T)^H (∂μ_k/∂θ_T)}
[FIM]_{α_T,α_T} = (2/σ_C,k^2) * Re{(∂μ_k/∂α_T)^H (∂μ_k/∂α_T)}
[FIM]_{θ_T,α_T} = (2/σ_C,k^2) * Re{(∂μ_k/∂θ_T)^H (∂μ_k/∂α_T)}
```

其中μ_k = g_T→C,k^H G_T w s(l)，关键偏导数为：
```
∂μ_k/∂θ_T = g_T→C,k^H α_T s(l) [(∂a/∂θ_T) a^H w + a (∂a^H/∂θ_T) w]
∂μ_k/∂α_T = g_T→C,k^H a a^H w s(l)
```

CRB矩阵：
```
CRB = FIM^(-1) = [CRB_θθ  CRB_θα]
                  [CRB_αθ  CRB_αα]
```

角度估计的MSE下界：CRB_θθ，反射系数估计的MSE下界：CRB_αα

#### 4.4 毫米波特性对安全性能的影响

**窄波束效应**：
- 若目标与用户波束对准：高通信泄漏风险
- 若用户偏离感知波束：低感知泄漏风险

**路径损耗影响**：
- 高频段路径损耗：∝ f^2 d^α
- 对通信泄漏：降低目标接收功率
- 对感知泄漏：降低用户接收的回波功率

**阵列增益**：
- 发射增益：G_t = N_BS (理想情况)
- 接收增益：影响SNR和CRB性能

## 技术创新点总结

1. **理论创新**：基于原始论文建立了ISAC系统双泄漏的完整理论框架
2. **方法创新**：提出了综合通信保密性和感知隐私性的联合评估指标体系
3. **算法创新**：设计了安全导向的多目标优化算法，考虑毫米波特性
4. **应用创新**：实现了实时安全监测和动态调整机制，适应不同威胁场景

## 与现有技术的区别

1. **统一建模**：首次将通信泄漏和感知泄漏纳入统一的数学框架
2. **毫米波特性**：充分考虑了毫米波频段的传播特性对安全性能的影响
3. **联合优化**：同时优化通信和感知性能，而非单独考虑
4. **实用性强**：提供了可量化的安全评估指标和实现算法

本发明为ISAC系统的安全隐私保护提供了完整的技术解决方案，具有重要的理论价值和广阔的应用前景。
