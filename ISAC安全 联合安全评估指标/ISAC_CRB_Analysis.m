%% ISAC系统Fisher信息矩阵和CRB分析
% 基于专利交底书公式(24)-(27)的详细实现
% 分析感知泄漏的安全风险

function ISAC_CRB_Analysis()
    clear; close all; clc;
    
    %% 系统参数
    N_BS = 64;          % 基站天线数
    K = 4;              % 用户数
    M = 2;              % 目标数
    fc = 28e9;          % 载频
    sigma2 = 1e-12;     % 噪声功率
    
    %% 场景设置
    % 目标参数
    theta_T_true = [pi/6; pi/3];  % 真实目标角度
    alpha_T_true = [0.1; 0.15];   % 真实反射系数
    
    % 用户位置（不同的泄漏风险）
    theta_users = [pi/6 + 0.1; pi/6 - 0.1; pi/3 + 0.1; pi/3 - 0.1]; % 接近目标角度
    
    %% 信道建模
    % 基站阵列导向矢量
    a_T = zeros(N_BS, M);
    for m = 1:M
        a_T(:,m) = array_response(theta_T_true(m), N_BS);
    end
    
    % 用户信道
    H_users = zeros(N_BS, K);
    for k = 1:K
        H_users(:,k) = 0.1 * array_response(theta_users(k), N_BS); % 较弱的双基地信道
    end
    
    %% 不同波束成形策略下的CRB分析
    strategies = {'无安全设计', '中等安全设计', '高安全设计'};
    num_strategies = length(strategies);
    
    % 权重参数
    security_weights = [0, 0.5, 0.9]; % 安全权重
    
    % 结果存储
    CRB_results = zeros(M, K, num_strategies); % [目标, 用户, 策略]
    leakage_snr = zeros(M, K, num_strategies);
    
    fprintf('开始CRB分析...\n');
    
    for s = 1:num_strategies
        fprintf('策略 %d: %s\n', s, strategies{s});
        
        % 生成对应的波束成形向量
        w = generate_beamforming(a_T, H_users, security_weights(s));
        
        for m = 1:M
            for k = 1:K
                % 计算Fisher信息矩阵
                FIM = compute_fisher_information_matrix(w, a_T(:,m), H_users(:,k), ...
                    theta_T_true(m), alpha_T_true(m), sigma2);
                
                % 计算CRB
                if rcond(FIM) > 1e-12
                    CRB_matrix = inv(FIM);
                    CRB_results(m, k, s) = sqrt(CRB_matrix(1,1)); % 角度估计的RMSE下界
                else
                    CRB_results(m, k, s) = inf; % 奇异矩阵
                end
                
                % 计算泄漏SNR
                G_T = alpha_T_true(m) * (a_T(:,m) * a_T(:,m)');
                leakage_signal = H_users(:,k)' * G_T * w;
                leakage_snr(m, k, s) = abs(leakage_signal)^2 / sigma2;
            end
        end
    end
    
    %% 可视化结果
    create_crb_plots(CRB_results, leakage_snr, strategies);
    
    %% 安全风险评估
    evaluate_security_risk(CRB_results, leakage_snr, strategies);
    
    fprintf('CRB分析完成！\n');
end

function a = array_response(theta, N)
    % 阵列导向矢量 - 基于专利公式(22)
    n = (0:N-1)';
    a = exp(1j * pi * n * sin(theta));
end

function w = generate_beamforming(a_T, H_users, security_weight)
    % 生成不同安全级别的波束成形向量
    
    [N_BS, M] = size(a_T);
    [~, K] = size(H_users);
    
    % 基础感知波束（指向目标）
    w_sensing = a_T * ones(M,1);
    w_sensing = w_sensing / norm(w_sensing);
    
    if security_weight == 0
        % 无安全设计：纯感知优化
        w = w_sensing;
    else
        % 有安全设计：添加零化约束
        % 尝试在用户方向形成零点以减少泄漏
        
        % 构造约束矩阵（简化版本）
        A_constraint = H_users'; % 用户信道作为约束
        
        % 使用投影方法
        P_null = eye(N_BS) - A_constraint' * pinv(A_constraint' * A_constraint) * A_constraint;
        w_secure = P_null * w_sensing;
        
        if norm(w_secure) > 1e-6
            w_secure = w_secure / norm(w_secure);
        else
            w_secure = w_sensing; % 退化情况
        end
        
        % 加权组合
        w = (1 - security_weight) * w_sensing + security_weight * w_secure;
        w = w / norm(w);
    end
end

function FIM = compute_fisher_information_matrix(w, a_T, h_user, theta_T, alpha_T, sigma2)
    % 计算Fisher信息矩阵 - 基于专利公式(24)-(27)
    
    N_BS = length(a_T);
    
    % 参数向量: [theta_T, alpha_T]
    num_params = 2;
    FIM = zeros(num_params, num_params);
    
    % 信号均值 mu_k = g_T→C,k^H * G_T * w * s(l)
    % 假设 s(l) = 1, g_T→C,k = h_user
    G_T = alpha_T * (a_T * a_T');
    mu_k = h_user' * G_T * w;
    
    % 计算偏导数
    % 对theta_T的偏导数 - 基于专利公式(25)-(26)
    da_dtheta = compute_array_derivative(theta_T, N_BS);
    dmu_dtheta = h_user' * alpha_T * (da_dtheta * a_T' * w + a_T * da_dtheta' * w);
    
    % 对alpha_T的偏导数 - 基于专利公式(27)
    dmu_dalpha = h_user' * (a_T * a_T') * w;
    
    % Fisher信息矩阵元素 - 基于专利公式(24)
    FIM(1,1) = (2/sigma2) * real(conj(dmu_dtheta) * dmu_dtheta);     % theta-theta
    FIM(1,2) = (2/sigma2) * real(conj(dmu_dtheta) * dmu_dalpha);     % theta-alpha
    FIM(2,1) = FIM(1,2);                                             % alpha-theta
    FIM(2,2) = (2/sigma2) * real(conj(dmu_dalpha) * dmu_dalpha);     % alpha-alpha
end

function da_dtheta = compute_array_derivative(theta, N)
    % 计算阵列导向矢量对角度的偏导数 - 基于专利公式(26)
    n = (0:N-1)';
    da_dtheta = 1j * pi * cos(theta) * n .* exp(1j * pi * n * sin(theta));
end

function create_crb_plots(CRB_results, leakage_snr, strategies)
    % 创建CRB分析图
    
    [M, K, num_strategies] = size(CRB_results);
    
    figure('Position', [100, 100, 1400, 600]);
    
    % 子图1：CRB对比（角度估计精度）
    subplot(1,3,1);
    CRB_mean = squeeze(mean(CRB_results, 2)); % 对用户平均
    CRB_mean_deg = CRB_mean * 180/pi; % 转换为度
    
    bar_data = CRB_mean_deg';
    bar(bar_data);
    xlabel('安全设计策略');
    ylabel('角度估计RMSE下界 (度)');
    title('目标角度估计精度 (CRB)');
    legend('目标1', '目标2', 'Location', 'best');
    set(gca, 'XTickLabel', strategies);
    grid on;
    
    % 子图2：泄漏SNR对比
    subplot(1,3,2);
    SNR_mean_dB = 10*log10(squeeze(mean(leakage_snr, 2)));
    
    bar_data = SNR_mean_dB';
    bar(bar_data);
    xlabel('安全设计策略');
    ylabel('平均泄漏SNR (dB)');
    title('感知信息泄漏强度');
    legend('目标1', '目标2', 'Location', 'best');
    set(gca, 'XTickLabel', strategies);
    grid on;
    
    % 子图3：安全性能综合评估
    subplot(1,3,3);
    % 安全指标：CRB越高越安全，泄漏SNR越低越安全
    security_score = 1./CRB_mean_deg ./ (SNR_mean_dB + 1); % 综合安全评分
    
    bar_data = security_score';
    bar(bar_data);
    xlabel('安全设计策略');
    ylabel('安全评分 (越高越安全)');
    title('综合安全性能评估');
    legend('目标1', '目标2', 'Location', 'best');
    set(gca, 'XTickLabel', strategies);
    grid on;
    
    sgtitle('ISAC系统感知泄漏安全风险分析', 'FontSize', 14, 'FontWeight', 'bold');
end

function evaluate_security_risk(CRB_results, leakage_snr, strategies)
    % 安全风险评估
    
    [M, K, num_strategies] = size(CRB_results);
    
    fprintf('\n=== 安全风险评估报告 ===\n');
    
    for s = 1:num_strategies
        fprintf('\n策略: %s\n', strategies{s});
        fprintf('----------------------------------------\n');
        
        for m = 1:M
            % 角度估计精度
            crb_values = squeeze(CRB_results(m, :, s));
            crb_mean_deg = mean(crb_values) * 180/pi;
            
            % 泄漏强度
            snr_values = squeeze(leakage_snr(m, :, s));
            snr_mean_db = 10*log10(mean(snr_values));
            
            fprintf('目标%d:\n', m);
            fprintf('  角度估计RMSE下界: %.2f度\n', crb_mean_deg);
            fprintf('  平均泄漏SNR: %.1f dB\n', snr_mean_db);
            
            % 风险等级评估
            if crb_mean_deg > 5 && snr_mean_db < 0
                risk_level = '低风险';
            elseif crb_mean_deg > 2 && snr_mean_db < 10
                risk_level = '中等风险';
            else
                risk_level = '高风险';
            end
            fprintf('  安全风险等级: %s\n', risk_level);
        end
    end
    
    % 安全设计有效性分析
    fprintf('\n=== 安全设计有效性分析 ===\n');
    
    % 比较无安全设计vs高安全设计
    crb_improvement = squeeze(CRB_results(:, :, 3)) ./ squeeze(CRB_results(:, :, 1));
    snr_reduction = squeeze(leakage_snr(:, :, 1)) ./ squeeze(leakage_snr(:, :, 3));
    
    fprintf('高安全设计相比无安全设计:\n');
    fprintf('  角度估计精度降低: %.1fx (CRB增加)\n', mean(crb_improvement(:)));
    fprintf('  泄漏SNR降低: %.1fx\n', mean(snr_reduction(:)));
    fprintf('  整体安全性提升: %.1f%%\n', (mean(snr_reduction(:)) - 1) * 100);
end

% 运行分析
ISAC_CRB_Analysis();
