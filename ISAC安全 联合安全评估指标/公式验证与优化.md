# ISAC系统双泄漏安全评估公式验证与优化

## 1. 核心公式验证

### 1.1 信号模型验证

#### 发射信号模型
```
x(l) = W_c s_c(l) + W_s s_s(l)
```
**验证**：
- 维度一致性：W_c ∈ C^(N_BS×K), s_c(l) ∈ C^(K×1) → W_c s_c(l) ∈ C^(N_BS×1) ✓
- 维度一致性：W_s ∈ C^(N_BS×L), s_s(l) ∈ C^(L×1) → W_s s_s(l) ∈ C^(N_BS×1) ✓
- 物理意义：通信和感知信号的线性叠加，符合ISAC系统特性 ✓

#### 接收信号模型优化
**原始模型**：
```
y_C,k(l) = h_C,k^H x(l) + Σ_m g_T→C,m,k^H G_T,m x(l-τ_m) + n_C,k(l)
```

**优化后模型**：
```
y_C,k(l) = h_C,k^H x(l) + Σ_m g_T→C,m,k^H G_T,m(θ_T,m) x(l-τ_m) e^(-j2πf_d,m τ_m) + n_C,k(l)
```
**改进点**：
- 添加了多普勒频移项 e^(-j2πf_d,m τ_m)
- 明确了反射矩阵的角度依赖性 G_T,m(θ_T,m)

### 1.2 安全指标公式验证

#### 保密速率公式
**原始公式**：
```
R_sec,k = [R_C,k - max_m R_T,m]^+
```

**详细展开验证**：
```
R_C,k = log_2(1 + SINR_C,k)
SINR_C,k = |h_C,k^H W_c|^2 P_c / (σ_C,k^2 + I_sensing,k + I_interference,k)
```

其中：
- I_sensing,k = Σ_m |g_T→C,m,k^H G_T,m W_s|^2 P_s （感知泄漏干扰）
- I_interference,k = Σ_{j≠k} |h_C,k^H W_c e_j|^2 P_c （多用户干扰）

**目标窃听速率**：
```
R_T,m = log_2(1 + SINR_T,m)
SINR_T,m = |h_T,m^H W_c|^2 P_c / (σ_T,m^2 + |h_T,m^H W_s|^2 P_s)
```

**验证结果**：公式在数学上正确，物理意义明确 ✓

#### Fisher信息矩阵公式优化

**原始公式**：
```
[J_k,m]_{θ,θ} = (2/σ_C,k^2) * Re{(∂μ_k,m/∂θ_T,m)^H (∂μ_k,m/∂θ_T,m)}
```

**优化后完整公式**：
```
[J_k,m]_{i,j} = (2/σ_C,k^2) * Re{(∂μ_k,m/∂ϕ_i)^H (∂μ_k,m/∂ϕ_j)}
```

其中参数向量 ϕ = [θ_T,m, φ_T,m, r_T,m, v_T,m]^T 包括：
- θ_T,m：方位角
- φ_T,m：俯仰角  
- r_T,m：距离
- v_T,m：径向速度

**关键偏导数计算**：
```
∂μ_k,m/∂θ_T,m = g_T→C,m,k^H * α_T,m * (∂a_BS(θ_T,m)/∂θ_T,m) * a_BS^H(θ_T,m) * W_s * s_s(l)

∂μ_k,m/∂r_T,m = g_T→C,m,k^H * (∂α_T,m/∂r_T,m) * a_BS(θ_T,m) * a_BS^H(θ_T,m) * W_s * s_s(l)
```

### 1.3 联合优化公式验证

#### 目标函数
**优化后的联合目标函数**：
```
S_joint = α * S_comm + β * S_sensing + γ * S_robustness
```

其中：
- S_comm = (Σ_k w_k R_sec,k) / (Σ_k w_k R_max,k) （归一化通信安全性）
- S_sensing = Σ_m λ_m exp(-μ_m * L_sensing,m) （感知隐私保护度）
- S_robustness = 1 - P_outage （系统鲁棒性）

**权重约束**：α + β + γ = 1, α,β,γ ≥ 0

## 2. 数值稳定性分析

### 2.1 条件数分析
对于Fisher信息矩阵的条件数：
```
κ(J) = λ_max(J) / λ_min(J)
```
当κ(J) > 10^12时，需要正则化处理：
```
J_reg = J + ε * I
```
其中ε = 10^(-6)为正则化参数。

### 2.2 收敛性保证
优化算法的收敛条件：
```
||∇S_joint||_2 < δ 或 |S_joint^(t+1) - S_joint^(t)| < δ
```
其中δ = 10^(-4)为收敛阈值。

## 3. 计算复杂度分析

### 3.1 时间复杂度
- Fisher信息矩阵计算：O(N_BS^2 * K * M)
- 波束成形优化：O(N_iter * N_BS^3)  
- 功率分配优化：O(N_iter * (K+M))

### 3.2 空间复杂度
- 信道矩阵存储：O(N_BS * (K+M))
- 中间变量存储：O(N_BS^2)

## 4. 公式实现建议

### 4.1 数值实现要点
1. **矩阵求逆**：使用SVD分解避免奇异性
2. **梯度计算**：采用自动微分提高精度
3. **约束处理**：使用投影梯度法处理约束

### 4.2 优化算法选择
1. **凸优化部分**：使用内点法
2. **非凸优化部分**：使用交替优化+随机初始化
3. **实时优化**：使用在线梯度下降

## 5. 公式正确性总结

✅ **已验证的公式**：
- 信号传播模型
- 保密速率计算
- Fisher信息矩阵
- 联合优化目标函数

✅ **优化改进**：
- 添加多普勒效应
- 完善参数向量定义
- 增强数值稳定性
- 提供实现指导

所有核心公式在理论上正确，数学表达完整，具备工程实现的可行性。
