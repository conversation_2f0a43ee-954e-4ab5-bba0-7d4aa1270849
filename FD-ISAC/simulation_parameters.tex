\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{booktabs}
\usepackage{array}
\usepackage{geometry}
\geometry{margin=1in}

\title{Simulation Parameters for Joint Mutual Information Analysis\\
in FD-ISAC Systems}
\author{Based on Equations (36)-(41) Implementation}
\date{\today}

\begin{document}
\maketitle

\section{Overview}

This document specifies the simulation parameters used for implementing the joint mutual information analysis based on equations (36)-(41) from the theoretical derivation. The simulation implements the dual-path FD-ISAC signal model:

\begin{equation}
\bm{y}_{\text{eve}}(l) = \bm{G}_E(\theta)\bm{x}(l) + \bm{H}_{CE}^H\bm{x}(l) + \bm{z}_{\text{tar}}(l)
\end{equation}

where $\bm{G}_E(\theta) = \beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L)$.

\section{System Configuration Parameters}

\subsection{Antenna Configuration}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$N_{BS}$ & 16 & Number of antennas at base station \\
$N_E$ & 8 & Number of antennas at eavesdropper \\
$K$ & 3 & Number of communication users \\
$L$ & 10 & Number of time slots for analysis \\
\hline
\end{tabular}
\caption{Antenna and temporal configuration}
\end{table}

\subsection{Frequency and Wavelength}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$f_c$ & 28 GHz & Carrier frequency \\
$\lambda$ & 10.7 mm & Wavelength \\
$d_{\text{antenna}}$ & $\lambda/2$ & Inter-antenna spacing \\
$B$ & 100 MHz & System bandwidth \\
\hline
\end{tabular}
\caption{Frequency domain parameters}
\end{table}

\section{Geometric Configuration}

\subsection{Angular Parameters}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Degrees} & \textbf{Description} \\
\hline
$\theta_L$ & $\pi/4$ & 45° & Target angle \\
$\theta_E$ & $\pi/3$ & 60° & Eavesdropper angle \\
\hline
\end{tabular}
\caption{Angular configuration}
\end{table}

\subsection{Distance Parameters}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$d_{\text{target}}$ & 1000 m & Distance to target \\
$d_{\text{eavesdropper}}$ & 500 m & Distance to eavesdropper \\
\hline
\end{tabular}
\caption{Distance configuration}
\end{table}

\section{Channel Model Parameters}

\subsection{Reflection Coefficient}

The reflection coefficient $\beta(l)$ is modeled as:
\begin{equation}
\beta(l) = |\beta| e^{j\phi(l)}
\end{equation}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$|\beta|$ & 0.8 & Magnitude of reflection coefficient \\
$\sigma_\phi$ & 0.1 rad & Phase variation standard deviation \\
\hline
\end{tabular}
\caption{Reflection coefficient parameters}
\end{table}

\subsection{Path Loss and Shadowing}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$\alpha$ & 2.5 & Path loss exponent \\
$\sigma_{\text{shadow}}$ & 8 dB & Shadowing standard deviation \\
$\rho_{\text{spatial}}$ & 0.7 & Spatial correlation coefficient \\
$\rho_{\text{temporal}}$ & 0.9 & Temporal correlation coefficient \\
\hline
\end{tabular}
\caption{Propagation parameters}
\end{table}

\section{Signal Design Parameters}

\subsection{Power Allocation}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$P_{\text{total}}$ & 1 (normalized) & Total transmit power \\
$\rho_{\text{sense}}$ & 0.6 & Fraction for sensing \\
$\rho_{\text{comm}}$ & 0.4 & Fraction for communication \\
\hline
\end{tabular}
\caption{Power allocation parameters}
\end{table}

\subsection{Signal Covariance Matrix}

The signal covariance matrix is designed as:
\begin{equation}
\bm{Q}_x = \frac{P_{\text{total}}}{N_{BS}} \bm{I}_{N_{BS}}
\end{equation}

This represents uniform power allocation across all antennas.

\section{Noise and SNR Parameters}

\subsection{Noise Model}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$NF$ & 7 dB & Noise figure \\
$T_{\text{noise}}$ & 290 K & Noise temperature \\
$k_B$ & $1.38 \times 10^{-23}$ J/K & Boltzmann constant \\
\hline
\end{tabular}
\caption{Noise parameters}
\end{table}

The thermal noise power is computed as:
\begin{equation}
\sigma_{\text{thermal}}^2 = k_B T_{\text{noise}} B \cdot 10^{NF/10}
\end{equation}

\subsection{SNR Range}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
SNR range & 0 to 30 dB & Simulation SNR range \\
SNR step & 2 dB & SNR increment \\
\hline
\end{tabular}
\caption{SNR simulation parameters}
\end{table}

\section{Security Metric Parameters}

\subsection{FD-WILM Weights}

The FD-ISAC Weighted Information Leakage Metric uses:
\begin{equation}
\text{FD-WILM} = w_s \cdot \frac{I(\theta_L; \bm{y}_{\text{vec}})}{H(\theta_L)} + w_c \cdot \frac{I(\bm{X}; \bm{y}_{\text{vec}})}{H(\bm{X})} + w_{\text{coup}} \cdot \frac{I(\theta_L; \bm{X} | \bm{y}_{\text{vec}})}{H(\theta_L, \bm{X})}
\end{equation}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Weight} & \textbf{Value} & \textbf{Description} \\
\hline
$w_s$ & 0.4 & Sensing security weight \\
$w_c$ & 0.4 & Communication security weight \\
$w_{\text{coup}}$ & 0.2 & Coupling penalty weight \\
\hline
\end{tabular}
\caption{Security metric weights (must sum to 1)}
\end{table}

\section{Simulation Control Parameters}

\subsection{Monte Carlo Parameters}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$N_{\text{MC}}$ & 1000 & Number of Monte Carlo runs \\
Random seed & 42 & For reproducibility \\
\hline
\end{tabular}
\caption{Monte Carlo simulation parameters}
\end{table}

\subsection{Convergence Parameters}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
Tolerance & $10^{-6}$ & Convergence tolerance \\
Max iterations & 100 & Maximum iterations \\
\hline
\end{tabular}
\caption{Convergence control parameters}
\end{table}

\subsection{Discretization Parameters}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
$N_{\theta}$ & 180 & Number of angle grid points \\
$\theta_{\text{grid}}$ & $[0, 2\pi]$ & Angle discretization range \\
\hline
\end{tabular}
\caption{Discretization parameters}
\end{table}

\section{Optimization Constraints}

\subsection{System Design Constraints}

For the robust FD-ISAC design optimization:

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Constraint} & \textbf{Value} & \textbf{Description} \\
\hline
$\text{SINR}_{\min}$ & 10 dB & Minimum SINR requirement \\
$\text{CRB}_{\max}$ & $10^{-3}$ & Maximum allowable CRB \\
$P_{\text{sense,min}}$ & 0.1 & Minimum sensing power \\
\hline
\end{tabular}
\caption{Design constraint parameters}
\end{table}

\subsection{SCA Parameters}

For Successive Convex Approximation:

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Description} \\
\hline
Max SCA iterations & 50 & Maximum SCA iterations \\
SCA tolerance & $10^{-4}$ & SCA convergence tolerance \\
\hline
\end{tabular}
\caption{SCA optimization parameters}
\end{table}

\section{Expected Results Validation}

\subsection{Sanity Check Ranges}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|l|}
\hline
\textbf{Metric} & \textbf{Expected Range} & \textbf{Description} \\
\hline
Joint MI & [0, 50] bits & Joint mutual information \\
FD-JILI & [0, 1] & Normalized information leakage \\
Coupling & [-10, 10] bits & Information coupling \\
\hline
\end{tabular}
\caption{Expected result ranges for validation}
\end{table}

\section{Implementation Notes}

\subsection{Key Equations Implemented}

\begin{enumerate}
    \item \textbf{Equation (36)}: Eavesdropper signal model
    \item \textbf{Equation (37)}: Effective channel computation
    \item \textbf{Equation (38)}: Joint mutual information
    \item \textbf{Equation (39)}: Prior entropy computation
    \item \textbf{Equation (40)}: Posterior covariance
    \item \textbf{Equation (41)}: CRB computation
\end{enumerate}

\subsection{Computational Considerations}

\begin{itemize}
    \item Use Gaussian approximations for computational tractability
    \item Implement block-diagonal approximation for weak coupling scenarios
    \item Vectorize operations for efficiency
    \item Use Monte Carlo averaging for statistical reliability
\end{itemize}

\section{File Structure}

\begin{itemize}
    \item \texttt{joint\_mutual\_info\_simulation.m}: Main simulation function
    \item \texttt{load\_system\_parameters.m}: Parameter loading function
    \item \texttt{simulation\_parameters.tex}: This parameter documentation
\end{itemize}

\end{document}
