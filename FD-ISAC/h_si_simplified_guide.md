# 自干扰信道 H_SI 简化建模指南

## 🎯 **核心问题**

在全双工ISAC系统中，自干扰信道 H_SI 表示同一节点发射和接收天线之间的耦合。这个信道通常比期望信号强60-100dB，需要准确建模以进行有效的干扰消除。

## 📊 **推荐的简化模型**

### **模型选择**: 增强LoS模型 ✅

```latex
H_SI = α_SI × a_r(0°) × a_t^H(0°) + H_scatter
```

**组成部分**:
- **主导LoS分量**: `α_SI × a_r(0°) × a_t^H(0°)` (确定性)
- **散射分量**: `H_scatter ~ CN(0, 0.1×P_SI×I)` (随机)

## 🔧 **具体建模方案**

### **方案1: 基础LoS模型** (最简单)

```latex
H_SI = α_SI × a_r(θ_SI) × a_t^H(θ_SI)
```

**参数设置**:
- `α_SI = √P_SI × e^(jφ_SI)` - 复路径增益
- `θ_SI = 0°` - 自干扰角度(通常为正前方)
- `P_SI = 10^8` - 参考功率(比噪声高80dB)
- `φ_SI = 0` - 参考相位

**适用场景**: 初步分析、概念验证

### **方案2: 多径簇模型** (中等复杂度)

```latex
H_SI = Σ(p=1 to P) α_SI,p × a_r(θ_r,p) × a_t^H(θ_t,p)
```

**参数设置**:
- `P = 5` - 主要路径数量
- **直射路径**: `α_SI,1 = √(0.7×P_SI)`, `θ_r,1 = θ_t,1 = 0°`
- **反射路径**: `α_SI,p = √(0.3×P_SI/(P-1))`, `θ_r,p, θ_t,p ~ U[-60°,60°]`

**适用场景**: 系统设计、性能评估

### **方案3: 瑞利衰落模型** (推荐) ⭐

```latex
H_SI = √(K/(K+1)) × H_SI^LoS + √(1/(K+1)) × H_SI^NLOS
```

**参数设置**:
- `K = 15 dB` - 瑞利K因子(自干扰通常K因子很高)
- `H_SI^LoS` - 确定性LoS分量
- `H_SI^NLOS` - 随机散射分量, `[H_SI^NLOS]_ij ~ CN(0, σ_SI²)`
- `σ_SI² = P_SI/(K+1)`

**适用场景**: 大多数实际应用

## 💻 **MATLAB实现代码**

### **基础实现**:
```matlab
function H_SI = generate_SI_channel_basic(N_r, N_t, P_SI)
    % 基础LoS模型
    alpha_SI = sqrt(P_SI);
    a_r = ones(N_r, 1);  % 均匀阵列响应
    a_t = ones(N_t, 1);  % 均匀阵列响应
    H_SI = alpha_SI * a_r * a_t';
end
```

### **推荐实现** (瑞利模型):
```matlab
function H_SI = generate_SI_channel_rician(N_r, N_t, P_SI, K_dB)
    % 瑞利衰落模型
    K = 10^(K_dB/10);
    
    % LoS分量
    alpha_SI = sqrt(P_SI * K/(K+1));
    a_r = ones(N_r, 1);
    a_t = ones(N_t, 1);
    H_SI_los = alpha_SI * a_r * a_t';
    
    % NLOS分量
    sigma_SI = sqrt(P_SI/(K+1));
    H_SI_nlos = sigma_SI * (randn(N_r, N_t) + 1j*randn(N_r, N_t))/sqrt(2);
    
    % 总信道
    H_SI = H_SI_los + H_SI_nlos;
end
```

### **完整实现** (包含散射):
```matlab
function H_SI = generate_SI_channel_enhanced(N_r, N_t, P_SI, scatter_ratio)
    % 增强LoS模型 (推荐用于ISAC)
    
    % 主导LoS分量
    alpha_SI = sqrt(P_SI * (1 - scatter_ratio));
    a_r = ones(N_r, 1);
    a_t = ones(N_t, 1);
    H_SI_main = alpha_SI * a_r * a_t';
    
    % 散射分量
    sigma_scatter = sqrt(P_SI * scatter_ratio);
    H_scatter = sigma_scatter * (randn(N_r, N_t) + 1j*randn(N_r, N_t))/sqrt(2);
    
    % 组合信道
    H_SI = H_SI_main + H_scatter;
end
```

## 📋 **典型参数设置**

### **标准配置**:
```matlab
% 系统参数
N_r = 8;           % 接收天线数
N_t = 8;           % 发射天线数
P_SI = 1e8;        % 自干扰功率 (80dB above noise)
K_dB = 15;         % 瑞利K因子
scatter_ratio = 0.1; % 散射比例 (10%)

% 生成信道
H_SI = generate_SI_channel_enhanced(N_r, N_t, P_SI, scatter_ratio);
```

### **不同场景的参数**:

| **场景** | **P_SI** | **K_dB** | **scatter_ratio** |
|---------|----------|----------|-------------------|
| 近距离FD | 1e9 (90dB) | 20 | 0.05 |
| 标准FD | 1e8 (80dB) | 15 | 0.1 |
| 远距离FD | 1e7 (70dB) | 10 | 0.2 |

## 🔄 **集成到ISAC系统**

### **完整接收信号模型**:
```latex
y(t) = H_SI × x(t) + H_desired × s(t) + z(t)
```

### **自干扰消除**:
```latex
y_clean(t) = y(t) - Ĥ_SI × x(t)
```

### **残余自干扰**:
```latex
y_residual(t) = ΔH_SI × x(t) + z_effective(t)
```
其中 `ΔH_SI = H_SI - Ĥ_SI` 是估计误差。

## 🎯 **实际应用建议**

### **模型选择指南**:

| **应用** | **推荐模型** | **复杂度** | **精度** |
|---------|-------------|-----------|----------|
| 初步分析 | 基础LoS | 低 | 基础 |
| 系统设计 | 瑞利模型 | 中 | 高 |
| 性能评估 | 增强LoS | 中 | 很高 |
| 实时系统 | 基础LoS | 低 | 足够 |

### **关键设计考虑**:

1. **功率设置**: `P_SI` 通常比期望信号高60-100dB
2. **K因子**: 自干扰通常有强LoS分量，K=10-20dB
3. **散射比例**: 10-20%的功率用于建模多径效应
4. **更新频率**: 自干扰信道相对稳定，可以较低频率更新

### **计算复杂度**:
- **存储**: O(N_r × N_t)
- **计算**: O(N_r × N_t) 每次更新
- **实时性**: 支持实时应用

## ✅ **推荐方案总结**

**最佳实践**: 使用**增强LoS模型**
```latex
H_SI = √(0.9×P_SI) × a_r(0°) × a_t^H(0°) + √(0.1×P_SI) × H_scatter
```

**优势**:
- ✅ 计算简单高效
- ✅ 物理意义明确  
- ✅ 参数易于调整
- ✅ 适合优化算法
- ✅ 支持实时实现

这个模型在准确性和计算效率之间提供了最佳平衡，适合大多数FD-ISAC系统分析和设计应用。
