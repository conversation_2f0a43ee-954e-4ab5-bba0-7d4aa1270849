function [results] = joint_mutual_info_simulation()
% JOINT_MUTUAL_INFO_SIMULATION - Simulation for Joint Mutual Information Analysis
% Based on equations (36)-(41) from joint_mutual_information_derivation.tex
%
% This function implements the dual-path FD-ISAC signal model:
% y_eve(l) = G_E(θ) x(l) + H_CE^H x(l) + z_tar(l)
% where G_E(θ) = β(l) b(θ_E) a^H(θ_L)

%% Load System Parameters
params = load_system_parameters();

%% Initialize Results Structure
results = struct();
results.params = params;

%% Simulation Setup
SNR_dB_range = params.SNR_dB_range;
num_SNR = length(SNR_dB_range);
num_monte_carlo = params.num_monte_carlo;

% Initialize result arrays
results.joint_mutual_info = zeros(num_SNR, 1);
results.sensing_mutual_info = zeros(num_SNR, 1);
results.comm_mutual_info = zeros(num_SNR, 1);
results.coupling_info = zeros(num_SNR, 1);
results.FD_JILI = zeros(num_SNR, 1);
results.FD_WILM = zeros(num_SNR, 1);

fprintf('Starting Joint Mutual Information Simulation...\n');
fprintf('SNR Range: %.1f to %.1f dB\n', min(SNR_dB_range), max(SNR_dB_range));
fprintf('Monte Carlo Runs: %d\n', num_monte_carlo);

%% Main Simulation Loop
for snr_idx = 1:num_SNR
    SNR_dB = SNR_dB_range(snr_idx);
    fprintf('Processing SNR = %.1f dB (%d/%d)\n', SNR_dB, snr_idx, num_SNR);
    
    % Convert SNR to linear scale
    SNR_linear = 10^(SNR_dB/10);
    
    % Update noise variance based on SNR
    sigma_tar_sq = params.P_total / SNR_linear;
    
    % Monte Carlo simulation for current SNR
    joint_mi_samples = zeros(num_monte_carlo, 1);
    sensing_mi_samples = zeros(num_monte_carlo, 1);
    comm_mi_samples = zeros(num_monte_carlo, 1);
    coupling_samples = zeros(num_monte_carlo, 1);
    
    for mc = 1:num_monte_carlo
        % Generate random system realization
        [channels, signals] = generate_system_realization(params, sigma_tar_sq);
        
        % Compute mutual information components
        [joint_mi, sensing_mi, comm_mi, coupling] = compute_mutual_information(...
            channels, signals, params, sigma_tar_sq);
        
        joint_mi_samples(mc) = joint_mi;
        sensing_mi_samples(mc) = sensing_mi;
        comm_mi_samples(mc) = comm_mi;
        coupling_samples(mc) = coupling;
    end
    
    % Average over Monte Carlo runs
    results.joint_mutual_info(snr_idx) = mean(joint_mi_samples);
    results.sensing_mutual_info(snr_idx) = mean(sensing_mi_samples);
    results.comm_mutual_info(snr_idx) = mean(comm_mi_samples);
    results.coupling_info(snr_idx) = mean(coupling_samples);
    
    % Compute security metrics
    H_prior = compute_prior_entropy(params);
    results.FD_JILI(snr_idx) = results.joint_mutual_info(snr_idx) / H_prior.joint;
    results.FD_WILM(snr_idx) = compute_FD_WILM(results, snr_idx, H_prior, params);
end

fprintf('Simulation completed successfully!\n');

%% Plot Results
plot_simulation_results(results);

%% Save Results
save('joint_mutual_info_results.mat', 'results');
fprintf('Results saved to joint_mutual_info_results.mat\n');

end

%% ========================================================================
%% SYSTEM REALIZATION GENERATION
%% ========================================================================

function [channels, signals] = generate_system_realization(params, sigma_tar_sq)
% Generate random channel and signal realizations

% Extract parameters
N_BS = params.N_BS;
N_E = params.N_E;
L = params.L;
theta_L = params.theta_L;
theta_E = params.theta_E;

% Generate steering vectors (Equation 36 components)
a_theta_L = exp(1j * pi * (0:N_BS-1)' * sin(theta_L));  % BS steering vector
b_theta_E = exp(1j * pi * (0:N_E-1)' * sin(theta_E));   % Eavesdropper steering vector

% Generate reflection coefficient β(l)
beta = params.beta_magnitude * exp(1j * 2*pi*rand);  % Random phase

% Generate target-reflected channel G_E(θ) = β(l) b(θ_E) a^H(θ_L)
G_E = beta * b_theta_E * a_theta_L';  % Equation (36)

% Generate direct channel H_CE^H
H_CE_H = (randn(N_E, N_BS) + 1j*randn(N_E, N_BS)) / sqrt(2);

% Effective channel H_eff = G_E + H_CE^H
H_eff = G_E + H_CE_H;  % Equation (37)

% Generate FD-ISAC signals
Q_x = params.Q_x;
X = zeros(N_BS, L);
for l = 1:L
    % Generate complex Gaussian signals
    X_real = mvnrnd(zeros(N_BS, 1), Q_x/2)';
    X_imag = mvnrnd(zeros(N_BS, 1), Q_x/2)';
    X(:, l) = X_real + 1j*X_imag;
end

% Generate eavesdropper observations y_eve(l)
Y_eve = zeros(N_E, L);
for l = 1:L
    noise = sqrt(sigma_tar_sq/2) * (randn(N_E, 1) + 1j*randn(N_E, 1));
    Y_eve(:, l) = H_eff * X(:, l) + noise;  % Equation (36)
end

% Package results
channels.G_E = G_E;
channels.H_CE_H = H_CE_H;
channels.H_eff = H_eff;
channels.a_theta_L = a_theta_L;
channels.b_theta_E = b_theta_E;
channels.beta = beta;

signals.X = X;
signals.Y_eve = Y_eve;

end

%% ========================================================================
%% MUTUAL INFORMATION COMPUTATION
%% ========================================================================

function [joint_mi, sensing_mi, comm_mi, coupling] = compute_mutual_information(...
    channels, signals, params, sigma_tar_sq)
% Compute mutual information components based on equations (38)-(41)

% Extract parameters
L = params.L;
N_BS = params.N_BS;
N_E = params.N_E;
Q_x = params.Q_x;
H_eff = channels.H_eff;

% Vectorize signals
X_vec = signals.X(:);  % vec(X)
Y_eve_vec = signals.Y_eve(:);  % vec(Y_eve)

% Compute prior entropies (Equation 38)
H_theta_L = log(2*pi);  % Uniform prior on [0, 2π)
H_X = L * log(det(pi * exp(1) * Q_x));  % Gaussian prior
H_joint_prior = H_theta_L + H_X;  % Equation (39)

% Compute effective channel matrix for vectorized model
H_eff_vec = kron(eye(L), H_eff);  % I_L ⊗ H_eff

% Compute observation covariance
Sigma_y = H_eff_vec * kron(eye(L), Q_x) * H_eff_vec' + sigma_tar_sq * eye(L*N_E);

% Compute conditional entropies using Gaussian approximation
% Posterior covariance for signals given angle (Equation 40)
Sigma_X_post = kron(eye(L), Q_x) - ...
    kron(eye(L), Q_x) * H_eff_vec' * (Sigma_y \ H_eff_vec) * kron(eye(L), Q_x);

% Compute CRB for angle estimation (Equation 41)
CRB_theta = compute_CRB_angle(channels, params, sigma_tar_sq);

% Joint posterior entropy approximation
% Using block diagonal approximation for computational efficiency
H_X_post = 0.5 * log(det(2*pi*exp(1) * Sigma_X_post));
H_theta_post = 0.5 * log(2*pi*exp(1) * CRB_theta);
H_joint_post = H_X_post + H_theta_post;  % Weak coupling approximation

% Compute mutual information components
joint_mi = H_joint_prior - H_joint_post;  % Equation (38)
sensing_mi = H_theta_L - H_theta_post;
comm_mi = H_X - H_X_post;

% Compute coupling information (Equation 41)
coupling = sensing_mi + comm_mi - joint_mi;

end

%% ========================================================================
%% CRB COMPUTATION
%% ========================================================================

function CRB_theta = compute_CRB_angle(channels, params, sigma_tar_sq)
% Compute Cramér-Rao Bound for angle estimation

L = params.L;
Q_x = params.Q_x;
beta = channels.beta;
b_theta_E = channels.b_theta_E;

% Derivative of steering vector
N_BS = length(channels.a_theta_L);
theta_L = params.theta_L;
da_dtheta = 1j * pi * (0:N_BS-1)' .* cos(theta_L) .* ...
    exp(1j * pi * (0:N_BS-1)' * sin(theta_L));

% Fisher Information Matrix (Equation 41)
FIM = (2*L*abs(beta)^2 / sigma_tar_sq) * norm(b_theta_E)^2 * ...
    real(da_dtheta' * Q_x * da_dtheta);

% CRB is inverse of FIM
CRB_theta = 1 / FIM;

end

%% ========================================================================
%% PRIOR ENTROPY COMPUTATION
%% ========================================================================

function H_prior = compute_prior_entropy(params)
% Compute prior entropies

H_prior.theta_L = log(2*pi);
H_prior.X = params.L * log(det(pi * exp(1) * params.Q_x));
H_prior.joint = H_prior.theta_L + H_prior.X;

end

%% ========================================================================
%% SECURITY METRICS
%% ========================================================================

function FD_WILM = compute_FD_WILM(results, snr_idx, H_prior, params)
% Compute FD-ISAC Weighted Information Leakage Metric

w_s = params.w_s;  % Sensing weight
w_c = params.w_c;  % Communication weight
w_coup = params.w_coup;  % Coupling weight

sensing_ratio = results.sensing_mutual_info(snr_idx) / H_prior.theta_L;
comm_ratio = results.comm_mutual_info(snr_idx) / H_prior.X;
coupling_ratio = results.coupling_info(snr_idx) / H_prior.joint;

FD_WILM = w_s * sensing_ratio + w_c * comm_ratio + w_coup * coupling_ratio;

end

%% ========================================================================
%% PLOTTING FUNCTIONS
%% ========================================================================

function plot_simulation_results(results)
% Plot simulation results

SNR_dB_range = results.params.SNR_dB_range;

figure('Position', [100, 100, 1200, 800]);

% Plot 1: Mutual Information Components
subplot(2, 2, 1);
plot(SNR_dB_range, results.joint_mutual_info, 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
hold on;
plot(SNR_dB_range, results.sensing_mutual_info, 'r--s', 'LineWidth', 2, 'MarkerSize', 6);
plot(SNR_dB_range, results.comm_mutual_info, 'g-.^', 'LineWidth', 2, 'MarkerSize', 6);
plot(SNR_dB_range, results.coupling_info, 'm:', 'LineWidth', 2, 'MarkerSize', 6);
xlabel('SNR (dB)');
ylabel('Mutual Information (bits)');
title('Mutual Information Components');
legend('Joint MI', 'Sensing MI', 'Communication MI', 'Coupling', 'Location', 'best');
grid on;

% Plot 2: Security Metrics
subplot(2, 2, 2);
plot(SNR_dB_range, results.FD_JILI, 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
hold on;
plot(SNR_dB_range, results.FD_WILM, 'r--s', 'LineWidth', 2, 'MarkerSize', 6);
xlabel('SNR (dB)');
ylabel('Security Metric');
title('FD-ISAC Security Metrics');
legend('FD-JILI', 'FD-WILM', 'Location', 'best');
grid on;

% Plot 3: Information Leakage Ratios
subplot(2, 2, 3);
H_prior = compute_prior_entropy(results.params);
sensing_ratio = results.sensing_mutual_info / H_prior.theta_L;
comm_ratio = results.comm_mutual_info / H_prior.X;
plot(SNR_dB_range, sensing_ratio, 'r--s', 'LineWidth', 2, 'MarkerSize', 6);
hold on;
plot(SNR_dB_range, comm_ratio, 'g-.^', 'LineWidth', 2, 'MarkerSize', 6);
xlabel('SNR (dB)');
ylabel('Information Leakage Ratio');
title('Normalized Information Leakage');
legend('Sensing Leakage', 'Communication Leakage', 'Location', 'best');
grid on;

% Plot 4: Coupling Analysis
subplot(2, 2, 4);
coupling_ratio = results.coupling_info / H_prior.joint;
plot(SNR_dB_range, coupling_ratio, 'm-o', 'LineWidth', 2, 'MarkerSize', 6);
xlabel('SNR (dB)');
ylabel('Coupling Ratio');
title('Information Coupling Strength');
grid on;

sgtitle('Joint Mutual Information Analysis for FD-ISAC Systems');

% Save as FIG file instead of PNG
savefig('joint_mutual_info_analysis.fig');
fprintf('Main analysis plot saved as joint_mutual_info_analysis.fig\n');

end
