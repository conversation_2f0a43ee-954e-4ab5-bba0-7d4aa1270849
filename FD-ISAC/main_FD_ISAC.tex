\documentclass[journal]{IEEEtran}

% Packages
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{cite}
\usepackage{array}
\usepackage{booktabs}
\usepackage{subfig}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\vec}{\text{vec}}

% Title and author information
\title{A Physical-Layer Security Framework for Joint Communication and Sensing in Full-Duplex ISAC Systems}

\author{Nanchi Su,~\IEEEmembership{Member,~IEEE}
\thanks{This work was supported in part by the Engineering and Physical Sciences Research Council (EPSRC) under Grant EP/S028455/1, in part by the National Natural Science Foundation of China under Grant 62101234, Grant U20B2039, <PERSON> 61831008 and Grant 62027802, in part by the Young Elite Scientist Sponsorship Program by CAST under Grant No. YESS20210055, and in part by the China Scholarship Council (CSC).}
\thanks{N. Su is with Guangdong Provincial Key Laboratory of Aerospace Communication and Networking Technology, Harbin Institute of Technology (Shenzhen), Shenzhen 518055, China; with the Department of Electronic and Electrical Engineering, Southern University of Science and Technology, Shenzhen 518055, China (e-mail: <EMAIL>).}}

\begin{document}

\maketitle

\begin{abstract}
This paper investigates the physical-layer security vulnerabilities in full-duplex integrated sensing and communication (FD-ISAC) systems, where a malicious eavesdropper can simultaneously intercept both communication data and sensing information. We consider a monostatic FD-ISAC system where a dual-functional base station serves multiple communication users while performing target sensing. The eavesdropper exploits two attack vectors: direct interception of downlink communication signals and passive collection of target-reflected sensing signals. We develop a comprehensive signal model that captures the dual-function nature of ISAC transmissions and the multi-path information leakage to the eavesdropper. Our analysis reveals the fundamental security trade-offs in FD-ISAC systems and provides insights for designing secure dual-function wireless networks. The proposed framework establishes the foundation for developing effective countermeasures against sophisticated eavesdropping attacks in next-generation ISAC systems.
\end{abstract}

\begin{IEEEkeywords}
Integrated sensing and communication, full-duplex systems, physical layer security, eavesdropping, information leakage, monostatic radar.
\end{IEEEkeywords}

\section{Introduction}
\IEEEPARstart{I}{ntegrated} sensing and communication (ISAC) has emerged as a key enabling technology for next-generation wireless networks, offering unprecedented spectral efficiency by allowing the same spectrum and hardware resources to serve both communication and sensing functions simultaneously~\cite{liu2022integrated}. The integration of radar sensing capabilities into communication systems enables a wide range of applications, including autonomous driving, smart cities, and Internet of Things (IoT) networks~\cite{zhang2021overview}.

Full-duplex (FD) operation further enhances the capabilities of ISAC systems by enabling simultaneous transmission and reception, thereby improving both communication throughput and sensing performance~\cite{ahmed2020survey}. However, this integration introduces novel security vulnerabilities that have not been adequately addressed in the literature.

The dual-function nature of ISAC signals creates unique security challenges. Unlike traditional communication systems where eavesdroppers can only intercept intended communication signals, ISAC systems expose additional attack surfaces through sensing functionalities. A sophisticated eavesdropper can potentially extract sensitive information from both communication data streams and radar sensing signals, including target locations, system parameters, and operational characteristics.

\subsection{Related Work and Motivation}

Physical-layer security in communication systems has been extensively studied~\cite{wyner1975wire}, with various techniques proposed to enhance security through signal design, artificial noise injection, and beamforming optimization~\cite{mukherjee2014principles}. However, the security implications of ISAC systems remain largely unexplored.

Recent works have begun to address security concerns in ISAC systems. The authors in~\cite{shi2021secure} investigated secure beamforming design for ISAC systems, while~\cite{xu2022secure} studied the trade-off between sensing performance and communication security. However, these works primarily focus on protecting communication information and do not consider the leakage of sensing information to eavesdroppers.

The unique challenge in FD-ISAC systems is that the same transmitted signal serves both communication and sensing purposes, creating correlated observations at the eavesdropper through multiple paths. This correlation can potentially be exploited by sophisticated eavesdroppers to improve their estimation performance through joint processing techniques.

\subsection{Contributions}

This paper makes the following key contributions:

\begin{itemize}
\item We develop a comprehensive signal model for monostatic FD-ISAC systems that captures the dual-function nature of transmitted signals and the multi-path information leakage to eavesdroppers.

\item We analyze the fundamental security vulnerabilities in FD-ISAC systems, identifying how eavesdroppers can exploit both direct communication channels and target-reflected sensing signals.

\item We establish a theoretical framework for understanding the information leakage mechanisms in FD-ISAC systems, considering both communication data leakage and sensing parameter estimation.

\item We provide insights into the security trade-offs inherent in FD-ISAC system design and identify key parameters that affect system vulnerability.
\end{itemize}

\subsection{Organization}

The remainder of this paper is organized as follows. Section~II presents the system model and signal formulations for the monostatic FD-ISAC system. Section~III analyzes the information leakage mechanisms and security vulnerabilities. Section~IV discusses the implications and potential countermeasures. Finally, Section~V concludes the paper.

\section{System Model}

We consider a monostatic FD-ISAC framework as depicted in Fig.~\ref{fig:system_model}. In this system, a single dual-functional base station operates as both radar transmitter and receiver while simultaneously providing communication services to multiple users.

\subsection{System Components}

The proposed monostatic secure ISAC system consists of the following components:

\begin{itemize}
    \item \textbf{Monostatic ISAC Base Station:} Equipped with $N_{\text{BS,t}}$ transmit antennas and $N_{\text{BS,r}}$ receive antennas, serving as:
    \begin{itemize}
        \item Radar transmitter for target illumination
        \item Radar receiver for echo signal processing  
        \item Communication transmitter for downlink data transmission
    \end{itemize}
    
    \item \textbf{Target:} A passive reflector within the sensing coverage area at angle $\theta_L$ relative to the base station
    
    \item \textbf{Communication Users:} $K$ single-antenna legitimate users receiving downlink communication signals
    
    \item \textbf{Eavesdropper:} A malicious terminal with $N_E$ antennas at angle $\theta_E$ attempting to intercept both sensing and communication information
\end{itemize}

\subsection{Threat Model}

The malicious eavesdropper (Eve) can exploit two primary vulnerabilities:
\begin{itemize}
    \item \textbf{Communication Data Leakage:} Eve may eavesdrop on the downlink communication signals intended for legitimate users, potentially extracting sensitive communication data.
    \item \textbf{Sensing Information Leakage:} Eve may intercept the reflected sensing signals from the target, thereby inferring sensitive information such as target location, system parameters, and operational characteristics.
\end{itemize}

The full-duplex base station is equipped with $N_{\text{BS,t}}$ transmit antennas and $N_{\text{BS,r}}$ receive antennas, which simultaneously perform radar and communication functions. The system serves $K$ single-antenna communication users, where we assume $K \leq N_{\text{BS,t}}$. A target exists within the sensing range of the radar, and an unauthorized malicious terminal is present in the vicinity, capable of intercepting both the radar's reflected sensing signals and the downlink communication intended for the legitimate users.

\subsection{Signal Model}

Let $\mathbf{s}(l) \in \mathbb{C}^{K \times 1}$ denote the transmitted symbol vector at the $l$-th time slot, and $\mathbf{F} \in \mathbb{C}^{N_{\text{BS,t}} \times K}$ represent the precoding matrix. The transmit waveform can be written as:
\begin{equation}
\mathbf{x}(l) = \mathbf{F}\mathbf{s}(l)
\label{eq:transmit_signal}
\end{equation}

The received signals at each facility are described in the following subsections.

\subsubsection{Received Signal at Monostatic Base Station}

The received signal at the monostatic ISAC base station can be expressed as:
\begin{equation}
\mathbf{y}_{\text{BS}}(l) = \mathbf{G}_T(\theta_L)\mathbf{x}(l) + \mathbf{H}_{\text{SI}}\mathbf{x}(l) + \mathbf{n}_{\text{BS}}(l)
\label{eq:bs_received}
\end{equation}
where $\mathbf{G}_T(\theta_L) = \alpha(l)\mathbf{a}_r(\theta_L)\mathbf{a}_t^H(\theta_L)$ represents the target reflection channel matrix for the monostatic configuration, with $\theta_L$ being the target angle, $\mathbf{a}_r(\theta_L) \in \mathbb{C}^{N_{\text{BS,r}} \times 1}$ and $\mathbf{a}_t(\theta_L) \in \mathbb{C}^{N_{\text{BS,t}} \times 1}$ denoting the receive and transmit array steering vectors, respectively, $\mathbf{H}_{\text{SI}} \in \mathbb{C}^{N_{\text{BS,r}} \times N_{\text{BS,t}}}$ denotes the self-interference channel matrix, and $\mathbf{n}_{\text{BS}}(l)$ is the additive noise.

For the residual self-interference channel at the base station, we follow~\cite{temiz2021dual, nguyen2014spectral, he2023full} and model each entry of $\mathbf{H}_{\text{SI}}$ as:
\begin{equation}
[\mathbf{H}_{\text{SI}}]_{p,q} = \sqrt{\alpha_{p,q}^{\text{SI}}} e^{-j2\pi \frac{d_{p,q}}{\lambda}}
\label{eq:si_channel}
\end{equation}
where $\alpha_{p,q}^{\text{SI}} > 0$ and $d_{p,q} > 0$ denote the residual self-interference channel power and the distance between the $q$-th transmit antenna and the $p$-th receive antenna, respectively. For simplicity, we set $\alpha^{\text{SI}} = \alpha_{p,q}^{\text{SI}} = -110$ dB and let $e^{-j2\pi \frac{d_{p,q}}{\lambda}}$ be a unit-modulus variable with random phase for all transceiver antenna pairs $(p,q)$.

\subsubsection{Array Steering Vectors}

We deploy uniform linear arrays (ULA). For an $N$-element ULA, the steering vector can be expressed as:
\begin{equation}
\mathbf{a}(\theta) = 
\begin{bmatrix} 
e^{-j\frac{N-1}{2}\pi \sin\theta}, e^{-j\frac{N-3}{2}\pi \sin\theta}, \ldots, e^{j\frac{N-1}{2}\pi \sin\theta}
\end{bmatrix}^T 
\label{eq:steering_vector}
\end{equation}
where $N$ denotes the number of antennas. We choose the center of the ULA antennas as the reference point. It can be verified that $\mathbf{a}^H(\theta) \dot{\mathbf{a}}(\theta) = 0$.

\subsubsection{Received Signal at Eavesdropper}

The received signal at the eavesdropper can be written as:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{G}_E(\boldsymbol{\theta})\mathbf{x}(l) + \mathbf{H}_{\text{CE}}\mathbf{x}(l) + \mathbf{n}_{\text{eve}}(l)
\label{eq:eve_received}
\end{equation}
where $\mathbf{G}_E(\boldsymbol{\theta}) = \beta(l)\mathbf{b}(\theta_E)\mathbf{a}_t^H(\theta_L)$ represents the target-reflected channel to the eavesdropper, $\boldsymbol{\theta} = [\theta_E, \theta_L]^T$ denotes the angle parameter vector, $\theta_E$ denotes the eavesdropper's angle, $\mathbf{b}(\theta_E) \in \mathbb{C}^{N_E \times 1}$ is the eavesdropper's array steering vector, $\mathbf{H}_{\text{CE}} \in \mathbb{C}^{N_E \times N_{\text{BS,t}}}$ denotes the direct channel matrix from the base station to the eavesdropper, and $\mathbf{n}_{\text{eve}}(l)\sim \mathcal{CN}(\mathbf{0}, \sigma_n^2 \mathbf{I}_{N_E})$ is the additive noise.

The effective channel matrix combining both signal paths is:
\begin{equation}
\mathbf{H}_{\text{eff}} = \mathbf{G}_E(\boldsymbol{\theta}) + \mathbf{H}_{\text{CE}} \in \mathbb{C}^{N_E \times N_{\text{BS,t}}}
\label{eq:effective_channel}
\end{equation}

\subsubsection{Received Signal at Communication Users}

The received signal at the $k$-th single-antenna communication user at the $l$-th time slot is given as:
\begin{equation}
y_{k}(l) = \mathbf{h}_{k}^H\mathbf{x}(l) + n_{k}(l)
\label{eq:user_received}
\end{equation}
where $\mathbf{h}_{k} \in \mathbb{C}^{N_{\text{BS,t}} \times 1}$ denotes the channel vector between the monostatic base station and the $k$-th user, and $n_{k}(l)$ denotes the additive white Gaussian noise with variance $\sigma^2_{k}$.

We assume that $\mathbf{h}_{k}$ follows a slow-fading Rician channel model:
\begin{equation}
\mathbf{h}_{k} = \sqrt{\frac{\kappa_k}{1+\kappa_k} }\mathbf{h}_{k}^{\text{LoS}} + \sqrt{\frac{1}{1+\kappa_k} }\mathbf{h}_{k}^{\text{NLoS}}
\label{eq:rician_channel}
\end{equation}
where $\kappa_k>0$ is the Rician $K$-factor of the $k$-th user, $\mathbf{h}_{k}^{\text{LoS}} = \sqrt{N_{\text{BS,t}}}\mathbf{a}_t(\omega_{k,0})$ denotes the line-of-sight component, and $\mathbf{a}_t(\omega_{k,0})$ is the array steering vector with $\omega_{k,0}\in[-\frac{\pi}{2},\frac{\pi}{2}]$ being the angle of departure from the base station to user $k$. The scattering component is:
\begin{equation}
\mathbf{h}_{k}^{\text{NLoS}} = \sqrt{\frac{N_{\text{BS,t}}}{L_p}}\sum_{p=1}^{L_p} c_{k,p} \mathbf{a}_t(\omega_{k,p})
\label{eq:nlos_component}
\end{equation}
where $L_p$ denotes the number of propagation paths, $c_{k,p}\sim \mathcal{CN}(0, 1)$ is the complex path gain, and $\omega_{k,p}$ is the angle of departure associated with the $(k,p)$-th propagation path.

\section{Information Leakage Analysis}

In this section, we analyze the fundamental information leakage mechanisms in the proposed FD-ISAC system. The eavesdropper can potentially extract both sensing and communication information through joint parameter estimation, exploiting the correlation between direct and reflected signal paths.

\subsection{Sensing Mutual Information Analysis}

We first analyze the sensing information leakage through the mutual information framework. The eavesdropper attempts to extract sensing parameters $\boldsymbol{\theta}_S = [\theta_E, \theta_L]^T$ from the received signals, where the transmitted signals $\mathbf{s}(l)$ are modeled as Gaussian random variables.

\subsubsection{Signal Model for Sensing MI}

For the sensing mutual information analysis, we consider the transmitted symbols as random Gaussian signals:
\begin{equation}
\mathbf{s}(l) \sim \mathcal{CN}(\mathbf{0}, \mathbf{R}_s)
\label{eq:gaussian_signal_model}
\end{equation}

where $\mathbf{R}_s = \mathbb{E}[\mathbf{s}(l)\mathbf{s}^H(l)] \in \mathbb{C}^{K \times K}$ is the signal covariance matrix. This Gaussian assumption is justified for analyzing the fundamental sensing performance limits and is commonly used in ISAC literature.

The eavesdropper's observation for sensing parameter estimation is:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{H}_{\text{eff}}(\boldsymbol{\theta}_S)\mathbf{F}\mathbf{s}(l) + \mathbf{n}_{\text{eve}}(l)
\label{eq:sensing_observation}
\end{equation}

where $\mathbf{H}_{\text{eff}}(\boldsymbol{\theta}_S) = \mathbf{G}_E(\boldsymbol{\theta}_S) + \mathbf{H}_{\text{CE}}$ depends on the sensing parameters.

\subsubsection{Sensing Mutual Information Definition}

The sensing mutual information quantifies the amount of information about the sensing parameters $\boldsymbol{\theta}_S$ that can be extracted from the eavesdropper's observations over $L$ time slots:

\begin{equation}
I_S = I(\boldsymbol{\theta}_S; \mathbf{Y}_{\text{eve}}^{(L)})
\label{eq:sensing_mi_definition}
\end{equation}

where $\mathbf{Y}_{\text{eve}}^{(L)} = [\mathbf{y}_{\text{eve}}(1), \mathbf{y}_{\text{eve}}(2), \ldots, \mathbf{y}_{\text{eve}}(L)]$ represents the collected observations over $L$ time slots.

\subsubsection{Sensing MI for Single Time Slot}

For a single time slot, the sensing mutual information can be expressed as:
\begin{equation}
I_S^{(1)} = I(\boldsymbol{\theta}_S; \mathbf{y}_{\text{eve}}(l))
\label{eq:single_slot_sensing_mi}
\end{equation}

Since the sensing parameters are deterministic but unknown, we model them with a prior distribution $p(\boldsymbol{\theta}_S)$. For mathematical tractability, we assume:
\begin{equation}
\boldsymbol{\theta}_S \sim \mathcal{N}(\boldsymbol{\mu}_{\theta}, \mathbf{C}_{\theta})
\label{eq:sensing_prior}
\end{equation}

where $\boldsymbol{\mu}_{\theta}$ and $\mathbf{C}_{\theta}$ represent the prior mean and covariance of the sensing parameters.

\subsubsection{Linearized Sensing Channel Model}

For small variations around the nominal sensing parameters $\boldsymbol{\theta}_S^{(0)}$, we can linearize the sensing channel:
\begin{equation}
\mathbf{H}_{\text{eff}}(\boldsymbol{\theta}_S) \approx \mathbf{H}_{\text{eff}}^{(0)} + \mathbf{J}_H (\boldsymbol{\theta}_S - \boldsymbol{\theta}_S^{(0)})
\label{eq:linearized_channel}
\end{equation}

where $\mathbf{H}_{\text{eff}}^{(0)} = \mathbf{H}_{\text{eff}}(\boldsymbol{\theta}_S^{(0)})$ and $\mathbf{J}_H \in \mathbb{C}^{N_E \times 2}$ is the Jacobian matrix:
\begin{equation}
\mathbf{J}_H = \left[\frac{\partial \mathbf{H}_{\text{eff}}}{\partial \theta_E}\Big|_{\boldsymbol{\theta}_S^{(0)}}, \frac{\partial \mathbf{H}_{\text{eff}}}{\partial \theta_L}\Big|_{\boldsymbol{\theta}_S^{(0)}}\right]
\label{eq:jacobian_matrix}
\end{equation}

The partial derivatives are:
\begin{align}
\frac{\partial \mathbf{H}_{\text{eff}}}{\partial \theta_E} &= \beta(l)\frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E}\mathbf{a}_t^H(\theta_L) \label{eq:partial_theta_e_sensing} \\
\frac{\partial \mathbf{H}_{\text{eff}}}{\partial \theta_L} &= \beta(l)\mathbf{b}(\theta_E)\frac{\partial \mathbf{a}_t^H(\theta_L)}{\partial \theta_L} \label{eq:partial_theta_l_sensing}
\end{align}

\subsubsection{Sensing MI with Gaussian Signals}

Under the linearized model and Gaussian assumptions, the sensing mutual information becomes:
\begin{equation}
I_S^{(1)} = \frac{1}{2}\log_2\det\left(\mathbf{I}_2 + \mathbf{C}_{\theta}\mathbf{J}_H^H\mathbf{R}_y^{-1}\mathbf{J}_H\right)
\label{eq:sensing_mi_gaussian}
\end{equation}

where $\mathbf{R}_y$ is the covariance matrix of the received signal:
\begin{equation}
\mathbf{R}_y = \mathbf{H}_{\text{eff}}^{(0)}\mathbf{F}\mathbf{R}_s\mathbf{F}^H(\mathbf{H}_{\text{eff}}^{(0)})^H + \sigma_n^2\mathbf{I}_{N_E}
\label{eq:received_signal_covariance}
\end{equation}

\subsubsection{Multi-Time Slot Sensing MI}

For $L$ time slots with independent signal realizations, the sensing mutual information accumulates as:
\begin{equation}
I_S^{(L)} = \frac{L}{2}\log_2\det\left(\mathbf{I}_2 + L\mathbf{C}_{\theta}\mathbf{J}_H^H\mathbf{R}_y^{-1}\mathbf{J}_H\right)
\label{eq:multi_slot_sensing_mi}
\end{equation}

This shows that the sensing mutual information grows logarithmically with the number of observation time slots, providing the eavesdropper with improved sensing parameter estimation over time.

\subsubsection{Sensing MI Components}

We can decompose the sensing mutual information into components corresponding to individual sensing parameters:

\textbf{Eavesdropper Angle Information:}
\begin{equation}
I_{S,\theta_E} = \frac{L}{2}\log_2\left(1 + L\sigma_{\theta_E}^2\mathbf{j}_{\theta_E}^H\mathbf{R}_y^{-1}\mathbf{j}_{\theta_E}\right)
\label{eq:theta_e_mi}
\end{equation}

\textbf{Target Angle Information:}
\begin{equation}
I_{S,\theta_L} = \frac{L}{2}\log_2\left(1 + L\sigma_{\theta_L}^2\mathbf{j}_{\theta_L}^H\mathbf{R}_y^{-1}\mathbf{j}_{\theta_L}\right)
\label{eq:theta_l_mi}
\end{equation}

where $\mathbf{j}_{\theta_E}$ and $\mathbf{j}_{\theta_L}$ are the corresponding columns of $\mathbf{J}_H$, and $\sigma_{\theta_E}^2$ and $\sigma_{\theta_L}^2$ are the prior variances of the sensing parameters.

\subsubsection{Sensing Information Leakage Metrics}

Based on the sensing mutual information analysis, we define sensing information leakage metrics:

\textbf{Total Sensing Leakage:}
\begin{equation}
\mathcal{L}_S^{\text{total}} = I_S^{(L)} = \frac{L}{2}\log_2\det\left(\mathbf{I}_2 + L\mathbf{C}_{\theta}\mathbf{J}_H^H\mathbf{R}_y^{-1}\mathbf{J}_H\right)
\label{eq:total_sensing_leakage}
\end{equation}

\textbf{Sensing Leakage Rate:}
\begin{equation}
\mathcal{R}_S = \frac{I_S^{(L)}}{L} = \frac{1}{2}\log_2\det\left(\mathbf{I}_2 + L\mathbf{C}_{\theta}\mathbf{J}_H^H\mathbf{R}_y^{-1}\mathbf{J}_H\right)
\label{eq:sensing_leakage_rate}
\end{equation}

\textbf{Asymptotic Sensing Leakage:}
For large $L$, the sensing leakage rate approaches:
\begin{equation}
\lim_{L \to \infty} \mathcal{R}_S = \frac{1}{2}\log_2\det\left(\mathbf{C}_{\theta}\mathbf{J}_H^H\mathbf{R}_y^{-1}\mathbf{J}_H\right)
\label{eq:asymptotic_sensing_leakage}
\end{equation}

\subsubsection{Sensing MI for Specific Signal Model}

For our specific FD-ISAC system model, we can derive more explicit expressions. The effective channel matrix is:
\begin{equation}
\mathbf{H}_{\text{eff}}(\boldsymbol{\theta}_S) = \beta(l)\mathbf{b}(\theta_E)\mathbf{a}_t^H(\theta_L) + \mathbf{H}_{\text{CE}}
\label{eq:specific_effective_channel}
\end{equation}

The Jacobian components become:
\begin{align}
\mathbf{j}_{\theta_E} &= \frac{\partial \mathbf{H}_{\text{eff}}\mathbf{F}\mathbf{s}(l)}{\partial \theta_E} = \beta(l)\dot{\mathbf{b}}(\theta_E)\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l) \label{eq:jacobian_theta_e} \\
\mathbf{j}_{\theta_L} &= \frac{\partial \mathbf{H}_{\text{eff}}\mathbf{F}\mathbf{s}(l)}{\partial \theta_L} = \beta(l)\mathbf{b}(\theta_E)\dot{\mathbf{a}}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l) \label{eq:jacobian_theta_l}
\end{align}

where $\dot{\mathbf{b}}(\theta_E) = \frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E}$ and $\dot{\mathbf{a}}_t(\theta_L) = \frac{\partial \mathbf{a}_t(\theta_L)}{\partial \theta_L}$.

\textbf{Simplified Analysis for Independent Parameters:}
Under the assumption of weakly coupled sensing parameters and using the chain rule of mutual information, we can decompose:
\begin{align}
I_S^{(L)} &= I(\theta_E, \theta_L; \mathbf{Y}_{\text{eve}}^{(L)}) \nonumber \\
&= I(\theta_E; \mathbf{Y}_{\text{eve}}^{(L)}) + I(\theta_L; \mathbf{Y}_{\text{eve}}^{(L)} | \theta_E) \label{eq:sensing_mi_decomposed}
\end{align}

For weakly coupled parameters, the conditional term can be approximated as:
\begin{align}
I_S^{(L)} &\approx I(\theta_E; \mathbf{Y}_{\text{eve}}^{(L)}) + I(\theta_L; \mathbf{Y}_{\text{eve}}^{(L)}) \label{eq:sensing_mi_approximated}
\end{align}

where the individual components are:
\begin{align}
I(\theta_E; \mathbf{Y}_{\text{eve}}^{(L)}) &= \frac{L}{2}\log_2\left(1 + L \cdot \text{SNR}_{\text{eff}}^{(\theta_E)}\right) \label{eq:theta_e_mi_individual} \\
I(\theta_L; \mathbf{Y}_{\text{eve}}^{(L)}) &= \frac{L}{2}\log_2\left(1 + L \cdot \text{SNR}_{\text{eff}}^{(\theta_L)}\right) \label{eq:theta_l_mi_individual}
\end{align}

The effective SNRs are:
\begin{align}
\text{SNR}_{\text{eff}}^{(\theta_E)} &= \frac{|\beta(l)|^2\sigma_{\theta_E}^2\|\dot{\mathbf{b}}(\theta_E)\|^2\mathbb{E}[|\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l)|^2]}{\sigma_n^2} \label{eq:snr_theta_e} \\
\text{SNR}_{\text{eff}}^{(\theta_L)} &= \frac{|\beta(l)|^2\sigma_{\theta_L}^2\|\mathbf{b}(\theta_E)\|^2\mathbb{E}[|\dot{\mathbf{a}}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l)|^2]}{\sigma_n^2} \label{eq:snr_theta_l}
\end{align}

For Gaussian signals with $\mathbf{s}(l) \sim \mathcal{CN}(\mathbf{0}, \mathbf{R}_s)$:
\begin{align}
\mathbb{E}[|\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l)|^2] &= \mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{R}_s\mathbf{F}^H\mathbf{a}_t(\theta_L) \label{eq:expectation_theta_l} \\
\mathbb{E}[|\dot{\mathbf{a}}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l)|^2] &= \dot{\mathbf{a}}_t^H(\theta_L)\mathbf{F}\mathbf{R}_s\mathbf{F}^H\dot{\mathbf{a}}_t(\theta_L) \label{eq:expectation_dot_theta_l}
\end{align}

and $\mathcal{I}_{\text{coupling}}$ represents the coupling term between the two sensing parameters.

\textbf{Precoding Impact on Sensing MI:}
The precoding matrix $\mathbf{F}$ significantly affects the sensing mutual information through the terms $\mathbf{a}_t^H(\theta_L)\mathbf{F}$ and $\dot{\mathbf{a}}_t^H(\theta_L)\mathbf{F}$. The sensing information leakage can be minimized by designing $\mathbf{F}$ such that:
\begin{equation}
\min_{\mathbf{F}} \left\|\mathbf{a}_t^H(\theta_L)\mathbf{F}\right\|^2 + \left\|\dot{\mathbf{a}}_t^H(\theta_L)\mathbf{F}\right\|^2
\label{eq:precoding_optimization}
\end{equation}

subject to communication performance constraints.

\subsubsection{Sensing MI with Self-Interference}

In the FD-ISAC system, self-interference affects the sensing mutual information. However, the eavesdropper does not directly experience self-interference since it only receives signals from the base station. The self-interference $\mathbf{H}_{\text{SI}}$ affects the base station's own reception, but the eavesdropper's received signal covariance is:
\begin{equation}
\mathbf{R}_y = \mathbf{H}_{\text{eff}}\mathbf{F}\mathbf{R}_s\mathbf{F}^H\mathbf{H}_{\text{eff}}^H + \sigma_n^2\mathbf{I}_{N_E}
\label{eq:received_covariance_corrected}
\end{equation}

The self-interference primarily affects the base station's sensing performance, but may indirectly influence the eavesdropper if the base station adapts its transmission strategy based on self-interference levels.

\subsubsection{Sensing Degrees of Freedom}

The sensing degrees of freedom (DoF) quantify the number of independent sensing parameters that can be reliably estimated. For our system, the DoF is determined by the rank of the Fisher Information Matrix:
\begin{equation}
\text{DoF}_S = \rank\left(\mathbf{J}_H^H\mathbf{R}_y^{-1}\mathbf{J}_H\right)
\label{eq:sensing_dof}
\end{equation}

where $\mathbf{J}_H \in \mathbb{C}^{N_E \times 2}$ is the Jacobian matrix containing the derivatives with respect to $\theta_E$ and $\theta_L$.

\textbf{DoF Analysis:}
\begin{itemize}
    \item \textbf{Maximum DoF:} $\text{DoF}_S \leq \min(2, N_E)$
    \item \textbf{Full DoF Condition:} $\text{DoF}_S = 2$ when $N_E \geq 2$ and the two sensing parameters are distinguishable
    \item \textbf{Reduced DoF:} $\text{DoF}_S < 2$ when parameters are highly correlated or $N_E = 1$
\end{itemize}

In the high SNR regime with sufficient antenna elements ($N_E \geq 2$), the eavesdropper can potentially estimate both sensing parameters independently, achieving $\text{DoF}_S = 2$.

\subsubsection{Sensing MI Scaling Properties}

The sensing mutual information exhibits specific scaling properties:

\textbf{Asymptotic Behavior:}
For large $L$, the sensing mutual information approaches:
\begin{equation}
I_S^{(L)} \approx \frac{1}{2}\log_2(L) + \frac{1}{2}\log_2\det\left(\mathbf{C}_{\theta}\mathbf{J}_H^H\mathbf{R}_y^{-1}\mathbf{J}_H\right)
\label{eq:sensing_mi_asymptotic}
\end{equation}

\textbf{Key Characteristics:}
\begin{itemize}
    \item \textbf{Logarithmic Growth:} $I_S^{(L)} \propto \frac{1}{2}\log_2(L)$ for large $L$
    \item \textbf{Parameter Limitation:} Upper bounded by the number of sensing parameters (2 in our case)
    \item \textbf{Geometric Dependence:} Depends on steering vector derivatives and array geometry
    \item \textbf{SNR Dependence:} Higher SNR increases the constant term but not the scaling
\end{itemize}

\textbf{Practical Implications:}
The logarithmic scaling means that the eavesdropper's sensing parameter estimation accuracy improves slowly but steadily over time, making long-term exposure particularly dangerous for sensing security.

\subsection{Tight Asymptotic Bounds Using Random Matrix Theory}

To obtain tighter bounds for the sensing mutual information, we apply the asymptotic approximation framework from random matrix theory for Gaussian channels with large coherent processing intervals.

\subsubsection{System Parameters for Asymptotic Analysis}

We define the key parameters for the asymptotic analysis:
\begin{itemize}
    \item $N_{\text{CPI}}$: Coherent processing interval length (number of symbols)
    \item $N_D$: Total number of data streams
    \item $c = N_{\text{CPI}}/N_D$: Load ratio (assumed finite as $N_{\text{CPI}} \to \infty$)
    \item $K_R$: Number of non-zero eigenvalues of the sensing correlation matrix
\end{itemize}

\subsubsection{Sensing Correlation Matrix}

For our FD-ISAC system, the sensing correlation matrix is defined as:
\begin{equation}
\mathbf{R}_{\text{sensing}} = \mathbb{E}[\text{vec}(\mathbf{G}_E^H)\text{vec}(\mathbf{G}_E^H)^H] \in \mathbb{C}^{N_E N_{\text{BS,t}} \times N_E N_{\text{BS,t}}}
\label{eq:sensing_correlation_matrix}
\end{equation}

For our specific model with $\mathbf{G}_E = \beta(l)\mathbf{b}(\theta_E)\mathbf{a}_t^H(\theta_L)$:
\begin{equation}
\mathbf{R}_{\text{sensing}} = |\beta|^2 (\mathbf{a}_t(\theta_L) \otimes \mathbf{b}(\theta_E))(\mathbf{a}_t(\theta_L) \otimes \mathbf{b}(\theta_E))^H
\label{eq:sensing_correlation_specific}
\end{equation}

This matrix has rank $K_R = 1$ since it is formed by the outer product of a single vector.

\subsubsection{Eigenvalue Analysis}

The non-zero eigenvalue of $\mathbf{R}_{\text{sensing}}$ is:
\begin{equation}
\lambda_{R,1} = |\beta|^2 \|\mathbf{a}_t(\theta_L)\|^2 \|\mathbf{b}(\theta_E)\|^2 = |\beta|^2 N_{\text{BS,t}} N_E
\label{eq:eigenvalue_sensing}
\end{equation}

The corresponding eigenvector is:
\begin{equation}
\mathbf{v}_1 = \frac{\mathbf{a}_t(\theta_L) \otimes \mathbf{b}(\theta_E)}{\|\mathbf{a}_t(\theta_L)\| \|\mathbf{b}(\theta_E)\|}
\label{eq:eigenvector_sensing}
\end{equation}

\subsubsection{Tight Asymptotic Bound}

Following the asymptotic approximation framework, the sensing mutual information can be expressed as:
\begin{equation}
I_S^{\text{tight}} = \bar{\varrho}_1(\boldsymbol{\Phi}) + \mathcal{O}\left(\frac{1}{N_{\text{CPI}}}\right)
\label{eq:tight_sensing_mi}
\end{equation}

where the tight bound component is:
\begin{align}
\bar{\varrho}_1(\boldsymbol{\Phi}) &= \log_2\left|\mathbf{I}_{N_E} + \frac{\lambda_{R,1}}{1 + \lambda_{R,1}\delta(\lambda_{R,1})}\mathbf{T}(\boldsymbol{\Phi})\right| \nonumber \\
&\quad + N_{\text{CPI}}\log_2(1 + \lambda_{R,1}\delta(\lambda_{R,1})) \nonumber \\
&\quad - N_{\text{CPI}}\frac{\lambda_{R,1}\delta(\lambda_{R,1})}{1 + \lambda_{R,1}\delta(\lambda_{R,1})}
\label{eq:tight_bound_component}
\end{align}

\subsubsection{Key Functions and Matrices}

The auxiliary function $\delta(\lambda)$ is defined as:
\begin{equation}
\delta(\lambda_{R,1}) = \frac{1}{N_{\text{CPI}}}\text{tr}\left(\mathbf{T}(\boldsymbol{\Phi})\left(\mathbf{I}_{N_E} + \frac{\lambda_{R,1}}{1 + \lambda_{R,1}\delta(\lambda_{R,1})}\mathbf{T}(\boldsymbol{\Phi})\right)^{-1}\right)
\label{eq:delta_function}
\end{equation}

The matrix $\mathbf{T}(\boldsymbol{\Phi})$ captures the signal and noise characteristics:
\begin{equation}
\mathbf{T}(\boldsymbol{\Phi}) = \frac{1}{\sigma_n^2}\mathbf{H}_{\text{eff}}\mathbf{F}\mathbf{R}_s\mathbf{F}^H\mathbf{H}_{\text{eff}}^H
\label{eq:T_matrix}
\end{equation}

\subsubsection{Simplified Tight Bound for High SNR}

In the high SNR regime where $\lambda_{R,1}\delta(\lambda_{R,1}) \gg 1$, the tight bound simplifies to:
\begin{align}
I_S^{\text{tight}} &\approx \log_2\left|\frac{\lambda_{R,1}}{\delta(\lambda_{R,1})}\mathbf{T}(\boldsymbol{\Phi})\right| + N_{\text{CPI}}\log_2(\lambda_{R,1}\delta(\lambda_{R,1})) \nonumber \\
&\quad - N_{\text{CPI}}\lambda_{R,1}\delta(\lambda_{R,1})
\label{eq:high_snr_tight_bound}
\end{align}

\subsubsection{Comparison with Previous Bounds}

The tight asymptotic bound provides several advantages over the previous Fisher information-based bounds:

\textbf{Accuracy Improvement:}
\begin{equation}
\left|I_S^{\text{tight}} - I_S^{\text{true}}\right| = \mathcal{O}\left(\frac{1}{N_{\text{CPI}}}\right)
\label{eq:accuracy_improvement}
\end{equation}

compared to the Fisher-based bound:
\begin{equation}
\left|I_S^{\text{Fisher}} - I_S^{\text{true}}\right| = \mathcal{O}\left(\frac{1}{\sqrt{N_{\text{CPI}}}}\right)
\label{eq:fisher_accuracy}
\end{equation}

\textbf{Finite Sample Correction:}
The tight bound accounts for finite sample effects through the $\delta(\lambda_{R,1})$ function, providing more accurate predictions for practical system parameters.

\textbf{Load Ratio Dependence:}
The bound explicitly captures the effect of the load ratio $c = N_{\text{CPI}}/N_D$, which is crucial for system design optimization.

\subsubsection{Numerical Computation Algorithm}

The tight bound can be computed using the following iterative algorithm:

\textbf{Algorithm 1: Tight Sensing MI Computation}
\begin{enumerate}
    \item \textbf{Initialize:} Set $\delta^{(0)} = 1/c$ (initial guess)
    \item \textbf{Iterate:} For $n = 1, 2, \ldots$ until convergence:
    \begin{align}
    \mathbf{Q}^{(n)} &= \left(\mathbf{I}_{N_E} + \frac{\lambda_{R,1}}{1 + \lambda_{R,1}\delta^{(n-1)}}\mathbf{T}(\boldsymbol{\Phi})\right)^{-1} \label{eq:Q_update} \\
    \delta^{(n)} &= \frac{1}{N_{\text{CPI}}}\text{tr}(\mathbf{T}(\boldsymbol{\Phi})\mathbf{Q}^{(n)}) \label{eq:delta_update}
    \end{align}
    \item \textbf{Compute:} Final sensing MI using converged $\delta^{(\infty)}$:
    \begin{align}
    I_S^{\text{tight}} &= \log_2\left|\mathbf{I}_{N_E} + \frac{\lambda_{R,1}}{1 + \lambda_{R,1}\delta^{(\infty)}}\mathbf{T}(\boldsymbol{\Phi})\right| \nonumber \\
    &\quad + N_{\text{CPI}}\log_2(1 + \lambda_{R,1}\delta^{(\infty)}) \nonumber \\
    &\quad - N_{\text{CPI}}\frac{\lambda_{R,1}\delta^{(\infty)}}{1 + \lambda_{R,1}\delta^{(\infty)}}
    \label{eq:final_tight_mi}
    \end{align}
\end{enumerate}

\subsubsection{Practical Implementation for FD-ISAC}

For our specific FD-ISAC system, the key matrices are:

\textbf{Effective Channel Matrix:}
\begin{equation}
\mathbf{H}_{\text{eff}} = \beta(l)\mathbf{b}(\theta_E)\mathbf{a}_t^H(\theta_L) + \mathbf{H}_{\text{CE}}
\label{eq:practical_heff}
\end{equation}

\textbf{Signal-to-Noise Matrix:}
\begin{equation}
\mathbf{T}(\boldsymbol{\Phi}) = \frac{1}{\sigma_n^2}\left(\beta(l)\mathbf{b}(\theta_E)\mathbf{a}_t^H(\theta_L) + \mathbf{H}_{\text{CE}}\right)\mathbf{F}\mathbf{R}_s\mathbf{F}^H\left(\beta(l)\mathbf{b}(\theta_E)\mathbf{a}_t^H(\theta_L) + \mathbf{H}_{\text{CE}}\right)^H
\label{eq:practical_T}
\end{equation}

\textbf{Eigenvalue:}
\begin{equation}
\lambda_{R,1} = |\beta(l)|^2 N_{\text{BS,t}} N_E
\label{eq:practical_eigenvalue}
\end{equation}

\subsubsection{Bounds Comparison and Validation}

The tight asymptotic bound provides the following hierarchy of bounds:

\textbf{Lower Bound (Fisher Information):}
\begin{equation}
I_S^{\text{lower}} = \frac{1}{2}\log_2\det\left(\mathbf{I}_2 + N_{\text{CPI}}\mathbf{C}_{\theta}\mathbf{J}_H^H\mathbf{R}_y^{-1}\mathbf{J}_H\right)
\label{eq:lower_bound}
\end{equation}

\textbf{Tight Bound (Random Matrix Theory):}
\begin{equation}
I_S^{\text{tight}} = \bar{\varrho}_1(\boldsymbol{\Phi}) + \mathcal{O}\left(\frac{1}{N_{\text{CPI}}}\right)
\label{eq:tight_bound_summary}
\end{equation}

\textbf{Upper Bound (Perfect CSI):}
\begin{equation}
I_S^{\text{upper}} = N_{\text{CPI}} \log_2\left(1 + \frac{|\beta(l)|^2 N_{\text{BS,t}} N_E \|\mathbf{F}\mathbf{R}_s^{1/2}\|_F^2}{\sigma_n^2}\right)
\label{eq:upper_bound}
\end{equation}

\textbf{Bound Relationship:}
\begin{equation}
I_S^{\text{lower}} \leq I_S^{\text{tight}} \leq I_S^{\text{upper}}
\label{eq:bound_hierarchy}
\end{equation}

with the tight bound achieving:
\begin{equation}
I_S^{\text{tight}} - I_S^{\text{lower}} = \mathcal{O}\left(\frac{\log N_{\text{CPI}}}{N_{\text{CPI}}}\right)
\label{eq:tightness_measure}
\end{equation}

\subsection{Communication Mutual Information Analysis}

Before analyzing the joint parameter estimation problem, we examine the communication mutual information from the eavesdropper's perspective. We assume that the eavesdropper (Eve) only has statistical knowledge of the communication channels between the base station and legitimate users, which is a realistic assumption for passive eavesdropping scenarios.

\subsubsection{Eavesdropper's Statistical CSI Assumption Justification}

The assumption that the eavesdropper only knows statistical channel information is highly reasonable for several reasons:

\textbf{Eavesdropper's Information Limitations:}
\begin{itemize}
    \item \textbf{Passive Nature:} Eve cannot participate in channel estimation procedures or pilot transmission
    \item \textbf{No Feedback Access:} Eve has no access to channel state information feedback from legitimate users
    \item \textbf{Independent Location:} Eve's channel to the BS is independent of legitimate users' channels
    \item \textbf{Limited Cooperation:} No coordination with the communication system for channel estimation
\end{itemize}

\textbf{Statistical Information Availability:}
\begin{itemize}
    \item \textbf{Long-term Observation:} Eve can estimate statistical parameters through prolonged eavesdropping
    \item \textbf{Geometric Inference:} LoS directions can be inferred from geographical information
    \item \textbf{Environment Analysis:} K-factors can be estimated based on propagation environment characteristics
    \item \textbf{Power Statistics:} Average received power levels can be observed over time
\end{itemize}

\textbf{Realistic Eavesdropping Scenario:}
\begin{itemize}
    \item \textbf{Instantaneous CSI Unknown:} Eve cannot know the exact channel realizations at each time instant
    \item \textbf{Statistical Model Reliance:} Eve must rely on statistical channel models for signal processing
    \item \textbf{Uncertainty in Decoding:} Channel uncertainty limits Eve's decoding capabilities
\end{itemize}

\subsubsection{Eavesdropper's Statistical Knowledge of Communication Channels}

From the eavesdropper's perspective, the communication channel between the BS and the $k$-th legitimate user follows the Rician model:
\begin{equation}
\mathbf{h}_{k} = \sqrt{\frac{\kappa_k}{1+\kappa_k} }\mathbf{h}_{k}^{\text{LoS}} + \sqrt{\frac{1}{1+\kappa_k} }\mathbf{h}_{k}^{\text{NLoS}}
\label{eq:rician_channel_eve_perspective}
\end{equation}

\textbf{Eve's Statistical Knowledge (Direct Communication Channel):}
For the direct communication path $\mathbf{H}_{\text{CE}}$, Eve can estimate:
\begin{itemize}
    \item \textbf{Channel Statistics:} $\mathbf{H}_{\text{CE}} \sim \mathcal{CN}(\boldsymbol{\mu}_{CE}, \mathbf{R}_{CE})$
    \item \textbf{Average Channel Power:} $\mathbb{E}[\|\mathbf{H}_{\text{CE}}\|_F^2]$
    \item \textbf{Spatial Correlation:} Correlation structure based on antenna geometry
    \item \textbf{Path Loss Model:} Distance-dependent attenuation characteristics
\end{itemize}

\textbf{Eve's Uncertainty (Sensing Interference):}
For the sensing path $\mathbf{G}_E(\boldsymbol{\theta})$, Eve faces significant uncertainty:
\begin{itemize}
    \item \textbf{Target Location:} $\theta_L$ is unknown and time-varying
    \item \textbf{Reflection Coefficient:} $\beta(l)$ is random and unpredictable
    \item \textbf{Eavesdropper Angle:} $\theta_E$ may be unknown to the system
    \item \textbf{Interference Power:} $\|\mathbf{G}_E(\boldsymbol{\theta})\mathbf{x}(l)\|^2$ is random
\end{itemize}

\subsubsection{Communication Mutual Information from Eavesdropper's Perspective}

From the eavesdropper's viewpoint, the communication mutual information quantifies how much information about the transmitted symbols can be extracted by observing the communication system, given only statistical channel knowledge.

\textbf{Eavesdropper's Observation Model:}
From equation (5), the eavesdropper receives:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{G}_E(\boldsymbol{\theta})\mathbf{x}(l) + \mathbf{H}_{\text{CE}}\mathbf{x}(l) + \mathbf{n}_{\text{eve}}(l)
\label{eq:eve_received_signal_decomposed}
\end{equation}

For communication mutual information analysis, we treat the signal paths differently:
\begin{itemize}
    \item \textbf{Desired Signal:} $\mathbf{H}_{\text{CE}}\mathbf{x}(l)$ - Direct communication path
    \item \textbf{Interference:} $\mathbf{G}_E(\boldsymbol{\theta})\mathbf{x}(l)$ - Target-reflected path (unknown sensing parameters)
    \item \textbf{Noise:} $\mathbf{n}_{\text{eve}}(l)$ - Thermal noise
\end{itemize}

The communication mutual information from Eve's perspective is:
\begin{equation}
I_{C}^{\text{Eve}} = I(\mathbf{s}; \mathbf{y}_{\text{eve}} | \text{statistical CSI of } \mathbf{H}_{\text{CE}}, \mathbf{G}_E \text{ as interference})
\label{eq:eve_comm_mi_definition}
\end{equation}

\textbf{Alternative Interpretation - Legitimate Users' Vulnerability:}
Alternatively, we can interpret this as the vulnerability of legitimate users' communications, measured by the mutual information they achieve under statistical CSI:
\begin{equation}
I_{C,k}^{\text{vuln}} = I(s_k; y_k | \text{statistical CSI of } \mathbf{h}_k)
\label{eq:user_vulnerability_mi}
\end{equation}

For all $K$ users collectively:
\begin{equation}
I_C^{\text{vuln}} = I(\mathbf{s}; \mathbf{y}_C | \text{statistical CSI})
\label{eq:total_comm_vulnerability}
\end{equation}

where $\mathbf{y}_C = [y_1, y_2, \ldots, y_K]^T$ is the vector of received signals at all communication users.

\subsubsection{Ergodic Communication Mutual Information Under Statistical CSI}

Under the statistical CSI assumption, we compute the ergodic mutual information averaged over the channel distribution. This represents the average information leakage/vulnerability when channels vary according to their statistical model.

\textbf{Ergodic MI for Communication Vulnerability:}
\begin{equation}
I_C^{\text{ergodic}} = \mathbb{E}_{\mathbf{H}_C}[I(\mathbf{s}; \mathbf{y}_C | \mathbf{H}_C)]
\label{eq:ergodic_comm_mi}
\end{equation}

where $\mathbf{H}_C = [\mathbf{h}_1, \mathbf{h}_2, \ldots, \mathbf{h}_K]^H \in \mathbb{C}^{K \times N_{\text{BS,t}}}$ is the communication channel matrix.

\textbf{Eavesdropper's Communication MI with Interference:}
The eavesdropper's received signal can be written as:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{H}_{\text{CE}}\mathbf{F}\mathbf{s}(l) + \mathbf{G}_E(\boldsymbol{\theta})\mathbf{F}\mathbf{s}(l) + \mathbf{n}_{\text{eve}}(l)
\label{eq:eve_signal_with_interference}
\end{equation}

Treating $\mathbf{G}_E(\boldsymbol{\theta})\mathbf{F}\mathbf{s}(l)$ as interference, the effective signal-to-interference-plus-noise ratio (SINR) is:
\begin{equation}
\text{SINR}_{\text{eve}} = \frac{\|\mathbf{H}_{\text{CE}}\mathbf{F}\mathbf{s}(l)\|^2}{\|\mathbf{G}_E(\boldsymbol{\theta})\mathbf{F}\mathbf{s}(l)\|^2 + \sigma_n^2}
\label{eq:eve_sinr}
\end{equation}

The ergodic mutual information becomes:
\begin{equation}
I_{C}^{\text{Eve,ergodic}} = \mathbb{E}_{\mathbf{H}_{\text{CE}}, \mathbf{G}_E}\left[\log_2\left(1 + \text{SINR}_{\text{eve}}\right)\right]
\label{eq:eve_ergodic_mi_with_interference}
\end{equation}

\subsubsection{Statistical Analysis of SINR Components}

\textbf{Signal Power (Known Statistics):}
The desired signal power from the direct path:
\begin{equation}
P_{\text{signal}} = \mathbb{E}[\|\mathbf{H}_{\text{CE}}\mathbf{F}\mathbf{s}(l)\|^2] = \text{tr}(\mathbf{F}^H\mathbb{E}[\mathbf{H}_{\text{CE}}^H\mathbf{H}_{\text{CE}}]\mathbf{F}\mathbf{R}_s)
\label{eq:signal_power}
\end{equation}

\textbf{Interference Power (Unknown Statistics):}
The interference power from the sensing path:
\begin{align}
P_{\text{interference}} &= \mathbb{E}[\|\mathbf{G}_E(\boldsymbol{\theta})\mathbf{F}\mathbf{s}(l)\|^2] \nonumber \\
&= \mathbb{E}[|\beta(l)|^2]\|\mathbf{b}(\theta_E)\|^2 \mathbb{E}[\mathbf{s}^H(l)\mathbf{F}^H\mathbf{a}_t(\theta_L)\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l)] \nonumber \\
&= \mathbb{E}[|\beta(l)|^2]\|\mathbf{b}(\theta_E)\|^2 \text{tr}(\mathbf{F}^H\mathbf{a}_t(\theta_L)\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{R}_s)
\label{eq:interference_power}
\end{align}

Since $\theta_L$ and $\beta(l)$ are unknown to Eve, the interference power is uncertain and time-varying.

\textbf{Average SINR:}
Under the assumption that Eve knows the statistics of $\mathbf{H}_{\text{CE}}$ but treats $\mathbf{G}_E(\boldsymbol{\theta})$ as random interference, and assuming the signal and interference are uncorrelated:
\begin{equation}
\mathbb{E}[\text{SINR}_{\text{eve}}] = \frac{\mathbb{E}[\|\mathbf{H}_{\text{CE}}\mathbf{F}\mathbf{s}(l)\|^2]}{\mathbb{E}[\|\mathbf{G}_E(\boldsymbol{\theta})\mathbf{F}\mathbf{s}(l)\|^2] + \sigma_n^2} = \frac{P_{\text{signal}}}{P_{\text{interference}} + \sigma_n^2}
\label{eq:average_sinr}
\end{equation}

Note: This equality holds when the signal and interference terms are statistically independent.

\textbf{Lower Bound on Communication MI:}
Using Jensen's inequality ($\mathbb{E}[\log(1+X)] \geq \log(1+\mathbb{E}[X])$ for $X \geq 0$):
\begin{equation}
I_{C}^{\text{Eve,ergodic}} \geq \log_2\left(1 + \frac{P_{\text{signal}}}{P_{\text{interference}} + \sigma_n^2}\right)
\label{eq:eve_mi_lower_bound}
\end{equation}

where we used the fact that $\mathbb{E}[\text{SINR}_{\text{eve}}] = \frac{P_{\text{signal}}}{P_{\text{interference}} + \sigma_n^2}$ under the independence assumption.

\subsubsection{Interference-Limited Regime Analysis}

When the sensing interference dominates ($P_{\text{interference}} \gg \sigma_n^2$), the SINR becomes:
\begin{equation}
\text{SINR}_{\text{interference-limited}} = \frac{P_{\text{signal}}}{P_{\text{interference}}} = \frac{\text{tr}(\mathbf{F}^H\mathbb{E}[\mathbf{H}_{\text{CE}}^H\mathbf{H}_{\text{CE}}]\mathbf{F}\mathbf{R}_s)}{\mathbb{E}[|\beta(l)|^2]\|\mathbf{b}(\theta_E)\|^2\text{tr}(\mathbf{F}^H\mathbf{a}_t(\theta_L)\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{R}_s)}
\label{eq:sinr_interference_limited}
\end{equation}

The ergodic mutual information in the interference-limited regime is upper bounded by:
\begin{equation}
I_{C}^{\text{Eve,ergodic}} \leq \log_2\left(1 + \text{SINR}_{\text{interference-limited}}\right)
\label{eq:interference_limited_mi}
\end{equation}

This shows that the sensing function naturally provides interference protection for communication security.

\textbf{Security Benefit of Sensing Interference:}
The sensing interference acts as a natural jammer:
\begin{equation}
I_{C}^{\text{Eve,no interference}} - I_{C}^{\text{Eve,with interference}} = \Delta I_{\text{security}}
\label{eq:security_benefit}
\end{equation}

where $\Delta I_{\text{security}} > 0$ represents the security improvement due to sensing interference.

\subsubsection{Rician Channel Ergodic MI Analysis}

For Rician channels, the ergodic mutual information can be computed using the moment generating function approach. The effective SNR for user $k$ is:
\begin{equation}
\gamma_k = \frac{\mathbf{h}_k^H\mathbf{F}\mathbf{R}_s\mathbf{F}^H\mathbf{h}_k}{\sigma_k^2}
\label{eq:effective_snr_user_k}
\end{equation}

\textbf{SNR Distribution Analysis:}
The effective SNR $\gamma_k$ follows a generalized distribution that depends on the Rician parameters:
\begin{align}
\gamma_k &= \frac{1}{\sigma_k^2}\left[\frac{\kappa_k}{1+\kappa_k}|\mathbf{h}_k^{\text{LoS},H}\mathbf{F}\mathbf{s}|^2 + \frac{1}{1+\kappa_k}|\mathbf{h}_k^{\text{NLoS},H}\mathbf{F}\mathbf{s}|^2\right. \nonumber \\
&\quad \left. + 2\sqrt{\frac{\kappa_k}{(1+\kappa_k)^2}}\text{Re}(\mathbf{h}_k^{\text{LoS},H}\mathbf{F}\mathbf{s} \cdot \overline{\mathbf{h}_k^{\text{NLoS},H}\mathbf{F}\mathbf{s}})\right]
\label{eq:snr_decomposition}
\end{align}

\subsubsection{Closed-Form Approximation}

For practical computation, we use the Jensen's inequality to obtain a lower bound:
\begin{equation}
I_{C,k}^{\text{ergodic}} \geq \log_2\left(1 + \mathbb{E}[\gamma_k]\right)
\label{eq:jensen_lower_bound}
\end{equation}

The expected SNR is:
\begin{equation}
\mathbb{E}[\gamma_k] = \frac{1}{\sigma_k^2}\mathbb{E}[\mathbf{h}_k^H\mathbf{F}\mathbf{R}_s\mathbf{F}^H\mathbf{h}_k] = \frac{\text{tr}(\mathbf{F}\mathbf{R}_s\mathbf{F}^H)}{\sigma_k^2}
\label{eq:expected_snr}
\end{equation}

\textbf{Total Communication MI Lower Bound:}
\begin{equation}
I_C^{\text{ergodic}} \geq \sum_{k=1}^K \log_2\left(1 + \frac{\text{tr}(\mathbf{F}\mathbf{R}_s\mathbf{F}^H)}{\sigma_k^2}\right)
\label{eq:total_comm_mi_lower}
\end{equation}

\subsubsection{Tighter Bound Using Rician Statistics}

For a tighter analysis, we exploit the specific structure of Rician channels. The ergodic mutual information can be expressed as:
\begin{equation}
I_{C,k}^{\text{ergodic}} = \mathbb{E}\left[\log_2\left(1 + \frac{\kappa_k}{1+\kappa_k}\gamma_{k,\text{LoS}} + \frac{1}{1+\kappa_k}\gamma_{k,\text{NLoS}}\right)\right]
\label{eq:rician_mi_decomposition}
\end{equation}

where:
\begin{align}
\gamma_{k,\text{LoS}} &= \frac{|\mathbf{h}_k^{\text{LoS},H}\mathbf{F}\mathbf{s}|^2}{\sigma_k^2} \label{eq:los_snr} \\
\gamma_{k,\text{NLoS}} &= \frac{|\mathbf{h}_k^{\text{NLoS},H}\mathbf{F}\mathbf{s}|^2}{\sigma_k^2} \label{eq:nlos_snr}
\end{align}

\textbf{LoS Component Analysis:}
The LoS component is deterministic:
\begin{equation}
\gamma_{k,\text{LoS}} = \frac{N_{\text{BS,t}}|\mathbf{a}_t^H(\omega_{k,0})\mathbf{F}\mathbf{s}|^2}{\sigma_k^2}
\label{eq:los_snr_explicit}
\end{equation}

\textbf{NLoS Component Analysis:}
The NLoS component follows a chi-squared distribution. For Gaussian signals $\mathbf{s} \sim \mathcal{CN}(\mathbf{0}, \mathbf{R}_s)$:
\begin{equation}
\mathbb{E}[\gamma_{k,\text{NLoS}}] = \frac{\text{tr}(\mathbf{F}\mathbf{R}_s\mathbf{F}^H)}{\sigma_k^2(1+\kappa_k)}
\label{eq:nlos_expected_snr}
\end{equation}

\subsubsection{Asymptotic Analysis for Large Antenna Arrays}

For large $N_{\text{BS,t}}$, using random matrix theory, the ergodic mutual information approaches:
\begin{equation}
I_{C,k}^{\text{ergodic}} \approx \log_2\left(1 + \frac{\kappa_k N_{\text{BS,t}}|\mathbf{a}_t^H(\omega_{k,0})\mathbf{f}_k|^2 s_k^2}{(1+\kappa_k)\sigma_k^2} + \frac{\text{tr}(\mathbf{F}\mathbf{R}_s\mathbf{F}^H)}{(1+\kappa_k)\sigma_k^2}\right)
\label{eq:asymptotic_comm_mi}
\end{equation}

where $\mathbf{f}_k$ is the precoding vector for user $k$ and $s_k^2 = \mathbb{E}[|s_k|^2]$.

\subsubsection{Multi-User Communication MI}

For the multi-user case, considering inter-user interference:
\begin{equation}
I_C^{\text{ergodic}} = \mathbb{E}\left[\log_2\det\left(\mathbf{I}_K + \mathbf{D}^{-1}\mathbf{H}_C\mathbf{F}\mathbf{R}_s\mathbf{F}^H\mathbf{H}_C^H\right)\right]
\label{eq:multiuser_comm_mi}
\end{equation}

where $\mathbf{D} = \text{diag}(\sigma_1^2, \sigma_2^2, \ldots, \sigma_K^2)$ is the noise covariance matrix.

\textbf{Rician Channel Matrix Statistics:}
The communication channel matrix has the structure:
\begin{equation}
\mathbf{H}_C = \sqrt{\frac{\kappa}{1+\kappa}}\mathbf{H}_C^{\text{LoS}} + \sqrt{\frac{1}{1+\kappa}}\mathbf{H}_C^{\text{NLoS}}
\label{eq:rician_matrix_structure}
\end{equation}

where $\mathbf{H}_C^{\text{LoS}} = \sqrt{N_{\text{BS,t}}}[\mathbf{a}_t(\omega_{1,0}), \mathbf{a}_t(\omega_{2,0}), \ldots, \mathbf{a}_t(\omega_{K,0})]^H$ and $\mathbf{H}_C^{\text{NLoS}}$ has i.i.d. $\mathcal{CN}(0,1)$ entries.

\subsubsection{Security Implications from Eavesdropper's Perspective}

The ergodic mutual information analysis reveals several security implications when the eavesdropper only has statistical CSI:

\textbf{Limited Eavesdropping Capability:}
Under statistical CSI, the eavesdropper's capability is fundamentally limited by channel uncertainty:
\begin{equation}
I_{C}^{\text{Eve,stat}} \leq I_C^{\text{ergodic}} < I_{C}^{\text{perfect CSI}}
\label{eq:eve_limitation}
\end{equation}

\textbf{LoS Component Vulnerability:}
The LoS component is more predictable and represents the primary vulnerability:
\begin{equation}
I_{C,\text{LoS}}^{\text{vuln}} = \sum_{k=1}^K \log_2\left(1 + \frac{\kappa_k N_{\text{BS,t}}|\mathbf{a}_t^H(\omega_{k,0})\mathbf{f}_k|^2 s_k^2}{(1+\kappa_k)\sigma_k^2}\right)
\label{eq:los_vulnerability}
\end{equation}

\textbf{Statistical CSI as Security Advantage:}
The uncertainty in instantaneous CSI provides inherent security benefits:
\begin{itemize}
    \item \textbf{Channel Uncertainty:} Random NLoS components create natural encryption
    \item \textbf{Reduced Eavesdropping Efficiency:} Eve cannot optimize reception without perfect CSI
    \item \textbf{Predictable Vulnerability:} System designers can quantify and minimize information leakage
\end{itemize}

\textbf{Design Implications:}
\begin{itemize}
    \item \textbf{LoS Mitigation:} Reduce LoS component strength through precoding design
    \item \textbf{NLoS Enhancement:} Increase scattering to enhance channel uncertainty
    \item \textbf{Statistical Optimization:} Design $\mathbf{F}$ to minimize ergodic mutual information:
    \begin{equation}
    \min_{\mathbf{F}} I_C^{\text{ergodic}} \quad \text{subject to} \quad \text{QoS constraints}
    \label{eq:security_optimization}
    \end{equation}
\end{itemize}

\textbf{Fundamental Security-Performance Trade-off:}
There exists a fundamental trade-off between communication performance and security:
\begin{equation}
\max_{\mathbf{F}} \left[I_C^{\text{legitimate}} - \alpha \cdot I_C^{\text{ergodic}}\right]
\label{eq:security_performance_tradeoff}
\end{equation}
where $\alpha > 0$ is the security weight parameter.

\subsection{Joint Parameter Estimation Problem}

The sophisticated eavesdropper attempts to jointly estimate parameters from both sensing and communication domains. We consider two analysis frameworks:

\subsubsection{Single Time Slot Analysis}

For a single time slot $l$, the eavesdropper estimates the parameter vector:
\begin{equation}
\boldsymbol{\phi} = [\theta_E, \theta_L, \text{Re}(s_1(l)), \text{Im}(s_1(l)), \ldots, \text{Re}(s_K(l)), \text{Im}(s_K(l))]^T
\label{eq:single_slot_params}
\end{equation}

This $(2 + 2K)$-dimensional parameter vector includes:
\begin{itemize}
    \item $\theta_E$: Eavesdropper's angle (sensing parameter)
    \item $\theta_L$: Target angle (sensing parameter)
    \item $\{\text{Re}(s_k(l)), \text{Im}(s_k(l))\}_{k=1}^K$: Communication symbols
\end{itemize}

\subsubsection{Multi-Time Slot Analysis}

For a frame of $L$ time slots, the parameter vector becomes:
\begin{equation}
\boldsymbol{\phi}_L = [\theta_E, \theta_L, \text{vec}(\text{Re}(\mathbf{S}))^T, \text{vec}(\text{Im}(\mathbf{S}))^T]^T
\label{eq:multi_slot_params}
\end{equation}

where $\mathbf{S} = [\mathbf{s}(1), \mathbf{s}(2), \ldots, \mathbf{s}(L)] \in \mathbb{C}^{K \times L}$ is the symbol matrix, and $\text{vec}(\cdot)$ denotes vectorization. This results in a $(2 + 2KL)$-dimensional parameter vector.

\subsection{Cramér-Rao Bound Analysis}

\subsubsection{Single Time Slot CRB}

For the single time slot case, the received signal at the eavesdropper is:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{H}_{\text{eff}}(\boldsymbol{\theta})\mathbf{F}\mathbf{s}(l) + \mathbf{n}_{\text{eve}}(l)
\label{eq:eve_observation}
\end{equation}

The mean vector is:
\begin{equation}
\boldsymbol{\mu}(\boldsymbol{\phi}) = \mathbf{H}_{\text{eff}}(\theta_E, \theta_L)\mathbf{F}\mathbf{s}(l)
\label{eq:mean_vector}
\end{equation}

The Fisher Information Matrix (FIM) for the joint parameter vector is:
\begin{equation}
[\mathbf{J}]_{i,j} = \frac{2}{\sigma_n^2} \text{Re}\left\{\left(\frac{\partial \boldsymbol{\mu}}{\partial \phi_i}\right)^H \frac{\partial \boldsymbol{\mu}}{\partial \phi_j}\right\}
\label{eq:fim_general}
\end{equation}

\subsubsection{Partial Derivatives}

The partial derivatives of the mean vector are:

\textbf{With respect to sensing parameters:}
\begin{align}
\frac{\partial \boldsymbol{\mu}}{\partial \theta_E} &= \frac{\partial \mathbf{H}_{\text{eff}}}{\partial \theta_E}\mathbf{F}\mathbf{s}(l) = \beta(l)\frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E}\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l) \label{eq:partial_theta_e} \\
\frac{\partial \boldsymbol{\mu}}{\partial \theta_L} &= \frac{\partial \mathbf{H}_{\text{eff}}}{\partial \theta_L}\mathbf{F}\mathbf{s}(l) = \beta(l)\mathbf{b}(\theta_E)\frac{\partial \mathbf{a}_t^H(\theta_L)}{\partial \theta_L}\mathbf{F}\mathbf{s}(l) \label{eq:partial_theta_l}
\end{align}

\textbf{With respect to communication parameters:}
\begin{align}
\frac{\partial \boldsymbol{\mu}}{\partial \text{Re}(s_k(l))} &= \mathbf{H}_{\text{eff}}\mathbf{f}_k \label{eq:partial_re_sk} \\
\frac{\partial \boldsymbol{\mu}}{\partial \text{Im}(s_k(l))} &= j\mathbf{H}_{\text{eff}}\mathbf{f}_k \label{eq:partial_im_sk}
\end{align}

where $\mathbf{f}_k$ is the $k$-th column of the precoding matrix $\mathbf{F}$.

\subsubsection{FIM Block Structure}

The FIM has a block structure:
\begin{equation}
\mathbf{J} = \begin{bmatrix}
\mathbf{J}_{SS} & \mathbf{J}_{SC} \\
\mathbf{J}_{CS} & \mathbf{J}_{CC}
\end{bmatrix}
\label{eq:fim_blocks}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{J}_{SS} \in \mathbb{R}^{2 \times 2}$: Sensing-sensing block for $[\theta_E, \theta_L]$
    \item $\mathbf{J}_{CC} \in \mathbb{R}^{2K \times 2K}$: Communication-communication block
    \item $\mathbf{J}_{SC} = \mathbf{J}_{CS}^T$: Sensing-communication coupling block
\end{itemize}

\textbf{Sensing-Sensing Block:}
\begin{align}
[\mathbf{J}_{SS}]_{1,1} &= \frac{2|\beta(l)|^2}{\sigma_n^2} \left\|\frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E}\right\|^2 |\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l)|^2 \\
[\mathbf{J}_{SS}]_{2,2} &= \frac{2|\beta(l)|^2}{\sigma_n^2} \|\mathbf{b}(\theta_E)\|^2 \left|\frac{\partial \mathbf{a}_t^H(\theta_L)}{\partial \theta_L}\mathbf{F}\mathbf{s}(l)\right|^2 \\
[\mathbf{J}_{SS}]_{1,2} &= \frac{2|\beta(l)|^2}{\sigma_n^2} \text{Re}\left\{\left(\frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E}\right)^H \mathbf{b}(\theta_E) \cdot \overline{\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l)} \cdot \frac{\partial \mathbf{a}_t^H(\theta_L)}{\partial \theta_L}\mathbf{F}\mathbf{s}(l)\right\}
\end{align}

\textbf{Communication-Communication Block:}
\begin{equation}
[\mathbf{J}_{CC}]_{2i-1,2j-1} = [\mathbf{J}_{CC}]_{2i,2j} = \frac{2}{\sigma_n^2} \text{Re}\{\mathbf{f}_i^H\mathbf{H}_{\text{eff}}^H\mathbf{H}_{\text{eff}}\mathbf{f}_j\}
\end{equation}

\textbf{Sensing-Communication Coupling:}
\begin{align}
[\mathbf{J}_{SC}]_{1,2k-1} &= \frac{2|\beta(l)|^2}{\sigma_n^2} \text{Re}\left\{\left(\frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E}\right)^H \mathbf{b}(\theta_E) \cdot \overline{\mathbf{a}_t^H(\theta_L)\mathbf{F}\mathbf{s}(l)} \cdot \mathbf{a}_t^H(\theta_L)\mathbf{f}_k\right\} \\
[\mathbf{J}_{SC}]_{2,2k-1} &= \frac{2|\beta(l)|^2}{\sigma_n^2} \text{Re}\left\{\|\mathbf{b}(\theta_E)\|^2 \cdot \overline{\frac{\partial \mathbf{a}_t^H(\theta_L)}{\partial \theta_L}\mathbf{F}\mathbf{s}(l)} \cdot \mathbf{a}_t^H(\theta_L)\mathbf{f}_k\right\}
\end{align}

\subsubsection{Multi-Time Slot CRB Analysis}

For the multi-time slot case with $L$ time slots, the observation vector becomes:
\begin{equation}
\mathbf{y}_{\text{total}} = \begin{bmatrix} \mathbf{y}_{\text{eve}}(1) \\ \mathbf{y}_{\text{eve}}(2) \\ \vdots \\ \mathbf{y}_{\text{eve}}(L) \end{bmatrix} \in \mathbb{C}^{LN_E \times 1}
\label{eq:multi_slot_observation}
\end{equation}

The mean vector is:
\begin{equation}
\boldsymbol{\mu}_L = \begin{bmatrix} \mathbf{H}_{\text{eff}}\mathbf{F}\mathbf{s}(1) \\ \mathbf{H}_{\text{eff}}\mathbf{F}\mathbf{s}(2) \\ \vdots \\ \mathbf{H}_{\text{eff}}\mathbf{F}\mathbf{s}(L) \end{bmatrix}
\label{eq:multi_slot_mean}
\end{equation}

The FIM for the multi-slot case has the structure:
\begin{equation}
\mathbf{J}_L = \begin{bmatrix}
\mathbf{J}_{SS}^{(L)} & \mathbf{J}_{SC}^{(L)} \\
\mathbf{J}_{CS}^{(L)} & \mathbf{J}_{CC}^{(L)}
\end{bmatrix}
\label{eq:multi_slot_fim}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{J}_{SS}^{(L)} \in \mathbb{R}^{2 \times 2}$: Accumulated sensing information over $L$ slots
    \item $\mathbf{J}_{CC}^{(L)} \in \mathbb{R}^{2KL \times 2KL}$: Communication symbols across all time slots
    \item $\mathbf{J}_{SC}^{(L)} \in \mathbb{R}^{2 \times 2KL}$: Sensing-communication coupling
\end{itemize}

\textbf{Accumulated Sensing Information:}
\begin{equation}
\mathbf{J}_{SS}^{(L)} = \sum_{l=1}^{L} \mathbf{J}_{SS}^{(l)}
\label{eq:accumulated_sensing_fim}
\end{equation}

This shows that sensing parameter estimation improves with the number of time slots, as more observations provide better angle estimation.

\textbf{Communication Block Structure:}
The communication block has a specific structure due to the independence of symbols across time slots:
\begin{equation}
\mathbf{J}_{CC}^{(L)} = \text{blkdiag}(\mathbf{J}_{CC}^{(1)}, \mathbf{J}_{CC}^{(2)}, \ldots, \mathbf{J}_{CC}^{(L)})
\label{eq:comm_block_structure}
\end{equation}

\subsection{CRB Computation and Analysis}

The CRB for the joint parameter vector is:
\begin{equation}
\text{CRB}(\boldsymbol{\phi}) = \mathbf{J}^{-1}
\label{eq:crb_joint}
\end{equation}

\subsubsection{Individual Parameter CRBs}

For sensing parameters:
\begin{align}
\text{CRB}(\theta_E) &= [\mathbf{J}^{-1}]_{1,1} \label{eq:crb_theta_e} \\
\text{CRB}(\theta_L) &= [\mathbf{J}^{-1}]_{2,2} \label{eq:crb_theta_l}
\end{align}

For communication parameters:
\begin{align}
\text{CRB}(\text{Re}(s_k(l))) &= [\mathbf{J}^{-1}]_{2+2k-1,2+2k-1} \label{eq:crb_re_sk} \\
\text{CRB}(\text{Im}(s_k(l))) &= [\mathbf{J}^{-1}]_{2+2k,2+2k} \label{eq:crb_im_sk}
\end{align}

\subsubsection{Coupling Effects}

The off-diagonal terms in $\mathbf{J}^{-1}$ reveal the coupling between sensing and communication parameter estimation. The coupling strength depends on:

\begin{itemize}
    \item \textbf{Signal Power:} Higher transmitted power increases coupling
    \item \textbf{Reflection Coefficient:} Stronger target reflection enhances sensing-communication correlation
    \item \textbf{Array Geometry:} Antenna configuration affects spatial correlation
    \item \textbf{Precoding Design:} The structure of $\mathbf{F}$ influences information leakage patterns
\end{itemize}

\subsubsection{Asymptotic Analysis}

For large $L$, the sensing parameter CRBs scale as:
\begin{equation}
\text{CRB}(\theta_E), \text{CRB}(\theta_L) \propto \frac{1}{L}
\label{eq:sensing_scaling}
\end{equation}

This indicates that sensing parameter estimation accuracy improves linearly with the observation time, giving the eavesdropper a significant advantage in long-term scenarios.

\subsection{Information Leakage Metrics}

Based on the CRB analysis, we define information leakage metrics:

\subsubsection{Sensing Information Leakage}
\begin{equation}
\mathcal{L}_S = \frac{1}{\text{CRB}(\theta_E)} + \frac{1}{\text{CRB}(\theta_L)}
\label{eq:sensing_leakage}
\end{equation}

\subsubsection{Communication Information Leakage}
\begin{equation}
\mathcal{L}_C = \sum_{k=1}^{K} \frac{1}{\text{CRB}(\text{Re}(s_k)) + \text{CRB}(\text{Im}(s_k))}
\label{eq:comm_leakage}
\end{equation}

\subsubsection{Joint Vulnerability Index}
\begin{equation}
\mathcal{V}_{\text{joint}} = w_S \mathcal{L}_S + w_C \mathcal{L}_C + w_{\text{coup}} \mathcal{L}_{\text{coup}}
\label{eq:joint_vulnerability}
\end{equation}

where $w_S + w_C + w_{\text{coup}} = 1$ and $\mathcal{L}_{\text{coup}}$ quantifies the coupling effects through the off-diagonal terms of $\mathbf{J}^{-1}$.

\bibliographystyle{IEEEtran}
\bibliography{references}

\end{document}
