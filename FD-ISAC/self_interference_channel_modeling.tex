\documentclass[journal]{IEEEtran}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{algorithmic}

\newcommand{\bm}[1]{\boldsymbol{#1}}

\title{Simplified Self-Interference Channel Modeling for Full-Duplex ISAC Systems}
\author{Self-Interference Channel $\mathbf{H}_{SI}$ Modeling}

\begin{document}
\maketitle

\begin{abstract}
This document presents simplified yet practical models for the self-interference channel $\mathbf{H}_{SI}$ in full-duplex ISAC systems. We provide multiple modeling approaches ranging from basic deterministic models to more sophisticated stochastic representations, enabling flexible implementation based on system requirements and computational constraints.
\end{abstract}

\section{Introduction}

In full-duplex ISAC systems, the self-interference channel $\mathbf{H}_{SI}$ represents the coupling between the transmit and receive antennas at the same node. This channel is typically much stronger than the desired signal channels and requires careful modeling for accurate system analysis and effective interference cancellation.

\section{Simplified Self-Interference Channel Models}

\subsection{Model 1: Deterministic Line-of-Sight (LoS) Model}

\subsubsection{Basic LoS Model}
The simplest model assumes pure line-of-sight propagation between transmit and receive antennas:

\begin{equation}
\mathbf{H}_{SI} = \alpha_{SI} \mathbf{a}_r(\theta_{SI}) \mathbf{a}_t^H(\theta_{SI})
\label{eq:los_model}
\end{equation}

where:
\begin{itemize}
    \item $\alpha_{SI}$ is the complex path gain
    \item $\mathbf{a}_r(\theta_{SI})$ is the receive array response vector
    \item $\mathbf{a}_t(\theta_{SI})$ is the transmit array response vector  
    \item $\theta_{SI}$ is the self-interference angle (typically 0° for co-located arrays)
\end{itemize}

\subsubsection{Path Gain Modeling}
The complex path gain can be modeled as:
\begin{equation}
\alpha_{SI} = \sqrt{\frac{P_{SI}}{d_{SI}^{\eta}}} e^{j\phi_{SI}}
\label{eq:path_gain}
\end{equation}

where:
\begin{itemize}
    \item $P_{SI}$ is the reference power at unit distance
    \item $d_{SI}$ is the distance between transmit and receive arrays
    \item $\eta$ is the path loss exponent (typically 2-4)
    \item $\phi_{SI}$ is the phase shift
\end{itemize}

\subsubsection{Practical Parameters}
For typical full-duplex systems:
\begin{align}
d_{SI} &= 0.1 \text{ to } 1 \text{ meter} \\
\eta &= 2 \text{ (free space)} \\
P_{SI} &= 60 \text{ to } 100 \text{ dB above noise floor}
\end{align}

\subsection{Model 2: Multi-Path Cluster Model}

\subsubsection{Finite Cluster Representation}
A more realistic model includes multiple propagation paths:

\begin{equation}
\mathbf{H}_{SI} = \sum_{p=1}^{P} \alpha_{SI,p} \mathbf{a}_r(\theta_{r,p}) \mathbf{a}_t^H(\theta_{t,p})
\label{eq:multipath_model}
\end{equation}

where:
\begin{itemize}
    \item $P$ is the number of significant paths (typically 3-10)
    \item $\alpha_{SI,p}$ is the complex gain of path $p$
    \item $\theta_{r,p}, \theta_{t,p}$ are the receive and transmit angles for path $p$
\end{itemize}

\subsubsection{Path Parameters}
\textbf{Direct Path (p=1):}
\begin{align}
\alpha_{SI,1} &= \sqrt{0.7 \cdot P_{SI}} e^{j\phi_1} \quad \text{(dominant)} \\
\theta_{r,1} = \theta_{t,1} &= 0° \quad \text{(boresight)}
\end{align}

\textbf{Reflection Paths (p=2,...,P):}
\begin{align}
\alpha_{SI,p} &= \sqrt{\frac{0.3 \cdot P_{SI}}{P-1}} e^{j\phi_p} \quad \text{(weaker)} \\
\theta_{r,p}, \theta_{t,p} &\sim \mathcal{U}[-60°, 60°] \quad \text{(random)}
\end{align}

\subsection{Model 3: Simplified Stochastic Model}

\subsubsection{Rician Fading Model}
For time-varying scenarios, use Rician fading:

\begin{equation}
\mathbf{H}_{SI} = \sqrt{\frac{K}{K+1}} \mathbf{H}_{SI}^{LoS} + \sqrt{\frac{1}{K+1}} \mathbf{H}_{SI}^{NLOS}
\label{eq:rician_model}
\end{equation}

where:
\begin{itemize}
    \item $K$ is the Rician K-factor (typically 10-20 dB for SI)
    \item $\mathbf{H}_{SI}^{LoS}$ is the deterministic LoS component
    \item $\mathbf{H}_{SI}^{NLOS}$ is the random scattering component
\end{itemize}

\subsubsection{Random Component}
The NLOS component follows:
\begin{equation}
[\mathbf{H}_{SI}^{NLOS}]_{i,j} \sim \mathcal{CN}(0, \sigma_{SI}^2)
\label{eq:nlos_component}
\end{equation}

with $\sigma_{SI}^2 = P_{SI} / (K+1)$.

\subsection{Model 4: Frequency-Selective Model}

\subsubsection{Tapped Delay Line Model}
For wideband systems, include frequency selectivity:

\begin{equation}
\mathbf{H}_{SI}(f) = \sum_{l=0}^{L-1} \mathbf{h}_{SI,l} e^{-j2\pi f \tau_l}
\label{eq:frequency_selective}
\end{equation}

where:
\begin{itemize}
    \item $L$ is the number of taps (typically 3-5)
    \item $\mathbf{h}_{SI,l}$ is the channel matrix for tap $l$
    \item $\tau_l$ is the delay of tap $l$
\end{itemize}

\subsubsection{Simplified Tap Structure}
\textbf{Tap 0 (Direct):}
\begin{align}
\mathbf{h}_{SI,0} &= \alpha_{SI,0} \mathbf{a}_r(0°) \mathbf{a}_t^H(0°) \\
\tau_0 &= 0
\end{align}

\textbf{Tap l (Reflections):}
\begin{align}
\mathbf{h}_{SI,l} &= \alpha_{SI,l} \mathbf{a}_r(\theta_{r,l}) \mathbf{a}_t^H(\theta_{t,l}) \\
\tau_l &= l \cdot \Delta\tau \quad \text{where } \Delta\tau = 10-50 \text{ ns}
\end{align}

\section{Practical Implementation Guidelines}

\subsection{Model Selection Criteria}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Application} & \textbf{Model} & \textbf{Complexity} & \textbf{Accuracy} \\
\hline
Initial Analysis & LoS & Low & Basic \\
System Design & Multi-path & Medium & Good \\
Performance Evaluation & Rician & Medium & High \\
Wideband Systems & Frequency-Selective & High & Very High \\
\hline
\end{tabular}
\caption{Model selection guidelines}
\end{table}

\subsection{Parameter Estimation}

\subsubsection{Measurement-Based Approach}
For practical systems, estimate $\mathbf{H}_{SI}$ using:

\begin{enumerate}
    \item \textbf{Pilot-based estimation}: Transmit known pilots and measure received SI
    \item \textbf{Least squares estimation}: $\hat{\mathbf{H}}_{SI} = \mathbf{Y}_{SI} \mathbf{X}^{\dagger}$
    \item \textbf{Averaging}: $\mathbf{H}_{SI} = \frac{1}{N} \sum_{n=1}^{N} \hat{\mathbf{H}}_{SI}^{(n)}$
\end{enumerate}

\subsubsection{Model-Based Approach}
Use the simplified models with typical parameters:

\begin{align}
P_{SI} &= 80 \text{ dB} \quad \text{(strong interference)} \\
K &= 15 \text{ dB} \quad \text{(high K-factor)} \\
P &= 5 \quad \text{(5 dominant paths)} \\
L &= 3 \quad \text{(3 delay taps)}
\end{align}

\subsection{Computational Complexity}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|}
\hline
\textbf{Model} & \textbf{Storage} & \textbf{Computation} \\
\hline
LoS & $\mathcal{O}(N_r + N_t)$ & $\mathcal{O}(N_r N_t)$ \\
Multi-path & $\mathcal{O}(P(N_r + N_t))$ & $\mathcal{O}(P N_r N_t)$ \\
Rician & $\mathcal{O}(N_r N_t)$ & $\mathcal{O}(N_r N_t)$ \\
Frequency-Selective & $\mathcal{O}(L N_r N_t)$ & $\mathcal{O}(L N_r N_t)$ \\
\hline
\end{tabular}
\caption{Computational complexity comparison}
\end{table}

\section{Integration with ISAC Signal Model}

\subsection{Complete System Model}

Including self-interference, the complete received signal becomes:

\begin{equation}
\mathbf{y}(t) = \mathbf{H}_{SI} \mathbf{x}(t) + \mathbf{H}_{\text{desired}} \mathbf{s}(t) + \mathbf{z}(t)
\label{eq:complete_model}
\end{equation}

\subsection{Self-Interference Cancellation}

\subsubsection{Digital Cancellation}
Estimate and subtract the SI component:

\begin{equation}
\mathbf{y}_{\text{clean}}(t) = \mathbf{y}(t) - \hat{\mathbf{H}}_{SI} \mathbf{x}(t)
\label{eq:digital_cancellation}
\end{equation}

\subsubsection{Residual SI Model}
After cancellation, residual SI can be modeled as:

\begin{equation}
\mathbf{y}_{\text{residual}}(t) = \Delta\mathbf{H}_{SI} \mathbf{x}(t) + \mathbf{z}_{\text{effective}}(t)
\label{eq:residual_si}
\end{equation}

where $\Delta\mathbf{H}_{SI} = \mathbf{H}_{SI} - \hat{\mathbf{H}}_{SI}$ is the estimation error.

\section{Recommended Simplified Model}

\subsection{Practical Choice: Enhanced LoS Model}

For most ISAC applications, we recommend:

\begin{equation}
\mathbf{H}_{SI} = \alpha_{SI} \mathbf{a}_r(0°) \mathbf{a}_t^H(0°) + \mathbf{H}_{\text{scatter}}
\label{eq:recommended_model}
\end{equation}

where:
\begin{align}
\alpha_{SI} &= \sqrt{P_{SI}} e^{j\phi_{SI}} \quad \text{(deterministic)} \\
\mathbf{H}_{\text{scatter}} &\sim \mathcal{CN}(0, 0.1 \cdot P_{SI} \mathbf{I}) \quad \text{(random)}
\end{align}

\subsection{Implementation Parameters}

\begin{align}
P_{SI} &= 10^{8} \quad \text{(80 dB above noise)} \\
\phi_{SI} &= 0 \quad \text{(reference phase)} \\
\text{Scatter ratio} &= 0.1 \quad \text{(10\% of main power)}
\end{align}

\subsection{MATLAB Implementation}

\begin{verbatim}
function H_SI = generate_SI_channel(N_r, N_t, P_SI, scatter_ratio)
    % Main LoS component
    a_r = ones(N_r, 1);  % Uniform array response
    a_t = ones(N_t, 1);  % Uniform array response
    alpha_SI = sqrt(P_SI);
    H_SI_los = alpha_SI * a_r * a_t';
    
    % Scattering component
    H_scatter = sqrt(scatter_ratio * P_SI) * ...
                (randn(N_r, N_t) + 1j*randn(N_r, N_t))/sqrt(2);
    
    % Combined channel
    H_SI = H_SI_los + H_scatter;
end
\end{verbatim}

\section{Conclusion}

The recommended simplified model balances accuracy and computational efficiency:

\begin{itemize}
    \item \textbf{Dominant LoS component}: Captures the main SI path
    \item \textbf{Random scattering}: Accounts for multipath effects
    \item \textbf{Low complexity}: Suitable for real-time implementation
    \item \textbf{Flexible parameters}: Adaptable to different scenarios
\end{itemize}

This model provides sufficient accuracy for ISAC system analysis while maintaining computational tractability for optimization and real-time applications.

\end{document}
