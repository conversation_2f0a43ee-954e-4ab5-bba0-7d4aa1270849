\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{booktabs}
\usepackage{array}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\argmin}{\text{argmin}}
\newcommand{\argmax}{\text{argmax}}

\title{Adversarial Privacy-Secrecy Equilibrium (APSE) for Monostatic ISAC Systems}
\author{Game-Theoretic Security Analysis Framework}
\date{\today}

\begin{document}
\maketitle

\begin{abstract}
This document presents a comprehensive analysis of the Adversarial Privacy-Secrecy Equilibrium (APSE) metric for monostatic integrated sensing and communication (ISAC) systems. The APSE metric employs game theory to model the adversarial interaction between the ISAC base station (defender) and potential eavesdroppers (adversaries), providing a robust framework for security evaluation under worst-case scenarios. We derive the metric based on our established monostatic ISAC signal model and provide practical computation methods and optimization strategies.
\end{abstract}

\section{Introduction}

The Adversarial Privacy-Secrecy Equilibrium (APSE) represents a paradigm shift in ISAC security evaluation by:
\begin{itemize}
    \item Modeling security as a two-player zero-sum game between defender and adversary
    \item Capturing adaptive adversarial strategies beyond passive eavesdropping
    \item Providing worst-case security guarantees through Nash equilibrium analysis
    \item Incorporating cross-domain information leakage through mutual information penalties
\end{itemize}

This game-theoretic approach addresses the limitation that traditional security metrics assume fixed adversarial capabilities, whereas real attackers adapt their strategies based on system design.

\section{APSE Metric Definition}

\subsection{Game-Theoretic Framework}

The APSE formulation considers a strategic game $\mathcal{G} = (\mathcal{P}, \mathcal{S}, \mathcal{U})$ where:
\begin{itemize}
    \item $\mathcal{P} = \{\text{Defender}, \text{Adversary}\}$ are the players
    \item $\mathcal{S} = \mathcal{D} \times \mathcal{A}$ is the strategy space
    \item $\mathcal{U}$ defines the utility functions
\end{itemize}

\subsubsection{Strategy Spaces}

\textbf{Defender (ISAC BS) Strategies} $\mathcal{D}$:
\begin{itemize}
    \item Signal covariance matrix $\bm{Q}_x \in \mathbb{C}^{N_{BS} \times N_{BS}}$
    \item Precoding matrices $\bm{W}_k \in \mathbb{C}^{N_{BS} \times 1}$ for users $k$
    \item Artificial noise covariance $\bm{Q}_n \in \mathbb{C}^{N_{BS} \times N_{BS}}$
    \item Beamforming parameters for sensing and communication
\end{itemize}

\textbf{Adversary (Eavesdropper) Strategies} $\mathcal{A}$:
\begin{itemize}
    \item Attack focus: communication-only, sensing-only, or joint exploitation
    \item Signal processing techniques: matched filtering, interference cancellation
    \item Resource allocation: antenna deployment, power allocation
    \item Cooperation strategies in multi-eavesdropper scenarios
\end{itemize}

\subsection{Utility Functions}

Based on our monostatic ISAC signal model:
\begin{equation}
\bm{y}_{\text{eve}}(l) = \bm{H}_{\text{eff}}(\theta_L)\bm{x}(l) + \bm{z}_{\text{tar}}(l)
\end{equation}
where $\bm{H}_{\text{eff}}(\theta_L) = \bm{G}_E(\theta_L) + \bm{H}_{CE}$.

\subsubsection{Communication Security Payoff}

The communication security utility for strategy pair $(\bm{W}, a)$ is:
\begin{equation}
U_c(\bm{W}, a) = R_s(\bm{W}, a)
\end{equation}

where the secrecy rate is:
\begin{equation}
R_s(\bm{W}, a) = \max\left(0, \log(1 + \text{SINR}_{\text{user}}) - \log(1 + \text{SINR}_e(a))\right)
\end{equation}

For our monostatic model, the eavesdropper SINR under attack mode $a$ is:
\begin{equation}
\text{SINR}_e(a) = \frac{\|\bm{h}_e^H(\theta_L, a)\bm{w}_k\|^2}{\sum_{j \neq k}\|\bm{h}_e^H(\theta_L, a)\bm{w}_j\|^2 + \sigma_e^2}
\end{equation}

where $\bm{h}_e(\theta_L, a)$ represents the effective eavesdropping channel under attack strategy $a$.

\subsubsection{Sensing Privacy Payoff}

The sensing privacy utility is defined as:
\begin{equation}
U_p(\bm{W}, a) = \frac{1}{\text{BCRB}(\bm{W}, a)}
\end{equation}

For angle estimation in our monostatic system, the Bayesian Cramér-Rao Bound is:
\begin{equation}
\text{BCRB}(\theta_L) = \left[\bm{J}_{\text{prior}}(\theta_L) + \bm{J}_{\text{data}}(\theta_L)\right]^{-1}
\end{equation}

where the data Fisher Information Matrix is:
\begin{equation}
\bm{J}_{\text{data}}(\theta_L) = \frac{2L}{\sigma_{\text{tar}}^2}\text{Re}\left\{\frac{\partial \bm{H}_{\text{eff}}^H}{\partial \theta_L}\bm{Q}_x\frac{\partial \bm{H}_{\text{eff}}}{\partial \theta_L}\right\}
\end{equation}

\subsubsection{Joint Payoff Function}

The adversary's total utility combines both domains with cross-correlation penalty:
\begin{align}
U(\bm{W}, a) &= \alpha U_c(\bm{W}, a) + (1-\alpha) U_p(\bm{W}, a) \\
&\quad + \beta \cdot I(\bm{s}; \theta_L|\bm{y}_{\text{eve}}, a)
\end{align}

where:
\begin{itemize}
    \item $\alpha \in [0,1]$ is adaptively chosen by the adversary
    \item $\beta > 0$ penalizes cross-domain correlations
    \item $I(\bm{s}; \theta_L|\bm{y}_{\text{eve}}, a)$ is the mutual information capturing information leakage
\end{itemize}

\subsection{APSE Formulation}

The APSE is defined as the value of the game at Nash equilibrium:
\begin{equation}
\text{APSE} = \min_{\bm{W} \in \mathcal{D}} \max_{a \in \mathcal{A}} U(\bm{W}, a) - \gamma \cdot \text{Regret}(\bm{W}^*)
\end{equation}

where:
\begin{itemize}
    \item The minimax term represents the equilibrium adversary gain
    \item $\bm{W}^*$ is the equilibrium strategy for the defender
    \item $\text{Regret}(\bm{W}^*) = \max_a U(\bm{W}^*, a) - \min_{\bm{W}} \max_a U(\bm{W}, a)$
    \item $\gamma > 0$ is a scaling factor rewarding low-regret designs
\end{itemize}

\section{Derivation for Monostatic ISAC System}

\subsection{Signal Model Integration}

For our monostatic ISAC system, the key components are:

\subsubsection{Effective Channel Matrix}
\begin{equation}
\bm{H}_{\text{eff}}(\theta_L, a) = \bm{G}_E(\theta_L, a) + \bm{H}_{CE}(a)
\end{equation}

where the attack strategy $a$ affects both sensing and communication channels through:
\begin{itemize}
    \item Antenna positioning and orientation
    \item Signal processing algorithms
    \item Interference generation capabilities
\end{itemize}

\subsubsection{Mutual Information Computation}

The cross-domain mutual information for strategy pair $(\bm{W}, a)$ is:
\begin{align}
I(\bm{s}; \theta_L|\bm{y}_{\text{eve}}, a) &= H(\bm{s}|\bm{y}_{\text{eve}}, a) + H(\theta_L|\bm{y}_{\text{eve}}, a) \\
&\quad - H(\bm{s}, \theta_L|\bm{y}_{\text{eve}}, a)
\end{align}

For Gaussian approximations:
\begin{equation}
I(\bm{s}; \theta_L|\bm{y}_{\text{eve}}, a) \approx \frac{1}{2}\log\frac{\det(\bm{\Sigma}_s(a))\det(\bm{\Sigma}_\theta(a))}{\det(\bm{\Sigma}_{s,\theta}(a))}
\end{equation}

\subsection{Nash Equilibrium Analysis}

\subsubsection{Defender's Best Response}

For fixed adversary strategy $a$, the defender solves:
\begin{align}
\bm{W}^*(a) = \argmin_{\bm{W} \in \mathcal{D}} \quad & U(\bm{W}, a) \\
\text{s.t.} \quad & \tr(\bm{Q}_x) \leq P_{\max} \\
& \text{SINR}_k \geq \Gamma_k, \quad \forall k \\
& \text{CRB}_{\text{BS}}(\theta_L) \leq \gamma_{\text{sense}}
\end{align}

\subsubsection{Adversary's Best Response}

For fixed defender strategy $\bm{W}$, the adversary chooses:
\begin{equation}
a^*(\bm{W}) = \argmax_{a \in \mathcal{A}} U(\bm{W}, a)
\end{equation}

This involves optimizing the attack focus parameter $\alpha$ and resource allocation.

\section{Computational Methods}

\subsection{Strategy Discretization}

\subsubsection{Defender Strategy Space}

Discretize the continuous strategy space:
\begin{itemize}
    \item \textbf{Power allocation}: Grid search over $\{P_1, P_2, \ldots, P_M\}$
    \item \textbf{Beamforming angles}: Uniform sampling in $[0, 2\pi)$
    \item \textbf{Artificial noise}: Eigenvalue decomposition with quantized levels
\end{itemize}

\subsubsection{Adversary Strategy Space}

\begin{itemize}
    \item \textbf{Attack focus}: $\alpha \in \{0, 0.1, 0.2, \ldots, 1.0\}$
    \item \textbf{Processing techniques}: Finite set of algorithms
    \item \textbf{Resource allocation}: Discretized power and antenna configurations
\end{itemize}

\subsection{Payoff Evaluation Algorithm}

\begin{algorithm}
\caption{APSE Payoff Evaluation}
\begin{algorithmic}[1]
\REQUIRE Defender strategy $\bm{W}$, Adversary strategy $a$
\REQUIRE System parameters: $\bm{H}_{CE}$, $\bm{G}_E(\theta_L)$, $\sigma_{\text{tar}}^2$
\ENSURE Utility value $U(\bm{W}, a)$

\STATE Compute effective channel: $\bm{H}_{\text{eff}}(\theta_L, a)$

\STATE Calculate communication SINR:
\STATE $\text{SINR}_e(a) = \frac{\|\bm{h}_e^H(\theta_L, a)\bm{w}_k\|^2}{\sum_{j \neq k}\|\bm{h}_e^H(\theta_L, a)\bm{w}_j\|^2 + \sigma_e^2}$

\STATE Compute secrecy rate:
\STATE $U_c = \max(0, \log(1 + \text{SINR}_{\text{user}}) - \log(1 + \text{SINR}_e(a)))$

\STATE Calculate sensing BCRB:
\STATE $\bm{J}_{\text{data}} = \frac{2L}{\sigma_{\text{tar}}^2}\text{Re}\left\{\frac{\partial \bm{H}_{\text{eff}}^H}{\partial \theta_L}\bm{Q}_x\frac{\partial \bm{H}_{\text{eff}}}{\partial \theta_L}\right\}$
\STATE $U_p = \frac{1}{\text{BCRB}}$

\STATE Estimate mutual information:
\STATE $I(\bm{s}; \theta_L|\bm{y}_{\text{eve}}, a)$ via Monte Carlo or Gaussian approximation

\STATE Compute total utility:
\STATE $U(\bm{W}, a) = \alpha U_c + (1-\alpha) U_p + \beta \cdot I(\bm{s}; \theta_L|\bm{y}_{\text{eve}}, a)$

\RETURN $U(\bm{W}, a)$
\end{algorithmic}
\end{algorithm}

\subsection{Nash Equilibrium Computation}

\subsubsection{Iterative Best Response Algorithm}

\begin{algorithm}
\caption{Nash Equilibrium for APSE}
\begin{algorithmic}[1]
\REQUIRE Strategy spaces $\mathcal{D}$, $\mathcal{A}$
\REQUIRE Convergence tolerance $\epsilon$
\ENSURE Nash equilibrium $(\bm{W}^*, a^*)$

\STATE Initialize $\bm{W}^{(0)} \in \mathcal{D}$, $a^{(0)} \in \mathcal{A}$
\STATE Set $t = 0$

\REPEAT
    \STATE \textbf{Defender Update:}
    \STATE $\bm{W}^{(t+1)} = \argmin_{\bm{W} \in \mathcal{D}} U(\bm{W}, a^{(t)})$

    \STATE \textbf{Adversary Update:}
    \STATE $a^{(t+1)} = \argmax_{a \in \mathcal{A}} U(\bm{W}^{(t+1)}, a)$

    \STATE $t = t + 1$
\UNTIL $\|\bm{W}^{(t)} - \bm{W}^{(t-1)}\| < \epsilon$ and $|a^{(t)} - a^{(t-1)}| < \epsilon$

\STATE Compute regret: $\text{Regret}(\bm{W}^{(t)}) = \max_a U(\bm{W}^{(t)}, a) - \min_{\bm{W}} \max_a U(\bm{W}, a)$

\STATE $\text{APSE} = U(\bm{W}^{(t)}, a^{(t)}) - \gamma \cdot \text{Regret}(\bm{W}^{(t)})$

\RETURN $(\bm{W}^{(t)}, a^{(t)})$, APSE
\end{algorithmic}
\end{algorithm}

\subsubsection{Linear Programming Formulation}

For discrete strategy spaces, the minimax problem can be formulated as:
\begin{align}
\min_{\bm{p}, v} \quad & v - \gamma \cdot r \\
\text{s.t.} \quad & \sum_{i} p_i U(\bm{W}_i, a_j) \leq v, \quad \forall j \\
& \sum_{i} p_i = 1, \quad p_i \geq 0 \\
& r \geq \max_j U(\bm{W}^*, a_j) - v
\end{align}

where $\bm{p}$ is the mixed strategy for the defender.

\section{APSE Properties and Interpretation}

\subsection{Metric Properties}

\begin{enumerate}
    \item \textbf{Worst-case guarantee}: APSE provides security bounds under optimal adversarial strategies
    \item \textbf{Adaptive modeling}: Captures adversarial adaptation to system design
    \item \textbf{Cross-domain awareness}: Mutual information term penalizes correlated leakage
    \item \textbf{Regret minimization}: Encourages robust designs with low performance deviation
\end{enumerate}

\subsection{Physical Interpretation}

\begin{itemize}
    \item \textbf{APSE $< 0$}: System is resilient; defender has advantage even under worst-case attacks
    \item \textbf{APSE $\approx 0$}: Balanced security; system performs adequately against adaptive adversaries
    \item \textbf{APSE $> 0$}: Vulnerability exists; adversary can exploit system weaknesses
    \item \textbf{High regret term}: Design is brittle; performance degrades significantly under attack
\end{itemize}

\subsection{Comparison with Existing Metrics}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
Metric & Adversarial & Adaptive & Cross-Domain & Worst-Case \\
\hline
WSI & No & No & Yes & No \\
JSI & No & No & Yes & No \\
EPSD & No & No & Yes & No \\
\textbf{APSE} & \textbf{Yes} & \textbf{Yes} & \textbf{Yes} & \textbf{Yes} \\
\hline
\end{tabular}
\caption{Comparison of security metrics capabilities}
\end{table}

\section{Optimization Framework}

\subsection{APSE-Based System Design}

The robust ISAC design problem becomes:
\begin{align}
\min_{\bm{Q}_x, \bm{Q}_n} \quad & \text{APSE}(\bm{Q}_x, \bm{Q}_n) \\
\text{s.t.} \quad & \tr(\bm{Q}_x + \bm{Q}_n) \leq P_{\max} \\
& \text{SINR}_k \geq \Gamma_k, \quad \forall k \\
& \text{CRB}_{\text{BS}}(\theta_L) \leq \gamma_{\text{sense}} \\
& \bm{Q}_x, \bm{Q}_n \succeq 0
\end{align}

\subsection{Successive Convex Approximation}

Since APSE is generally non-convex, we employ SCA:

\subsubsection{Linearization of Minimax Term}

At iteration $n$, approximate:
\begin{equation}
\min_{\bm{W}} \max_a U(\bm{W}, a) \approx \min_{\bm{W}} \max_a \left[U(\bm{W}^{(n)}, a) + \nabla_{\bm{W}} U(\bm{W}^{(n)}, a)^T(\bm{W} - \bm{W}^{(n)})\right]
\end{equation}

\subsubsection{Regret Approximation}

The regret term can be approximated using:
\begin{equation}
\text{Regret}(\bm{W}) \approx \text{Regret}(\bm{W}^{(n)}) + \nabla \text{Regret}(\bm{W}^{(n)})^T(\bm{W} - \bm{W}^{(n)})
\end{equation}

\subsection{Distributed Implementation}

For large-scale systems, decompose the problem:
\begin{itemize}
    \item \textbf{Communication subproblem}: Optimize precoding for secrecy rate
    \item \textbf{Sensing subproblem}: Optimize beamforming for privacy
    \item \textbf{Coordination}: Update cross-domain coupling through dual variables
\end{itemize}

\section{Numerical Examples and Validation}

\subsection{System Configuration}

Consider a monostatic ISAC system with:
\begin{itemize}
    \item $N_{BS} = 16$ antennas at base station
    \item $N_E = 8$ antennas at eavesdropper
    \item $K = 3$ communication users
    \item Target at angle $\theta_L = \pi/4$
    \item SNR range: 0-30 dB
\end{itemize}

\subsection{Strategy Spaces}

\textbf{Defender strategies}:
\begin{itemize}
    \item Power allocation: 10 discrete levels
    \item Beamforming: 36 angular directions
    \item Artificial noise: 5 covariance structures
\end{itemize}

\textbf{Adversary strategies}:
\begin{itemize}
    \item Attack focus: $\alpha \in \{0, 0.25, 0.5, 0.75, 1.0\}$
    \item Processing: 3 algorithms (matched filter, MMSE, ML)
    \item Resource allocation: 8 configurations
\end{itemize}

\subsection{Performance Analysis}

\subsubsection{APSE vs. SNR}

Preliminary analysis shows:
\begin{enumerate}
    \item APSE decreases with increasing SNR (better security)
    \item Optimal defender strategies shift from communication-focused to balanced
    \item Adversary adapts attack focus based on system configuration
\end{enumerate}

\subsubsection{Convergence Properties}

\begin{itemize}
    \item Nash equilibrium typically reached within 10-15 iterations
    \item Convergence rate depends on strategy space discretization
    \item Mixed strategies emerge in balanced scenarios
\end{itemize}

\section{Implementation Considerations}

\subsection{Computational Complexity}

\begin{itemize}
    \item \textbf{Strategy evaluation}: $O(|\mathcal{D}| \times |\mathcal{A}|)$ per iteration
    \item \textbf{Nash computation}: $O(T \times |\mathcal{D}| \times |\mathcal{A}|)$ where $T$ is iterations
    \item \textbf{Mutual information}: $O(N_{\text{samples}})$ for Monte Carlo estimation
\end{itemize}

\subsection{Practical Approximations}

\subsubsection{Reduced Strategy Spaces}

\begin{itemize}
    \item Focus on dominant strategies identified through preliminary analysis
    \item Use hierarchical decomposition for large spaces
    \item Employ machine learning for strategy prediction
\end{itemize}

\subsubsection{Real-time Implementation}

\begin{itemize}
    \item Pre-compute equilibria for common scenarios
    \item Use online learning for strategy adaptation
    \item Implement distributed computation across network nodes
\end{itemize}

\section{Extensions and Future Work}

\subsection{Multi-Target Scenarios}

Extend APSE to multiple targets:
\begin{equation}
U_p(\bm{W}, a) = \sum_{i=1}^{N_T} w_i \frac{1}{\text{BCRB}(\theta_{L,i})}
\end{equation}

where $w_i$ represents target priority weights.

\subsection{Dynamic Games}

Consider time-varying strategies:
\begin{equation}
\text{APSE}_{\text{dynamic}} = \sum_{t=1}^T \delta^{t-1} \text{APSE}(t)
\end{equation}

where $\delta$ is the discount factor.

\subsection{Incomplete Information}

Model scenarios where players have partial knowledge:
\begin{itemize}
    \item Bayesian games with uncertain channel states
    \item Learning-based strategy adaptation
    \item Robust optimization under uncertainty
\end{itemize}

\section{Conclusions}

\subsection{Key Contributions}

\begin{enumerate}
    \item Developed APSE metric for game-theoretic ISAC security analysis
    \item Provided computational algorithms for Nash equilibrium computation
    \item Demonstrated integration with monostatic ISAC signal model
    \item Established optimization framework for robust system design
\end{enumerate}

\subsection{Advantages of APSE}

\begin{itemize}
    \item \textbf{Realistic threat modeling}: Captures adaptive adversarial behavior
    \item \textbf{Worst-case guarantees}: Provides security bounds under optimal attacks
    \item \textbf{Design guidance}: Identifies vulnerabilities and robust configurations
    \item \textbf{Cross-domain integration}: Unified framework for sensing and communication security
\end{itemize}

\subsection{Future Directions}

\begin{enumerate}
    \item Extend to multi-cell and cooperative scenarios
    \item Develop machine learning-enhanced strategy prediction
    \item Investigate dynamic and repeated game formulations
    \item Create experimental validation frameworks
\end{enumerate}

The APSE metric represents a significant advancement in ISAC security evaluation, providing a principled game-theoretic approach to robust system design under adversarial conditions.

\end{document}
