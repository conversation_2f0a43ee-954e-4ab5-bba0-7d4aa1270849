%% RUN_SIMULATION - Execute Joint Mutual Information Analysis
% This script runs the complete simulation for equations (36)-(41)
% from the joint mutual information derivation

clear; close all; clc;

%% Add paths and check dependencies
fprintf('=== Joint Mutual Information Simulation ===\n');
fprintf('Checking dependencies...\n');

% Check if required functions exist
required_files = {'joint_mutual_info_simulation.m', 'load_system_parameters.m'};
for i = 1:length(required_files)
    if ~exist(required_files{i}, 'file')
        error('Required file %s not found!', required_files{i});
    end
end
fprintf('All required files found.\n\n');

%% Run the main simulation
try
    fprintf('Starting simulation...\n');
    tic;
    
    % Execute main simulation function
    results = joint_mutual_info_simulation();
    
    simulation_time = toc;
    fprintf('\nSimulation completed successfully!\n');
    fprintf('Total simulation time: %.2f seconds\n', simulation_time);
    
catch ME
    fprintf('Error during simulation:\n');
    fprintf('Error message: %s\n', ME.message);
    fprintf('Error location: %s (line %d)\n', ME.stack(1).name, ME.stack(1).line);
    rethrow(ME);
end

%% Display key results
fprintf('\n=== Key Results Summary ===\n');
SNR_dB_range = results.params.SNR_dB_range;

% Find results at specific SNR points
snr_10dB_idx = find(SNR_dB_range == 10, 1);
snr_20dB_idx = find(SNR_dB_range == 20, 1);

if ~isempty(snr_10dB_idx)
    fprintf('Results at SNR = 10 dB:\n');
    fprintf('  Joint MI: %.3f bits\n', results.joint_mutual_info(snr_10dB_idx));
    fprintf('  Sensing MI: %.3f bits\n', results.sensing_mutual_info(snr_10dB_idx));
    fprintf('  Communication MI: %.3f bits\n', results.comm_mutual_info(snr_10dB_idx));
    fprintf('  Coupling: %.3f bits\n', results.coupling_info(snr_10dB_idx));
    fprintf('  FD-JILI: %.3f\n', results.FD_JILI(snr_10dB_idx));
    fprintf('  FD-WILM: %.3f\n', results.FD_WILM(snr_10dB_idx));
end

if ~isempty(snr_20dB_idx)
    fprintf('\nResults at SNR = 20 dB:\n');
    fprintf('  Joint MI: %.3f bits\n', results.joint_mutual_info(snr_20dB_idx));
    fprintf('  Sensing MI: %.3f bits\n', results.sensing_mutual_info(snr_20dB_idx));
    fprintf('  Communication MI: %.3f bits\n', results.comm_mutual_info(snr_20dB_idx));
    fprintf('  Coupling: %.3f bits\n', results.coupling_info(snr_20dB_idx));
    fprintf('  FD-JILI: %.3f\n', results.FD_JILI(snr_20dB_idx));
    fprintf('  FD-WILM: %.3f\n', results.FD_WILM(snr_20dB_idx));
end

%% Validate results
fprintf('\n=== Result Validation ===\n');
validate_simulation_results(results);

%% Save additional analysis
fprintf('\n=== Saving Additional Analysis ===\n');
save_detailed_analysis(results);

fprintf('\nSimulation and analysis complete!\n');
fprintf('Check the generated plots and saved files for detailed results.\n');

%% ========================================================================
%% VALIDATION FUNCTION
%% ========================================================================

function validate_simulation_results(results)
% Validate simulation results against expected ranges

params = results.params;
SNR_dB_range = params.SNR_dB_range;

% Check for NaN or Inf values
if any(isnan(results.joint_mutual_info)) || any(isinf(results.joint_mutual_info))
    warning('Joint mutual information contains NaN or Inf values!');
end

if any(isnan(results.FD_JILI)) || any(isinf(results.FD_JILI))
    warning('FD-JILI contains NaN or Inf values!');
end

% Check value ranges
max_joint_MI = max(results.joint_mutual_info);
min_joint_MI = min(results.joint_mutual_info);

if max_joint_MI > params.expected_joint_MI_range(2)
    warning('Joint MI exceeds expected maximum: %.3f > %.3f', ...
            max_joint_MI, params.expected_joint_MI_range(2));
end

if min_joint_MI < params.expected_joint_MI_range(1)
    warning('Joint MI below expected minimum: %.3f < %.3f', ...
            min_joint_MI, params.expected_joint_MI_range(1));
end

% Check FD-JILI range
max_JILI = max(results.FD_JILI);
min_JILI = min(results.FD_JILI);

if max_JILI > params.expected_JILI_range(2)
    warning('FD-JILI exceeds expected maximum: %.3f > %.3f', ...
            max_JILI, params.expected_JILI_range(2));
end

if min_JILI < params.expected_JILI_range(1)
    warning('FD-JILI below expected minimum: %.3f < %.3f', ...
            min_JILI, params.expected_JILI_range(1));
end

% Check monotonicity (MI should generally increase with SNR)
joint_MI_diff = diff(results.joint_mutual_info);
if sum(joint_MI_diff < 0) > length(joint_MI_diff) * 0.3
    warning('Joint MI does not show expected monotonic increase with SNR');
end

fprintf('Validation completed. Check warnings above for any issues.\n');

% Print validation summary
fprintf('Joint MI range: [%.3f, %.3f] bits\n', min_joint_MI, max_joint_MI);
fprintf('FD-JILI range: [%.3f, %.3f]\n', min_JILI, max_JILI);
fprintf('Coupling range: [%.3f, %.3f] bits\n', ...
        min(results.coupling_info), max(results.coupling_info));

end

%% ========================================================================
%% DETAILED ANALYSIS FUNCTION
%% ========================================================================

function save_detailed_analysis(results)
% Save detailed analysis and additional plots

% Create detailed results table
SNR_dB_range = results.params.SNR_dB_range;
detailed_table = table(SNR_dB_range', results.joint_mutual_info, ...
                      results.sensing_mutual_info, results.comm_mutual_info, ...
                      results.coupling_info, results.FD_JILI, results.FD_WILM, ...
                      'VariableNames', {'SNR_dB', 'Joint_MI', 'Sensing_MI', ...
                      'Comm_MI', 'Coupling', 'FD_JILI', 'FD_WILM'});

% Save table to CSV
writetable(detailed_table, 'joint_mi_detailed_results.csv');
fprintf('Detailed results saved to joint_mi_detailed_results.csv\n');

% Create additional analysis plots
figure('Position', [200, 200, 1000, 600]);

% Plot 1: MI vs SNR with trend lines
subplot(2, 2, 1);
plot(SNR_dB_range, results.joint_mutual_info, 'b-o', 'LineWidth', 2);
hold on;
% Add polynomial fit
p = polyfit(SNR_dB_range, results.joint_mutual_info, 2);
fit_curve = polyval(p, SNR_dB_range);
plot(SNR_dB_range, fit_curve, 'r--', 'LineWidth', 1.5);
xlabel('SNR (dB)');
ylabel('Joint Mutual Information (bits)');
title('Joint MI with Polynomial Fit');
legend('Simulation', 'Polynomial Fit', 'Location', 'best');
grid on;

% Plot 2: Security metrics comparison
subplot(2, 2, 2);
yyaxis left;
plot(SNR_dB_range, results.FD_JILI, 'b-o', 'LineWidth', 2);
ylabel('FD-JILI');
yyaxis right;
plot(SNR_dB_range, results.FD_WILM, 'r-s', 'LineWidth', 2);
ylabel('FD-WILM');
xlabel('SNR (dB)');
title('Security Metrics Comparison');
grid on;

% Plot 3: Information decomposition
subplot(2, 2, 3);
area(SNR_dB_range, [results.sensing_mutual_info, results.comm_mutual_info, ...
     results.coupling_info], 'LineWidth', 1.5);
xlabel('SNR (dB)');
ylabel('Mutual Information (bits)');
title('Information Decomposition');
legend('Sensing', 'Communication', 'Coupling', 'Location', 'best');
grid on;

% Plot 4: Coupling analysis
subplot(2, 2, 4);
coupling_ratio = results.coupling_info ./ results.joint_mutual_info;
plot(SNR_dB_range, coupling_ratio, 'm-o', 'LineWidth', 2);
xlabel('SNR (dB)');
ylabel('Coupling Ratio');
title('Coupling as Fraction of Joint MI');
grid on;

sgtitle('Detailed Joint Mutual Information Analysis');

% Save the figure as FIG format
savefig('joint_mi_detailed_analysis.fig');
fprintf('Detailed analysis plots saved as FIG format.\n');

% Compute and save statistics
stats.mean_joint_MI = mean(results.joint_mutual_info);
stats.std_joint_MI = std(results.joint_mutual_info);
stats.mean_FD_JILI = mean(results.FD_JILI);
stats.std_FD_JILI = std(results.FD_JILI);
stats.correlation_sensing_comm = corr(results.sensing_mutual_info, results.comm_mutual_info);
stats.max_coupling = max(abs(results.coupling_info));

save('simulation_statistics.mat', 'stats');
fprintf('Simulation statistics saved to simulation_statistics.mat\n');

end
