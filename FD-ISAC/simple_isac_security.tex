\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\title{Joint CRB-Based Security Framework for Monostatic ISAC Systems}
\author{Security Metrics Analysis}
\date{\today}

\begin{document}

\maketitle

\begin{abstract}
This document presents a comprehensive joint Cramér-Rao bound (CRB) based security framework for monostatic integrated sensing and communication (ISAC) systems. We develop unified security metrics that capture both sensing and communication information leakage through a joint parameter estimation perspective.
\end{abstract}

\section{Introduction}

In monostatic ISAC systems, the eavesdropper faces a joint parameter estimation problem: simultaneously estimating the target location $\theta_L$ and the communication signal $\bm{x}$. This unified perspective enables the development of more accurate and theoretically rigorous security metrics.

\section{System Model}

\subsection{Monostatic ISAC Configuration}

We consider a monostatic FD-ISAC system with the following components:
\begin{itemize}
    \item \textbf{Monostatic Base Station}: $N_{BS}$ antennas for radar and communication
    \item \textbf{Target}: Passive reflector at angle $\theta_L$
    \item \textbf{Communication Users}: $K$ single-antenna users
    \item \textbf{Eavesdropper}: $N_E$ antennas attempting to intercept information
\end{itemize}

\subsection{Signal Model}

The monostatic BS transmits signal $\bm{x}(l) \in \C^{N_{BS} \times 1}$ at time slot $l$.

\subsubsection{BS Received Signal}
\begin{equation}
\bm{y}_{BS}(l) = \bm{G}_L(\theta_L)\bm{x}(l) + \bm{H}_{SI}\bm{x}(l) + \bm{n}_{BS}(l)
\end{equation}

\subsubsection{Eavesdropper Received Signal}
\begin{equation}
\bm{y}_{eve}(l) = \bm{G}_E(\theta_E)\bm{x}(l) + \bm{H}_{CE}\bm{x}(l) + \bm{z}_{tar}(l)
\end{equation}

\subsubsection{User Received Signal}
\begin{equation}
y_{C,k}(l) = \bm{h}^H_{C,k}\bm{x}(l) + z_{C,k}(l)
\end{equation}

\section{Joint CRB Security Framework}

\subsection{Joint Parameter Vector}

The eavesdropper attempts to estimate:
\begin{equation}
\bm{\phi} = \begin{bmatrix} \theta_L \\ \text{vec}(\bm{X}) \end{bmatrix}
\end{equation}
where $\bm{X} = [\bm{x}(1), \ldots, \bm{x}(L)]$.

\subsection{Joint Fisher Information Matrix}

The joint FIM has structure:
\begin{equation}
\bm{J}_{joint} = \begin{bmatrix}
\bm{J}_{\theta\theta} & \bm{J}_{\theta\bm{x}} \\
\bm{J}_{\bm{x}\theta} & \bm{J}_{\bm{x}\bm{x}}
\end{bmatrix}
\end{equation}

For the angle parameter:
\begin{equation}
\bm{J}_{\theta\theta} = \frac{2}{\sigma^2_{tar}} \sum_{l=1}^L \text{Re}\left\{ \frac{\partial \bm{g}_E^H(\theta_L)}{\partial \theta_L} \bm{x}(l) \bm{x}^H(l) \frac{\partial \bm{g}_E(\theta_L)}{\partial \theta_L} \right\}
\end{equation}

For the communication signals:
\begin{equation}
\bm{J}_{\bm{x}\bm{x}} = \frac{2}{\sigma^2_{tar}} \bm{I}_L \otimes \text{Re}\{[\bm{G}_E(\theta_L) + \bm{H}_{CE}]^H [\bm{G}_E(\theta_L) + \bm{H}_{CE}]\}
\end{equation}

\subsection{Joint Security Metrics}

\subsubsection{Joint Security Index (JSI)}
\begin{equation}
\text{JSI} = \det(\bm{C}_{joint})^{1/\text{dim}(\bm{\phi})}
\end{equation}
where $\bm{C}_{joint} = \bm{J}_{joint}^{-1}$.

\subsubsection{Weighted Joint CRB Metric}
\begin{equation}
\text{WJCRB} = w_\theta \cdot \frac{\text{CRB}_{eve}(\theta_L)}{\text{CRB}_{BS}(\theta_L)} + w_x \cdot \frac{\text{CRB}_{eve}(\bm{x})}{\text{CRB}_{ref}(\bm{x})}
\end{equation}

\subsubsection{Normalized Joint Security Metric}
\begin{equation}
\text{NJSM} = \frac{\det(\bm{C}_{joint})}{\det(\bm{C}_{ref})}
\end{equation}

\section{Traditional Security Metrics}

For comparison, we also define traditional separate metrics:

\subsection{Sensing Security Metric}
\begin{equation}
R_{sense} = \frac{1}{2}\log_2\left(\frac{\text{CRB}_{eve}(\theta_L)}{\text{CRB}_{BS}(\theta_L)}\right)
\end{equation}

\subsection{Communication Security Metric}
\begin{equation}
R_{comm} = \left[I(\bm{x}; \bm{y}_{CU}) - I(\bm{x}; \bm{y}_{eve})\right]^+
\end{equation}

\subsection{Weighted Security Index}
\begin{equation}
\text{WSI} = w_s \cdot \frac{R_{sense}}{R_{sense}^{max}} + w_c \cdot \frac{R_{comm}}{R_{comm}^{max}}
\end{equation}

\section{Optimization Framework}

The security optimization problem can be formulated as:
\begin{align}
\max_{\bm{Q}_x} \quad & \text{JSI}(\bm{Q}_x) \\
\text{s.t.} \quad & \tr(\bm{Q}_x) \leq P_{max} \\
& \text{SINR}_k \geq \Gamma_k, \quad \forall k \\
& \text{CRB}_{BS}(\theta_L) \leq \gamma_{sense} \\
& \bm{Q}_x \succeq 0
\end{align}

\section{Key Advantages of Joint CRB Approach}

\begin{enumerate}
\item \textbf{Unified Framework}: Single metric for both security aspects
\item \textbf{Theoretical Rigor}: Based on fundamental estimation limits
\item \textbf{Coupling Effects}: Captures interaction between sensing and communication
\item \textbf{Optimization Friendly}: Suitable for joint optimization problems
\end{enumerate}

\section{Implementation Guidelines}

\subsection{Computational Complexity}
\begin{itemize}
\item FIM computation: $\mathcal{O}(L^2 N_{BS}^2 N_E)$
\item Matrix inversion: $\mathcal{O}((LN_{BS})^3)$
\item Determinant: $\mathcal{O}((LN_{BS})^3)$
\end{itemize}

\subsection{Practical Simplifications}
\begin{itemize}
\item \textbf{Block-diagonal approximation}: For weak coupling scenarios
\item \textbf{Single-slot analysis}: For computational efficiency
\item \textbf{Weighted combination}: For flexible security assessment
\end{itemize}

\section{Conclusions}

The proposed joint CRB-based security framework provides a unified, theoretically rigorous approach to security evaluation in monostatic ISAC systems. The key innovation is recognizing that sensing and communication security are fundamentally coupled estimation problems that should be analyzed jointly rather than separately.

The JSI and WJCRB metrics offer significant advantages over traditional separate metrics:
\begin{itemize}
\item More accurate security assessment
\item Natural coupling consideration
\item Better optimization properties
\item Unified theoretical foundation
\end{itemize}

Future work will extend this framework to multi-target scenarios and investigate robust security design under imperfect channel knowledge.

\end{document}
