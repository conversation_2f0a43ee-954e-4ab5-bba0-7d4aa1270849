function [EPSD, components] = compute_EPSD(system_params, y_eve, theta_L_true)
% COMPUTE_EPSD - Compute Entropic Privacy-Secrecy Divergence for monostatic ISAC
%
% Inputs:
%   system_params - Structure containing system parameters
%   y_eve         - Eavesdropper received signal [N_E x L]
%   theta_L_true  - True target angle (for simulation)
%
% Outputs:
%   EPSD         - Computed EPSD value
%   components   - Structure with individual components
%
% Based on the monostatic ISAC signal model:
% y_eve(l) = [G_E(theta_L) + H_CE] * x(l) + z_tar(l)

%% Extract system parameters
N_BS = system_params.N_BS;           % Number of BS antennas
N_E = system_params.N_E;             % Number of eavesdropper antennas
K = system_params.K;                 % Number of users
L = system_params.L;                 % Number of time slots
Q_x = system_params.Q_x;             % Signal covariance matrix
H_CE = system_params.H_CE;           % Communication channel to eavesdropper
sigma_tar_sq = system_params.sigma_tar_sq; % Target noise variance
theta_range = system_params.theta_range;   % Possible target angles

%% Step 1: Compute effective eavesdropping channel
% H_eff(theta_L) = G_E(theta_L) + H_CE
G_E = @(theta) compute_sensing_channel(theta, system_params);
H_eff = @(theta) G_E(theta) + H_CE;

%% Step 2: Compute communication posterior distribution
% For given theta_L, compute P(x|y_eve, theta_L)
function [mu_x, Sigma_x] = compute_comm_posterior(theta_L)
    H_eff_theta = H_eff(theta_L);
    
    % Posterior covariance
    Sigma_x = inv(inv(Q_x) + (1/sigma_tar_sq) * H_eff_theta' * H_eff_theta);
    
    % Posterior mean
    mu_x = (1/sigma_tar_sq) * Sigma_x * H_eff_theta' * y_eve;
end

%% Step 3: Compute sensing posterior distribution
% Q(theta_L|y_eve) using Bayesian update
theta_grid = linspace(theta_range(1), theta_range(2), 100);
log_likelihood = zeros(size(theta_grid));
Q_theta_post = zeros(size(theta_grid));

% Prior distribution (uniform for simplicity)
Q_theta_prior = ones(size(theta_grid)) / length(theta_grid);

for i = 1:length(theta_grid)
    theta = theta_grid(i);
    [mu_x_theta, Sigma_x_theta] = compute_comm_posterior(theta);
    H_eff_theta = H_eff(theta);
    
    % Compute likelihood p(y_eve|theta_L)
    y_pred = H_eff_theta * mu_x_theta;
    residual = y_eve - y_pred;
    
    % Log-likelihood (assuming Gaussian noise)
    log_likelihood(i) = -0.5 * (norm(residual, 'fro')^2 / sigma_tar_sq + ...
                                N_E * L * log(2*pi*sigma_tar_sq));
end

% Posterior (unnormalized)
log_posterior = log_likelihood + log(Q_theta_prior);
% Normalize
log_posterior = log_posterior - max(log_posterior); % Numerical stability
Q_theta_post = exp(log_posterior);
Q_theta_post = Q_theta_post / sum(Q_theta_post);

%% Step 4: Compute KL divergences

% 4.1 Communication KL divergence
% Use MAP estimate of theta_L for communication posterior
[~, idx_map] = max(Q_theta_post);
theta_L_map = theta_grid(idx_map);
[mu_x_map, Sigma_x_map] = compute_comm_posterior(theta_L_map);

% D_KL(P(x|y_eve) || P_0(x)) where P_0(x) = CN(0, Q_x)
D_KL_comm = 0.5 * (trace(inv(Q_x) * Sigma_x_map) - N_BS + ...
                   log(det(Q_x) / det(Sigma_x_map)) + ...
                   mu_x_map' * inv(Q_x) * mu_x_map);

% 4.2 Sensing KL divergence
% D_KL(Q(theta_L|y_eve) || Q_0(theta_L))
% Numerical integration
dtheta = theta_grid(2) - theta_grid(1);
D_KL_sense = sum(Q_theta_post .* log(Q_theta_post ./ Q_theta_prior)) * dtheta;

%% Step 5: Compute entropy terms

% 5.1 Individual entropies
% Communication entropy
H_comm = 0.5 * log(det(2*pi*exp(1) * Sigma_x_map));

% Sensing entropy (using posterior variance)
theta_mean = sum(theta_grid .* Q_theta_post) * dtheta;
theta_var = sum((theta_grid - theta_mean).^2 .* Q_theta_post) * dtheta;
H_sense = 0.5 * log(2*pi*exp(1) * theta_var);

% 5.2 Joint entropy estimation
% Method 1: Gaussian approximation (assuming weak coupling)
H_joint_approx = H_comm + H_sense;

% Method 2: Monte Carlo estimation (more accurate)
n_samples = 1000;
samples_x = mvnrnd(mu_x_map, Sigma_x_map, n_samples);
samples_theta = randsample(theta_grid, n_samples, true, Q_theta_post);

% Estimate joint entropy using k-nearest neighbors
H_joint_mc = estimate_joint_entropy_knn(samples_x, samples_theta);

% Use MC estimate if available, otherwise use approximation
if ~isnan(H_joint_mc) && H_joint_mc > 0
    H_joint = H_joint_mc;
else
    H_joint = H_joint_approx;
end

%% Step 6: Compute EPSD
% EPSD = D_KL_comm * D_KL_sense * exp(-(H_joint - H_comm - H_sense))
mutual_info_term = H_joint - H_comm - H_sense;
exponential_penalty = exp(-mutual_info_term);

EPSD = D_KL_comm * D_KL_sense * exponential_penalty;

%% Store components for analysis
components.D_KL_comm = D_KL_comm;
components.D_KL_sense = D_KL_sense;
components.H_comm = H_comm;
components.H_sense = H_sense;
components.H_joint = H_joint;
components.mutual_info_term = mutual_info_term;
components.exponential_penalty = exponential_penalty;
components.theta_posterior = Q_theta_post;
components.theta_grid = theta_grid;
components.theta_map = theta_L_map;

end

%% Helper function: Compute sensing channel
function G_E = compute_sensing_channel(theta_L, params)
% Compute sensing channel matrix G_E(theta_L) for eavesdropper
% Based on array response vector

N_E = params.N_E;
lambda = params.lambda;  % Wavelength
d = params.d;           % Antenna spacing

% Array response vector for eavesdropper
a_E = exp(1j * 2*pi * d/lambda * (0:N_E-1)' * sin(theta_L));

% Sensing channel (simplified model)
% G_E = alpha * a_E * a_BS^H where a_BS is BS array response
alpha = params.sensing_gain;
a_BS = exp(1j * 2*pi * d/lambda * (0:params.N_BS-1)' * sin(theta_L));

G_E = alpha * a_E * a_BS';
end

%% Helper function: Estimate joint entropy using k-NN
function H_joint = estimate_joint_entropy_knn(samples_x, samples_theta)
% Estimate joint entropy using k-nearest neighbors method
% This is a simplified implementation

try
    % Combine samples
    if size(samples_x, 2) > 1
        % For vector x, use first component for simplicity
        combined_samples = [real(samples_x(:,1)), imag(samples_x(:,1)), samples_theta(:)];
    else
        combined_samples = [real(samples_x), imag(samples_x), samples_theta(:)];
    end
    
    % Remove any NaN or Inf values
    valid_idx = all(isfinite(combined_samples), 2);
    combined_samples = combined_samples(valid_idx, :);
    
    if size(combined_samples, 1) < 10
        H_joint = NaN;
        return;
    end
    
    % Simple entropy estimation using histogram
    n_bins = min(20, floor(sqrt(size(combined_samples, 1))));
    
    % Discretize each dimension
    edges_x_real = linspace(min(combined_samples(:,1)), max(combined_samples(:,1)), n_bins+1);
    edges_x_imag = linspace(min(combined_samples(:,2)), max(combined_samples(:,2)), n_bins+1);
    edges_theta = linspace(min(combined_samples(:,3)), max(combined_samples(:,3)), n_bins+1);
    
    % Compute 3D histogram
    [~, bin_x_real] = histc(combined_samples(:,1), edges_x_real);
    [~, bin_x_imag] = histc(combined_samples(:,2), edges_x_imag);
    [~, bin_theta] = histc(combined_samples(:,3), edges_theta);
    
    % Count occurrences
    bin_indices = sub2ind([n_bins, n_bins, n_bins], ...
                         max(1, min(bin_x_real, n_bins)), ...
                         max(1, min(bin_x_imag, n_bins)), ...
                         max(1, min(bin_theta, n_bins)));
    
    counts = accumarray(bin_indices, 1, [n_bins^3, 1]);
    
    % Compute probabilities
    probs = counts / sum(counts);
    probs = probs(probs > 0); % Remove zero probabilities
    
    % Compute entropy
    H_joint = -sum(probs .* log2(probs));
    
catch
    % Fallback to NaN if estimation fails
    H_joint = NaN;
end
end

%% Example usage and testing function
function test_EPSD()
% Test function for EPSD computation

% System parameters
params.N_BS = 8;
params.N_E = 4;
params.K = 2;
params.L = 1;
params.lambda = 0.1;
params.d = params.lambda / 2;
params.sensing_gain = 0.5;
params.sigma_tar_sq = 0.1;
params.theta_range = [-pi/3, pi/3];

% Signal covariance matrix (random positive definite)
params.Q_x = eye(params.N_BS) + 0.1 * randn(params.N_BS);
params.Q_x = params.Q_x * params.Q_x'; % Ensure positive definite

% Communication channel to eavesdropper (random)
params.H_CE = (randn(params.N_E, params.N_BS) + 1j*randn(params.N_E, params.N_BS)) / sqrt(2);

% Generate test signal
theta_L_true = pi/6;
x_test = (randn(params.N_BS, params.L) + 1j*randn(params.N_BS, params.L)) / sqrt(2);
G_E_true = compute_sensing_channel(theta_L_true, params);
H_eff_true = G_E_true + params.H_CE;
noise = sqrt(params.sigma_tar_sq) * (randn(params.N_E, params.L) + 1j*randn(params.N_E, params.L)) / sqrt(2);
y_eve = H_eff_true * x_test + noise;

% Compute EPSD
[EPSD_value, comp] = compute_EPSD(params, y_eve, theta_L_true);

% Display results
fprintf('EPSD Test Results:\n');
fprintf('EPSD Value: %.4f\n', EPSD_value);
fprintf('Communication KL Divergence: %.4f\n', comp.D_KL_comm);
fprintf('Sensing KL Divergence: %.4f\n', comp.D_KL_sense);
fprintf('Exponential Penalty: %.4f\n', comp.exponential_penalty);
fprintf('Estimated Target Angle: %.4f rad (True: %.4f rad)\n', comp.theta_map, theta_L_true);

% Plot posterior distribution
figure;
plot(comp.theta_grid, comp.theta_posterior, 'b-', 'LineWidth', 2);
hold on;
plot(theta_L_true, 0, 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'r');
plot(comp.theta_map, max(comp.theta_posterior), 'g^', 'MarkerSize', 10, 'MarkerFaceColor', 'g');
xlabel('Target Angle (rad)');
ylabel('Posterior Probability');
title('Sensing Posterior Distribution');
legend('Posterior', 'True Angle', 'MAP Estimate');
grid on;
end
