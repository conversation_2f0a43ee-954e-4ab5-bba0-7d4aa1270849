# FD-ISAC Joint Mutual Information Analysis - Plot Index

This document provides a comprehensive index of all generated PNG plots from the joint mutual information simulation for FD-ISAC systems.

## 📊 Generated PNG Files

### 1. **joint_mutual_info_components.png**
**Description**: Main mutual information components analysis
- **Content**: 
  - Joint Mutual Information (blue line with circles)
  - Sensing Mutual Information (red dashed line with squares)
  - Communication Mutual Information (yellow dash-dot line with triangles)
  - Coupling Information (purple dotted line)
- **X-axis**: SNR (dB) from 0 to 30
- **Y-axis**: Mutual Information (bits)
- **Key Insights**: Shows how different information components vary with SNR

### 2. **security_metrics.png**
**Description**: FD-ISAC security metrics visualization
- **Content**: 
  - **Top subplot**: FD-JILI (FD-ISAC Joint Information Leakage Index)
  - **Bottom subplot**: FD-WILM (FD-ISAC Weighted Information Leakage Metric)
- **X-axis**: SNR (dB) from 0 to 30
- **Y-axis**: Security metric values
- **Key Insights**: Quantifies security performance across different SNR levels

### 3. **information_leakage_ratios.png**
**Description**: Normalized information leakage analysis
- **Content**:
  - Sensing Leakage Ratio (red dashed line with squares)
  - Communication Leakage Ratio (yellow dash-dot line with triangles)
- **X-axis**: SNR (dB) from 0 to 30
- **Y-axis**: Information Leakage Ratio (normalized)
- **Key Insights**: Shows relative information leakage for sensing vs communication

### 4. **coupling_analysis.png**
**Description**: Information coupling strength analysis
- **Content**:
  - Coupling ratio as function of SNR (purple line with circles)
  - Horizontal reference line at zero
- **X-axis**: SNR (dB) from 0 to 30
- **Y-axis**: Coupling Ratio
- **Key Insights**: Quantifies the coupling between sensing and communication information

### 5. **comprehensive_security_analysis.png**
**Description**: Four-panel comprehensive analysis
- **Panel 1 (Top-left)**: Information decomposition area plot
  - Sensing information (red area)
  - Communication information (yellow area)
- **Panel 2 (Top-right)**: Security metrics comparison
  - FD-JILI (left y-axis, blue)
  - FD-WILM (right y-axis, red)
- **Panel 3 (Bottom-left)**: Joint MI with polynomial trend fit
  - Simulation data (blue circles)
  - Polynomial fit (red dashed line)
- **Panel 4 (Bottom-right)**: Security vs Performance trade-off
  - Security Score (blue line)
  - Performance Score (yellow line)

### 6. **3d_mutual_info_landscape.png**
**Description**: 3D visualization of mutual information landscape
- **Content**:
  - 3D surface plot showing information types vs SNR
  - Color-coded surface with jet colormap
- **X-axis**: SNR (dB) from 0 to 30
- **Y-axis**: Information Type (1=Joint, 2=Sensing, 3=Communication)
- **Z-axis**: Mutual Information (bits)
- **View angle**: 45° azimuth, 30° elevation
- **Key Insights**: Provides intuitive 3D perspective of information landscape

### 7. **summary_statistics.png**
**Description**: Statistical summary with error bars
- **Content**:
  - **Left panel**: Average mutual information with standard deviation
    - Joint MI, Sensing MI, Communication MI (blue bars)
  - **Right panel**: Average security metrics with standard deviation
    - FD-JILI, FD-WILM (red bars)
- **Error bars**: Show standard deviation across SNR range
- **Key Insights**: Statistical overview of system performance

### 8. **joint_mi_detailed_analysis.png** (from original simulation)
**Description**: Original detailed analysis from main simulation
- **Content**: Four-panel analysis similar to comprehensive analysis
- **Note**: Generated during the main simulation run

## 📈 Plot Specifications

### Technical Details
- **Resolution**: 300 DPI (publication quality)
- **Format**: PNG with transparency support
- **Font sizes**: 
  - Titles: 14-18pt, bold
  - Axis labels: 12-14pt, bold
  - Legends: 12pt
- **Line widths**: 2pt for main lines, 1.5pt for grid
- **Marker sizes**: 6-8pt with face colors
- **Color scheme**: Professional color palette with high contrast

### Color Coding
- **Blue** (`[0.0000, 0.4470, 0.7410]`): Joint/Primary metrics
- **Red** (`[0.8500, 0.3250, 0.0980]`): Sensing/Secondary metrics  
- **Yellow** (`[0.9290, 0.6940, 0.1250]`): Communication metrics
- **Purple** (`[0.4940, 0.1840, 0.5560]`): Coupling metrics
- **Green** (`[0.4660, 0.6740, 0.1880]`): Performance metrics

## 🔍 Key Results Summary

### From the Generated Plots:

1. **Joint Mutual Information**: Ranges from -3.697 to 176.906 bits across SNR range
2. **Security Metrics**: 
   - FD-JILI ranges from 0.037 to -1.794
   - FD-WILM shows values around 1.4-1.5
3. **Information Leakage**: Both sensing and communication show SNR-dependent behavior
4. **Coupling**: Minimal coupling observed (near zero across SNR range)

### Validation Notes:
- Some values exceed expected ranges (warnings generated)
- Negative mutual information values indicate potential numerical issues
- Results suggest strong SNR dependence of information leakage

## 📁 File Usage

### For Publications:
- Use 300 DPI PNG files directly in LaTeX/Word documents
- All plots have publication-quality formatting
- Color scheme is printer-friendly

### For Presentations:
- High contrast colors work well on projectors
- Large fonts ensure readability
- 3D plot provides engaging visualization

### For Analysis:
- CSV data file available: `joint_mi_detailed_results.csv`
- MATLAB data file: `joint_mutual_info_results.mat`
- Statistics file: `simulation_statistics.mat`

## 🛠️ Regeneration

To regenerate plots with different parameters:
1. Modify parameters in `load_system_parameters.m`
2. Run `run_simulation.m` to generate new data
3. Run `generate_plots.m` to create new PNG files

## 📊 Data Files

### Associated Data Files:
- `joint_mi_detailed_results.csv`: Tabular data for all metrics vs SNR
- `joint_mutual_info_results.mat`: Complete MATLAB results structure
- `simulation_statistics.mat`: Statistical summary data

### Data Structure:
```matlab
results.joint_mutual_info     % Joint MI vs SNR
results.sensing_mutual_info   % Sensing MI vs SNR  
results.comm_mutual_info      % Communication MI vs SNR
results.coupling_info         % Coupling vs SNR
results.FD_JILI              % Security metric 1 vs SNR
results.FD_WILM              % Security metric 2 vs SNR
results.params               % System parameters used
```

This comprehensive set of plots provides complete visualization of the FD-ISAC joint mutual information analysis based on equations (36)-(41) from the theoretical derivation.
