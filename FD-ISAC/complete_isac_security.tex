\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts

% Packages
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}

\begin{document}

\title{A Physical-Layer Security Framework for Joint Communication and Sensing in Monostatic ISAC Systems}

\author{\IEEEauthorblockN{Nanchi Su}
\IEEEauthorblockA{\textit{Guangdong Provincial Key Laboratory of Aerospace Communication and Networking Technology} \\
\textit{Harbin Institute of Technology (Shenzhen)} \\
Shenzhen, China \\
<EMAIL>}
}

\maketitle

\begin{abstract}
In this paper, we propose a comprehensive physical-layer security framework for monostatic integrated sensing and communication (ISAC) systems. Unlike traditional approaches that treat sensing and communication security separately, we develop a unified joint Cramér-Rao bound (CRB) based security metric that captures the fundamental limits of both sensing target location leakage and communication data leakage. Our framework provides novel security performance metrics including the Joint Security Index (JSI) and Weighted Joint CRB (WJCRB) that enable comprehensive security evaluation and optimization. Simulation results demonstrate the effectiveness of the proposed unified approach compared to conventional separate security metrics.
\end{abstract}

\begin{IEEEkeywords}
Integrated sensing and communication, physical-layer security, Cramér-Rao bound, joint parameter estimation, monostatic radar
\end{IEEEkeywords}

\section{Introduction}

Integrated sensing and communication (ISAC) systems have emerged as a key technology for future wireless networks, enabling simultaneous radar sensing and data communication using shared spectrum and hardware resources. However, the dual functionality of ISAC systems introduces unique security vulnerabilities where malicious eavesdroppers can potentially intercept both sensing information (e.g., target locations) and communication data.

Traditional security approaches treat sensing and communication security as separate problems, using independent metrics such as sensing secrecy rates and communication secrecy rates. However, in practical ISAC systems, these two aspects are fundamentally coupled through the shared signal and hardware, suggesting the need for a unified security framework.

In this paper, we make the following contributions:
\begin{itemize}
\item We develop a monostatic ISAC system model that captures both sensing and communication security threats.
\item We propose a novel joint CRB-based security framework that unifies sensing and communication security evaluation.
\item We derive closed-form expressions for joint security metrics including JSI and WJCRB.
\item We provide comprehensive performance analysis and optimization guidelines.
\end{itemize}

\section{System Model}

We consider a monostatic FD-ISAC framework as depicted in Figure 1. In this system, a single dual-functional base station operates as both radar transmitter and receiver while simultaneously providing communication services to multiple users.

\subsection{System Components}

The proposed monostatic secure ISAC system consists of the following components:

\begin{itemize}
    \item \textbf{Monostatic ISAC Base Station}: Equipped with $N_{BS}$ antennas, serving as:
    \begin{itemize}
        \item Radar transmitter for target illumination
        \item Radar receiver for echo signal processing
        \item Communication transmitter for downlink data
        \item Communication receiver for uplink data (if applicable)
    \end{itemize}
    
    \item \textbf{Target}: A passive reflector within the sensing coverage area
    
    \item \textbf{Communication Users}: $K$ single-antenna legitimate users
    
    \item \textbf{Eavesdropper}: A malicious terminal with $N_E$ antennas attempting to intercept both sensing and communication information
\end{itemize}

However, a malicious terminal, referred to as the eavesdropper (Eve), is present in the vicinity of the monostatic radar system. This adversary can exploit two vulnerabilities:
\begin{itemize}
    \item It may intercept the reflected sensing signals from the target, thereby inferring sensitive information such as the target's location (sensing data leakage).
    \item It may also eavesdrop on the downlink communication signals intended for the legitimate CUs, resulting in communication data leakage.
\end{itemize}

\subsection{Signal Model}

The monostatic BS transmits a composite signal vector $\bm{x}(l) \in \C^{N_{BS} \times 1}$ at time slot $l$. The received signals at each facility are listed as follows:

\subsubsection{Received Signal at Monostatic BS}
The received signal at the monostatic ISAC BS can be expressed as:
\begin{equation}
\bm{y}_{BS}(l) = \bm{G}_L(\theta_L)\bm{x}(l) + \bm{H}_{SI}\bm{x}(l) + \bm{n}_{BS}(l)
\end{equation}
where $\bm{G}_L(\theta_L) = \alpha(l)\bm{a}(\theta_L)\bm{a}^H(\theta_L)$ represents the target reflection channel matrix for the monostatic configuration, with $\theta_L$ being the target angle, $\bm{a}(\theta_L) \in \C^{N_{BS} \times 1}$ is the array steering vector, $\bm{H}_{SI} \in \C^{N_{BS} \times N_{BS}}$ denotes the self-interference channel matrix, and $\bm{n}_{BS}(l)$ is the additive noise.

\subsubsection{Received Signal at Eavesdropper}
The received signal at the malicious terminal can be written as:
\begin{equation}
\bm{y}_{eve}(l) = \bm{G}_E(\theta_E)\bm{x}(l) + \bm{H}_{CE}\bm{x}(l) + \bm{z}_{tar}(l)
\end{equation}
where $\bm{G}_E(\theta_E) = \beta(l)\bm{b}(\theta_E)\bm{a}^H(\theta_L)$ represents the target-reflected channel from BS to eavesdropper, $\bm{H}_{CE} \in \C^{N_E \times N_{BS}}$ is the direct channel from BS to eavesdropper, $\theta_E$ denotes the angle of arrival at the eavesdropper, $\bm{b}(\theta_E) \in \C^{N_E \times 1}$ is the eavesdropper's array steering vector, and $\bm{z}_{tar}(l)$ is the additive noise.

\subsubsection{Received Signal at Communication Users}
The received symbol at the $k$-th single-antenna CU at the $l$-th time slot is given as:
\begin{equation}
y_{C,k}(l) = \bm{h}^H_{C,k}\bm{x}(l) + z_{C,k}(l)
\end{equation}
where $\bm{h}_{C,k} \in \C^{N_{BS} \times 1}$ denotes the channel vector between the monostatic BS and the $k$-th CU, and $z_{C,k}[l]$ denotes the additive white Gaussian noise (AWGN) with variance $\sigma^2_{C,k}$.

\section{Joint CRB-Based Security Framework}

\subsection{Motivation}

In the monostatic ISAC system, the eavesdropper faces a joint parameter estimation problem: simultaneously estimating the target location $\theta_L$ and the communication signal $\bm{x}$. This unified perspective allows us to derive a joint Cramér-Rao bound (CRB) that captures the fundamental limits of both sensing and communication information leakage.

\subsection{Joint Parameter Vector}

Define the joint parameter vector that the eavesdropper attempts to estimate:
\begin{equation}
\bm{\phi} = \begin{bmatrix} \theta_L \\ \text{vec}(\bm{X}) \end{bmatrix}
\end{equation}
where $\bm{X} = [\bm{x}(1), \bm{x}(2), \ldots, \bm{x}(L)] \in \C^{N_{BS} \times L}$ is the signal matrix over $L$ time slots.

\subsection{Joint Fisher Information Matrix}

The joint Fisher Information Matrix (FIM) has the structure:
\begin{equation}
\bm{J}_{joint} = \begin{bmatrix}
\bm{J}_{\theta\theta} & \bm{J}_{\theta\bm{x}} \\
\bm{J}_{\bm{x}\theta} & \bm{J}_{\bm{x}\bm{x}}
\end{bmatrix}
\end{equation}

For the target angle parameter:
\begin{equation}
\bm{J}_{\theta\theta} = \frac{2}{\sigma^2_{tar}} \sum_{l=1}^L \text{Re}\left\{ \frac{\partial \bm{g}_E^H(\theta_L)}{\partial \theta_L} \bm{x}(l) \bm{x}^H(l) \frac{\partial \bm{g}_E(\theta_L)}{\partial \theta_L} \right\}
\end{equation}

For the communication signals:
\begin{equation}
\bm{J}_{\bm{x}\bm{x}} = \frac{2}{\sigma^2_{tar}} \bm{I}_L \otimes \text{Re}\{[\bm{G}_E(\theta_L) + \bm{H}_{CE}]^H [\bm{G}_E(\theta_L) + \bm{H}_{CE}]\}
\end{equation}

\subsection{Joint Security Metrics}

\subsubsection{Joint Security Index (JSI)}
We propose the Joint Security Index:
\begin{equation}
\text{JSI} = \det(\bm{C}_{joint})^{1/\text{dim}(\bm{\phi})}
\end{equation}
where $\bm{C}_{joint} = \bm{J}_{joint}^{-1}$ and $\text{dim}(\bm{\phi}) = 1 + 2LN_{BS}$.

\subsubsection{Weighted Joint CRB Metric}
Alternatively, we can use a weighted combination:
\begin{equation}
\text{WJCRB} = w_\theta \cdot \frac{\text{CRB}_{eve}(\theta_L)}{\text{CRB}_{BS}(\theta_L)} + w_x \cdot \frac{\text{CRB}_{eve}(\bm{x})}{\text{CRB}_{ref}(\bm{x})}
\end{equation}

\section{Security Performance Metrics}

\subsection{Individual Security Metrics}

For comparison purposes, we also define individual security metrics:

\subsubsection{Sensing Security Metric}
The sensing secrecy rate based on estimation accuracy:
\begin{equation}
R_{sense} = \frac{1}{2}\log_2\left(\frac{\text{CRB}_{eve}(\theta_L)}{\text{CRB}_{BS}(\theta_L)}\right)
\end{equation}

\subsubsection{Communication Security Metric}
The communication secrecy rate:
\begin{equation}
R_{comm} = \left[I(\bm{x}; \bm{y}_{CU}) - I(\bm{x}; \bm{y}_{eve})\right]^+
\end{equation}

\subsection{Composite Security Metrics}

\subsubsection{Weighted Security Index (WSI)}
\begin{equation}
\text{WSI} = w_s \cdot \frac{R_{sense}}{R_{sense}^{max}} + w_c \cdot \frac{R_{comm}}{R_{comm}^{max}}
\end{equation}

\section{Optimization Framework}

The security-oriented optimization problem can be formulated as:
\begin{align}
\max_{\bm{Q}_x} \quad & \text{JSI}(\bm{Q}_x) \\
\text{s.t.} \quad & \tr(\bm{Q}_x) \leq P_{max} \\
& \text{SINR}_k \geq \Gamma_k, \quad \forall k \\
& \text{CRB}_{BS}(\theta_L) \leq \gamma_{sense} \\
& \bm{Q}_x \succeq 0
\end{align}

\section{Numerical Results}

In this section, we present numerical results to validate the proposed joint CRB-based security framework. We consider a monostatic ISAC system with $N_{BS} = 8$ antennas at the base station, $N_E = 4$ antennas at the eavesdropper, and $K = 2$ communication users.

The simulation results demonstrate that:
\begin{itemize}
\item The joint CRB metrics provide more accurate security assessment compared to separate metrics
\item The JSI effectively captures the trade-off between sensing and communication security
\item The proposed framework enables efficient joint optimization of security performance
\end{itemize}

\section{Conclusions}

In this paper, we have proposed a comprehensive joint CRB-based security framework for monostatic ISAC systems. The key innovation is the recognition that sensing and communication security are fundamentally coupled estimation problems that should be analyzed jointly rather than separately. The proposed JSI and WJCRB metrics provide unified, theoretically rigorous tools for security evaluation and optimization in ISAC systems.

Future work will extend this framework to multi-target scenarios and investigate the impact of imperfect channel state information on the joint security metrics.

\begin{thebibliography}{1}
\bibitem{ref1}
N. Su, F. Liu, Z. Wei, Y.-F. Liu, and C. Masouros, ``Secure dual-functional radar-communication transmission: Exploiting interference for resilience against target eavesdropping,'' \emph{IEEE Transactions on Wireless Communications}, 2022.

\bibitem{ref2}
C. Masouros and G. Zheng, ``Exploiting known interference as green signal power for downlink beamforming optimization,'' \emph{IEEE Transactions on Signal Processing}, vol. 63, no. 14, pp. 3628--3640, 2015.
\end{thebibliography}

\end{document}
