%% RUN_FIG_GENERATION - Generate all FIG format plots
% This script generates interactive MATLAB FIG format plots for the 
% joint mutual information analysis

clear; close all; clc;

fprintf('=== FIG Format Plot Generation ===\n');

%% Check if simulation results exist
if ~exist('joint_mutual_info_results.mat', 'file')
    fprintf('Simulation results not found. Running simulation first...\n');
    try
        results = joint_mutual_info_simulation();
        fprintf('Simulation completed successfully.\n');
    catch ME
        fprintf('Error during simulation: %s\n', ME.message);
        return;
    end
else
    fprintf('Found existing simulation results.\n');
end

%% Generate FIG format plots
try
    fprintf('Generating FIG format plots...\n');
    tic;
    
    % Run the FIG plot generation function
    generate_fig_plots();
    
    generation_time = toc;
    fprintf('FIG plot generation completed in %.2f seconds.\n', generation_time);
    
catch ME
    fprintf('Error during FIG plot generation:\n');
    fprintf('Error message: %s\n', ME.message);
    fprintf('Error location: %s (line %d)\n', ME.stack(1).name, ME.stack(1).line);
    return;
end

%% List generated FIG files
fprintf('\n=== Generated FIG Files ===\n');
fig_files = dir('*.fig');
if ~isempty(fig_files)
    for i = 1:length(fig_files)
        fprintf('%d. %s (%.2f KB)\n', i, fig_files(i).name, fig_files(i).bytes/1024);
    end
else
    fprintf('No FIG files found.\n');
end

%% Provide usage instructions
fprintf('\n=== Usage Instructions ===\n');
fprintf('To open any FIG file in MATLAB:\n');
fprintf('  >> openfig(''filename.fig'')\n');
fprintf('  >> openfig(''filename.fig'', ''reuse'')  %% Reuse existing figure\n');
fprintf('  >> openfig(''filename.fig'', ''new'')    %% Always create new figure\n\n');

fprintf('To open all FIG files at once:\n');
fprintf('  >> open_all_figs()\n\n');

fprintf('To convert FIG to other formats:\n');
fprintf('  >> fig_to_png(''filename.fig'')     %% Convert to PNG\n');
fprintf('  >> fig_to_pdf(''filename.fig'')     %% Convert to PDF\n');
fprintf('  >> fig_to_eps(''filename.fig'')     %% Convert to EPS\n\n');

%% Create utility functions
create_utility_functions();

fprintf('FIG generation complete! All plots are now available as interactive MATLAB figures.\n');

%% ========================================================================
%% UTILITY FUNCTIONS
%% ========================================================================

function create_utility_functions()
% Create utility functions for working with FIG files

%% Function to open all FIG files
fid = fopen('open_all_figs.m', 'w');
fprintf(fid, 'function open_all_figs()\n');
fprintf(fid, '%% OPEN_ALL_FIGS - Open all FIG files in the current directory\n\n');
fprintf(fid, 'fig_files = dir(''*.fig'');\n');
fprintf(fid, 'if isempty(fig_files)\n');
fprintf(fid, '    fprintf(''No FIG files found in current directory.\\n'');\n');
fprintf(fid, '    return;\n');
fprintf(fid, 'end\n\n');
fprintf(fid, 'fprintf(''Opening %%d FIG files...\\n'', length(fig_files));\n');
fprintf(fid, 'for i = 1:length(fig_files)\n');
fprintf(fid, '    fprintf(''Opening: %%s\\n'', fig_files(i).name);\n');
fprintf(fid, '    openfig(fig_files(i).name, ''new'');\n');
fprintf(fid, 'end\n\n');
fprintf(fid, 'fprintf(''All FIG files opened successfully.\\n'');\n');
fprintf(fid, 'end\n');
fclose(fid);

%% Function to convert FIG to PNG
fid = fopen('fig_to_png.m', 'w');
fprintf(fid, 'function fig_to_png(fig_filename, varargin)\n');
fprintf(fid, '%% FIG_TO_PNG - Convert FIG file to PNG format\n');
fprintf(fid, '%% Usage: fig_to_png(''filename.fig'', ''resolution'', 300)\n\n');
fprintf(fid, 'p = inputParser;\n');
fprintf(fid, 'addRequired(p, ''fig_filename'', @ischar);\n');
fprintf(fid, 'addParameter(p, ''resolution'', 300, @isnumeric);\n');
fprintf(fid, 'parse(p, fig_filename, varargin{:});\n\n');
fprintf(fid, 'if ~exist(p.Results.fig_filename, ''file'')\n');
fprintf(fid, '    error(''FIG file not found: %%s'', p.Results.fig_filename);\n');
fprintf(fid, 'end\n\n');
fprintf(fid, 'fig_handle = openfig(p.Results.fig_filename, ''invisible'');\n');
fprintf(fid, '[~, name, ~] = fileparts(p.Results.fig_filename);\n');
fprintf(fid, 'png_filename = [name ''.png''];\n\n');
fprintf(fid, 'print(fig_handle, png_filename, ''-dpng'', sprintf(''-r%%d'', p.Results.resolution));\n');
fprintf(fid, 'close(fig_handle);\n\n');
fprintf(fid, 'fprintf(''Converted %%s to %%s\\n'', p.Results.fig_filename, png_filename);\n');
fprintf(fid, 'end\n');
fclose(fid);

%% Function to convert FIG to PDF
fid = fopen('fig_to_pdf.m', 'w');
fprintf(fid, 'function fig_to_pdf(fig_filename)\n');
fprintf(fid, '%% FIG_TO_PDF - Convert FIG file to PDF format\n');
fprintf(fid, '%% Usage: fig_to_pdf(''filename.fig'')\n\n');
fprintf(fid, 'if ~exist(fig_filename, ''file'')\n');
fprintf(fid, '    error(''FIG file not found: %%s'', fig_filename);\n');
fprintf(fid, 'end\n\n');
fprintf(fid, 'fig_handle = openfig(fig_filename, ''invisible'');\n');
fprintf(fid, '[~, name, ~] = fileparts(fig_filename);\n');
fprintf(fid, 'pdf_filename = [name ''.pdf''];\n\n');
fprintf(fid, 'print(fig_handle, pdf_filename, ''-dpdf'', ''-bestfit'');\n');
fprintf(fid, 'close(fig_handle);\n\n');
fprintf(fid, 'fprintf(''Converted %%s to %%s\\n'', fig_filename, pdf_filename);\n');
fprintf(fid, 'end\n');
fclose(fid);

%% Function to convert FIG to EPS
fid = fopen('fig_to_eps.m', 'w');
fprintf(fid, 'function fig_to_eps(fig_filename)\n');
fprintf(fid, '%% FIG_TO_EPS - Convert FIG file to EPS format\n');
fprintf(fid, '%% Usage: fig_to_eps(''filename.fig'')\n\n');
fprintf(fid, 'if ~exist(fig_filename, ''file'')\n');
fprintf(fid, '    error(''FIG file not found: %%s'', fig_filename);\n');
fprintf(fid, 'end\n\n');
fprintf(fid, 'fig_handle = openfig(fig_filename, ''invisible'');\n');
fprintf(fid, '[~, name, ~] = fileparts(fig_filename);\n');
fprintf(fid, 'eps_filename = [name ''.eps''];\n\n');
fprintf(fid, 'print(fig_handle, eps_filename, ''-depsc'', ''-tiff'');\n');
fprintf(fid, 'close(fig_handle);\n\n');
fprintf(fid, 'fprintf(''Converted %%s to %%s\\n'', fig_filename, eps_filename);\n');
fprintf(fid, 'end\n');
fclose(fid);

fprintf('Created utility functions:\n');
fprintf('  - open_all_figs.m\n');
fprintf('  - fig_to_png.m\n');
fprintf('  - fig_to_pdf.m\n');
fprintf('  - fig_to_eps.m\n');

end
