\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{algorithm}
\usepackage{algorithmic}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\argmin}{\text{argmin}}
\newcommand{\argmax}{\text{argmax}}

\title{Joint Mutual Information Analysis for FD-ISAC Security: \\
Eavesdropper's Information Leakage Assessment}
\author{Unified Information-Theoretic Security Framework}
\date{\today}

\begin{document}
\maketitle

\begin{abstract}
This document derives the joint mutual information expression for assessing information leakage in full-duplex (FD) monostatic ISAC systems from the eavesdropper's perspective. Based on the accurate signal model where the eavesdropper receives $\bm{y}_{\text{eve}}(l) = \bm{G}_E(\theta)\bm{x}(l) + \bm{H}_{CE}^H\bm{x}(l) + \bm{z}_{\text{tar}}(l)$, we analyze how much information about both the target angle $\theta_L$ (sensing information) and communication signals $\bm{x}$ (communication information) can be extracted through the dual-path observation mechanism. This unified approach provides a comprehensive security metric that captures the fundamental trade-offs between sensing privacy and communication secrecy in FD-ISAC systems, accounting for both target-reflected and direct signal paths.
\end{abstract}

\section{Introduction}

\subsection{Motivation}

In full-duplex monostatic ISAC systems, the eavesdropper faces a unique joint information extraction problem due to the dual-path signal observation mechanism. According to the accurate signal model from the PDF document, the eavesdropper receives:

\begin{equation}
\bm{y}_{\text{eve}}(l) = \bm{G}_E(\theta)\bm{x}(l) + \bm{H}_{CE}^H\bm{x}(l) + \bm{z}_{\text{tar}}(l)
\end{equation}

where $\bm{G}_E(\theta) = \beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L)$ represents the target-reflected path and $\bm{H}_{CE}^H$ represents the direct path. This creates a sophisticated eavesdropping scenario where:

\begin{itemize}
    \item The same signal $\bm{x}(l)$ is observed through two distinct paths
    \item The target-reflected path carries sensing information about $\theta_L$
    \item The direct path carries communication information
    \item Both paths contribute to the eavesdropper's joint estimation capability
\end{itemize}

Traditional security metrics evaluate sensing privacy and communication secrecy separately, missing the crucial coupling that emerges from this dual-path observation structure.

\subsection{Key Insight}

The eavesdropper's capability is fundamentally limited by the **joint mutual information**:
\begin{equation}
I(\theta_L, \bm{x}; \bm{y}_{\text{eve}}) = H(\theta_L, \bm{x}) - H(\theta_L, \bm{x} | \bm{y}_{\text{eve}})
\end{equation}

This quantity represents the total information about both sensing and communication that leaks to the eavesdropper through the dual-path FD-ISAC signal structure, accounting for the complex interplay between target geometry, eavesdropper position, and signal design.

\section{FD-ISAC Signal Model and Problem Formulation}

\subsection{Parameter Classification}

\textbf{Important Clarification}: In this FD-ISAC security analysis, we distinguish between known and unknown parameters from the eavesdropper's perspective:

\begin{itemize}
    \item \textbf{Unknown Parameters (to be estimated by eavesdropper)}:
    \begin{itemize}
        \item $\theta_L$: Target angle - sensing information that the eavesdropper wants to extract
        \item $\bm{X}$: FD-ISAC signal matrix - communication information that the eavesdropper wants to recover
    \end{itemize}

    \item \textbf{Known Parameters (available to eavesdropper)}:
    \begin{itemize}
        \item $\theta_E$: Eavesdropper's own angle/location
        \item $\bm{H}_{CE}$: Direct channel from BS to eavesdropper (can be estimated)
        \item $\beta(l)$: Reflection coefficient (may be estimated or assumed)
        \item System parameters: $N_{BS}$, $N_E$, $L$, etc.
    \end{itemize}
\end{itemize}

The joint mutual information $I(\theta_L, \bm{X}; \bm{y}_{\text{eve}})$ quantifies how much information about the unknown parameters $(\theta_L, \bm{X})$ leaks to the eavesdropper through its observations $\bm{y}_{\text{eve}}$.

\subsection{System Configuration}

We consider a full-duplex monostatic ISAC system with:
\begin{itemize}
    \item \textbf{FD-ISAC Base Station}: $N_{BS}$ antennas serving dual functions
    \item \textbf{Target}: Passive reflector at angle $\theta_L$
    \item \textbf{Communication Users}: $K$ single-antenna legitimate users
    \item \textbf{Eavesdropper}: $N_E$ antennas attempting to intercept information
\end{itemize}

\subsection{Eavesdropper's Received Signal}

Based on the accurate FD-ISAC signal model from the PDF document, the eavesdropper receives:
\begin{equation}
\bm{y}_{\text{eve}}(l) = \bm{G}_E(\theta)\bm{x}(l) + \bm{H}_{CE}^H\bm{x}(l) + \bm{z}_{\text{tar}}(l)
\end{equation}

where:
\begin{itemize}
    \item $\bm{G}_E(\theta) = \beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L) \in \C^{N_E \times N_{BS}}$ is the target-reflected channel
    \item $\beta(l)$ is the complex reflection coefficient toward the eavesdropper
    \item $\theta_E$ is the angle of arrival at the eavesdropper
    \item $\theta_L$ is the target angle
    \item $\bm{b}(\theta_E) \in \C^{N_E \times 1}$ is the eavesdropper's array steering vector
    \item $\bm{a}(\theta_L) \in \C^{N_{BS} \times 1}$ is the BS array steering vector
    \item $\bm{H}_{CE}^H \in \C^{N_{BS} \times N_E}$ is the direct channel from BS to eavesdropper
    \item $\bm{x}(l) \in \C^{N_{BS} \times 1}$ is the FD-ISAC signal (serving both sensing and communication)
    \item $\bm{z}_{\text{tar}}(l) \sim \mathcal{CN}(\bm{0}, \sigma^2_{\text{tar}} \bm{I}_{N_E})$ is the noise
\end{itemize}

This can be written compactly as:
\begin{equation}
\bm{y}_{\text{eve}}(l) = \bm{H}_{\text{eff}}(\theta_L, \theta_E)\bm{x}(l) + \bm{z}_{\text{tar}}(l)
\end{equation}

where $\bm{H}_{\text{eff}}(\theta_L) = \bm{G}_E(\theta_L, \theta_E) + \bm{H}_{CE}^H$ is the effective eavesdropping channel that depends on the unknown target angle $\theta_L$ (to be estimated) and the known eavesdropper angle $\theta_E$.

\subsection{Joint Parameter Space}

The eavesdropper attempts to jointly estimate:
\begin{itemize}
    \item \textbf{Target angle}: $\theta_L \in [0, 2\pi)$ (sensing information) - \emph{unknown parameter to be estimated}
    \item \textbf{FD-ISAC signals}: $\bm{X} = [\bm{x}(1), \ldots, \bm{x}(L)] \in \C^{N_{BS} \times L}$ (communication information) - \emph{unknown signals to be recovered}
\end{itemize}

\textbf{Important Note}: The eavesdropper angle $\theta_E$ is assumed to be known to the eavesdropper (its own location), and is therefore not an estimation parameter.

\textbf{Key Insight for FD-ISAC}: The eavesdropper observes the same signal $\bm{x}(l)$ through two paths:
\begin{enumerate}
    \item \textbf{Target-reflected path}: $\bm{G}_E(\theta) = \beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L)$ - carries sensing information about $\theta_L$
    \item \textbf{Direct path}: $\bm{H}_{CE}^H$ - carries communication information
\end{enumerate}

This dual-path observation creates strong coupling between sensing and communication information extraction, making joint estimation particularly beneficial for the eavesdropper.

\subsection{Information Leakage Decomposition}

The joint mutual information can be decomposed as:
\begin{align}
I(\theta_L, \bm{X}; \bm{Y}_{\text{eve}}) &= I(\theta_L; \bm{Y}_{\text{eve}}) + I(\bm{X}; \bm{Y}_{\text{eve}} | \theta_L) \\
&= I(\bm{X}; \bm{Y}_{\text{eve}}) + I(\theta_L; \bm{Y}_{\text{eve}} | \bm{X})
\end{align}

This reveals two attack perspectives for the eavesdropper:
\begin{itemize}
    \item \textbf{Sensing-first attack}: Extract target information first, then use it to improve signal recovery
    \item \textbf{Communication-first attack}: Recover FD-ISAC signals first, then use them to estimate target location
\end{itemize}

\subsection{FD-ISAC Specific Coupling}

In FD-ISAC systems, the coupling is particularly strong because:
\begin{equation}
\bm{G}_E(\theta) = \beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L)
\end{equation}

This structure reveals several key coupling mechanisms:
\begin{itemize}
    \item \textbf{Geometric coupling}: Both $\theta_L$ (target angle) and $\theta_E$ (eavesdropper angle) affect the channel
    \item \textbf{Signal coupling}: The same $\bm{x}(l)$ is observed through both reflected and direct paths
    \item \textbf{Temporal coupling}: The reflection coefficient $\beta(l)$ varies with time
    \item \textbf{Array coupling}: Both BS array $\bm{a}(\theta_L)$ and eavesdropper array $\bm{b}(\theta_E)$ contribute
\end{itemize}

The target angle $\theta_L$ directly affects the sensing channel strength, which in turn affects how well the eavesdropper can recover the communication content from the same signal $\bm{x}(l)$.

\section{Detailed Derivation}

\subsection{Vectorized Signal Model}

For $L$ time slots, vectorize the observations:
\begin{equation}
\bm{y}_{\text{vec}} = \text{vec}(\bm{Y}_{\text{eve}}) = (\bm{I}_L \otimes \bm{H}_{\text{eff}}(\theta_L)) \text{vec}(\bm{X}) + \bm{z}_{\text{vec}}
\end{equation}

where:
\begin{itemize}
    \item $\bm{y}_{\text{vec}} \in \C^{LN_E \times 1}$ - vectorized eavesdropper observations
    \item $\text{vec}(\bm{X}) \in \C^{LN_{BS} \times 1}$ - vectorized FD-ISAC signals (unknown)
    \item $\bm{z}_{\text{vec}} \sim \mathcal{CN}(\bm{0}, \sigma^2_{\text{tar}} \bm{I}_{LN_E})$ - vectorized noise
    \item $\bm{I}_L \otimes \bm{H}_{\text{eff}}(\theta_L)$ - Kronecker product capturing temporal structure
    \item $\bm{H}_{\text{eff}}(\theta_L) = \beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L) + \bm{H}_{CE}^H$ - depends on unknown $\theta_L$
\end{itemize}

\textbf{FD-ISAC Interpretation}: The vectorized model captures how the eavesdropper observes the same FD-ISAC signal through both target-reflected path (dependent on unknown $\theta_L$) and direct path over multiple time slots. The eavesdropper knows its own angle $\theta_E$ but must estimate the target angle $\theta_L$.

\subsection{Prior Distributions}

\subsubsection{Target Angle Prior}

Assume uniform prior for target angle:
\begin{equation}
p(\theta_L) = \frac{1}{2\pi}, \quad \theta_L \in [0, 2\pi)
\end{equation}

Therefore: $H(\theta_L) = \log(2\pi)$

\subsubsection{FD-ISAC Signal Prior}

For Gaussian FD-ISAC signals with covariance $\bm{Q}_x$:
\begin{equation}
\text{vec}(\bm{X}) \sim \mathcal{CN}(\bm{0}, \bm{I}_L \otimes \bm{Q}_x)
\end{equation}

The entropy is:
\begin{equation}
H(\bm{X}) = L \log \det(\pi e \bm{Q}_x)
\end{equation}

\textbf{FD-ISAC Design Consideration}: The covariance matrix $\bm{Q}_x$ must be designed to satisfy both sensing and communication requirements:
\begin{itemize}
    \item \textbf{Sensing}: Sufficient power in directions of interest for target detection
    \item \textbf{Communication}: Appropriate beamforming for user coverage
    \item \textbf{Security}: Minimize information leakage to eavesdroppers
\end{itemize}

\subsection{Joint Prior Distribution}

Assuming independence between target angle and FD-ISAC signals:
\begin{equation}
H(\theta_L, \bm{X}) = H(\theta_L) + H(\bm{X}) = \log(2\pi) + L \log \det(\pi e \bm{Q}_x)
\end{equation}

\textbf{Independence Assumption Justification}: In FD-ISAC systems, the target location $\theta_L$ is typically independent of the transmitted signal design $\bm{X}$, as the signal is designed based on communication requirements and sensing coverage, not specific target locations.

\subsection{Conditional Entropy Computation}

\subsubsection{Likelihood Function}

Given the unknown target angle $\theta_L$ and unknown signals $\bm{X}$, the likelihood based on the accurate PDF signal model is:
\begin{equation}
p(\bm{y}_{\text{vec}} | \theta_L, \bm{X}) = \frac{1}{(\pi \sigma^2_{\text{tar}})^{LN_E}} \exp\left(-\frac{\|\bm{y}_{\text{vec}} - (\bm{I}_L \otimes \bm{H}_{\text{eff}}(\theta_L))\text{vec}(\bm{X})\|^2}{\sigma^2_{\text{tar}}}\right)
\end{equation}

where the effective channel matrix from the PDF model is:
\begin{equation}
\bm{H}_{\text{eff}}(\theta_L) = \beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L) + \bm{H}_{CE}^H
\end{equation}

Note that $\theta_E$ (eavesdropper angle) is known to the eavesdropper, while $\theta_L$ (target angle) is the unknown parameter to be estimated.

\textbf{Dual-Path Signal Model Analysis}:
\begin{itemize}
    \item \textbf{Target-reflected component}: $\beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L)$ depends on both target angle $\theta_L$ and eavesdropper angle $\theta_E$
    \item \textbf{Direct component}: $\bm{H}_{CE}^H$ provides direct observation of the communication signal
    \item \textbf{Reflection coefficient}: $\beta(l)$ controls the strength of sensing information leakage
    \item \textbf{Geometric coupling}: The Kronecker structure $\bm{b}(\theta_E) \bm{a}^H(\theta_L)$ creates fundamental coupling between target and eavesdropper geometries
    \item \textbf{Joint estimation benefit}: The dual-path observation allows the eavesdropper to leverage both sensing and communication information simultaneously
\end{itemize}

\subsubsection{Conditional Entropy - Gaussian Approximation}

For computational tractability, we use Gaussian approximations for the posterior distributions.

\textbf{Posterior of FD-ISAC signals given angle}:
\begin{equation}
p(\bm{X} | \bm{y}_{\text{vec}}, \theta_L) \approx \mathcal{CN}(\hat{\bm{X}}(\theta_L), \bm{\Sigma}_{\bm{X}|\theta_L})
\end{equation}

where:
\begin{align}
\hat{\bm{X}}(\theta_L) &= (\bm{I}_L \otimes \bm{Q}_x \bm{H}_{\text{eff}}^H(\theta_L)) (\bm{I}_L \otimes \bm{\Sigma}_y^{-1}(\theta_L)) \bm{y}_{\text{vec}} \\
\bm{\Sigma}_{\bm{X}|\theta_L} &= \bm{I}_L \otimes [\bm{Q}_x - \bm{Q}_x \bm{H}_{\text{eff}}^H(\theta_L) \bm{\Sigma}_y^{-1}(\theta_L) \bm{H}_{\text{eff}}(\theta_L) \bm{Q}_x]
\end{align}

with $\bm{\Sigma}_y(\theta_L) = \bm{H}_{\text{eff}}(\theta_L) \bm{Q}_x \bm{H}_{\text{eff}}^H(\theta_L) + \sigma^2_{\text{tar}} \bm{I}_{N_E}$.

\textbf{FD-ISAC Insight}: The posterior covariance $\bm{\Sigma}_{\bm{X}|\theta_L}$ shows that signal estimation accuracy depends on the unknown target angle $\theta_L$ through the effective channel. The eavesdropper knows its own angle $\theta_E$, but the uncertainty in $\theta_L$ affects both sensing and communication signal recovery. The reflection coefficient $\beta(l)$ determines the strength of the target-reflected path.

\textbf{Posterior of target angle given FD-ISAC signals}:
\begin{equation}
p(\theta_L | \bm{y}_{\text{vec}}, \bm{X}) \propto p(\bm{y}_{\text{vec}} | \theta_L, \bm{X}) p(\theta_L)
\end{equation}

This is generally non-Gaussian, but can be approximated using:
\begin{equation}
p(\theta_L | \bm{y}_{\text{vec}}, \bm{X}) \approx \mathcal{N}(\hat{\theta}_L, \text{CRB}_{\text{eve}}(\theta_L))
\end{equation}

where $\text{CRB}_{\text{eve}}(\theta_L)$ is the Cramér-Rao bound for target angle estimation by the eavesdropper.

\textbf{FD-ISAC Specific CRB}: The CRB depends on the FD-ISAC signal design and system geometry:
\begin{equation}
\text{CRB}_{\text{eve}}(\theta_L) = \left[\frac{2L|\beta(l)|^2}{\sigma^2_{\text{tar}}} \text{Re}\left\{\text{tr}\left(\frac{\partial (\bm{b}(\theta_E) \bm{a}^H(\theta_L))^H}{\partial \theta_L} \bm{Q}_x \frac{\partial (\bm{b}(\theta_E) \bm{a}^H(\theta_L))}{\partial \theta_L}\right)\right\}\right]^{-1}
\end{equation}

This simplifies to:
\begin{equation}
\text{CRB}_{\text{eve}}(\theta_L) = \left[\frac{2L|\beta(l)|^2}{\sigma^2_{\text{tar}}} \|\bm{b}(\theta_E)\|^2 \text{Re}\left\{\frac{\partial \bm{a}^H(\theta_L)}{\partial \theta_L} \bm{Q}_x \frac{\partial \bm{a}(\theta_L)}{\partial \theta_L}\right\}\right]^{-1}
\end{equation}

\subsection{Joint Mutual Information Expression for FD-ISAC}

\subsubsection{Exact Expression}

The joint mutual information for FD-ISAC systems quantifies how much information about the unknown parameters $(\theta_L, \bm{X})$ leaks to the eavesdropper:
\begin{align}
&I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) \\
&= \int \int p(\theta_L, \bm{x}, \bm{y}_{\text{vec}}) \log \frac{p(\theta_L, \bm{x} | \bm{y}_{\text{vec}})}{p(\theta_L, \bm{x})} d\theta_L d\bm{x}
\end{align}

where $\theta_L$ is the unknown target angle and $\bm{X}$ are the unknown FD-ISAC signals that the eavesdropper attempts to estimate jointly.

\subsubsection{Gaussian Approximation for FD-ISAC}

Under Gaussian approximations:
\begin{align}
I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) &\approx H(\theta_L) + H(\bm{X}) - H(\theta_L, \bm{X} | \bm{y}_{\text{vec}}) \\
&= \log(2\pi) + L \log \det(\pi e \bm{Q}_x) - H(\theta_L, \bm{X} | \bm{y}_{\text{vec}})
\end{align}

\textbf{FD-ISAC Specific Interpretation}: This expression quantifies how much information about both the target location and the FD-ISAC signal content leaks to the eavesdropper through the combined sensing and communication channels.

\subsubsection{Conditional Entropy Approximation for FD-ISAC}

The conditional entropy can be approximated as:
\begin{equation}
H(\theta_L, \bm{X} | \bm{y}_{\text{vec}}) \approx \frac{1}{2} \log \det(2\pi e \bm{C}_{\text{joint}})
\end{equation}

where $\bm{C}_{\text{joint}}$ is the joint covariance matrix of the posterior distribution:
\begin{equation}
\bm{C}_{\text{joint}} = \begin{bmatrix}
\text{CRB}_{\text{eve}}(\theta_L) & \bm{C}_{\theta,\bm{x}} \\
\bm{C}_{\bm{x},\theta} & \bm{\Sigma}_{\bm{X}|\text{avg}}
\end{bmatrix}
\end{equation}

\textbf{FD-ISAC Cross-Correlation Terms}: The off-diagonal terms $\bm{C}_{\theta,\bm{x}}$ capture the correlation between target angle estimation and FD-ISAC signal recovery:
\begin{equation}
\bm{C}_{\theta,\bm{x}} = \E\left[(\theta_L - \hat{\theta}_L)(\text{vec}(\bm{X}) - \hat{\bm{X}})^H | \bm{y}_{\text{vec}}\right]
\end{equation}

This correlation is particularly strong in FD-ISAC systems due to the shared signal structure.

\section{Practical Computation Methods}

\subsection{Monte Carlo Estimation}

\begin{algorithm}
\caption{Monte Carlo Joint Mutual Information Estimation}
\begin{algorithmic}[1]
\REQUIRE Eavesdropper signal $\bm{y}_{\text{vec}}$, system parameters
\ENSURE Joint mutual information $I(\theta_L, \bm{X}; \bm{y}_{\text{vec}})$

\STATE Generate samples $\{(\theta_L^{(i)}, \bm{X}^{(i)})\}_{i=1}^N$ from prior
\STATE Compute likelihood weights $w^{(i)} = p(\bm{y}_{\text{vec}} | \theta_L^{(i)}, \bm{X}^{(i)})$
\STATE Normalize weights: $\tilde{w}^{(i)} = w^{(i)} / \sum_j w^{(j)}$
\STATE Estimate posterior entropy:
\STATE $\hat{H}(\theta_L, \bm{X} | \bm{y}_{\text{vec}}) = -\sum_{i=1}^N \tilde{w}^{(i)} \log \tilde{w}^{(i)}$
\STATE Compute mutual information:
\STATE $\hat{I} = H(\theta_L, \bm{X}) - \hat{H}(\theta_L, \bm{X} | \bm{y}_{\text{vec}})$
\RETURN $\hat{I}$
\end{algorithmic}
\end{algorithm}

\subsection{Variational Approximation}

For real-time computation, use variational inference:
\begin{equation}
q(\theta_L, \bm{X} | \bm{y}_{\text{vec}}) = q(\theta_L | \bm{y}_{\text{vec}}) q(\bm{X} | \bm{y}_{\text{vec}})
\end{equation}

The mutual information is approximated as:
\begin{equation}
I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) \approx H(\theta_L) + H(\bm{X}) - H(q(\theta_L)) - H(q(\bm{X}))
\end{equation}

\section{Security Metric Definition}

\subsection{FD-ISAC Joint Information Leakage Index (FD-JILI)}

Define the normalized joint information leakage for FD-ISAC systems:
\begin{equation}
\text{FD-JILI} = \frac{I(\theta_L, \bm{X}; \bm{y}_{\text{vec}})}{H(\theta_L, \bm{X})}
\end{equation}

\textbf{Interpretation for FD-ISAC}:
\begin{itemize}
    \item $\text{FD-JILI} = 0$: Perfect security - no information leakage about target or signals
    \item $\text{FD-JILI} = 1$: Complete compromise - eavesdropper can fully recover both sensing and communication information
    \item Lower FD-JILI indicates better overall security
    \item FD-JILI captures the inherent coupling in FD-ISAC systems
\end{itemize}

\subsection{FD-ISAC Weighted Information Leakage Metric}

For FD-ISAC system design, use weighted combination:
\begin{equation}
\text{FD-WILM} = w_s \cdot \frac{I(\theta_L; \bm{y}_{\text{vec}})}{H(\theta_L)} + w_c \cdot \frac{I(\bm{X}; \bm{y}_{\text{vec}})}{H(\bm{X})} + w_{coup} \cdot \frac{I(\theta_L; \bm{X} | \bm{y}_{\text{vec}})}{H(\theta_L, \bm{X})}
\end{equation}

where:
\begin{itemize}
    \item $w_s$: Weight for sensing privacy (target location secrecy)
    \item $w_c$: Weight for communication secrecy (signal content protection)
    \item $w_{coup}$: Weight for coupling penalty (information synergy)
    \item $w_s + w_c + w_{coup} = 1$
\end{itemize}

\textbf{FD-ISAC Design Insight}: The coupling term $w_{coup}$ is particularly important in FD-ISAC systems as it penalizes designs where knowing one type of information significantly helps in estimating the other.

\section{Coupling Analysis and Decomposition}

\subsection{Information Coupling Quantification}

The coupling between sensing and communication information can be quantified by:
\begin{equation}
I_{\text{coupling}} = I(\theta_L; \bm{X} | \bm{y}_{\text{vec}})
\end{equation}

This represents how much knowing one parameter helps in estimating the other, given the eavesdropper's observation.

\subsection{Decomposition Identity}

The joint mutual information satisfies:
\begin{align}
I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) &= I(\theta_L; \bm{y}_{\text{vec}}) + I(\bm{X}; \bm{y}_{\text{vec}}) - I_{\text{coupling}}
\end{align}

\textbf{Physical Interpretation}:
\begin{itemize}
    \item When $I_{\text{coupling}} = 0$: Independent information leakage
    \item When $I_{\text{coupling}} > 0$: Synergistic leakage (knowing one helps estimate the other)
    \item When $I_{\text{coupling}} < 0$: Competitive leakage (trade-off between estimations)
\end{itemize}

\subsection{Channel-Dependent Coupling}

The coupling strength depends on the effective channel structure:
\begin{equation}
I_{\text{coupling}} \propto \left\|\frac{\partial \bm{H}_{\text{eff}}(\theta_L)}{\partial \theta_L}\right\|_F^2 \cdot \|\bm{Q}_x\|_F
\end{equation}

This shows that coupling increases with:
\begin{itemize}
    \item Stronger angle-dependence of the eavesdropping channel
    \item Higher signal power and correlation
\end{itemize}

\section{Optimization Framework}

\subsection{Security-Aware FD-ISAC Signal Design}

The robust FD-ISAC design problem becomes:
\begin{align}
\min_{\bm{Q}_x} \quad & I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) \\
\text{s.t.} \quad & \tr(\bm{Q}_x) \leq P_{\max} \\
& \text{SINR}_k \geq \Gamma_k, \quad \forall k \\
& \text{CRB}_{\text{BS}}(\theta_L) \leq \gamma_{\text{sense}} \\
& \|\bm{Q}_x \bm{a}(\theta_L)\|^2 \geq P_{\text{sense}} \\
& \bm{Q}_x \succeq 0
\end{align}

\textbf{FD-ISAC Specific Constraints}:
\begin{itemize}
    \item \textbf{Communication QoS}: $\text{SINR}_k \geq \Gamma_k$ ensures user service quality
    \item \textbf{Sensing Performance}: $\text{CRB}_{\text{BS}}(\theta_L) \leq \gamma_{\text{sense}}$ maintains target detection capability
    \item \textbf{Sensing Power}: $\|\bm{Q}_x \bm{a}(\theta_L)\|^2 \geq P_{\text{sense}}$ ensures sufficient power for sensing
    \item \textbf{Security Objective}: Minimize joint information leakage to eavesdroppers
\end{itemize}

\subsection{Successive Convex Approximation}

Since the mutual information is generally non-convex in $\bm{Q}_x$, we use SCA:

\subsubsection{First-Order Approximation}

At iteration $n$:
\begin{equation}
I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) \approx I^{(n)} + \tr\left(\nabla_{\bm{Q}_x} I^{(n)} (\bm{Q}_x - \bm{Q}_x^{(n)})\right)
\end{equation}

\subsubsection{Gradient Computation for FD-ISAC}

The gradient with respect to FD-ISAC signal covariance is:
\begin{equation}
\frac{\partial I}{\partial \bm{Q}_x} = \frac{\partial H(\bm{X})}{\partial \bm{Q}_x} - \frac{\partial H(\theta_L, \bm{X} | \bm{y}_{\text{vec}})}{\partial \bm{Q}_x}
\end{equation}

\textbf{FD-ISAC Specific Gradient Terms}:
\begin{align}
\frac{\partial H(\bm{X})}{\partial \bm{Q}_x} &= L \bm{Q}_x^{-1} \\
\frac{\partial H(\theta_L, \bm{X} | \bm{y}_{\text{vec}})}{\partial \bm{Q}_x} &= \frac{\partial}{\partial \bm{Q}_x} \left[\frac{1}{2} \log \det(\bm{C}_{\text{joint}})\right]
\end{align}

The second term involves the derivative of the joint covariance matrix, which captures how the FD-ISAC signal design affects both sensing and communication information leakage simultaneously.

\section{Numerical Implementation}

\subsection{Algorithm Summary}

\begin{algorithm}
\caption{Joint Mutual Information Computation for FD-ISAC (PDF Model)}
\begin{algorithmic}[1]
\REQUIRE System parameters: $\bm{H}_{CE}^H$, $\beta(l)$, $\bm{a}(\cdot)$, $\bm{b}(\theta_E)$, $\bm{Q}_x$, $\sigma^2_{\text{tar}}$
\REQUIRE Eavesdropper signal: $\bm{y}_{\text{vec}}$
\REQUIRE Known eavesdropper angle: $\theta_E$ (eavesdropper knows its own location)
\ENSURE Joint mutual information $I(\theta_L, \bm{X}; \bm{y}_{\text{vec}})$ for unknown $(\theta_L, \bm{X})$

\STATE \textbf{Step 1}: Compute prior entropies
\STATE $H(\theta_L) = \log(2\pi)$
\STATE $H(\bm{X}) = L \log \det(\pi e \bm{Q}_x)$

\STATE \textbf{Step 2}: Estimate posterior distributions for unknown parameters
\FOR{$\theta_L$ in discretized range (unknown target angle)}
    \STATE Compute target-reflected channel: $\bm{G}_E(\theta_L) = \beta(l) \bm{b}(\theta_E) \bm{a}^H(\theta_L)$
    \STATE Compute effective channel: $\bm{H}_{\text{eff}}(\theta_L) = \bm{G}_E(\theta_L) + \bm{H}_{CE}^H$
    \STATE Compute posterior $p(\bm{X} | \bm{y}_{\text{vec}}, \theta_L)$ for unknown signals
    \STATE Compute likelihood $p(\bm{y}_{\text{vec}} | \theta_L)$ for unknown target angle
\ENDFOR

\STATE \textbf{Step 3}: Compute joint posterior entropy
\STATE Account for dual-path coupling in entropy estimation
\STATE $H(\theta_L, \bm{X} | \bm{y}_{\text{vec}}) = -\E[\log p(\theta_L, \bm{X} | \bm{y}_{\text{vec}})]$

\STATE \textbf{Step 4}: Compute mutual information
\STATE $I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) = H(\theta_L) + H(\bm{X}) - H(\theta_L, \bm{X} | \bm{y}_{\text{vec}})$

\RETURN $I(\theta_L, \bm{X}; \bm{y}_{\text{vec}})$
\end{algorithmic}
\end{algorithm}

\subsection{Computational Complexity}

\begin{itemize}
    \item \textbf{Exact computation}: $\mathcal{O}(N_{\theta} \cdot N_{BS}^3 \cdot L^3)$ where $N_{\theta}$ is angle discretization
    \item \textbf{Monte Carlo}: $\mathcal{O}(N_{\text{samples}} \cdot N_{BS}^2 \cdot L^2)$
    \item \textbf{Variational}: $\mathcal{O}(N_{\text{iter}} \cdot N_{BS}^2 \cdot L^2)$
\end{itemize}

\section{Validation and Comparison}

\subsection{Consistency Checks}

\begin{enumerate}
    \item \textbf{Non-negativity}: $I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) \geq 0$
    \item \textbf{Upper bound}: $I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) \leq H(\theta_L, \bm{X})$
    \item \textbf{Monotonicity}: Increasing with SNR
    \item \textbf{Subadditivity}: $I(\theta_L, \bm{X}; \bm{y}_{\text{vec}}) \leq I(\theta_L; \bm{y}_{\text{vec}}) + I(\bm{X}; \bm{y}_{\text{vec}})$
\end{enumerate}

\subsection{Comparison with Existing Metrics}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
Metric & Unified & Coupling & Information-Theoretic & Optimization-Friendly \\
\hline
WSI & Yes & No & Partial & Yes \\
JSI & Yes & Yes & No & Yes \\
EPSD & Yes & Yes & Yes & Moderate \\
\textbf{JILI} & \textbf{Yes} & \textbf{Yes} & \textbf{Yes} & \textbf{Yes} \\
\hline
\end{tabular}
\caption{Comparison of security metrics}
\end{table}

\section{Conclusions and Future Work}

\subsection{Key Contributions}

\begin{enumerate}
    \item \textbf{Accurate Signal Model Integration}: Derived joint mutual information expression based on the precise PDF signal model $\bm{y}_{\text{eve}}(l) = \bm{G}_E(\theta)\bm{x}(l) + \bm{H}_{CE}^H\bm{x}(l) + \bm{z}_{\text{tar}}(l)$
    \item \textbf{Dual-Path Analysis}: Characterized information leakage through both target-reflected and direct paths
    \item \textbf{Geometric Coupling Quantification}: Analyzed the impact of both target angle $\theta_L$ and eavesdropper angle $\theta_E$ on security
    \item \textbf{Practical Algorithms}: Provided computation methods accounting for the Kronecker structure $\bm{b}(\theta_E) \bm{a}^H(\theta_L)$
    \item \textbf{Reflection Coefficient Impact}: Quantified how $\beta(l)$ affects sensing information leakage strength
\end{enumerate}

\subsection{Advantages of Dual-Path Joint Approach}

\begin{itemize}
    \item \textbf{Model Accuracy}: Based on the exact PDF signal model, not approximations
    \item \textbf{Dual-Path Awareness}: Captures both direct and reflected signal observations
    \item \textbf{Geometric Realism}: Accounts for actual target and eavesdropper positioning
    \item \textbf{Temporal Dynamics}: Incorporates time-varying reflection coefficient $\beta(l)$
    \item \textbf{Design Optimization}: Enables joint optimization of beamforming and power allocation
    \item \textbf{Security Guarantees}: Provides worst-case information leakage bounds
\end{itemize}

\subsection{Future Directions}

\begin{enumerate}
    \item \textbf{Multi-Target Extension}: Extend to scenarios with multiple targets at different angles
    \item \textbf{Dynamic Reflection Modeling}: Investigate time-varying $\beta(l)$ optimization strategies
    \item \textbf{Eavesdropper Uncertainty}: Handle unknown eavesdropper angle $\theta_E$ scenarios
    \item \textbf{Experimental Validation}: Validate theoretical predictions with real FD-ISAC testbeds
    \item \textbf{Machine Learning Integration}: Develop ML-based approximation for real-time implementation
\end{enumerate}

The dual-path joint mutual information approach, based on the accurate PDF signal model, provides a rigorous foundation for FD-ISAC security analysis. It enables the design of systems that are inherently secure against sophisticated eavesdropping attacks while accounting for the realistic dual-path signal propagation mechanism.

\end{document}
