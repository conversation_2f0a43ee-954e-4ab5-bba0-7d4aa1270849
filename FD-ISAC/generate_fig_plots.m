function generate_fig_plots()
% GENERATE_FIG_PLOTS - Generate MATLAB FIG format plots for joint mutual information analysis
% This function loads the simulation results and creates interactive FIG plots

%% Load simulation results
if ~exist('joint_mutual_info_results.mat', 'file')
    error('Simulation results not found. Please run the simulation first.');
end

fprintf('Loading simulation results...\n');
load('joint_mutual_info_results.mat', 'results');

%% Set up plotting parameters
set(0, 'DefaultAxesFontSize', 12);
set(0, 'DefaultTextFontSize', 12);
set(0, 'DefaultLineLineWidth', 2);
set(0, 'DefaultAxesLineWidth', 1.5);

% Color scheme
colors = [
    0.0000, 0.4470, 0.7410;  % Blue
    0.8500, 0.3250, 0.0980;  % Red
    0.9290, 0.6940, 0.1250;  % Yellow
    0.4940, 0.1840, 0.5560;  % Purple
    0.4660, 0.6740, 0.1880;  % Green
    0.3010, 0.7450, 0.9330;  % Light Blue
    0.6350, 0.0780, 0.1840;  % Dark Red
];

SNR_dB_range = results.params.SNR_dB_range;

%% Figure 1: Joint Mutual Information Components
fprintf('Generating Figure 1: Mutual Information Components...\n');
fig1 = figure('Name', 'Joint Mutual Information Components', 'Position', [100, 100, 800, 600]);
hold on;

plot(SNR_dB_range, real(results.joint_mutual_info), '-o', ...
     'Color', colors(1,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(1,:), ...
     'DisplayName', 'Joint MI');
plot(SNR_dB_range, real(results.sensing_mutual_info), '--s', ...
     'Color', colors(2,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(2,:), ...
     'DisplayName', 'Sensing MI');
plot(SNR_dB_range, real(results.comm_mutual_info), '-.^', ...
     'Color', colors(3,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(3,:), ...
     'DisplayName', 'Communication MI');
plot(SNR_dB_range, real(results.coupling_info), ':', ...
     'Color', colors(4,:), 'LineWidth', 3, 'DisplayName', 'Coupling');

xlabel('SNR (dB)', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('Mutual Information (bits)', 'FontSize', 14, 'FontWeight', 'bold');
title('Joint Mutual Information Analysis for FD-ISAC Systems', ...
      'FontSize', 16, 'FontWeight', 'bold');
legend('show', 'Location', 'best', 'FontSize', 12);
grid on;
grid minor;
xlim([min(SNR_dB_range), max(SNR_dB_range)]);

% Save as FIG file
savefig(fig1, 'joint_mutual_info_components.fig');
fprintf('Saved: joint_mutual_info_components.fig\n');

%% Figure 2: Security Metrics
fprintf('Generating Figure 2: Security Metrics...\n');
fig2 = figure('Name', 'Security Metrics', 'Position', [200, 200, 800, 600]);

subplot(2, 1, 1);
plot(SNR_dB_range, real(results.FD_JILI), '-o', ...
     'Color', colors(1,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(1,:));
xlabel('SNR (dB)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('FD-JILI', 'FontSize', 12, 'FontWeight', 'bold');
title('FD-ISAC Joint Information Leakage Index', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
grid minor;

subplot(2, 1, 2);
plot(SNR_dB_range, real(results.FD_WILM), '-s', ...
     'Color', colors(2,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(2,:));
xlabel('SNR (dB)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('FD-WILM', 'FontSize', 12, 'FontWeight', 'bold');
title('FD-ISAC Weighted Information Leakage Metric', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
grid minor;

% Save as FIG file
savefig(fig2, 'security_metrics.fig');
fprintf('Saved: security_metrics.fig\n');

%% Figure 3: Information Leakage Ratios
fprintf('Generating Figure 3: Information Leakage Ratios...\n');
fig3 = figure('Name', 'Information Leakage Ratios', 'Position', [300, 300, 800, 600]);

% Compute prior entropies
H_theta_L = log(2*pi);
H_X = results.params.L * log(det(pi * exp(1) * results.params.Q_x));

sensing_ratio = real(results.sensing_mutual_info) / H_theta_L;
comm_ratio = real(results.comm_mutual_info) / H_X;

hold on;
plot(SNR_dB_range, sensing_ratio, '--s', ...
     'Color', colors(2,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(2,:), ...
     'DisplayName', 'Sensing Leakage Ratio');
plot(SNR_dB_range, comm_ratio, '-.^', ...
     'Color', colors(3,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(3,:), ...
     'DisplayName', 'Communication Leakage Ratio');

xlabel('SNR (dB)', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('Information Leakage Ratio', 'FontSize', 14, 'FontWeight', 'bold');
title('Normalized Information Leakage Analysis', 'FontSize', 16, 'FontWeight', 'bold');
legend('show', 'Location', 'best', 'FontSize', 12);
grid on;
grid minor;
xlim([min(SNR_dB_range), max(SNR_dB_range)]);

% Save as FIG file
savefig(fig3, 'information_leakage_ratios.fig');
fprintf('Saved: information_leakage_ratios.fig\n');

%% Figure 4: Coupling Analysis
fprintf('Generating Figure 4: Coupling Analysis...\n');
fig4 = figure('Name', 'Coupling Analysis', 'Position', [400, 400, 800, 600]);

coupling_ratio = real(results.coupling_info) ./ (real(results.joint_mutual_info) + eps);

plot(SNR_dB_range, coupling_ratio, '-o', ...
     'Color', colors(4,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(4,:));

xlabel('SNR (dB)', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('Coupling Ratio', 'FontSize', 14, 'FontWeight', 'bold');
title('Information Coupling Strength Analysis', 'FontSize', 16, 'FontWeight', 'bold');
grid on;
grid minor;
xlim([min(SNR_dB_range), max(SNR_dB_range)]);

% Add horizontal line at zero
yline(0, '--k', 'LineWidth', 1, 'DisplayName', 'Zero Reference');
legend('show', 'Location', 'best');

% Save as FIG file
savefig(fig4, 'coupling_analysis.fig');
fprintf('Saved: coupling_analysis.fig\n');

%% Figure 5: Comprehensive Security Analysis
fprintf('Generating Figure 5: Comprehensive Security Analysis...\n');
fig5 = figure('Name', 'Comprehensive Security Analysis', 'Position', [500, 500, 1200, 800]);

% Subplot 1: MI Components with area plot
subplot(2, 2, 1);
sensing_pos = max(0, real(results.sensing_mutual_info));
comm_pos = max(0, real(results.comm_mutual_info));
area(SNR_dB_range, [sensing_pos(:), comm_pos(:)], 'LineWidth', 1.5);
colormap([colors(2,:); colors(3,:)]);
xlabel('SNR (dB)', 'FontSize', 12);
ylabel('Mutual Information (bits)', 'FontSize', 12);
title('Information Decomposition', 'FontSize', 14, 'FontWeight', 'bold');
legend('Sensing', 'Communication', 'Location', 'best');
grid on;

% Subplot 2: Security metrics comparison
subplot(2, 2, 2);
yyaxis left;
plot(SNR_dB_range, real(results.FD_JILI), '-o', 'Color', colors(1,:), 'MarkerSize', 6);
ylabel('FD-JILI', 'Color', colors(1,:), 'FontSize', 12);
yyaxis right;
plot(SNR_dB_range, real(results.FD_WILM), '-s', 'Color', colors(2,:), 'MarkerSize', 6);
ylabel('FD-WILM', 'Color', colors(2,:), 'FontSize', 12);
xlabel('SNR (dB)', 'FontSize', 12);
title('Security Metrics', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% Subplot 3: Joint MI with trend
subplot(2, 2, 3);
plot(SNR_dB_range, real(results.joint_mutual_info), '-o', ...
     'Color', colors(1,:), 'MarkerSize', 6, 'MarkerFaceColor', colors(1,:));
hold on;
% Add polynomial fit
valid_idx = ~isnan(real(results.joint_mutual_info)) & ~isinf(real(results.joint_mutual_info));
if sum(valid_idx) > 3
    p = polyfit(SNR_dB_range(valid_idx), real(results.joint_mutual_info(valid_idx)), 2);
    fit_curve = polyval(p, SNR_dB_range);
    plot(SNR_dB_range, fit_curve, '--', 'Color', colors(2,:), 'LineWidth', 2);
    legend('Simulation', 'Polynomial Fit', 'Location', 'best');
end
xlabel('SNR (dB)', 'FontSize', 12);
ylabel('Joint MI (bits)', 'FontSize', 12);
title('Joint Mutual Information', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% Subplot 4: System performance summary
subplot(2, 2, 4);
% Create a summary metric
security_score = 1 - abs(real(results.FD_JILI));  % Higher is better
performance_score = (real(results.sensing_mutual_info) + real(results.comm_mutual_info)) / 2;
performance_score = performance_score / max(performance_score);  % Normalize

plot(SNR_dB_range, security_score, '-o', 'Color', colors(1,:), 'MarkerSize', 6, ...
     'DisplayName', 'Security Score');
hold on;
plot(SNR_dB_range, performance_score, '-s', 'Color', colors(3,:), 'MarkerSize', 6, ...
     'DisplayName', 'Performance Score');
xlabel('SNR (dB)', 'FontSize', 12);
ylabel('Normalized Score', 'FontSize', 12);
title('Security vs Performance Trade-off', 'FontSize', 14, 'FontWeight', 'bold');
legend('show', 'Location', 'best');
grid on;

sgtitle('Comprehensive FD-ISAC Security Analysis', 'FontSize', 18, 'FontWeight', 'bold');

% Save as FIG file
savefig(fig5, 'comprehensive_security_analysis.fig');
fprintf('Saved: comprehensive_security_analysis.fig\n');

%% Figure 6: 3D Surface Plot
fprintf('Generating Figure 6: 3D Visualization...\n');
fig6 = figure('Name', '3D Mutual Information Landscape', 'Position', [600, 600, 800, 600]);

% Create meshgrid for 3D plot
[SNR_mesh, Metric_mesh] = meshgrid(SNR_dB_range, 1:3);
Z_data = [real(results.joint_mutual_info)'; 
          real(results.sensing_mutual_info)'; 
          real(results.comm_mutual_info)'];

surf(SNR_mesh, Metric_mesh, Z_data, 'EdgeColor', 'none', 'FaceAlpha', 0.8);
colormap(jet);
colorbar;

xlabel('SNR (dB)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('Information Type', 'FontSize', 12, 'FontWeight', 'bold');
zlabel('Mutual Information (bits)', 'FontSize', 12, 'FontWeight', 'bold');
title('3D Mutual Information Landscape', 'FontSize', 14, 'FontWeight', 'bold');

% Set y-axis labels
yticks([1, 2, 3]);
yticklabels({'Joint', 'Sensing', 'Communication'});

view(45, 30);
grid on;

% Save as FIG file
savefig(fig6, '3d_mutual_info_landscape.fig');
fprintf('Saved: 3d_mutual_info_landscape.fig\n');

%% Figure 7: Summary Statistics
fprintf('Generating Figure 7: Summary Statistics...\n');
fig7 = figure('Name', 'Summary Statistics', 'Position', [700, 700, 1000, 600]);

% Create bar plots with error bars
subplot(1, 2, 1);
mi_data = [mean(real(results.joint_mutual_info)), ...
           mean(real(results.sensing_mutual_info)), ...
           mean(real(results.comm_mutual_info))];
mi_std = [std(real(results.joint_mutual_info)), ...
          std(real(results.sensing_mutual_info)), ...
          std(real(results.comm_mutual_info))];

bar(mi_data, 'FaceColor', colors(1,:), 'EdgeColor', 'k', 'LineWidth', 1);
hold on;
errorbar(1:3, mi_data, mi_std, 'k', 'LineStyle', 'none', 'LineWidth', 2);
set(gca, 'XTickLabel', {'Joint MI', 'Sensing MI', 'Comm MI'});
ylabel('Mean Mutual Information (bits)', 'FontSize', 12);
title('Average MI Across SNR Range', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

subplot(1, 2, 2);
security_data = [mean(real(results.FD_JILI)), mean(real(results.FD_WILM))];
security_std = [std(real(results.FD_JILI)), std(real(results.FD_WILM))];

bar(security_data, 'FaceColor', colors(2,:), 'EdgeColor', 'k', 'LineWidth', 1);
hold on;
errorbar(1:2, security_data, security_std, 'k', 'LineStyle', 'none', 'LineWidth', 2);
set(gca, 'XTickLabel', {'FD-JILI', 'FD-WILM'});
ylabel('Mean Security Metric Value', 'FontSize', 12);
title('Average Security Metrics', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

sgtitle('Statistical Summary of FD-ISAC Security Analysis', 'FontSize', 16, 'FontWeight', 'bold');

% Save as FIG file
savefig(fig7, 'summary_statistics.fig');
fprintf('Saved: summary_statistics.fig\n');

%% Print summary
fprintf('\n=== FIG Plot Generation Complete ===\n');
fprintf('Generated FIG files:\n');
fprintf('1. joint_mutual_info_components.fig\n');
fprintf('2. security_metrics.fig\n');
fprintf('3. information_leakage_ratios.fig\n');
fprintf('4. coupling_analysis.fig\n');
fprintf('5. comprehensive_security_analysis.fig\n');
fprintf('6. 3d_mutual_info_landscape.fig\n');
fprintf('7. summary_statistics.fig\n');
fprintf('\nAll plots saved as interactive MATLAB FIG files.\n');
fprintf('You can open them in MATLAB using: openfig(''filename.fig'')\n');

end
