\section{Overview of SAGIN Architectures and Components}

Space-air-ground integrated networks (SAGINs) integrate satellite, aerial, and terrestrial segments to enable ubiquitous connectivity in wireless communications \cite{liu2018space,chen2022sagin}. This architecture overcomes limitations of conventional terrestrial networks by offering global coverage, improved reliability, and support for applications such as remote sensing, disaster response, and broadband access in remote regions \cite{wang2022disaster}. SAGINs are integral to the transition toward 6G, facilitating seamless multi-domain integration to achieve ultra-low latency, massive connectivity, and enhanced throughput \cite{zhang20236g,saad20206g}.


\subsection{Architecture, Components, and Interfaces}

SAGIN architectures adopt a multi-layered model, including physical, data link, network, and application layers, with inter-domain interfaces ensuring coordination across space, air, and ground segments \cite{liu2018space,tang2021architectures}. The physical layer employs diverse transmission media, such as radio frequency (RF) for ground-air links, free-space optics (FSO) for air-space communications, and millimeter-wave (mmWave) or terahertz bands for high-throughput satellite services, addressing challenges like propagation delays and Doppler shifts in dynamic environments \cite{li2023physical,zhao2023terahertz}. The data link layer manages medium access control (MAC) and error correction, adapting to varying topologies via protocols like adaptive modulation and coding (AMC) or hybrid automatic repeat request (HARQ), which are crucial for maintaining link quality in mobile scenarios \cite{kumar2022adaptive}.

The network layer handles routing, resource allocation, and mobility management across domains, often utilizing hierarchical software-defined networking (SDN) controllers to optimize paths and mitigate congestion \cite{zhao2023sdn}. Inter-domain interfaces, including satellite-air handovers, ground-satellite backhauls, and air-ground relays, maintain seamless connectivity through standardized protocols, enabling efficient data forwarding and load balancing \cite{ahmed2022handover,patel2023interfaces}. The application layer supports service-oriented orchestration, facilitating customized resource provisioning for diverse scenarios such as emergency management and IoT integration \cite{chen2022iot}.

Primary SAGIN configurations include hybrid satellite-terrestrial relay networks for extended coverage, backhaul architectures for high-capacity links, cognitive networks for spectrum efficiency, and cooperative systems for joint resource utilization, each tailored to operational requirements like latency-sensitive or data-intensive applications \cite{tang2021architectures,rodriguez2023cognitive}.

SAGIN consists of three main segments. The space segment encompasses satellites in geostationary (GEO), medium (MEO), and low Earth orbit (LEO), where LEO constellations with inter-satellite links (ISL) and inter-layer links (ILL) provide low-latency global coverage (typically 20-50 ms), GEO supports wide-area broadcasting, and MEO offers balanced performance for navigation and communication \cite{liu2018space,zhang20236g}. Supporting infrastructure includes ground stations and control centers for orbit management and data processing. The air segment features high-altitude platforms (HAPs), such as stratospheric balloons and aircraft at 17-22 km altitude, delivering regional coverage for broadcasting and emergencies, alongside low-altitude platforms (LAPs), primarily unmanned aerial vehicles (UAVs), which enable flexible, on-demand relaying with enhanced endurance through advanced batteries, solar charging, and swarm formations for improved resilience in dynamic conditions \cite{chen2022sagin,yang2022uav}. The ground segment incorporates 5G/6G base stations, mobile ad hoc networks (MANETs), wireless local area networks (WLANs), and IoT gateways, unified for efficient resource management and integration with urban or rural infrastructures \cite{wang2022ground,kim2023integration}.


\subsection{Standardization and Enabling Technologies}

Standardization ensures SAGIN interoperability and global adoption. The 3rd Generation Partnership Project (3GPP) Release 17 integrates non-terrestrial networks (NTN) into 5G, supporting satellite-terrestrial convergence for ubiquitous access, including beam management and handover mechanisms, extending prior releases focused on terrestrial enhancements \cite{3gpp2022rel17,chen2022sagin}. The International Telecommunication Union (ITU) regulates spectrum allocations, such as L-band for satellite-ground links and higher bands (Ku, Ka, Q/V, W) for throughput-intensive services, while addressing interference, orbital debris, and environmental factors through frameworks like IMT-2030 for 6G alignment \cite{tang2021architectures,saad20206g}. Additional efforts by organizations like ETSI and IEEE focus on protocol harmonization for multi-domain operations.

Software-defined networking (SDN) and network function virtualization (NFV) underpin SAGIN operations by decoupling control and data planes for dynamic orchestration, mobility management, and scalable deployment \cite{zhang20236g,zhao2023sdn}. SDN enables centralized controllers to adapt to topology changes, while NFV virtualizes functions like routing and security for flexibility, cost reduction, and network slicing to isolate services with varying QoS requirements \cite{yang2022uav}. Other enablers include mobile edge computing (MEC) for low-latency processing at network edges, reducing backhaul dependency; blockchain for secure, decentralized data handling and trust management in distributed environments; and emerging technologies like reconfigurable intelligent surfaces (RIS) for signal enhancement in challenging propagation scenarios \cite{wang2022ground,zhao2023terahertz}. These technologies collectively address SAGIN challenges such as resource heterogeneity, energy constraints, and integration complexities.
\subsection{Comparative Analysis and Recent Deployments}

Table~\ref{tab:comparison} compares SAGIN with 5G and projected 6G terrestrial networks across key performance metrics, highlighting SAGIN's advantages in coverage and integration.

\begin{table*}[t]
\centering
\caption{Comparison of SAGIN with 5G and 6G Terrestrial Networks}
\label{tab:comparison}
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Metric} & \textbf{SAGIN} & \textbf{5G Terrestrial} & \textbf{6G Terrestrial (Projected)} \\
\hline
Latency & Low (LEO: $\sim$20-50 ms; GEO: higher) & Ultra-low ($\sim$1 ms) & Sub-millisecond \\
Bandwidth & High (multi-beam, up to Tbps aggregate) & Up to 20 Gbps & $>$100 Gbps \\
Energy Efficiency & Moderate (optimized via edge/MEC) & High (mMTC optimized) & Ultra-low power \\
Coverage & Global, 3D (space-air extensions) & Urban/rural, 2D & Enhanced, NTN-limited \\
Reliability & High (multi-path redundancy) & uRLLC (99.999\%) & Extreme (99.99999\%) \\
Cost & High initial, scalable & Moderate & Lower with AI optimization \\
\hline
\end{tabular}
\end{table*}

SAGIN provides superior three-dimensional global coverage by integrating NTN for remote and maritime areas, albeit with higher latency in GEO configurations compared to 5G's ultra-reliable low-latency communications (uRLLC) \cite{liu2018space,chen2022sagin}. In 6G contexts, SAGIN advances enhanced mobile broadband (eMBB) to further eMBB with terabit-scale rates and improved energy efficiency through cloud-edge synergy, addressing 5G limitations in power-constrained aerial nodes and orbital dynamics \cite{tang2021architectures,li2023physical}. Quantitative analyses show SAGIN outperforming terrestrial networks in outage probability under mobility (e.g., <1\% vs. 5-10\% in rural 5G) and throughput in integrated scenarios \cite{kumar2022adaptive}.

Notable recent implementations underscore SAGIN's viability. SpaceX's Starlink LEO constellation, with 8,094 satellites in orbit as of August 2025 (of which 8,075 are operational), delivers low-latency broadband globally and incorporates UAV integrations for enhanced operations in challenging terrains and maritime environments \cite{starlink2025status,musk2024integration}. Amazon's Project Kuiper has initiated beta services in 2025, deploying initial satellites to compete in providing high-speed internet to underserved areas, with plans for over 3,000 satellites \cite{amazon2025kuiper}. UAV-assisted SAGIN deployments in disaster relief, such as simulated networks for flood and earthquake responses, demonstrate advancements in adaptive routing, resource allocation, and real-time data fusion \cite{wang2022disaster,chen2022iot}. Furthermore, integrations like 5G core networks on satellites and hybrid systems in military applications (e.g., U.S. Army's use of SAGIN elements for Middle East operations) highlight progress in NTN convergence and resilient communications \cite{military2024sagin,defense2023hybrid}.
