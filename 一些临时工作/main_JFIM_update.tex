\documentclass[lettersize,journal]{IEEEtran}
\usepackage{graphicx}
\usepackage{subfigure}
\usepackage{subeqnarray}
\usepackage{bm}
\usepackage{amsfonts}
\usepackage{amsthm}
\usepackage{amssymb}
\usepackage{makecell}
\usepackage{calc}
\usepackage{color}
\usepackage{mathrsfs}
\usepackage{flushend}
\usepackage{url}
\usepackage{caption}
%\usepackage{subcaption}
\usepackage{subfloat}
\usepackage{color}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{amsmath,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
%\usepackage{algpseudocode}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{graphicx}
\usepackage{cite}
\hyphenation{op-tical net-works semi-conduc-tor IEEE-Xplore}
% updated with editorial comments 8/9/2021
\usepackage[outdir=./]{epstopdf}
\usepackage{booktabs}
\usepackage{cuted}
\newtheorem{thm}{Theorem}
\newtheorem{lemma}{Lemma}
\newtheorem{corollary}{Corollary}
\newtheorem{remark}{Remark}
\newtheorem{proposition}{Proposition}
\newtheorem{example}{Example}
\usepackage[inline]{enumitem}


\begin{document}

\title{A Physical-Layer Security Framework for Joint Communication and Sensing in ISAC}

\author{Nanchi Su,~\IEEEmembership{Member,~IEEE}


        % <-this % stops a space
\thanks{{This work was supported in part by the Engineering and Physical Sciences Research Council (EPSRC) under Grant EP/S028455/1, in part by the National Natural Science Foundation of China under Grant 62101234, Grant U20B2039, Grant 61831008 and Grant 62027802, in part by the Young Elite Scientist Sponsorship Program by CAST under Grant No. YESS20210055, and in part by the China Scholarship Council (CSC).}}
\thanks{N. Su is with Guangdong Provincial Key Laboratory of Aerospace Communication and Networking Technology, Harbin Institute of Technology (Shenzhen), Shenzhen  518055, China; with the Department of Electronic and Electrical Engineering, Southern University of Science and Technology, Shenzhen 518055, China}

}

% The paper headers
%\markboth{Journal of \LaTeX\ Class Files,~Vol.~14, No.~8, August~2021}%
%{Shell \MakeLowercase{\textit{et al.}}: A Sample Article Using IEEEtran.cls for IEEE Journals}

%\IEEEpubid{0000--0000/00\$00.00~\copyright~2021 IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.

\maketitle

\begin{abstract}
In this letter, 
\end{abstract}

\begin{IEEEkeywords}
 Integrated sensing and communication, successive convex approximation, Bayesian Cram\textquotesingle{}er-Rao bound, constructive interference.
\end{IEEEkeywords}

\section{Introduction}
\IEEEPARstart{T}{raditional} wireless 


\begin{figure}[!ht]
\centering
\includegraphics[width=0.45\textwidth]{SM_mono.pdf}
\caption{The proposed FD-enabled secure ISAC system model.}
\label{fig_SystemModel}
\end{figure}


\section{System Model}

We consider a monostatic FD-ISAC framework as depicted in Fig.~\ref{fig_SystemModel}. In this system, a single dual-functional base station operates as both radar transmitter and receiver while simultaneously providing communication services to multiple users.

\subsection{System Components}

The proposed monostatic secure ISAC system consists of the following components:

\begin{itemize}
    \item \textbf{Monostatic ISAC Base Station:} Equipped with $N_{\text{BS,t}}$ transmit antennas and $N_{\text{BS,r}}$ receive antennas, serving as:
    \begin{itemize}
        \item Radar transmitter for target illumination
        \item Radar receiver for echo signal processing  
        \item Communication transmitter for downlink data transmission
    \end{itemize}
    
    \item \textbf{Target:} A passive reflector within the sensing coverage area at angle $\theta_L$ relative to the base station
    
    \item \textbf{Communication Users:} $K$ single-antenna legitimate users receiving downlink communication signals
    
    \item \textbf{Eavesdropper:} A malicious terminal equipped with $N_E$ antennas at angle $\theta_E$ attempting to intercept both sensing and communication information
\end{itemize}

\subsection{Threat Model}

The malicious eavesdropper (Eve) can exploit two primary vulnerabilities:
\begin{itemize}
    \item \textbf{Communication Data Leakage:} Eve may eavesdrop on the downlink communication signals intended for legitimate users, potentially extracting sensitive communication data.
    \item \textbf{Sensing Information Leakage:} Eve may intercept the reflected sensing signals from the target, thereby inferring sensitive information such as target location, system parameters, and operational characteristics.
\end{itemize}

The full-duplex base station is equipped with $N_{\text{BS,t}}$ transmit antennas and $N_{\text{BS,r}}$ receive antennas, which simultaneously perform radar and communication functions. The system serves $K$ single-antenna communication users, where we assume $K \leq N_{\text{BS,t}}$. A target exists within the sensing range of the radar, and an unauthorized malicious terminal is present in the vicinity, capable of intercepting both the radar's reflected sensing signals and the downlink communication intended for the legitimate users.

\subsection{Signal Model}

Let $\mathbf{s}(l) \in \mathbb{C}^{K \times 1}$ denote the transmitted symbol vector for the $K$ users at the $l$-th time slot, satisfying $E[\mathbf{s}(l)\mathbf{s}(l)^H] = \mathbf{I}_K$. Let $\mathbf{F} \in \mathbb{C}^{N_{\text{BS,t}} \times K}$ represent the precoding matrix. To enhance physical-layer security, we introduce an artificial noise (AN) signal, $\mathbf{z}(l) \in \mathbb{C}^{N_{\text{BS,t}} \times 1}$, which is designed to interfere with the eavesdropper. The AN is modeled as a zero-mean complex Gaussian vector with covariance $\mathbf{Z} = E[\mathbf{z}(l)\mathbf{z}(l)^H]$. The total transmit waveform is:
\begin{equation}
\mathbf{x}(l) = \mathbf{F}\mathbf{s}(l) + \mathbf{z}(l)
\label{eq:transmit_signal}
\end{equation}
The total transmit power is constrained by $\text{tr}(\mathbf{F}\mathbf{F}^H + \mathbf{Z}) \leq P_{\text{max}}$. The received signals at each entity now include the effects of this composite signal.

\subsubsection{Received Signal at Monostatic Base Station}

The received signal at the monostatic ISAC base station, which includes the target echo and self-interference, is expressed as:
\begin{equation}
\mathbf{y}_{\text{BS}}(l) = \mathbf{G}_T(\theta_L)\mathbf{x}(l) + \mathbf{H}_{\text{SI}}\mathbf{x}(l) + \mathbf{n}_{\text{BS}}(l)
\label{eq:bs_received}
\end{equation}
where $\mathbf{G}_T(\theta_L) = \alpha(l)\mathbf{a}_r(\theta_L)\mathbf{a}_t^H(\theta_L)$ is the target reflection channel, and $\mathbf{H}_{\text{SI}}$ is the self-interference channel. To evaluate the BS's sensing performance, we use the Signal-to-Interference-plus-Noise Ratio (SINR) of the received echo. Assuming a receive beamformer $\mathbf{w}_r$ (typically matched to the target's steering vector, i.e., $\mathbf{w}_r = \mathbf{a}_r(\theta_L)$), the radar SINR is:
\begin{equation}
\begin{aligned}
\text{SINR}_{\text{BS}} &= \frac{E[|\mathbf{w}_r^H \mathbf{G}_T(\theta_L)\mathbf{x}(l)|^2]}{E[|\mathbf{w}_r^H (\mathbf{H}_{\text{SI}}\mathbf{x}(l) + \mathbf{n}_{\text{BS}}(l))|^2]} \\ &= \frac{|\alpha|^2 |\mathbf{w}_r^H \mathbf{a}_r(\theta_L)|^2 \mathbf{a}_t^H(\theta_L)(\mathbf{Q}_s + \mathbf{Z})\mathbf{a}_t(\theta_L)}{\mathbf{w}_r^H \mathbf{H}_{\text{SI}} (\mathbf{Q}_s + \mathbf{Z}) \mathbf{H}_{\text{SI}}^H \mathbf{w}_r + \sigma_{\text{BS}}^2 |\|\mathbf{w}_r||^2}
\label{eq:sinr_bs}
\end{aligned}
\end{equation}
A higher $\text{SINR}_{\text{BS}}$ corresponds to better target detection and estimation performance. It is assumed that advanced self-interference cancellation techniques are employed at the BS to keep the impact of $\mathbf{H}_{\text{SI}}$ manageable.

For the residual self-interference channel at the base station, we follow~\cite{temiz2021dual, nguyen2014spectral, he2023full} and model each entry of $\mathbf{H}_{\text{SI}}$ as:
\begin{equation}
[\mathbf{H}_{\text{SI}}]_{p,q} = \sqrt{\alpha_{p,q}^{\text{SI}}} e^{-j2\pi \frac{d_{p,q}}{\lambda}}
\label{eq:si_channel}
\end{equation}
where $\alpha_{p,q}^{\text{SI}} > 0$ and $d_{p,q} > 0$ denote the residual self-interference channel power and the distance between the $q$-th transmit antenna and the $p$-th receive antenna, respectively. For simplicity, we set $\alpha^{\text{SI}} = \alpha_{p,q}^{\text{SI}} = -110$ dB and let $e^{-j2\pi \frac{d_{p,q}}{\lambda}}$ be a unit-modulus variable with random phase for all transceiver antenna pairs $(p,q)$.

\subsubsection{Array Steering Vectors}

We deploy uniform linear arrays (ULA). For an $N$-element ULA, the steering vector can be expressed as:
\begin{equation}
\mathbf{a}(\theta) = 
\begin{bmatrix} 
e^{-j\frac{N-1}{2}\pi \sin\theta}, e^{-j\frac{N-3}{2}\pi \sin\theta}, \ldots, e^{j\frac{N-1}{2}\pi \sin\theta}
\end{bmatrix}^T 
\label{eq:steering_vector}
\end{equation}
where $N$ denotes the number of antennas. We choose the center of the ULA antennas as the reference point. It can be verified that $\mathbf{a}^H(\theta) \dot{\mathbf{a}}(\theta) = 0$.

\subsubsection{Received Signal at Eavesdropper}

The received signal at the eavesdropper can be written as:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{G}_E(\boldsymbol{\theta})\mathbf{x}(l) + \mathbf{H}_{\text{CE}}\mathbf{x}(l) + \mathbf{n}_{\text{eve}}(l)
\label{eq:eve_received}
\end{equation}
where $\mathbf{G}_E(\boldsymbol{\theta}) = \beta(l)\mathbf{b}(\theta_E)\mathbf{a}_t^H(\theta_L)$ represents the target-reflected channel to the eavesdropper, $\boldsymbol{\theta} = [\theta_E, \theta_L]^T$ denotes the angle parameter vector, $\theta_E$ denotes the eavesdropper's angle, $\mathbf{b}(\theta_E) \in \mathbb{C}^{N_E \times 1}$ is the eavesdropper's array steering vector, $\mathbf{H}_{\text{CE}} \in \mathbb{C}^{N_E \times N_{\text{BS,t}}}$ denotes the direct channel matrix from the base station to the eavesdropper, and $\mathbf{n}_{\text{eve}}(l)\sim \mathcal{CN}(\mathbf{0}, \sigma_n^2 \mathbf{I}_{N_E})$ is the additive noise.

The effective channel matrix combining both signal paths is:
\begin{equation}
\mathbf{H}_{\text{eff}} = \mathbf{G}_E(\boldsymbol{\theta}) + \mathbf{H}_{\text{CE}} \in \mathbb{C}^{N_E \times N_{\text{BS,t}}}
\label{eq:effective_channel}
\end{equation}

\subsubsection{Received Signal at Communication Users}

The received signal at the $k$-th single-antenna communication user at the $l$-th time slot is given as:
\begin{equation}
y_{k}(l) = \mathbf{h}_{k}^H\mathbf{x}(l) + n_{k}(l) = \mathbf{h}_{k}^H\mathbf{F}\mathbf{s}(l) + \mathbf{h}_{k}^H\mathbf{z}(l) + n_{k}(l)
\label{eq:user_received}
\end{equation}
where $\mathbf{h}_{k} \in \mathbb{C}^{N_{\text{BS,t}} \times 1}$ denotes the channel vector between the monostatic base station and the $k$-th user, and $n_{k}(l)$ denotes the additive white Gaussian noise with variance $\sigma^2_{k}$. The term $\mathbf{h}_{k}^H\mathbf{z}(l)$ represents interference caused by the artificial noise. The Signal-to-Interference-plus-Noise Ratio (SINR) for the $k$-th user, which is a key metric for Quality of Service (QoS), is given by:
\begin{equation}
\text{SINR}_k = \frac{|\mathbf{h}_k^H \mathbf{f}_k|^2}{\sum_{j \neq k, j=1}^K |\mathbf{h}_k^H \mathbf{f}_j|^2 + \mathbf{h}_k^H \mathbf{Z} \mathbf{h}_k + \sigma_{k}^2}
\label{eq:sinr_k}
\end{equation}
where $\mathbf{f}_k$ is the $k$-th column of the precoding matrix $\mathbf{F}$. For the system to be effective, the AN covariance $\mathbf{Z}$ must be designed to lie in the null space of the legitimate users' channels, i.e., $\mathbf{h}_{k}^H\mathbf{Z}\mathbf{h}_{k} \approx 0$ for all $k$.

We assume that $\mathbf{h}_{k}$ follows a slow-fading Rician channel model:
\begin{equation}
\mathbf{h}_{k} = \sqrt{\frac{\kappa_k}{1+\kappa_k} }\mathbf{h}_{k}^{\text{LoS}} + \sqrt{\frac{1}{1+\kappa_k} }\mathbf{h}_{k}^{\text{NLoS}}
\label{eq:rician_channel}
\end{equation}
where $\kappa_k>0$ is the Rician $K$-factor of the $k$-th user, $\mathbf{h}_{k}^{\text{LoS}} = \sqrt{N_{\text{BS,t}}}\mathbf{a}_t(\omega_{k,0})$ denotes the line-of-sight component, and $\mathbf{a}_t(\omega_{k,0})$ is the array steering vector with $\omega_{k,0}\in[-\frac{\pi}{2},\frac{\pi}{2}]$ being the angle of departure from the base station to user $k$. The scattering component is:
\begin{equation}
\mathbf{h}_{k}^{\text{NLoS}} = \sqrt{\frac{N_{\text{BS,t}}}{L_p}}\sum_{p=1}^{L_p} c_{k,p} \mathbf{a}_t(\omega_{k,p})
\label{eq:nlos_component}
\end{equation}
where $L_p$ denotes the number of propagation paths, $c_{k,p}\sim \mathcal{CN}(0, 1)$ is the complex path gain, and $\omega_{k,p}$ is the angle of departure associated with the $(k,p)$-th propagation path.



\section{Fisher Information Matrix Analysis for Eavesdropping}

We analyze the eavesdropper's estimation capability under two regimes that differ in the knowledge of the communication symbols $\mathbf{s}(l)$ at time slot $l$.
Throughout, the received signal at the eavesdropper is
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{H}_{\text{eff}}(\theta_E,\theta_L)\big(\mathbf{F}\mathbf{s}(l)+\mathbf{z}(l)\big)+\mathbf{n}_{\text{eve}}(l),
\label{eq:eve_rx_model}
\end{equation}
where $\theta_E$ and $\theta_L$ denote the eavesdropper and target angles, respectively, $\mathbf{F}$ is the precoder, $\mathbf{z}(l)\!\sim\!\mathcal{CN}(\mathbf{0},\mathbf{Z})$ is artificial noise (AN), and $\mathbf{n}_{\text{eve}}(l)\!\sim\!\mathcal{CN}(\mathbf{0},\sigma_n^2\mathbf{I}_{N_E})$ is thermal noise.

The AN and thermal noise yield the effective disturbance
\begin{equation}
\tilde{\mathbf{n}}_{\text{eve}}(l) = \mathbf{H}_{\text{eff}}\mathbf{z}(l) + \mathbf{n}_{\text{eve}}(l),
\end{equation}
with covariance
\begin{equation}
\mathbf{R}_{\tilde{n}} = \mathbb{E}\!\left[\tilde{\mathbf{n}}_{\text{eve}}(l)\tilde{\mathbf{n}}_{\text{eve}}^H(l)\right]
= \mathbf{H}_{\text{eff}} \mathbf{Z} \mathbf{H}_{\text{eff}}^H + \sigma_n^2 \mathbf{I}_{N_E}.
\label{eq:an_noise_cov}
\end{equation}
Using the Woodbury identity gives
\begin{equation}
\mathbf{R}_{\tilde{n}}^{-1} = \frac{1}{\sigma_n^2}\mathbf{I}_{N_E}
- \frac{1}{\sigma_n^2}\mathbf{H}_{\text{eff}}\!\left(\mathbf{Z}^{-1}
+ \frac{1}{\sigma_n^2}\mathbf{H}_{\text{eff}}^H \mathbf{H}_{\text{eff}} \right)^{-1} \!\mathbf{H}_{\text{eff}}^H.
\label{eq:inv_an_noise_cov_woodbury}
\end{equation}

\subsection{Case I: $\mathbf{s}(l)\!\sim\!\mathcal{CN}(\mathbf{0},\mathbf{I}_K)$ (Known Symbol Distribution)}

In many ISAC designs, the information symbols are modeled as i.i.d.\ standard complex Gaussian, i.e., $\mathbf{s}(l)\!\sim\!\mathcal{CN}(\mathbf{0},\mathbf{I}_K)$.
The \emph{transmit} covariance thus reads
\begin{equation}
\mathbf{R}_x \triangleq \mathbb{E}\!\left[\mathbf{F}\mathbf{s}(l)\mathbf{s}^H(l)\mathbf{F}^H\right] = \mathbf{F}\mathbf{F}^H,
\label{eq:txcov}
\end{equation}
which is the typical optimization variable in design problems (either through $\mathbf{F}$ or directly via $\mathbf{R}_x\succeq \mathbf{0}$ with ${\rm rank}(\mathbf{R}_x)\!\le\!K$).
In this regime, the observation is zero-mean complex Gaussian
\begin{equation}
\mathbf{y}_{\text{eve}}(l) \sim \mathcal{CN}\big(\mathbf{0}, \ \boldsymbol{\Sigma}(\boldsymbol{\vartheta})\big), \quad
\boldsymbol{\vartheta}\triangleq[\theta_E,\theta_L]^T,
\end{equation}
with covariance
\begin{equation}
\boldsymbol{\Sigma}(\boldsymbol{\vartheta})
= \mathbf{H}_{\text{eff}}\!\left(\mathbf{R}_x + \mathbf{Z}\right)\!\mathbf{H}_{\text{eff}}^H
+ \sigma_n^2\mathbf{I}_{N_E}.
\label{eq:cov_random_s_correct}
\end{equation}
For real parameters and a complex Gaussian model with parameter-dependent covariance, the (Slepian--Bangs) FIM is
\begin{equation}
\big[\mathbf{J}_{\text{SB}}(\boldsymbol{\vartheta})\big]_{i,j}
= \frac{1}{2}\,\mathrm{Tr}\!\left\{\boldsymbol{\Sigma}^{-1}
\frac{\partial \boldsymbol{\Sigma}}{\partial \vartheta_i}\,
\boldsymbol{\Sigma}^{-1}\frac{\partial \boldsymbol{\Sigma}}{\partial \vartheta_j}\right\},
\label{eq:SlepianBangs_correct}
\end{equation}
with
\begin{equation}
\frac{\partial \boldsymbol{\Sigma}}{\partial \vartheta_i}
= \dot{\mathbf{H}}_i \left(\mathbf{R}_x+\mathbf{Z}\right)\mathbf{H}_{\text{eff}}^H
+ \mathbf{H}_{\text{eff}}\left(\mathbf{R}_x+\mathbf{Z}\right)\dot{\mathbf{H}}_i^{H}, \qquad
\dot{\mathbf{H}}_i \triangleq \frac{\partial \mathbf{H}_{\text{eff}}}{\partial \vartheta_i}.
\label{eq:Sigma_derivative_correct}
\end{equation}
\textbf{CRB:} For any unbiased estimator of $\boldsymbol{\vartheta}$, the covariance is lower-bounded by
\begin{equation}
\mathrm{Cov}(\hat{\boldsymbol{\vartheta}}) \succeq \mathbf{J}_{\text{SB}}^{-1}(\boldsymbol{\vartheta}), \quad
\mathrm{CRB}(\vartheta_i) = \big[\mathbf{J}_{\text{SB}}^{-1}\big]_{i,i}.
\end{equation}
\textbf{HCRB remark:} Since $\boldsymbol{\vartheta}$ is treated as deterministic and $\mathbf{s}(l)$ as random with a known prior, the \emph{hybrid} FIM equals
\begin{equation}
\big[\mathbf{J}_{\mathrm{H}}(\boldsymbol{\vartheta})\big]_{i,j}
= \mathbb{E}_{\mathbf{s}}\!\Big[2\,\mathrm{Re}\!\left\{ \!\left(\frac{\partial \boldsymbol{\mu}}{\partial \vartheta_i}\right)^{\!H}\!
\boldsymbol{\Sigma}^{-1}\! \left(\frac{\partial \boldsymbol{\mu}}{\partial \vartheta_j}\right)\!\right\}\Big]
+ \frac{1}{2}\mathrm{Tr}\!\left\{\boldsymbol{\Sigma}^{-1}
\frac{\partial \boldsymbol{\Sigma}}{\partial \vartheta_i}\,
\boldsymbol{\Sigma}^{-1}\frac{\partial \boldsymbol{\Sigma}}{\partial \vartheta_j}\right\},
\end{equation}
where $\boldsymbol{\mu}\!=\!\mathbf{0}$ here, so the first term vanishes and $\mathbf{J}_{\mathrm{H}}\!=\!\mathbf{J}_{\text{SB}}$.
Accordingly, the HCRB coincides with the above CRB: $\mathrm{HCRB}(\boldsymbol{\vartheta})=\mathbf{J}_{\text{SB}}^{-1}(\boldsymbol{\vartheta})$.

\subsection{Case II: $\mathbf{s}(l)$ Unknown (Deterministic Nuisance)}

In this ``deterministic-$\mathbf{s}$'' regime, we stack the parameters at slot $l$ as
\begin{equation}
\boldsymbol{\phi} = \big[\theta_E,\ \theta_L,\ \mathrm{Re}(s_1(l)),\ \mathrm{Im}(s_1(l)),\ldots,
\mathrm{Re}(s_K(l)),\ \mathrm{Im}(s_K(l))\big]^T.
\label{eq:single_slot_params}
\end{equation}
The mean and covariance of $\mathbf{y}_{\text{eve}}(l)$ are
\begin{equation}
\boldsymbol{\mu}(\boldsymbol{\phi})=\mathbf{H}_{\text{eff}}(\theta_E,\theta_L)\mathbf{F}\mathbf{s}(l),\qquad
\mathrm{Cov}=\mathbf{R}_{\tilde{n}}.
\end{equation}
For complex Gaussian observations with parameter-dependent mean and parameter-independent covariance,
the (real-parameter) FIM is
\begin{equation}
\big[\mathbf{J}(\boldsymbol{\phi})\big]_{i,j}
= 2\,\mathrm{Re}\!\left\{\left(\frac{\partial \boldsymbol{\mu}}{\partial \phi_i}\right)^{\!H}
\mathbf{R}_{\tilde{n}}^{-1}\left(\frac{\partial \boldsymbol{\mu}}{\partial \phi_j}\right)\right\}.
\label{eq:fim_mean_known_cov}
\end{equation}
Writing $\dot{\mathbf{H}}_E\!\triangleq\!\frac{\partial \mathbf{H}_{\text{eff}}}{\partial \theta_E}$ and
$\dot{\mathbf{H}}_L\!\triangleq\!\frac{\partial \mathbf{H}_{\text{eff}}}{\partial \theta_L}$, and letting
$\mathbf{f}_k$ be the $k$-th column of $\mathbf{F}$, the block structure of $\mathbf{J}(\boldsymbol{\phi})$ is
\begin{equation}
\mathbf{J}(\boldsymbol{\phi}) = \begin{bmatrix}
\mathbf{J}_{SS} & \mathbf{J}_{SC} \\
\mathbf{J}_{CS} & \mathbf{J}_{CC}
\end{bmatrix},
\qquad S\!=\!\{\theta_E,\theta_L\},\ C\!=\!\{\mathrm{Re}\,\mathbf{s},\mathrm{Im}\,\mathbf{s}\}.
\label{eq:blockJ}
\end{equation}
The sensing block is
\begin{align}
[\mathbf{J}_{SS}]_{1,1} &= 2\,\mathrm{Re}\!\left\{\mathbf{s}^H\mathbf{F}^H \dot{\mathbf{H}}_E^H
\mathbf{R}_{\tilde{n}}^{-1} \dot{\mathbf{H}}_E \mathbf{F}\mathbf{s}\right\}, \\
[\mathbf{J}_{SS}]_{2,2} &= 2\,\mathrm{Re}\!\left\{\mathbf{s}^H\mathbf{F}^H \dot{\mathbf{H}}_L^H
\mathbf{R}_{\tilde{n}}^{-1} \dot{\mathbf{H}}_L \mathbf{F}\mathbf{s}\right\}, \\
[\mathbf{J}_{SS}]_{1,2} &= 2\,\mathrm{Re}\!\left\{\mathbf{s}^H\mathbf{F}^H \dot{\mathbf{H}}_E^H
\mathbf{R}_{\tilde{n}}^{-1} \dot{\mathbf{H}}_L \mathbf{F}\mathbf{s}\right\}.
\end{align}
The cross block $\mathbf{J}_{SC}$ (sensing vs.\ symbol components) has $(2\times 2K)$ entries
\begin{align}
\frac{\partial \boldsymbol{\mu}}{\partial \mathrm{Re}(s_k)} &= \mathbf{H}_{\text{eff}}\mathbf{f}_k, \qquad
\frac{\partial \boldsymbol{\mu}}{\partial \mathrm{Im}(s_k)} = j\,\mathbf{H}_{\text{eff}}\mathbf{f}_k, \\
[\mathbf{J}_{SC}]_{i,2k-1} &= 2\,\mathrm{Re}\!\left\{ \big(\dot{\mathbf{H}}_i \mathbf{F}\mathbf{s}\big)^{H}
\mathbf{R}_{\tilde{n}}^{-1}\mathbf{H}_{\text{eff}}\mathbf{f}_k \right\}, \\
[\mathbf{J}_{SC}]_{i,2k}   &= 2\,\mathrm{Re}\!\left\{ \big(\dot{\mathbf{H}}_i \mathbf{F}\mathbf{s}\big)^{H}
\mathbf{R}_{\tilde{n}}^{-1} j\,\mathbf{H}_{\text{eff}}\mathbf{f}_k \right\},
\end{align}
for $i\in\{E,L\}$. Finally, the communication block $\mathbf{J}_{CC}$ is
\begin{align}
[\mathbf{J}_{CC}]_{2i-1,2j-1} &= 2\,\mathrm{Re}\!\left\{ \mathbf{f}_i^H \mathbf{H}_{\text{eff}}^H
\mathbf{R}_{\tilde{n}}^{-1}\mathbf{H}_{\text{eff}}\mathbf{f}_j \right\}, \\
[\mathbf{J}_{CC}]_{2i-1,2j}   &= -2\,\mathrm{Im}\!\left\{ \mathbf{f}_i^H \mathbf{H}_{\text{eff}}^H
\mathbf{R}_{\tilde{n}}^{-1}\mathbf{H}_{\text{eff}}\mathbf{f}_j \right\}, \\
[\mathbf{J}_{CC}]_{2i,2j}     &= 2\,\mathrm{Re}\!\left\{ \mathbf{f}_i^H \mathbf{H}_{\text{eff}}^H
\mathbf{R}_{\tilde{n}}^{-1}\mathbf{H}_{\text{eff}}\mathbf{f}_j \right\}.
\end{align}
To obtain the angle-only bound while eliminating the nuisance $\mathbf{s}(l)$, use the Schur complement:
\begin{equation}
\tilde{\mathbf{J}}_{SS} \triangleq \mathbf{J}_{SS} - \mathbf{J}_{SC}\mathbf{J}_{CC}^{-1}\mathbf{J}_{CS},
\qquad
\mathrm{CRB}([\theta_E,\theta_L]^T) = \tilde{\mathbf{J}}_{SS}^{-1},
\label{eq:schur_crb}
\end{equation}
so that $\mathrm{CRB}(\theta_E) = [\tilde{\mathbf{J}}_{SS}^{-1}]_{1,1}$ and similarly for $\theta_L$.
\section{Secure Waveform Design via D-Optimality}

Our goal is to design the signal covariance matrix $\mathbf{Q}_s = \mathbf{F}\mathbf{F}^H$ and the artificial noise covariance matrix $\mathbf{Z}$ to minimize the eavesdropper's ability to jointly estimate the sensing and communication parameters. Following the D-optimality criterion, this is equivalent to minimizing the determinant of the eavesdropper's FIM, which maximizes the volume of the estimation error ellipsoid.

\subsection{Problem Formulation}

To handle the different scales and units of the parameters, we use a normalized FIM. The normalization is performed using a diagonal matrix of thresholds, $\mathbf{T} = \text{diag}(T_1, T_2, \dots, T_{2+2K})$, where each $T_i$ represents a benchmark variance for the corresponding parameter $\phi_i$. The values $T_i$ can be determined in several ways, with two common approaches being:
\begin{itemize}
    \item \textbf{Baseline Performance Normalization:} The thresholds $T_i$ are set to the diagonal elements of the eavesdropper's CRB matrix calculated using a simple, non-secure baseline precoder (e.g., Zero-Forcing or Matched-Filter). This method benchmarks the security improvement against a standard design.
    \item \textbf{Application-Driven Normalization:} The thresholds are set based on the level of estimation error that would render the eavesdropper's information useless for a specific task (e.g., an angle error variance making tracking impossible, or a symbol error variance leading to an unacceptably high SER).
\end{itemize}
The normalized FIM is then defined as $[\mathbf{J}_{\text{norm}}]_{i,j} = [\bar{\mathbf{J}}]_{i,j} \cdot \sqrt{T_i T_j}$.\footnote{This normalization is crucial for making the FIM dimensionless, which is necessary because the parameters have different physical units (e.g., radians vs. unitless) and numerical scales. The unit of a FIM element $[\bar{\mathbf{J}}]_{i,j}$ is $1/(\text{unit of } \phi_i \cdot \text{unit of } \phi_j)$, while the unit of a threshold $T_i$ (a variance) is $(\text{unit of } \phi_i)^2$. Multiplying them cancels the units, making the determinant of the normalized FIM a meaningful scalar objective. Note that normalizing the CRB matrix (the inverse of the FIM) would require division instead of multiplication.} The optimization problem is then:
\begin{align}
\underset{\mathbf{Q}_s, \mathbf{Z}}{\text{minimize}} & \quad \log(\det(\mathbf{J}_{\text{norm}})) \label{opt:d_opt_obj} \\
\text{subject to} & \quad \text{SINR}_k \geq \Gamma_k, \quad \forall k \in \{1, \dots, K\} \label{opt:qos_c} \\
                      & \quad \text{SINR}_{\text{BS}} \geq \Gamma_{\text{BS}} \label{opt:sensing_c} \\
                      & \quad \text{tr}(\mathbf{Q}_s + \mathbf{Z}) \leq P_{\text{max}} \label{opt:power_c} \\
                      & \quad \mathbf{Q}_s \succeq 0, \mathbf{Z} \succeq 0 \label{opt:psd_c}
\end{align}
where $\log(\det(\cdot))$ is used for numerical stability and to facilitate convex optimization techniques. The constraints ensure that while security is maximized, the system's operational requirements are met:
\begin{itemize}
    \item  Guarantees a minimum Quality of Service (SINR $\Gamma_k$) for each legitimate communication user.
    \item Ensures the BS's own sensing performance meets a minimum quality threshold (SINR above threshold $\Gamma_{\text{BS}}$).
    \item  Enforces the total transmit power budget.
    \item Ensures the covariance matrices are physically meaningful.
\end{itemize}

This problem is highly non-convex due to the objective function and the SINR and CRB constraints. However, it can be effectively solved using iterative algorithms like Successive Convex Approximation (SCA), which find a high-quality, locally optimal solution.

\section{SCA-Based Algorithm for Secure Waveform Design}
The optimization problem in \eqref{opt:d_opt_obj}-\eqref{opt:psd_c} is a non-convex problem. The non-convexity arises from two main sources: 1) the objective function $\log(\det(\mathbf{J}_{\text{norm}}))$ is a concave function of the optimization variables, and minimizing a concave function is non-convex; 2) the SINR constraints for communication users in \eqref{opt:qos_c} are non-convex as they are formulated.

To tackle this problem, we employ the SCA framework. The core idea is to approximate the non-convex problem with a sequence of convex subproblems.

\subsection{Problem Reformulation and Convex Approximation}
First, we address the non-convex SINR constraints for the communication users. The term $|\mathbf{h}_k^H \mathbf{f}_k|^2$ depends on the individual beamforming vector $\mathbf{f}_k$, while the optimization variable is the covariance matrix $\mathbf{Q}_s = \sum_{k=1}^K \mathbf{f}_k \mathbf{f}_k^H$. To resolve this, we introduce new matrix variables $\mathbf{F}_k = \mathbf{f}_k \mathbf{f}_k^H$, which must be positive semidefinite and have rank one, i.e., $\mathbf{F}_k \succeq 0$ and $\text{rank}(\mathbf{F}_k) = 1$. The signal covariance matrix is then $\mathbf{Q}_s = \sum_{k=1}^K \mathbf{F}_k$. The rank-one constraint is non-convex, so we relax it using Semidefinite Relaxation (SDR), keeping only $\mathbf{F}_k \succeq 0$. After solving the relaxed problem, we can recover a rank-one solution.

With this change, the SINR constraint \eqref{opt:qos_c} becomes:

\begin{equation}
\frac{\text{tr}(\mathbf{H}_k \mathbf{F}_k)}{\sum_{j \neq k} \text{tr}(\mathbf{H}_k \mathbf{F}_j) + \text{tr}(\mathbf{H}_k \mathbf{Z}) + \sigma_k^2} \geq \Gamma_k
\end{equation}
where $\mathbf{H}_k = \mathbf{h}_k \mathbf{h}_k^H$. This can be rewritten as a linear constraint:

\begin{equation}
\text{tr}(\mathbf{H}_k \mathbf{F}_k) \geq \Gamma_k \left( \sum_{j \neq k} \text{tr}(\mathbf{H}_k \mathbf{F}_j) + \text{tr}(\mathbf{H}_k \mathbf{Z}) + \sigma_k^2 \right)
\label{eq:sinr_k_convex}
\end{equation}
This constraint is now convex (in fact, linear) with respect to the variables {$\mathbf{F}_k$} and $\mathbf{Z}$.

Similarly, the sensing SINR constraint \eqref{opt:sensing_c} can be rewritten from \eqref{eq:sinr_bs} as:

\begin{equation}
\frac{|\alpha|^2 C_r \text{tr}(\mathbf{A}_t (\sum_k \mathbf{F}_k + \mathbf{Z}))}{\text{tr}(\mathbf{H}_{\text{SI,eff}} (\sum_k \mathbf{F}_k + \mathbf{Z})) + \sigma_{\text{BS}}^2 N_{\text{BS,r}}} \geq \Gamma_{\text{BS}}
\end{equation}

where $C_r = |\mathbf{w}_r^H \mathbf{a}_r(\theta_L)|^2$, $\mathbf{A}_t = \mathbf{a}_t(\theta_L)\mathbf{a}_t^H(\theta_L)$, and $\mathbf{H}_{\text{SI,eff}} = \mathbf{H}_{\text{SI}}^H \mathbf{w}_r \mathbf{w}_r^H \mathbf{H}_{\text{SI}}$. This is a linear fractional constraint, which is convex and can be transformed into a linear one:

\begin{equation}
|\alpha|^2 C_r \text{tr}(\mathbf{A}_t (\mathbf{Q}_s + \mathbf{Z})) \geq \Gamma_{\text{BS}} \left( \text{tr}(\mathbf{H}_{\text{SI,eff}} (\mathbf{Q}_s + \mathbf{Z})) + \sigma_{\text{BS}}^2 N_{\text{BS,r}} \right)
\label{eq:sinr_bs_convex}
\end{equation}
where $\mathbf{Q}_s = \sum_k \mathbf{F}_k$.

Next, we handle the non-convex objective function. Let $\mathcal{F} = \{\mathbf{F}_1, \dots, \mathbf{F}_K\}$. The objective function is $g(\mathcal{F}, \mathbf{Z}) = \log(\det(\mathbf{J}_{\text{norm}}(\mathcal{F}, \mathbf{Z})))$. Since this is a concave function, we can replace it with its first-order Taylor expansion around a feasible point $(\mathcal{F}^{(n)}, \mathbf{Z}^{(n)})$ from the $n$-th iteration. This provides a linear upper bound on the objective:

\begin{align}
g(\mathcal{F}, \mathbf{Z}) \leq g(\mathcal{F}^{(n)}, \mathbf{Z}^{(n)}) &+ \sum_{k=1}^K \text{tr}\left( \nabla_{\mathbf{F}_k} g^{(n)T} (\mathbf{F}_k - \mathbf{F}_k^{(n)}) \right) \nonumber \\
&+ \text{tr}\left( \nabla_{\mathbf{Z}} g^{(n)T} (\mathbf{Z} - \mathbf{Z}^{(n)}) \right)
\label{eq:obj_linearized}
\end{align}
where $\nabla_{\mathbf{F}_k} g^{(n)}$ and $\nabla_{\mathbf{Z}} g^{(n)}$ are the gradients of the objective function with respect to $\mathbf{F}_k$ and $\mathbf{Z}$ evaluated at the point $(\mathcal{F}^{(n)}, \mathbf{Z}^{(n)})$. Minimizing the right-hand side of \eqref{eq:obj_linearized} is a convex problem.

\subsection{The SCA Subproblem}
By replacing the original objective and constraints with their convex approximations, we formulate the following convex subproblem to be solved at each iteration $(n+1)$: 

\begin{align}
\underset{\{\mathbf{F}_k\}, \mathbf{Z}}{\text{minimize}} & \quad \sum_{k=1}^K \text{tr}(\nabla_{\mathbf{F}_k} g^{(n)T} \mathbf{F}_k) + \text{tr}(\nabla_{\mathbf{Z}} g^{(n)T} \mathbf{Z}) \label{opt:sca_obj} \\
\text{s.t.} & \quad \text{\eqref{eq:sinr_k_convex}, \eqref{eq:sinr_bs_convex} hold for all } k \label{opt:sca_c1} \\
             & \quad \sum_{k=1}^K \text{tr}(\mathbf{F}_k) + \text{tr}(\mathbf{Z}) \leq P_{\text{max}} \label{opt:sca_c2} \\
             & \quad \mathbf{F}_k \succeq 0, \mathbf{Z} \succeq 0 \label{opt:sca_c3}
\end{align}
This is a standard semidefinite program (SDP) and can be solved efficiently using existing convex optimization solvers such as CVX or YALMIP.

\subsection{Algorithm Summary}
The overall SCA-based algorithm is summarized in Algorithm~\ref{alg:sca}.

\begin{algorithm}[h]
\caption{SCA-Based Secure Waveform Design}
\label{alg:sca}
\begin{algorithmic}[1]
\STATE \textbf{Initialize:} Set iteration index $n=0$. Find a feasible starting point $(\{\mathbf{F}_k^{(0)}\}, \mathbf{Z}^{(0)})$.
\STATE \textbf{repeat}
\STATE \quad Calculate the gradients $\nabla_{\mathbf{F}_k} g^{(n)}$ and $\nabla_{\mathbf{Z}} g^{(n)}$ based on the current point $(\{\mathbf{F}_k^{(n)}\}, \mathbf{Z}^{(n)})$.
\STATE \quad Solve the convex subproblem \eqref{opt:sca_obj}-\eqref{opt:sca_c3} to obtain the optimal solution $(\{\mathbf{F}_k^*\}, \mathbf{Z}^*)$.
\STATE \quad Update the variables: $(\{\mathbf{F}_k^{(n+1)}\}, \mathbf{Z}^{(n+1)}) \leftarrow (\{\mathbf{F}_k^*\}, \mathbf{Z}^*)$.
\STATE \quad Set $n \leftarrow n+1$.
\STATE \textbf{until} a convergence criterion is met (e.g., the change in the objective function is below a threshold).
\STATE \textbf{Recover Rank-One Solution:} For each $\mathbf{F}_k$ that is not rank-one, perform a rank-one approximation (e.g., via Gaussian randomization) to obtain the final beamforming vectors $\mathbf{f}_k$.
\end{algorithmic}
\end{algorithm}

This iterative process is guaranteed to converge to a locally optimal solution of the original non-convex problem.
\subsection{Solution Approaches}

To address the non-convex optimization problem in \eqref{opt:d_opt_obj}-\eqref{opt:psd_c}, we propose two solution approaches. The first approach decouples the design by employing zero-forcing (ZF) for the artificial noise and SCA for the beamforming design, serving as a benchmark. The second approach jointly optimizes both variables using SCA.

\subsubsection{Benchmark: Zero-Forcing AN with SCA-Based Beamforming Design}

As a benchmark solution, we first consider a simplified approach where the AN is designed using the zero-forcing criterion, and subsequently, the beamforming matrix is optimized via SCA.

\paragraph{Zero-Forcing AN Design}
The ZF approach constrains the AN to lie in the null space of all legitimate users' channels to eliminate interference at the intended receivers. Let $\mathbf{H}_U = [\mathbf{h}_1, \mathbf{h}_2, \ldots, \mathbf{h}_K]^H \in \mathbb{C}^{K \times N_{\text{BS,t}}}$ denote the aggregate channel matrix. The null space projection matrix is given by
\begin{equation}
\mathbf{P}_{\perp} = \mathbf{I}_{N_{\text{BS,t}}} - \mathbf{H}_U^H(\mathbf{H}_U\mathbf{H}_U^H)^{-1}\mathbf{H}_U.
\label{eq:null_proj}
\end{equation}

The AN covariance matrix is then parameterized as
\begin{equation}
\mathbf{Z} = \mathbf{P}_{\perp}\mathbf{\Sigma}_Z\mathbf{P}_{\perp}^H,
\label{eq:zf_an}
\end{equation}
where $\mathbf{\Sigma}_Z \succeq 0$ is a design parameter matrix. This ensures that $\mathbf{h}_k^H\mathbf{Z}\mathbf{h}_k = 0$ for all $k \in \{1, \ldots, K\}$, thereby eliminating AN-induced interference at legitimate users.

\paragraph{SCA-Based Beamforming Optimization}
With the AN structure fixed via \eqref{eq:zf_an}, the optimization problem reduces to
\begin{align}
\underset{\mathbf{Q}_s, \mathbf{\Sigma}_Z}{\text{minimize}} & \quad \log(\det(\mathbf{J}_{\text{norm}}(\mathbf{Q}_s, \mathbf{Z}(\mathbf{\Sigma}_Z)))) \label{opt:zf_sca_obj} \\
\text{subject to} & \quad \text{SINR}_k \geq \Gamma_k, \quad \forall k \label{opt:zf_qos} \\
                      & \quad \text{SINR}_{\text{BS}} \geq \Gamma_{\text{BS}} \label{opt:zf_sensing} \\
                      & \quad \text{tr}(\mathbf{Q}_s) + \text{tr}(\mathbf{\Sigma}_Z) \leq P_{\text{max}} \label{opt:zf_power} \\
                      & \quad \mathbf{Q}_s \succeq 0, \mathbf{\Sigma}_Z \succeq 0. \label{opt:zf_psd}
\end{align}

Note that with the ZF constraint, the user SINR in \eqref{eq:sinr_k} simplifies to
\begin{equation}
\text{SINR}_k = \frac{|\mathbf{h}_k^H \mathbf{f}_k|^2}{\sum_{j \neq k} |\mathbf{h}_k^H \mathbf{f}_j|^2 + \sigma_{k}^2}.
\label{eq:sinr_k_zf}
\end{equation}

This problem remains non-convex due to the objective function and the structure of $\mathbf{Q}_s$. We apply SCA with semidefinite relaxation (SDR) as follows. Introduce $\mathbf{F}_k = \mathbf{f}_k\mathbf{f}_k^H$ with $\mathbf{Q}_s = \sum_{k=1}^K \mathbf{F}_k$, and linearize the objective function around the current iterate $(\mathbf{Q}_s^{(t)}, \mathbf{\Sigma}_Z^{(t)})$.

\subsubsection{Joint SCA-Based Design for Both Variables}

For the joint optimization approach, we simultaneously optimize both $\mathbf{Q}_s$ and $\mathbf{Z}$ without imposing the ZF structure, allowing for a more flexible design that can potentially achieve better security performance.

\paragraph{Problem Reformulation}
We introduce auxiliary matrix variables $\mathbf{F}_k = \mathbf{f}_k\mathbf{f}_k^H$ for $k = 1, \ldots, K$, with $\mathbf{Q}_s = \sum_{k=1}^K \mathbf{F}_k$. The SINR constraint for user $k$ becomes
\begin{equation}
\frac{\text{tr}(\mathbf{H}_k \mathbf{F}_k)}{\sum_{j \neq k} \text{tr}(\mathbf{H}_k \mathbf{F}_j) + \text{tr}(\mathbf{H}_k \mathbf{Z}) + \sigma_k^2} \geq \Gamma_k,
\label{eq:sinr_k_sdp}
\end{equation}
where $\mathbf{H}_k = \mathbf{h}_k\mathbf{h}_k^H$. This can be equivalently written as the linear constraint
\begin{equation}
\text{tr}(\mathbf{H}_k \mathbf{F}_k) - \Gamma_k \sum_{j \neq k} \text{tr}(\mathbf{H}_k \mathbf{F}_j) - \Gamma_k\text{tr}(\mathbf{H}_k \mathbf{Z}) \geq \Gamma_k\sigma_k^2.
\label{eq:sinr_k_linear}
\end{equation}

Similarly, the sensing SINR constraint \eqref{opt:sensing_c} is reformulated as
\begin{equation}
|\alpha|^2 C_r \text{tr}(\mathbf{A}_t (\mathbf{Q}_s + \mathbf{Z})) - \Gamma_{\text{BS}} \text{tr}(\mathbf{H}_{\text{SI,eff}} (\mathbf{Q}_s + \mathbf{Z})) \geq \Gamma_{\text{BS}}\sigma_{\text{BS}}^2 N_{\text{BS,r}},
\label{eq:sinr_bs_linear}
\end{equation}
where $C_r = |\mathbf{w}_r^H \mathbf{a}_r(\theta_L)|^2$, $\mathbf{A}_t = \mathbf{a}_t(\theta_L)\mathbf{a}_t^H(\theta_L)$, and $\mathbf{H}_{\text{SI,eff}} = \mathbf{H}_{\text{SI}}^H \mathbf{w}_r \mathbf{w}_r^H \mathbf{H}_{\text{SI}}$.

\paragraph{Successive Convex Approximation}
At iteration $t$, we linearize the concave objective function $g(\{\mathbf{F}_k\}, \mathbf{Z}) = \log(\det(\mathbf{J}_{\text{norm}}))$ around the current point $(\{\mathbf{F}_k^{(t)}\}, \mathbf{Z}^{(t)})$:
\begin{align}
g(\{\mathbf{F}_k\}, \mathbf{Z}) &\leq g(\{\mathbf{F}_k^{(t)}\}, \mathbf{Z}^{(t)}) + \sum_{k=1}^K \text{tr}(\nabla_{\mathbf{F}_k} g^{(t)} (\mathbf{F}_k - \mathbf{F}_k^{(t)})) \nonumber \\
&\quad + \text{tr}(\nabla_{\mathbf{Z}} g^{(t)} (\mathbf{Z} - \mathbf{Z}^{(t)})),
\label{eq:obj_linear}
\end{align}
where the gradients are computed as
\begin{align}
\nabla_{\mathbf{F}_k} g^{(t)} &= \frac{\partial \log(\det(\mathbf{J}_{\text{norm}}))}{\partial \mathbf{F}_k}\bigg|_{(\{\mathbf{F}_k^{(t)}\}, \mathbf{Z}^{(t)})}, \\
\nabla_{\mathbf{Z}} g^{(t)} &= \frac{\partial \log(\det(\mathbf{J}_{\text{norm}}))}{\partial \mathbf{Z}}\bigg|_{(\{\mathbf{F}_k^{(t)}\}, \mathbf{Z}^{(t)})}.
\end{align}

\paragraph{Convex Subproblem}
The SCA subproblem at iteration $t+1$ is formulated as
\begin{align}
\underset{\{\mathbf{F}_k\}, \mathbf{Z}}{\text{minimize}} & \quad \sum_{k=1}^K \text{tr}(\nabla_{\mathbf{F}_k} g^{(t)} \mathbf{F}_k) + \text{tr}(\nabla_{\mathbf{Z}} g^{(t)} \mathbf{Z}) \label{opt:sca_sub_obj} \\
\text{subject to} & \quad \text{\eqref{eq:sinr_k_linear} for all } k \in \{1, \ldots, K\} \label{opt:sca_sub_c1} \\
             & \quad \text{\eqref{eq:sinr_bs_linear}} \label{opt:sca_sub_c2} \\
             & \quad \sum_{k=1}^K \text{tr}(\mathbf{F}_k) + \text{tr}(\mathbf{Z}) \leq P_{\text{max}} \label{opt:sca_sub_c3} \\
             & \quad \mathbf{F}_k \succeq 0, \forall k; \quad \mathbf{Z} \succeq 0. \label{opt:sca_sub_c4}
\end{align}

This is a standard semidefinite program (SDP) that can be solved efficiently using interior-point methods.

\paragraph{Algorithm Implementation}
The complete joint SCA algorithm is summarized in Algorithm~\ref{alg:joint_sca}.

\begin{algorithm}[h]
\caption{Joint SCA-Based Secure Waveform Design}
\label{alg:joint_sca}
\begin{algorithmic}[1]
\STATE \textbf{Initialize:} Set $t=0$. Choose feasible starting points $\{\mathbf{F}_k^{(0)}\}_{k=1}^K$ and $\mathbf{Z}^{(0)}$ satisfying all constraints.
\REPEAT
\STATE Compute the normalized FIM $\mathbf{J}_{\text{norm}}^{(t)}$ and its gradients $\nabla_{\mathbf{F}_k} g^{(t)}$, $\nabla_{\mathbf{Z}} g^{(t)}$.
\STATE Solve the convex subproblem \eqref{opt:sca_sub_obj}-\eqref{opt:sca_sub_c4} to obtain $(\{\mathbf{F}_k^*\}, \mathbf{Z}^*)$.
\STATE Update: $\mathbf{F}_k^{(t+1)} = \mathbf{F}_k^*$, $\mathbf{Z}^{(t+1)} = \mathbf{Z}^*$ for all $k$.
\STATE $t \leftarrow t+1$.
\UNTIL{$\|\mathbf{F}_k^{(t)} - \mathbf{F}_k^{(t-1)}\|_F / \|\mathbf{F}_k^{(t-1)}\|_F < \epsilon$ for all $k$ and $\|\mathbf{Z}^{(t)} - \mathbf{Z}^{(t-1)}\|_F / \|\mathbf{Z}^{(t-1)}\|_F < \epsilon$}
\STATE \textbf{Output:} If $\text{rank}(\mathbf{F}_k) > 1$ for any $k$, apply Gaussian randomization to obtain rank-one solutions.
\end{algorithmic}
\end{algorithm}

\paragraph{Convergence Analysis}
\begin{proposition}
The sequence $\{(\{\mathbf{F}_k^{(t)}\}, \mathbf{Z}^{(t)})\}_{t=0}^{\infty}$ generated by Algorithm~\ref{alg:joint_sca} satisfies:
\begin{enumerate}
\item The objective sequence $\{\log(\det(\mathbf{J}_{\text{norm}}^{(t)}))\}$ is monotonically non-increasing.
\item Every limit point of the sequence satisfies the Karush-Kuhn-Tucker (KKT) conditions of the original problem \eqref{opt:d_opt_obj}-\eqref{opt:psd_c}.
\end{enumerate}
\end{proposition}

\begin{IEEEproof}
The proof follows from the standard convergence analysis of the majorization-minimization (MM) framework. Since the linearization in \eqref{eq:obj_linear} provides a tight upper bound at the current iterate, and the feasible set is compact, the sequence converges to a stationary point of the original problem.
\end{IEEEproof}

\paragraph{Computational Complexity}
The per-iteration complexity of solving the SDP subproblem is $\mathcal{O}((K+1)^3 N_{\text{BS,t}}^3)$, where the factor $(K+1)$ accounts for the $K$ beamforming matrices and one AN covariance matrix. For typical ISAC systems with moderate numbers of antennas and users, this complexity is tractable using modern SDP solvers.



\bibliographystyle{IEEEtran}
% argument is your BibTeX string definitions and bibliography database(s)
\bibliography{IEEEabrv,CEP_REF}


\end{document}
